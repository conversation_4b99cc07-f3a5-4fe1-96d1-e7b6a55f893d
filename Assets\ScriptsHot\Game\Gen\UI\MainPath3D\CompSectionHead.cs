/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.MainPath3D
{
    public partial class CompSectionHead : UIBindT
    {
        public override string pkgName => "MainPath3D";
        public override string comName => "CompSectionHead";

        public GImage bg;
        public GTextField title1;
        public GTextField title2;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            bg = (GImage)com.GetChildAt(0);
            title1 = (GTextField)com.GetChildAt(1);
            title2 = (GTextField)com.GetChildAt(2);

            SetMultiLanguageInChildren();
            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            bg = null;
            title1 = null;
            title2 = null;
        }

        public void SetMultiLanguageInChildren()
        {
            this.bg.<PERSON><PERSON>("BLUR");  // ""
        }
    }
}