/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class CompleteAnswer : AFragAnswer
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "CompleteAnswer";
        public static string url => "ui://cmoz5osjz7rm32";

        public GTextField tfSample;
        public GList listTexts;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(CompleteAnswer));
        }

        public override void ConstructFromXML(XML xml)
        {
            tfSample = GetChildAt(1) as GTextField;
            listTexts = GetChildAt(2) as GList;
        }
        public override void Dispose()
        {
            tfSample = null;
            listTexts = null;

            base.Dispose();
        }
    }
}