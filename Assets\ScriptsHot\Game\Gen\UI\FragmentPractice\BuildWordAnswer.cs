/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class BuildWordAnswer : AFragAnswer
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "BuildWordAnswer";
        public static string url => "ui://cmoz5osjvvg3uvptd5";

        public GList listOptions;
        public GTextField tfAnswer;
        public GTextField tfDebug;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(BuildWordAnswer));
        }

        public override void ConstructFromXML(XML xml)
        {
            listOptions = GetChildAt(0) as GList;
            tfAnswer = GetChildAt(2) as GTextField;
            tfDebug = GetChildAt(3) as GTextField;
        }
        public override void Dispose()
        {
            listOptions = null;
            tfAnswer = null;
            tfDebug = null;

            base.Dispose();
        }
    }
}