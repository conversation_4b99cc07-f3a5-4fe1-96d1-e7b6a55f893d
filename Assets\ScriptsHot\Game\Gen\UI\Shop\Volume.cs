/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.Shop
{
    public partial class Volume : ExtendedComponent
    {
        public static string pkgName => "Shop";
        public static string comName => "Volume";
        public static string url => "ui://0xk8jvcep4mu9e";

        public GImage imageForeground;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(Volume));
        }

        public override void ConstructFromXML(XML xml)
        {
            imageForeground = GetChildAt(0) as GImage;

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();

            imageForeground = null;

            base.Dispose();
        }
    }
}