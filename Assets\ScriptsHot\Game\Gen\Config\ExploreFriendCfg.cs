
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;



public sealed partial class ExploreFriendCfg : Luban.BeanBase
{
    public ExploreFriendCfg(ByteBuf _buf) 
    {
        ID = _buf.ReadInt();
        NameKey = _buf.ReadString();
        Rarity = _buf.ReadInt();
        RarityValue = _buf.ReadInt();
        Job = _buf.ReadString();
        Hobby = _buf.ReadString();
        Character = _buf.ReadString();
        BGImgUrlResult = _buf.ReadString();
        BGImgUrl = _buf.ReadString();
        AvatarModleId = _buf.ReadLong();
        HeadIcon = _buf.ReadString();
        introduceSoundId1 = _buf.ReadString();
        introduceSoundId2 = _buf.ReadString();
        introduceTxt1 = _buf.ReadString();
        introduceTxt2 = _buf.ReadString();
    }

    public static ExploreFriendCfg DeserializeExploreFriendCfg(ByteBuf _buf)
    {
        return new ExploreFriendCfg(_buf);
    }

    /// <summary>
    /// 角色Id
    /// </summary>
    public readonly int ID;
    /// <summary>
    /// 名字多语言
    /// </summary>
    public readonly string NameKey;
    /// <summary>
    /// 稀有度(显示flag
    /// </summary>
    public readonly int Rarity;
    /// <summary>
    /// 稀有度 * 100(例：3.3%就填330)
    /// </summary>
    public readonly int RarityValue;
    /// <summary>
    /// job多语言
    /// </summary>
    public readonly string Job;
    /// <summary>
    /// Hobby多语言
    /// </summary>
    public readonly string Hobby;
    /// <summary>
    /// Character多语言
    /// </summary>
    public readonly string Character;
    /// <summary>
    /// 背景图片(抽卡结果用
    /// </summary>
    public readonly string BGImgUrlResult;
    /// <summary>
    /// 背景图片(展示
    /// </summary>
    public readonly string BGImgUrl;
    /// <summary>
    /// 模型Id
    /// </summary>
    public readonly long AvatarModleId;
    /// <summary>
    /// 头像
    /// </summary>
    public readonly string HeadIcon;
    /// <summary>
    /// 介绍语音1
    /// </summary>
    public readonly string introduceSoundId1;
    /// <summary>
    /// 介绍语音2
    /// </summary>
    public readonly string introduceSoundId2;
    /// <summary>
    /// 介绍文本1
    /// </summary>
    public readonly string introduceTxt1;
    /// <summary>
    /// 介绍文本2
    /// </summary>
    public readonly string introduceTxt2;
   
    public const int __ID__ = -1840107597;
    public override int GetTypeId() => __ID__;

    public  void ResolveRef(Tables tables)
    {
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
    }

    public override string ToString()
    {
        return "{ "
        + "ID:" + ID + ","
        + "NameKey:" + NameKey + ","
        + "Rarity:" + Rarity + ","
        + "RarityValue:" + RarityValue + ","
        + "Job:" + Job + ","
        + "Hobby:" + Hobby + ","
        + "Character:" + Character + ","
        + "BGImgUrlResult:" + BGImgUrlResult + ","
        + "BGImgUrl:" + BGImgUrl + ","
        + "AvatarModleId:" + AvatarModleId + ","
        + "HeadIcon:" + HeadIcon + ","
        + "introduceSoundId1:" + introduceSoundId1 + ","
        + "introduceSoundId2:" + introduceSoundId2 + ","
        + "introduceTxt1:" + introduceTxt1 + ","
        + "introduceTxt2:" + introduceTxt2 + ","
        + "}";
    }
}


