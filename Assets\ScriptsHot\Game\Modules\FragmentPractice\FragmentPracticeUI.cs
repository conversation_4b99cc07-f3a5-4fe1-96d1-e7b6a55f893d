﻿using FairyGUI;
using UIBind.FragmentPractice;
using Game.Modules.FragmentPractice;
using UnityEngine;
using UIBind.Main;

namespace ScriptsHot.Game.Modules.FragmentPractice
{
    [TransitionUI(CutInEffect.BottomToTop)]
    public class FragmentPracticeUI : BaseUI<FragmentPracticePanel>
    {

        public enum PageState
        {
            normal = 0, //正常
            stamina = 1, //体力任务
            resign = 2, //打卡补签
        }

        public struct FragmentPracticeUIArgs
        {
            public PageState pageState;
            public int signDays;
            public int breakDays;
            public int earnStaminaCnt;
        }

        public FragmentPracticeUI(string name) : base(name)
        {
        }

        public override string uiLayer => UILayerConsts.Top;
        private FragmentPracticeUIArgs _openArgs;

        private CompMain compMain;
        private StarX5Background Background => ui.imgBG as StarX5Background;

        protected override void OnInit(GComponent uiCom)
        {
            compMain = ui.CompMain;
            compMain.Init(this, Background);
            compMain.CompHead.OnExit += OnExit;
        }

        protected override void OnShow()
        {
            QuestionEventManager.Instance.Reset();
            ui.compLoad.state.SetSelectedPage("normal");
            ui.compLoad.tfLoading.SetKey("fragment_loading");

            ui.CompMain.CompBottom.touchable = true;
            
            ResetetFullScreen();

            if (args.Length > 0)
            {
                _openArgs = (FragmentPracticeUIArgs)args[0];
                ui.CompMain.CompHead.SetOpenArgs(_openArgs);
                if (_openArgs.pageState == PageState.stamina)
                {
                    ui.compLoad.tfStamina.SetKeyArgs("ui_fragmentPractice_loading_stamina", (int)(_openArgs.earnStaminaCnt / 20));
                }

                if (_openArgs.pageState == PageState.resign)
                {
                    ui.compLoad.tfReSign.SetKeyArgs("ui_fragmentPractice_loading_sign", _openArgs.signDays);
                }
            }
            ui.compLoad.state.SetSelectedPage(_openArgs.pageState.ToString());

            ui.fakeLoad.Play(1, 0, () =>
             {
                 if (AppConst.IsDebug && FragModel.fixedQids != null)
                 {
                     // 调试路径
                     FragController.DebugEnterPractices();
                 }
                 else
                 {
                     FragController.SendGetQuickPracticeListReq();
                 }
             });

            ui.CompMain.OnShow();
        }
        public override void OnBackBtnClick()
        {
            compMain.CompHead?.OnClickClose();
        }

        //loading图的临时补丁，现在他不是baseUI类的，也不是扩展类的
        private void ResetetFullScreen()
        {
            var imgBG = this.ui.compLoad.imgBG;
                   
            var fullRect = GRoot.inst.TransformRect(new Rect(0, -UIManager.instance.safeAreaTopHeight, UIManager.instance.width,
                UIManager.instance.height + UIManager.instance.safeAreaTopHeight + UIManager.instance.safeAreaBottomHeight), this.uiCom);
            imgBG.SetSize(fullRect.width, fullRect.height);
            imgBG.SetXY(fullRect.x, fullRect.y);
        }

        public void FinalShow()
        {
            DataDotAppear_FragmentPractice_appear dot = new DataDotAppear_FragmentPractice_appear();
            dot.List_id = FragController.UnitId;
            DataDotMgr.Collect(dot);

            ui.CompMain.CompHead.FirstShowBar();
            if (FragModel.JumpMode)
            {
                // ui.CompMain.CompContinue.ShowJumpPage(FragModel.JumpTaskShieldCurNum);
                // ui.CompMain.CompBottom.EnableContinueBtn();
                ui.CompMain.CompHead.state.selectedPage = "jump";
                
                // ui.show.Play();
                // return;
            }
            ui.show.Play();
            ui.CompMain.ShowPractices();
        }

        protected override void OnHide()
        {
            ui.CompMain.OnHide();
            if (GSoundManager.instance.IsPlaying("TTS")) TTSManager.instance.StopTTS();
            FragController.QuitPractice();
        }

        protected override bool isFullScreen => true;

        private FragmentPracticeController FragController =>
            GetController<FragmentPracticeController>(ModelConsts.FragmentPractice);

        private FragmentPracticeModel FragModel => GetModel<FragmentPracticeModel>(ModelConsts.FragmentPractice);
        
        private async void OnExit()
        {
            Notifier.instance.SendNotification(NotifyConsts.MainPath3DBackPath);
            FragController.SendExitQuickPracticeReq();
            //FragController.ExitPractice();
            Hide();
        }
    }
}