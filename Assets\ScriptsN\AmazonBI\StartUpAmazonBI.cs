using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class StartUpAmazonBI
{
    private const string _cut_start = "cut_start";
    private const string _cut_hotupdate_complete = "cut_hotupdate_complete";
    private const string _appear_login_ui = "appear_login_ui";
    private const string _click_apple_id = "click_apple_id";
    private const string _cut_login_complate = "cut_login_complate";

    private const string _key = "device_id";

    public static void cut_start()
    {
        Dictionary<string, object> dic = ApplyBiDic();
        dic.TryAdd("usage_id", Main.BiGuid);
        dic.TryAdd(_key, AmazonBi.DeviceId);
        Collect(_cut_start, dic);
    }
    public static void cut_hotupdate_complete()
    {
        Dictionary<string, object> dic = ApplyBiDic();
        dic.TryAdd(_key, AmazonBi.DeviceId);
        Collect(_cut_hotupdate_complete, dic);
    }
    public static void appear_login_ui()
    {
        Dictionary<string, object> dic = ApplyBiDic();
        dic.TryAdd(_key, AmazonBi.DeviceId);
        Collect(_appear_login_ui, dic);
    }
    public static void click_apple_id()
    {
        Dictionary<string, object> dic = ApplyBiDic();
        dic.TryAdd(_key, AmazonBi.DeviceId);
        Collect(_click_apple_id, dic);
    }
    public static void cut_login_complate()
    {
        Dictionary<string, object> dic = ApplyBiDic();
        dic.TryAdd(_key, AmazonBi.DeviceId);
        Collect(_cut_login_complate, dic);
    }
    
    

    
    
    
    
    #region BI复制过来的
    private static Queue<Dictionary<string, object>> _dicPool = new Queue<Dictionary<string, object>>();
    public static Dictionary<string, object> ApplyBiDic()
    {
        Dictionary<string, object> dic;
        if (_dicPool.Count > 0)
            dic = _dicPool.Dequeue();
        {
            dic = new Dictionary<string, object>();
        }
        return dic;
    }

    public static void reclaimTask(Dictionary<string, object> dic)
    {
        if (dic == null) return;
        dic.Clear();
        _dicPool.Enqueue(dic);
    }

    /// <summary>
    /// 打点公共事件
    /// </summary>
    /// <param name="eventName">事件名称</param>
    /// <param name="paramDict">打点数据</param>
    public static void Collect(string eventName, Dictionary<string, object> paramDict = null)
    {
        AddBaseData(paramDict);
        AmazonBi.SendBI(eventName,paramDict);
        reclaimTask(paramDict);
    }

    private static void AddBaseData(Dictionary<string, object> dic)
    {
        dic.TryAdd("client_version", "0");
        dic.TryAdd("userId", AmazonBi.MainModel_userID);
        dic.TryAdd("model", SystemInfo.deviceModel);        
        dic.TryAdd("networkType", Application.internetReachability);
  
        if (Application.platform == RuntimePlatform.IPhonePlayer)
        {
            dic.TryAdd("os", "iOS");
        }
        else if (Application.platform == RuntimePlatform.Android)
        {
            dic.TryAdd("os", "Android");
        }
        dic.TryAdd("osVersion", SystemInfo.operatingSystem);
        dic.TryAdd("version", Application.version);
        dic.TryAdd("ip", AmazonBi.GetLocalIPAddress());
        dic.TryAdd("session-id", Main.BiGuid);
    }
    #endregion
}
