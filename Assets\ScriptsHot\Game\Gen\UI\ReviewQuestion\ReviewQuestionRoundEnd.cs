/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.ReviewQuestion
{
    public partial class ReviewQuestionRoundEnd : AQuestionCard
    {
        public static string pkgName => "ReviewQuestion";
        public static string comName => "ReviewQuestionRoundEnd";
        public static string url => "ui://xlh8p6j0l44c3e";

        public GTextField mainTip;
        public GTextField subtitle;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(ReviewQuestionRoundEnd));
        }

        public override void ConstructFromXML(XML xml)
        {
            mainTip = GetChildAt(2) as GTextField;
            subtitle = GetChildAt(3) as GTextField;

            SetMultiLanguageInChildren();
        }
        public override void Dispose()
        {
            mainTip = null;
            subtitle = null;

            base.Dispose();
        }
        public void SetMultiLanguageInChildren()
        {
            this.subtitle.SetKey("question_complete_complete");  // "COMPLETED!"
        }
    }
}