﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Msg;
using Msg.task_process;
using UnityEngine;

public class GameStateSceneLoading : GameStateBase
{
    private string _nextStateName = GameState.None;

    public GameStateSceneLoading() : base(GameState.SceneLoading){ }

    public override void OnEnter(params object[] args)
    {
        base.OnEnter(args);
        
        this.owner.GetUI(UIConsts.SceneLocate).Hide();
        // this.owner.GetUI(UIConsts.MainHeader).Hide();
        
        this.owner.GetUI(UIConsts.CenterHome).Hide();
    }

    public override void OnExit()
    {
        base.OnExit();
    }


    public void SetNextState(string nextState)
    {

    }
}