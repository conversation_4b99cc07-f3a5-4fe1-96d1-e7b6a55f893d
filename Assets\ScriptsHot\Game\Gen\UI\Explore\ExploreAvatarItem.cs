/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Explore
{
    public partial class ExploreAvatarItem : UIBindT
    {
        public override string pkgName => "Explore";
        public override string comName => "ExploreAvatarItem";

        public Controller ctrl;
        public ExploreAvatarTxt comTxt;
        public ExploreSoundFlag effectSound;
        public ExploreTranslateBtn btnTranslate;
        public GGroup groupBtn;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ctrl = com.GetControllerAt(0);
            comTxt = new ExploreAvatarTxt();
            comTxt.Construct(com.GetChildAt(0).asCom);
            effectSound = new ExploreSoundFlag();
            effectSound.Construct(com.GetChildAt(1).asCom);
            btnTranslate = new ExploreTranslateBtn();
            btnTranslate.Construct(com.GetChildAt(2).asCom);
            groupBtn = (GGroup)com.GetChildAt(3);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ctrl = null;
            comTxt.Dispose();
            comTxt = null;
            effectSound.Dispose();
            effectSound = null;
            btnTranslate.Dispose();
            btnTranslate = null;
            groupBtn = null;
        }
    }
}