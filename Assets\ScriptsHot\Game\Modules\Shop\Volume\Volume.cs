
using FairyGUI;
using UnityEngine;
using Random = UnityEngine.Random;

namespace UIBind.Shop
{
    public partial class Volume : ExtendedComponent
    {
        private static Texture noiseTexture;
        private static readonly string texName = "Volume_noiseTexture2";
        private static readonly int ColorTex = Shader.PropertyToID("_ColorTex");
        private static readonly int NoiseTex = Shader.PropertyToID("_NoiseTex");
        private static readonly int RandomSeed = Shader.PropertyToID("_RandomSeed");
        private static readonly int DodgeAmount = Shader.PropertyToID("_DodgeAmount");
        private static readonly int InvertUI = Shader.PropertyToID("_InvertUI");
        private static readonly int EdgeX = Shader.PropertyToID("_EdgeX");
        private static readonly int UIMinMax = Shader.PropertyToID("_UIMinMax");
        private static readonly int FillX = Shader.PropertyToID("_FillX");

        private Material _material;


        protected override void OnAddedToStage()
        {
            if (!noiseTexture)
            {
                if (GResManager.instance.CheckLocationValid(texName))
                {
                    noiseTexture = GResManager.instance.LoadTextureSync(texName);
                }
                else
                {
                    VFDebug.LogError("没找到噪声图资源!!查一下资源目录/YooAsset配置");
                }
            }
            
            this.ChangeMat(this.imageForeground);
        }

        protected override void OnRemovedFromStage()
        {
            base.OnRemovedFromStage();
        }
        
        private void ChangeMat(GImage image)
        {
            var displayObject = image.displayObject;
            
            if (displayObject == null)
            {
                VFDebug.LogError("追踪Renderer失败!displayObject为null");
                return;
            }
      
            if (displayObject.graphics == null)
            {
                VFDebug.LogError("追踪Renderer失败!displayObject.graphics为null");
                return;
            }

            var material = displayObject.graphics.material;

            Debug.LogError(image.flip.ToString());
            
            // if (!material || material.shader.name == "Universal Render Pipeline/Lit" || material.shader.name == "Hidden/InternalErrorShader")
            // {
            //     material = displayObject.graphics.material;
            // }
            
            if (material)
            {
                material = new Material(Shader.Find("SpecialUI/VolumeShader"));
                material.SetTexture(ColorTex,image.texture.nativeTexture);
                material.SetTexture(NoiseTex,noiseTexture);
                material.SetTextureScale(NoiseTex,new Vector2(0.75f,0.75f));
                // ... existing code ...
                long userID = GameEntry.MainC.MainModel.userID;
                int hash = userID.GetHashCode();
                float t = (hash / (float)int.MaxValue + 1f) / 2f; // 0~1
                material.SetFloat(RandomSeed, t);
                material.SetFloat(DodgeAmount,0.8f);
                // #if UNITY_IOS && !UNITY_EDITOR
                // material.SetFloat(InvertUI,0.0f);
                // #endif
                
                material.EnableKeyword("_INVERTUI");
                
                material.SetFloat(EdgeX,0.12f);
                Rect rect = image.texture.uvRect;
                material.SetVector(UIMinMax,new Vector4(rect.xMin,rect.yMin,rect.xMax,rect.yMax));
                Debug.LogError($"{rect.xMin},{rect.yMin},{rect.xMax},{rect.yMax}");
                displayObject.graphics.material = material;
                this._material = material;
                SetFilling(1);
            }
            else
            {
                VFDebug.LogError($"添加失败,信息:{displayObject.name}+{displayObject.gOwner.name}+{displayObject.gOwner.gameObjectName}" );
            }
            
        }

        public void SetFilling(float fill)
        {
            if (_material == null)
            {
                this.ChangeMat(this.imageForeground);
                VFDebug.Log("SetFilling material");
                return;
            }
            this._material.SetFloat(FillX,fill);
        }
    }   
}