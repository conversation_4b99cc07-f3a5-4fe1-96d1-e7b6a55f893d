using System;
using System.Collections.Generic;
using FairyGUI;
using Lean.Touch;
using LitJson;
using Msg.center;
using Msg.economic;
using Msg.social;
using ScriptsHot.Game.Modules.Scene.Level.Component;
using ScriptsHot.Game.Modules.Scene.Level.Homepage;
using Msg.incentive;
using ScriptsHot.Game.Modules.Managers;
using ScriptsHot.Game.Modules.ChatLogicNew;
using ScriptsHot.Game.Modules.Consts;
using ScriptsHot.Game.Modules.Login.OnBoarding;
using ScriptsHot.Game.Modules.Main;
using ScriptsHot.Game.Modules.Profile;
using ScriptsHot.Game.Modules.Sign;
using ScriptsHot.Game.Modules.Stamina;
using UnityEngine;
using ScriptsHot.Game.Modules.ChatStart;
using ScriptsHot.Game.Modules.Guide;
using ScriptsHot.Game.Modules.Shop;
using ScriptsHot.Game.Modules.System;
using UIBind.Main;
using UIBind.Shop;
#if UNITY_IOS && !UNITY_EDITOR
using MTPush;
using JPush;
#endif

public class MainController : BaseController
{
    private GameStateMachine _stateMachine = null;
    private Dictionary<float, GestureParam> _leanGestureParams = null;
    private Dictionary<string, Queue<Action>> _stateActionCaches = new();

    public MainController() : base(ModelConsts.Main) { }

    private bool _isFingerDownOnUI = false;
    
    private MainModel _MainModel => this.GetModel<MainModel>(ModelConsts.Main);
    public MainModel MainModel => _MainModel;
    
    private ChatStartController chatStartCtrl => GetController<ChatStartController>(ModelConsts.ChatStartController);
    
    public override void OnInit()
    {
        CommonUtils.Instance.InitBinds();
        this.RegisterModel(new MainModel());
        this._stateMachine = new GameStateMachine(this);
        this.InitLeanTouch();
        _MainModel.SetChatEnterPos();
        SDKManager.instance.RegisterAction(SDKEvents.OnOpenUI, OnOpenUI);
        //TryGetCacheLaunchOptions();
        
#if !PHONE4CN
        new TriggerEeaAuthorization().Fire(); //20250520 by raybit 1.2.0首轮热更重禁用，后续在处理
#endif

        
        Notifier.instance.RegisterNotification(ModelConsts.ToGameIdleState,OnChangeToIdle);
        Notifier.instance.RegisterNotification(NotifyConsts.AddGrowthExp,AddGrowthExp);
    }

    public override void OnUIInit()
    {
        // this.RegisterUI(new MainHeaderUI(UIConsts.MainHeader));
        this.RegisterUI(new WorldHomepageUI(UIConsts.WorldHomepageUI));
    }

    public override void OnNetReConn()
    {
        base.OnNetReConn();
        _stateMachine.OnNetReConn();
    }

    public override void OnNetDisConn()
    {
        base.OnNetDisConn();
        _stateMachine.OnNetDisConn();
    }

    //=============================================================外部接口==================================================

    // public void ShowGrowthExpAnime()
    // {
    //     GetUI<MainHeaderUI>(UIConsts.MainHeader).RefreshGrowthExp();
    // }

    public void DoEnterGame()
    {
        ControllerManager.instance.OnEnterGame();
        CacheMsgManager.instance.ReqCommonCacheDataBatch();
        GameEntry.ShopC.GetUserVoiceCloneAudio();
        
        // SendLearnPathProto();
        //ReqGetIncentiveData(); 让首页自己去刷
    }

    // private async void SendLearnPathProto()
    // {
    //     //提前申请数据
    //     var learnCtrl = ControllerManager.instance.GetController<LearnPathController>(ModelConsts.LearnPath);
    //
    //     learnCtrl.SendGetChapterProgressInfo();//比较快
    //
    //     //var resp = await MsgManager.instance.SendAsyncMsg<SC_GetLearnPathPageTypeAck>(new CS_GetLearnPathPageTypeReq() );
    //
    //     //onboarding的首次登录时这条协议立即发较大概率会报错，尝试delay一点
    //     TimerManager.instance.RegisterTimer((c) =>
    //     {
    //         learnCtrl.SendGetUserGoalNodeOnEntergame(0, chatStartCtrl.PreLoadChatStartData);
    //     }, 300, 1);
    //
    //
    // }
    public override void OnUpdate(int interval)
    {
        base.OnUpdate(interval);
        _stateMachine.Update(interval);
    }
    
    public void RefreshCurrency()
    {
        // var mainHeader = GetUI<MainHeaderUI>(UIConsts.MainHeader);
        // if (mainHeader.isShow)
        // {
        //     mainHeader.RefershHeadBar(false, true);
        // }
        
        SendNotification(NotifyConsts.MainHeadRefreshEvent);
        
    }

    public void ChangeState(string stateName, params object[] args)
    {
        //TODO 判定当前状态是否可外部流转
        this._stateMachine.ChangeState(stateName, args);
        if (_stateActionCaches.ContainsKey(stateName) && _stateActionCaches[stateName].Count > 0)
        {
            InvokeActionCaches(stateName);

            SendNotification(NotifyConsts.MainCtrlChangeState, stateName);
        }
    }

    private void OnChangeToIdle(string s, object body)
    {
      
        ChangeState(GameState.SceneIdle, body);
    }

    public void RegisterActionCache(string stateName, Action action)
    {
        if (!_stateActionCaches.ContainsKey(stateName))
        {
            _stateActionCaches.Add(stateName, new Queue<Action>());
        }

        _stateActionCaches[stateName].Enqueue(action);
    }

    public void ClearActionCache(string stateName)
    {
        if (_stateActionCaches.ContainsKey(stateName))
            _stateActionCaches[stateName].Clear();
    }

    private void InvokeActionCaches(string stateName)
    {
        //VFDebug.Log("start dequeue state machine action");
        if (_stateActionCaches[stateName].Count > 0)
        {
            VFDebug.Log($"start dequeue {stateName} action cache: curIndex {_stateActionCaches[stateName].Count}");
            _stateActionCaches[stateName].Dequeue().Invoke();
            InvokeActionCaches(stateName);
        }
    }

    public void ChangeToTaskState(SceneStateTaskParam param)
    {

        SceneController sceneController = this.GetController<SceneController>(ModelConsts.Scene);
        var chatComp = sceneController.scene.GetComponent<ChatComponent>();

        if (chatComp != null)
        {
            chatComp.SetNextAvatar(param.avatarId, 0);
          
            string nextTaskStateStr;
            if (sceneController.scene.sceneType == ESceneType.HomepageRoom)
            {
                nextTaskStateStr = SceneState.PreChat;
            }
            else
            {
                //测试代码 -- start
                if (ChatLogicController.TestAvatarChat.IndexOf(param.avatarId) >= 0)
                {
                    nextTaskStateStr = SceneState.ChatNew;
                }
                else
                {
                    nextTaskStateStr = SceneState.Task;
                }
                
                this.GetUI(UIConsts.WorldHomepageUI).Hide();
            }

            NotifyInfo data = new NotifyInfo
            {
                key = nextTaskStateStr,
                param = param
            };
            Debug.Log("ChangeToTaskState  notify  key:"+ nextTaskStateStr);
            Notifier.instance.SendNotification(SceneConsts.ChangeState, data);
        }
        else {
            Debug.LogError("ChangeToTaskState err: null ChatComponent");
        }

      
    }

    public string GetCurStateName()
    {
        if(_stateMachine != null && _stateMachine.currState != null)
            return _stateMachine.currState.name;
        return string.Empty;
    }
    
    // 当前游戏状态
    public GameStateMachine GetCurrentState()
    {
        return _stateMachine;
    }

    #region select role

    public void StartSelectRole()
    {
        if (this._stateMachine.currState is GameStateSelectRole)
            (this._stateMachine.currState as GameStateSelectRole).StartSelectRole();
    }

    public void FocusOnSelectRole()
    {
        if (this._stateMachine.currState is GameStateSelectRole)
            (this._stateMachine.currState as GameStateSelectRole).FocusOnSelectRole();
    }

    public void PlayRoleAniByStateName(string stateName)
    {
        if (this._stateMachine.currState is GameStateSelectRole)
            (this._stateMachine.currState as GameStateSelectRole).PlayRoleAniByStateName(stateName);
    }

    public Transform GetSelectedRole()
    {
        if (this._stateMachine.currState is GameStateSelectRole)
            return (this._stateMachine.currState as GameStateSelectRole).GetSelectedRole();
        return null;
    }

    public void PlayPlayerToRight()
    {
        if (this._stateMachine.currState is GameStateSelectRole)
            (this._stateMachine.currState as GameStateSelectRole).PlayPlayerToTwo();
    }

    public void PlayPlayerToLeft()
    {
        if (this._stateMachine.currState is GameStateSelectRole)
            (this._stateMachine.currState as GameStateSelectRole).PlayPlayerToOne();
    }

    public void PlaySimpleClick()
    {
        if (this._stateMachine.currState is GameStateSelectRole)
            (this._stateMachine.currState as GameStateSelectRole).PlaySimpleClick();
    }

    // 显示前一个角色
    public OnBoardRolesCfg GetCurrentRoleCfg()
    {
        if (this._stateMachine.currState is GameStateSelectRole)
            return (this._stateMachine.currState as GameStateSelectRole).GetCurrentRoleCfg();
        return null;
    }
    
    #endregion
    
    public void SetInputEventActive(bool active)
    {
        if (this._stateMachine.currState is GameStateSceneIdle)
            (this._stateMachine.currState as GameStateSceneIdle).SetInputEventActive(active);
    }
    
    #region AvatarTask 游戏状态切换

    
    public void ChangeToAvatarTaskState(GameStateAvatarTaskParam param)
    {
        // 经过去确认  SC_AgreeAvatarMoveAck 协议不会触发了   CS_AgreeAvatarMoveReq 的方法也没有调用
        // 删除 GameStateAvatarTask 状态
        // this.ChangeState(GameState.AvatarTask,param);
    }

    #endregion
    
    #region OnBoard
    
    public bool BeOnBoardState(out GameStateOnBoard stateOnBoard)
    {
        stateOnBoard = null;
        var be = _stateMachine.currState is GameStateOnBoard;
        if (be)
        {
            stateOnBoard = _stateMachine.currState as GameStateOnBoard;
        }
        return  be;
    }
    
    private void SimpleChangeState(string stateName, params object[] args)
    {
        _stateMachine.SimpleChangeState(stateName, args);
    }
    
    public void SimpleChangeToOnBoardState()
    {
        SimpleChangeState(GameState.OnBoard, new SceneStateTaskParam());
    }
    
    public void SimpleChangeToSelectRole()
    {
        SimpleChangeState(GameState.SelectRole, new SceneStateTaskParam());
    }

    // 显示前一个角色
    public OnBoardRolesCfg ShowOnBoardingRolePre()
    {
        var be = BeOnBoardState(out GameStateOnBoard gameStateOnBoard);
        if (!be)
        {
            return null;
        }
        return gameStateOnBoard.ShowRolePre();
    }
    // 显示下一个角色
    public OnBoardRolesCfg  ShowOnBoardingRoleNext()
    {
        var be = BeOnBoardState(out GameStateOnBoard gameStateOnBoard);
        if (!be)
        {
            return null;
        }
        return gameStateOnBoard.ShowRoleNext();
    }
    public void SetOnBoardToSelect()
    {
        var be = BeOnBoardState(out GameStateOnBoard gameStateOnBoard);
        if (!be)
        {
            return;
        }
        gameStateOnBoard.ChangeStateToSelect();
    }
    
    // 进入Talk 之前状态（远景）
    public void SetOnBoardToPreTalk(int styleId = 0)
    {
        var be = BeOnBoardState(out GameStateOnBoard gameStateOnBoard);
        if (!be)
        {
            return;
        }
       // gameStateOnBoard.ChangeStateToPreTalk(styleId);
    }
    // 进入Talk 状态（近景）
    public void SetOnBoardToTalk(int styleId = 0)
    {
        var be = BeOnBoardState(out GameStateOnBoard gameStateOnBoard);
        if (!be)
        {
            return;
        }
       // gameStateOnBoard.ChangeStateToTalk(styleId);
    }
    
    // 打招呼动画
    public void PlayNpcToMeet()
    {
        var be = BeOnBoardState(out GameStateOnBoard gameStateOnBoard);
        if (!be)
        {
            return;
        }
       // gameStateOnBoard.PlayNpcToMeet();
    }
    
    // 静止动画
    public void PlayNpcToIdle()
    {
        var be = BeOnBoardState(out GameStateOnBoard gameStateOnBoard);
        if (!be)
        {
            return;
        }
       // gameStateOnBoard.PlayNpcToIdle();
    }
    
    // 获得当前的Audio
    public AudioSource GetCurrentAudio()
    {
        var be = BeOnBoardState(out GameStateOnBoard gameStateOnBoard);
        if (!be)
        {
            return null;
        }
        return null;
    }
    // 播放TTS声音
    public void PlayNpcSound(long tts_record_id)
    {
        var be = BeOnBoardState(out GameStateOnBoard gameStateOnBoard);
        if (!be)
        {
            return;
        }
       // gameStateOnBoard.PlayNpcSound(tts_record_id);
    }
    #endregion

    //=============================================================请求==================================================
    public void SendPullGold()
    {
        // var msg = new CS_EconomicCoinGetBalanceReq()
        // {       
        //     user_id =  _MainModel.UserID
        // };
        // MsgManager.instance.SendMsg(msg);      
    }

    public async void RequsetUnReadMessageNum()
    {
        CS_QueryUnReadMessageNumReq req = new CS_QueryUnReadMessageNumReq();
        var resp = await MsgManager.instance.SendAsyncMsg<SC_QueryUnReadMessageNumAck>(req);
        if (resp.code == 0)
        {
            _MainModel.SetUnreadCount((int)resp.num);
            var ui = GetUI<WorldHomepageUI>(UIConsts.WorldHomepageUI);
            if (ui.isShow)
                ui.SetSocialChatUnReadNum();
            var main = GetUI<CenterHomeUI>(UIConsts.CenterHome);
            // if (main.isInit)
            //     main.SetSocialChatUnReadNum();
        }
    }

    //=============================================================回包==================================================
    private void AddGrowthExp(string name, object body)
    {
        var addNum = (int)body;
        _MainModel.SetAniGrowthExp(addNum);
        GetController<HomepageController>(ModelConsts.Homepage).ReqGetIncentiveData();
    }
    
    //=============================================================leanTouch相关=========================================
    private void InitLeanTouch()
    {
        LeanTouch.OnFingerDown += OnLeanTouchFingerDown;
        LeanTouch.OnFingerUp += OnLeanTouchFingerUp;

        LeanTouch.OnFingerTap += OnLeanTouchFingerTap;
        LeanTouch.OnFingerSwipe += OnLeanTouchFingerSwipe;
        LeanTouch.OnGesture += OnLeanTouchGesture;
    }

    private void OnLeanTouchFingerDown(LeanFinger finger)
    {
        _isFingerDownOnUI = Stage.isTouchOnUI;
        GetController<SceneController>(ModelConsts.Scene).OnLeanTouchFingerDown(finger);
    }

    private void OnLeanTouchFingerUp(LeanFinger finger)
    {
        GetController<SceneController>(ModelConsts.Scene).OnLeanTouchFingerUp(finger, this._leanGestureParams);
        this._leanGestureParams = null;
        _isFingerDownOnUI = false;
    }

    private void OnLeanTouchFingerTap(LeanFinger finger)
    {
        GetController<SceneController>(ModelConsts.Scene).OnLeanTouchFingerTap(finger);
    }

    private void OnLeanTouchFingerSwipe(LeanFinger finger)
    {
        GetController<SceneController>(ModelConsts.Scene).OnLeanTouchFingerSwipe(finger);
    }

    private void OnLeanTouchGesture(List<LeanFinger> fingers)
    {
        if (Stage.isTouchOnUI || _isFingerDownOnUI)
            return;
        if(fingers.Count > 0)
        {
            if (_leanGestureParams == null)
            {
                _leanGestureParams = new Dictionary<float, GestureParam>();
                _leanGestureParams.Add(_leanGestureParams.Count, new GestureParam(
                TimeExt.time, LeanGesture.GetTwistDegrees(fingers), LeanGesture.GetPinchScale(fingers), LeanGesture.GetScreenDelta(fingers)));
            }
            else
            {
                var lastParam = _leanGestureParams[_leanGestureParams.Count - 1];
                _leanGestureParams.Add(_leanGestureParams.Count, new GestureParam(
                TimeExt.time, lastParam.TotalTwistDegrees + LeanGesture.GetTwistDegrees(fingers), 
                lastParam.TotalPinchScale + LeanGesture.GetPinchScale(fingers) - 1, lastParam.TotalDelta + LeanGesture.GetScreenDelta(fingers)));
            }
        }
        GetController<SceneController>(ModelConsts.Scene).OnLeanTouchGesture(fingers);
    }

    private void OnOpenUI(object param)
    {
        var url = (string)param;
        if (!string.IsNullOrEmpty(url))
        {
            var action = url;
            var paramStr = "";
            for(int posIndex = 0; posIndex < url.Length; posIndex++)
            {
                if(url[posIndex] == '?')
                {
                    action = url.Substring(0, posIndex);
                    paramStr = url.Substring(posIndex, url.Length-posIndex);
                    break;
                }
            }

            Dictionary<string, string> queryParams = new Dictionary<string, string>();
            if (!string.IsNullOrEmpty(paramStr))
            {
                string[] pairs = paramStr.Split('?')[1].Split('&');
                foreach (string pair in pairs)
                {
                    string[] keyValue = pair.Split('=');
                    queryParams.Add(keyValue[0], keyValue[1]);
                    Debug.Log($"{keyValue[0]} = {keyValue[1]}");
                }
            }

            if (action.Equals("rank"))
            {
                if (_stateMachine.currState is GameStateSceneIdle)
                {
                    GetController<RankController>(ModelConsts.Rank).EnterRank();
                }
            }
            else if (action.Equals("sign"))
            {
                if (_stateMachine.currState is GameStateSceneIdle)
                {
                    GetController<SignController>(ModelConsts.Sign).EnterSign();
                }
            }
            else if(action.Equals("SocialChat"))
            {
                GetController<SocialChatController>(ModelConsts.SocialChat).PushEnter(long.Parse(queryParams["receiverId"]),queryParams["groupType"]);
            } 
            else if(action.Equals("profile"))
            {
                GetController<ProfileController>(ModelConsts.Profile).OnClickOther(long.Parse(queryParams["user_id"]));
            }
            else if (action.Equals("voice_paywall"))
            {
                GetUI<SpeakPlanPromotionStep1UI>(UIConsts.SpeakPlanPromotionStep1UI).Show();
            }
        }
    }

    public void EnterSelectRole(bool enterWorldImmediately = false)
    {
        var be = GetController<MainController>(ModelConsts.Main).BeOnBoardState(out GameStateOnBoard gameStateOnBoard);
        if (!be)
        {
            GetUI<SelectRoleUI>(UIConsts.SelectRoleUI).enterWorld = enterWorldImmediately;
            GetController<SceneController>(ModelConsts.Scene).LoadSceneById(Cfg.T.TbConst.SelectRoleSceneId);
        }
    }

    public bool IsState<T>()
    {
        return _stateMachine.currState is T;
    }

    public void TryGetCacheLaunchOptions()
    {
        // RegisterActionCache(GameState.SceneIdle, () =>
        // {
            try
            {
                string str = "";
                var rkStr = AppRegionInfo.GetCurrRegionKeyAsStr();
#if UNITY_IOS && !UNITY_EDITOR
                if (rkStr == AppRegionKey.jpTest.ToString())
                {
                    str = JPushBinding.GetCachedLaunchParams();
                }
                else
                {
                    str = MTPushBinding.GetCachedLaunchParams();
                }
#endif
                VFDebug.Log("GetCachedLaunchParams" + str);
                if (!string.IsNullOrEmpty(str))
                {
                    var msg = JsonMapper.ToObject<SDKManager.NotificationContent>(str);
                    if (msg.action.Equals("open_ui") && !string.IsNullOrEmpty(msg.action_value))
                    {
                        Debug.Log("MainController: TryGetCacheLaunchOptions And OpenUI" + msg.action_value);
                        OnOpenUI(msg.action_value);
                    }
                }
            }
            catch (Exception e)
            {
                VFDebug.LogWarning("Convert CachedLaunchParams Error:" + e);
            }
        // });
    }

    public void UpdateTabRedDotState(TabIndex tabIndex)
    {
        if (tabIndex == TabIndex.Explore)
        {
            if (PlayerPrefs.HasKey(MainModel.userID + PlayPrefsConst.ExploreTabRedStateKey))
            {
                PlayerPrefs.SetInt(MainModel.userID + PlayPrefsConst.ExploreTabRedStateKey, 0);
                PlayerPrefs.Save();
            }
        }
        else if (tabIndex == TabIndex.Course)
        {
            if (PlayerPrefs.HasKey(MainModel.userID + PlayPrefsConst.MainTabRedStateKey))
            {
                PlayerPrefs.SetInt(MainModel.userID + PlayPrefsConst.MainTabRedStateKey, 0);
                PlayerPrefs.Save();
            }
        }
    }
}

public struct GestureParam
{
    public float FingersTime;
    public float TotalTwistDegrees;
    public float TotalPinchScale;
    public Vector2 TotalDelta;

    public GestureParam(float fingersTime, float totalTwistDegrees, float totalPinchScale, Vector2 totalDelta)
    {
        FingersTime = fingersTime;
        TotalTwistDegrees = totalTwistDegrees;
        TotalPinchScale = totalPinchScale;
        TotalDelta = totalDelta;
    }
}

public struct GameStateAvatarTaskParam
{
    public long avatarId;
    public long taskId;
}