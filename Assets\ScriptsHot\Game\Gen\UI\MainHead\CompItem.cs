/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.MainHead
{
    public partial class CompItem : UIBindT
    {
        public override string pkgName => "MainHead";
        public override string comName => "CompItem";

        public Controller icon;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            icon = com.GetControllerAt(0);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            icon = null;
        }
    }
}