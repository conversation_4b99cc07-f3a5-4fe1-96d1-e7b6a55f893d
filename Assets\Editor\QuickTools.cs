/*
 * Author: <EMAIL>
 * Date: 2024-10-18
 * Description: Utilities for testing in Unity Editor
 * Update: 2024-11-08
 * Add haptic remote feature that can vibrate the device from Unity Editor
 */

using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.Linq;
using DG.Tweening;
using System;
using System.Reflection;
public class QuickTools : EditorWindow
{
    const string PREF_KEY_TEST_ACCOUNTS = "PK_TEST_ACCOUNTS";
    static TestAccountList testAccountList = new TestAccountList();


    const string PREF_KEY_SYS_LAN = "PK_SYS_LAN_USE";
    public static SystemLanguageOption languageOption = default;


    bool isAddingAccount = false;
    string newAccount = "";

    public string hapticRemoteIP = "localhost";
    public int hapticRemotePort = 6000;

    public HapticType hapticType = HapticType.NotificationSuccess;

    private bool drawPerformanceOptimizationModule = true;
    private bool drawUITestModule = true;
    private bool drawHapticRemoteModule = true;


    [MenuItem("Tools/Quick Tools %t")]
    public static void ShowWindow()
    {
        var window = GetWindow<QuickTools>(false, "Quick Tools");
    }

    private void OnEnable()
    {
        LoadTestAccounts();
        LoadSysLanguages();
        Application.logMessageReceived += OnLogMessageReceived;
    }
    private void OnDisable()
    {
        SaveTestAccounts();
        SaveSysLanguages();
        Application.logMessageReceived -= OnLogMessageReceived;
        isAddingAccount = false;
    }

    private void OnLogMessageReceived(string condition, string stackTrace, LogType type)
    {
        if (condition.Contains("[TestAccount]"))
        {
            OnTestAccountLogReceived(condition);
        }
        else if (condition.Contains("[LoginUI]Opened"))
        {
            OnLoginUIOpened(condition);
        }
    }

    private void OnGUI()
    {
        DrawTestAccountModule();

        DrawDebugPortal();

        DrawLanguage();

        GUILayout.Space(10);

        drawPerformanceOptimizationModule = EditorGUILayout.Foldout(drawPerformanceOptimizationModule, "性能优化", true);
        if (drawPerformanceOptimizationModule)
        {
            DrawPerformanceOptimizationModule();
        }
        GUILayout.Space(10);

        drawUITestModule = EditorGUILayout.Foldout(drawUITestModule, "UI测试", true);
        if (drawUITestModule)
        {
            DrawUITestModule();
        }
        GUILayout.Space(10);

        drawHapticRemoteModule = EditorGUILayout.Foldout(drawHapticRemoteModule, "远程震动反馈", true);
        if (drawHapticRemoteModule)
        {
            DrawHapticRemoteModule();
        }
    }

    int potral;

    private void DrawDebugPortal()
    {
        if (potral == 0) potral = GetDebugPortal();

        GUILayout.BeginHorizontal();
        {
            GUILayout.Label($"Debug Portal:{potral}", EditorStyles.boldLabel);
            if(GUILayout.Button("刷新")) potral = GetDebugPortal();
        }
        GUILayout.EndHorizontal();
    }
    
    public static int GetDebugPortal()
    {
        System.Reflection.Assembly assembly = System.Reflection.Assembly.GetAssembly(typeof(UnityEditor.EditorWindow));
        System.Type debuggerType = assembly.GetType("UnityEditor.MonoManager") ?? 
                                assembly.GetType("UnityEditor.ScriptDebugger");
        
        if (debuggerType != null)
        {
            var portProperty = debuggerType.GetProperty("listenerPort", 
                System.Reflection.BindingFlags.Static | 
                System.Reflection.BindingFlags.Public | 
                System.Reflection.BindingFlags.NonPublic);
                
            if (portProperty != null)
            {
                return (int)portProperty.GetValue(null);
            }
        }
        return 0;
    }

    private void DrawLanguage()
    {
        GUILayout.BeginHorizontal();
        {
            // toggle
            languageOption.useCustomLanguage = EditorGUILayout.Toggle("锁定系统语言", languageOption.useCustomLanguage);

            GUI.enabled = languageOption.useCustomLanguage;
            languageOption.systemLanguage = (SystemLanguage)EditorGUILayout.EnumPopup(languageOption.systemLanguage);
            if (languageOption.useCustomLanguage)
            {
                I18N.systemLanguage = languageOption.systemLanguage;
            }
            else
            {
                I18N.systemLanguage = Application.systemLanguage;
            }
            GUI.enabled = true;
        }
        GUILayout.EndHorizontal();
    }

    private float duration = 0.1f;
    private float interval = 0.2f;
    private float deltaX = 100f;
    private Ease ease = Ease.OutCubic;
    private void DrawUITestModule()
    {
        //Homepage UI
        GUILayout.Label("Homepage UI Animation", EditorStyles.boldLabel);

        duration = EditorGUILayout.FloatField("持续时间", duration);
        interval = EditorGUILayout.FloatField("间隔", interval);
        deltaX = EditorGUILayout.FloatField("偏移", deltaX);
        ease = (Ease)EditorGUILayout.EnumPopup("缓动", ease);

        GUI.enabled = Application.isPlaying;
        GUILayout.Label("FeatureButtonList", EditorStyles.boldLabel);
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("Show"))
        {
            var homepageUI = UIManager.instance.GetUI("Homepage") as CenterHomeUI;
            if(homepageUI != null && homepageUI.isShow)
            {
                homepageUI.ShowFeatureButtonList(0f, interval, duration, deltaX, ease, true);
            }
        }
        if (GUILayout.Button("Hide"))
        {
            var homepageUI = UIManager.instance.GetUI("Homepage") as CenterHomeUI;
            if(homepageUI != null && homepageUI.isShow)
            {
                homepageUI.HideFeatureButtonList(0f, interval, duration, deltaX, ease, true);
            }
        }
        GUILayout.EndHorizontal();

        GUILayout.Label("BottomButtons", EditorStyles.boldLabel);
        GUILayout.BeginHorizontal();
        //if (GUILayout.Button("Show"))
        //{
        //    var homepageUI = UIManager.instance.GetUI("Homepage") as CenterHomeUI;
        //    if(homepageUI != null && homepageUI.isShow)
        //    {
        //        homepageUI.ShowBottomButtons(0f, true);
        //    }
        //}
        //if (GUILayout.Button("Show(Force)"))
        //{
        //    var homepageUI = UIManager.instance.GetUI("Homepage") as CenterHomeUI;
        //    if(homepageUI != null && homepageUI.isShow)
        //    {
        //        homepageUI.ShowBottomButtons(0f, true, null, true);
        //    }
        //}
        //if (GUILayout.Button("Hide"))
        //{
        //    var homepageUI = UIManager.instance.GetUI("Homepage") as CenterHomeUI;
        //    if(homepageUI != null && homepageUI.isShow)
        //    {
        //        homepageUI.HideBottomButtons(0f, true);
        //    }
        //}
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        // if (GUILayout.Button("Talk 正常"))
        // {
        //     var homepageUI = UIManager.instance.GetUI("Homepage") as CenterHomeUI;
        //     if(homepageUI != null && homepageUI.isShow)
        //     {
        //         var itemInfo = new LearnPathNextTaskItemInfo();
        //         itemInfo.nextStatus = LearnPathNextTaskItemStatus.ToSpeak;
        //         homepageUI.UpdateSpeakState(itemInfo);
        //     }
        // }
        // if (GUILayout.Button("Talk 小礼物"))
        // {
        //     var homepageUI = UIManager.instance.GetUI("Homepage") as CenterHomeUI;
        //     if(homepageUI != null && homepageUI.isShow)
        //     {
        //         var itemInfo = new LearnPathNextTaskItemInfo();
        //         itemInfo.nextStatus = LearnPathNextTaskItemStatus.ToClaimBox;
        //         homepageUI.UpdateSpeakState(itemInfo);
        //     }
        // }   
        // if(GUILayout.Button("Talk 大礼物"))
        // {
        //     var homepageUI = UIManager.instance.GetUI("Homepage") as CenterHomeUI;
        //     if(homepageUI != null && homepageUI.isShow)
        //     {
        //         var itemInfo = new LearnPathNextTaskItemInfo();
        //         itemInfo.nextStatus = LearnPathNextTaskItemStatus.ToClaimBox;
        //         homepageUI.UpdateSpeakState(itemInfo);
        //     }
        // }
        // if (GUILayout.Button("Talk 大礼物"))
        // {
        //     var homepageUI = UIManager.instance.GetUI("Homepage") as CenterHomeUI;
        //     if (homepageUI != null && homepageUI.isShow)
        //     {
        //         var itemInfo = new LearnPathNextTaskItemInfo();
        //         itemInfo.nextStatus = LearnPathNextTaskItemStatus.ToClaimBox;
        //         homepageUI.UpdateSpeakState(itemInfo);
        //     }
        // }
        // if (GUILayout.Button("Talk Gallery"))
        // {
        //     var homepageUI = UIManager.instance.GetUI("Homepage") as CenterHomeUI;
        //     if (homepageUI != null && homepageUI.isShow)
        //     {
        //         var itemInfo = new LearnPathNextTaskItemInfo();
        //         itemInfo.nextStatus = LearnPathNextTaskItemStatus.ToChangeStory;
        //         homepageUI.UpdateSpeakState(itemInfo);
        //     }
        // }
        GUILayout.EndHorizontal();

        GUILayout.Label("MainHeader", EditorStyles.boldLabel);
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("In"))
        {
            // var header = UIManager.instance.GetUI<MainHeaderUI>(UIConsts.MainHeader);
            // if (header != null && header.isShow)
            // {
            //     header.SetVisible(true, true);
            // }
        }
        if (GUILayout.Button("Out"))
        {
            // var header = UIManager.instance.GetUI<MainHeaderUI>(UIConsts.MainHeader);
            // if (header != null && header.isShow)
            // {
            //     header.SetVisible(false, true);
            // }
        }
        if (GUILayout.Button("Visible"))
        {
            // var header = UIManager.instance.GetUI<MainHeaderUI>(UIConsts.MainHeader);
            // if (header != null && header.isShow)
            // {
            //     header.SetVisible(true, false);
            // }
        }
        if (GUILayout.Button("Invisible"))
        {
            // var header = UIManager.instance.GetUI<MainHeaderUI>(UIConsts.MainHeader);
            // if (header != null && header.isShow)
            // {
            //     header.SetVisible(false, false);
            // }
        }
        GUILayout.EndHorizontal();
        GUI.enabled = true;
    }

    private void DrawTestAccountModule()
    {
        var originalColor = GUI.color;
        GUILayout.Label("点击账号填入，点击X移除", EditorStyles.miniLabel);

        for (int i = 0; i < testAccountList.accounts.Count; i++)
        {
            EditorGUILayout.BeginHorizontal();
            GUI.enabled = Application.isPlaying;
            if (GUILayout.Button(new GUIContent(testAccountList.accounts[i].account, "点击填入"), EditorStyles.miniButtonLeft, GUILayout.ExpandWidth(true)))
            {
                OnAccountClicked(testAccountList.accounts[i].account);
            }
            GUI.enabled = true;

            //If is quick login, set gui to green
            GUI.color = testAccountList.accounts[i].isQuickLogin ? Color.green : Color.white;
            if (GUILayout.Button(new GUIContent(testAccountList.accounts[i].isQuickLogin ? "自动: 开" : "自动: 关", "点击切换自动登录"), EditorStyles.miniButtonMid, GUILayout.Width(60)))
            {
                testAccountList.accounts[i].isQuickLogin = !testAccountList.accounts[i].isQuickLogin;
                if (testAccountList.accounts[i].isQuickLogin)
                {
                    //Disable all other quick login
                    for (int j = 0; j < testAccountList.accounts.Count; j++)
                    {
                        if (i != j)
                        {
                            testAccountList.accounts[j].isQuickLogin = false;
                        }
                    }
                }
                SaveTestAccounts();
            }
            GUI.color = originalColor;

            if (GUILayout.Button(new GUIContent("X", "点击移除"), EditorStyles.miniButtonRight, GUILayout.Width(20)))
            {
                if (EditorUtility.DisplayDialog("移除账号", "确定要移除账号吗？", "确定", "取消"))
                {
                    testAccountList.accounts.RemoveAt(i);
                    SaveTestAccounts();
                    GUIUtility.ExitGUI();
                }
            }

            EditorGUILayout.EndHorizontal();
        }

        if (isAddingAccount)
        {
            EditorGUILayout.BeginHorizontal();
            newAccount = EditorGUILayout.TextField(newAccount);
            if (GUILayout.Button("确定", EditorStyles.miniButtonLeft, GUILayout.Width(60)))
            {
                if (!string.IsNullOrWhiteSpace(newAccount))
                {
                    testAccountList.accounts.Add(new TestAccount { account = newAccount, isQuickLogin = false });
                    SaveTestAccounts();
                }
                isAddingAccount = false;
            }
            if (GUILayout.Button("取消", EditorStyles.miniButtonRight, GUILayout.Width(60)))
            {
                newAccount = "";
                isAddingAccount = false;
            }
            EditorGUILayout.EndHorizontal();
        }

        if (GUILayout.Button("+ 添加", GUILayout.Width(60)))
        {
            isAddingAccount = true;
        }
    }

    private void DrawPerformanceOptimizationModule()
    {
        GUI.enabled = Application.isPlaying;
        PerformanceSaver.instance.isEnabled = EditorGUILayout.Toggle("启用自动帧率调整器", PerformanceSaver.instance.isEnabled);
        EditorGUILayout.LabelField("Current Target FPS", Application.targetFrameRate.ToString());
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Set 120"))
        {
            Application.targetFrameRate = 120;
        }
        if (GUILayout.Button("Set 60"))
        {
            Application.targetFrameRate = 60;
        }
        if (GUILayout.Button("Set 30"))
        {
            Application.targetFrameRate = 30;
        }
        if (GUILayout.Button("Set 15"))
        {
            Application.targetFrameRate = 15;
        }
        EditorGUILayout.EndHorizontal();
        GUI.enabled = true;
    }

    private void DrawHapticRemoteModule()
    {
        hapticRemoteIP = EditorGUILayout.TextField("IP", hapticRemoteIP);
        hapticRemotePort = EditorGUILayout.IntField("Port", hapticRemotePort);

        if (GUILayout.Button("连接"))
        {
            HapticUtils.Connect(hapticRemoteIP, hapticRemotePort);
        }
        if (GUILayout.Button("断开"))
        {
            HapticUtils.Disconnect();
        }

        hapticType = (HapticType)EditorGUILayout.EnumPopup("测试命令", hapticType);
        if (GUILayout.Button("发送"))
        {
            HapticUtils.Vibrate(hapticType);
        }
    }

    // Placeholder for OnAccountClicked method
    void OnAccountClicked(string account)
    {
        if (!Application.isPlaying)
        {
            return;
        }
        //Find the debug input field in the scene
        var debugInput = GameObject.Find("LoginDebugCom");
        if (debugInput != null && debugInput.activeInHierarchy)
        {
            var loginUI = UIManager.instance.GetUI("Login") as LoginUI;
            if (loginUI != null)
            {
                loginUI.GMFillAccount(account);
            }
        }
    }

    private void OnTestAccountLogReceived(string condition)
    {
        if (condition.Contains("[TestAccount]"))
        {
            var account = condition.Replace("[TestAccount]", "").Trim();
            if (string.IsNullOrWhiteSpace(account))
            {
                return;
            }
            if (!testAccountList.accounts.Any(a => a.account == account))
            {
                testAccountList.accounts.Add(new TestAccount { account = account, isQuickLogin = false });
                SaveTestAccounts();
            }
            else
            {
                //move to the top
                var item = testAccountList.accounts.Find(a => a.account == account);
                testAccountList.accounts.Remove(item);
                testAccountList.accounts.Insert(0, item);
                SaveTestAccounts();
            }
        }
    }

    private void OnLoginUIOpened(string condition)
    {
        //Get login ui
        var loginUI = UIManager.instance.GetUI("Login") as LoginUI;
        if (loginUI != null)
        {
            //Quick login the first account with quick login enabled
            for (int i = 0; i < testAccountList.accounts.Count; i++)
            {
                if (testAccountList.accounts[i].isQuickLogin)
                {
                    loginUI.GMQuickLogin(testAccountList.accounts[i].account);
                    //Move to the top
                    var item = testAccountList.accounts[i];
                    testAccountList.accounts.Remove(item);
                    testAccountList.accounts.Insert(0, item);
                    SaveTestAccounts();
                    break;
                }
            }
        }
    }

    private void LoadTestAccounts()
    {
        testAccountList = new TestAccountList();
        testAccountList.accounts = new List<TestAccount>();
        var loaded = EditorPrefs.GetString(PREF_KEY_TEST_ACCOUNTS);
        if (!string.IsNullOrWhiteSpace(loaded))
        {
            testAccountList = JsonUtility.FromJson<TestAccountList>(loaded);
            //Remove all items with empty account
            testAccountList.accounts.RemoveAll(a => string.IsNullOrWhiteSpace(a.account));
            SaveTestAccounts();
        }
        if (testAccountList.accounts == null)
        {
            testAccountList.accounts = new List<TestAccount>();
            SaveTestAccounts();
        }
    }

    private void SaveTestAccounts()
    {
        EditorPrefs.SetString(PREF_KEY_TEST_ACCOUNTS, JsonUtility.ToJson(testAccountList));
    }

    private void LoadSysLanguages()
    {
        var loaded = EditorPrefs.GetString(PREF_KEY_SYS_LAN);
        if (!string.IsNullOrWhiteSpace(loaded))
        { 
            languageOption = JsonUtility.FromJson<SystemLanguageOption>(loaded); 
        }
        else
        { 
            languageOption = new SystemLanguageOption(); 
        }
    }


    private void SaveSysLanguages()
    {
        EditorPrefs.SetString(PREF_KEY_SYS_LAN, JsonUtility.ToJson(languageOption));
    }
}

[System.Serializable]
public class TestAccountList
{
    public List<TestAccount> accounts;
}

[System.Serializable]
public class TestAccount
{
    public string account;
    public string password;
    public bool isQuickLogin;
}

[System.Serializable]
public class SystemLanguageOption
{
    public bool useCustomLanguage = false;
    public SystemLanguage systemLanguage = Application.systemLanguage;
}
