/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Explore
{
    public partial class ExploreScaffold : UIBindT
    {
        public override string pkgName => "Explore";
        public override string comName => "ExploreScaffold";

        public Controller ctrl;
        public ExploreScaffoldTranslateTxt comTrans;
        public ExploreScaffoldTxt comMain;
        public ExploreSoundFlag effectSound;
        public ExploreTranslateBtn btnTranslate;
        public ExploreNoticeBtn btnNotice;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ctrl = com.GetControllerAt(0);
            comTrans = new ExploreScaffoldTranslateTxt();
            comTrans.Construct(com.GetChildAt(0).asCom);
            comMain = new ExploreScaffoldTxt();
            comMain.Construct(com.GetChildAt(1).asCom);
            effectSound = new ExploreSoundFlag();
            effectSound.Construct(com.GetChildAt(2).asCom);
            btnTranslate = new ExploreTranslateBtn();
            btnTranslate.Construct(com.GetChildAt(3).asCom);
            btnNotice = new ExploreNoticeBtn();
            btnNotice.Construct(com.GetChildAt(4).asCom);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ctrl = null;
            comTrans.Dispose();
            comTrans = null;
            comMain.Dispose();
            comMain = null;
            effectSound.Dispose();
            effectSound = null;
            btnTranslate.Dispose();
            btnTranslate = null;
            btnNotice.Dispose();
            btnNotice = null;
        }
    }
}