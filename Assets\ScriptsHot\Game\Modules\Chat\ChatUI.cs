using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using FairyGUI;
using Msg.basic;
using Msg.dialog_task;
using ScriptsHot.Game.Modules.Chat.ChatCell;
using ScriptsHot.Game.Modules.Common;
using ScriptsHot.Game.Modules.Guide;

using ScriptsHot.Game.Modules.ReviewQuestion;
using ScriptsHot.Game.Modules.Setting;
using ScriptsHot.Game.Modules.Task;
using TMPro;
using UnityEngine;
using UnityEngine.UIElements;
using Object = UnityEngine.Object;

public class ChatUI : BaseUI<UIBind.Chat.ChatPanel>, IBaseUIUpdate
{
    public override void OnBackBtnClick()
    {
        // var mainHeaderUI = GetUI<MainHeaderUI>(UIConsts.MainHeader);
        // if (mainHeaderUI.isShow)
        // {
        //     mainHeaderUI.OnBtnLeftTopClick();
        // }
    }
    
    private Dictionary<ChatCellType, List<GComponent>> _pools = new Dictionary<ChatCellType, List<GComponent>>();

    //private Dictionary<int,BaseChatCell> _chatCellScripts = new Dictionary<int,BaseChatCell>();
    //private Dictionary<int,GComponent> _chatCells = new Dictionary<int,GComponent>();
    private ChatController _chatController => this.GetController<ChatController>(ModelConsts.Chat);
    private CurrencyController _currencyController => GetController<CurrencyController>(ModelConsts.CurrencyController);
    protected override bool isFullScreen => true;
    
    private ChatModel _chatModel => this.GetModel<ChatModel>(ModelConsts.Chat);
    private RecommendCardModel _recommendCardModel => GetModel<RecommendCardModel>(ModelConsts.RecommendCard);
    private ReviewQuestionModel _reviewQuestionModel => GetModel<ReviewQuestionModel>(ModelConsts.ReviewQuestionModel);
    private UIPackage _uiPackage;
    private int _bubbleId = 0;
    private const int MAXNUM = 0;
    private float _bottomY = 0;
    private const float _containHeight = 999;
    private int _emptyBubbleId = 0;
    private int _combo = 0;
    private GComponent _curNewWordCmp;
    private SpinePanelExtension _spCollect;
    private Animator _flowAnimator;
    
    private SettingModel _settingModel => this.GetModel<SettingModel>(ModelConsts.Setting);
    
    public ChatUI(string name) : base(name)
    {
    }

    public override string uiLayer => UILayerConsts.Home;

    public override EUIDeleteRule deleteRule => EUIDeleteRule.Never;
    private const string DefaultIconUrl = "ui://gu4fqlmfbenkxxx7n";

    protected override void OnInit(GComponent uiCom)
    {
        _uiPackage = FairyGUI.UIPackage.GetByName("Chat");
        // ui.stateType.title = "ChatState";
        // this.AddUIEvent(this.ui.testBtn.onClick, this.OnTestChatPage);

        // ************************************Test Logic Start**************************************
        this.AddUIEvent(this.ui.testBtn2.onClick, this.OnCreateCell);
        // ************************************Test Logic End****************************************
        // this.AddUIEvent(this.ui.stateType.onChanged, this.OnChangeState);
        // this.AddUIEvent(this.ui.testBtn3.onClick, this.test);
        _bottomY = this.ui.comCellContainer.comContent.com.height+ui.comCellContainer.comContent.com.position.y;
        // _containHeight = this.ui.comCellContainer.comContent.com.size.y;
        ui.comCellContainer.comContent.com.scrollPane.onScroll.Clear();
        ui.comCellContainer.comContent.com.scrollPane.onScroll.Add(() =>
        {
            if (_curNewWordCmp != null)
            {
                _curNewWordCmp.Dispose();
                _curNewWordCmp = null;
            }
            // var ui = GetUI<GuideRoundUI>(UIConsts.GuideRound);
            // if (ui.isShow)
            // {
            //     ui.Hide();
            // }
        });
        this.AddUIEvent(ui.imgCloseTask.onClick, OnImgCloseTaskClicked);
        _spCollect = ui.spLoader as SpinePanelExtension;
        _spCollect.SetModel(ResUtils.GetSpinePath("spCollectWord"),
            () => { 
                Vector2 screenPos = ui.posCollectEnd.LocalToGlobal(Vector2.zero);
                screenPos.y = Screen.height - screenPos.y; 
                _spCollect.go.transform.Find("root/root/end").position = StageCamera.main.ScreenToWorldPoint(screenPos);
            });
        _spCollect.visible = false;
        _spCollect.loop = false;
        _spCollect.fill = SpinePanelFill.None;
        ui.posCollectEnd.visible = false;
        ui.loader.visible = false;
        ui.comChatCombo.com.visible = false;
    }

    private void OnImgCloseTaskClicked()
    {
        GetController<TaskController>(ModelConsts.Task).OnClosePanel();
    }

    #region Test Logic
    // ************************************Test Logic Start**************************************


    private void OnCreateCell()
    {
        switch (this.ui.cellType.value)
        {
            case "1":
                OnCreateAvatarNormalCell();
                break;
            case "2":
                OnCreatePlayerNormalCell();
                break;
            case "3":
                OnCreatePlayerFollowCell();
                break;
            case "4":
                OnCreatePreTalkCell();
                break;
            case "5":
                OnCreateSuggestCell();
                break;
            case "6":
                OnCreateToastCell();
                break;
            case "7":
                OnCreateScaffoldCell();
                break;
        }
    }

    public void AddChildCom(GComponent cmp)
    {
        if (_curNewWordCmp != null)
        {
            _curNewWordCmp.Dispose();
            _curNewWordCmp = null;
        }
        _curNewWordCmp = cmp;
        ui.com.AddChild(cmp);
    }

    private void OnChangeState()
    {
        //Debug.LogError("ui.cellType.value  "+ui.stateType.value);
        // switch (this.ui.stateType.value)
        // {
        //     case "1":
        //
        //         break;
        //     case "2":
        //         OnEnterSelect();
        //         break;
        //     case "3":
        //
        //         break;
        //     case "4":
        //         OnEnterAvatar();
        //         break;
        //     case "5":
        //         break;
        //     case "6":
        //         break;
        // }
    }

    private void OnEnterSelect()
    {
        SC_GetDialogTaskModeListAck data = new SC_GetDialogTaskModeListAck();
        data.data = new PB_GetDialogTaskModeListResp();
        data.data.task_title = "哈哈哈哈哈";
        data.data.description = "test";
        List<PB_DialogTaskModeItem> mode_list = new List<PB_DialogTaskModeItem>();

        PB_DialogTaskModeItem item1 = new PB_DialogTaskModeItem();
        item1.mode = PB_DialogMode.Career;
        item1.status = PB_ModeStatus.ModeComplete;
        PB_StarItem star1 = new PB_StarItem();
        star1.is_get = true;
        star1.is_highlight = true;
        item1.star_list.Add(star1);
        mode_list.Add(item1);

        PB_DialogTaskModeItem item2 = new PB_DialogTaskModeItem();
        item2.mode = PB_DialogMode.Open;
        mode_list.Add(item2);

        data.data.mode_list.AddRange(mode_list);
        _chatController.GetDialogTaskModeListAck(data);
    }

    public void OnEnterAvatar()
    {
        // SC_CreateDialogTaskAck msg = new SC_CreateDialogTaskAck();
        // msg.data = new PB_CreateDialogTaskResp();
        // msg.data.msg_id = 1;
        // msg.data.dialog_id = 1;
        SC_DialogTaskMsgHandleAck msg = new SC_DialogTaskMsgHandleAck();
        PB_DialogTaskMsgItem data1 = new PB_DialogTaskMsgItem();
        data1.sequence_id = 1;
        data1.msg_id = 1;
        data1.msg_owner = PB_MsgBelong.Avatar;
        data1.msg_biz_type = PB_DownMsgBizType.AvatarReply;
        data1.avatar_reply_data = new PB_DialogTask_AvatarReply_AckFields();
        data1.avatar_reply_data.ori_text =
            "Hello, my name is flow.I like to eat fried chicken wings and hamburgers.I often go skiing on weekends.";
        data1.avatar_reply_data.trans_text = "你好，俺我的名字叫做flow。俺喜欢吃炸鸡和汉堡包。俺周末经常去滑雪。";

        PB_DialogTaskMsgItem data2 = new PB_DialogTaskMsgItem();
        data2.sequence_id = 2;
        data2.msg_id = 2;
        data2.msg_owner = PB_MsgBelong.User;
        data2.msg_biz_type = PB_DownMsgBizType.AvatarReply;
        data2.avatar_reply_data = new PB_DialogTask_AvatarReply_AckFields();
        data2.avatar_reply_data.ori_text = "I like to eat fried chicken wings and hamburgers.";
        data2.avatar_reply_data.trans_text = "俺喜欢吃炸鸡和汉堡包。";

        // msg.data.msg_items.Add(data2);
        // msg.data.msg_items.Add(data1);
        _chatController.DialogTaskMsgHandleAck(msg);


        this.GetUI<RecordUI>(UIConsts.RecordUI).Show();
    }

    private void OnCreateAvatarNormalCell()
    {
        AvatarNormalCell avatarNormalCell = new AvatarNormalCell();
        var ctrl = ControllerManager.instance.GetController(ModelConsts.Chat) as ChatController;
        this.AddChatCellCom(avatarNormalCell, ChatCellType.AvatarNormal, null);

        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.Text,
        //     Content = "Let's flow!",
        //     Length =  11,
        //     IsSentenceEnd = true
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.Text,
        //     Content = " Which",
        //     Length =  6,
        //     IsSentenceEnd = false
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.Text,
        //     Content = "Let's flow!",
        //     Length =  11,
        //     IsSentenceEnd = true
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.Text,
        //     Content = " Which",
        //     Length =  6,
        //     IsSentenceEnd = false
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.Text,
        //     Content = "Let's flow!",
        //     Length =  11,
        //     IsSentenceEnd = true
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.Text,
        //     Content = " Which",
        //     Length =  6,
        //     IsLast = false,
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.Text,
        //     Content = " ",
        //     Length =  1,
        //     Color = Color.magenta,
        //     IsSentenceEnd = false
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.Mask,
        //     Content = "Game",
        //     Length = 4,
        //     Color = Color.magenta,
        //     Id = 1,
        //     IsSentenceEnd = false
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.SplitChar,
        //     Length = 1,
        //     IsSentenceEnd = false
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.Text,
        //     Content = " do you like best?",
        //     Length =  18,
        //     Color = Color.green,
        //     IsSentenceEnd = true
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.Text,
        //     Content = " I ",
        //     Length =  3,
        //     Color = Color.magenta,
        //     IsSentenceEnd = false
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.IntonationDown,
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.CoreWord,
        //     Content = "like ",
        //     Length =  5,
        //     IsSentenceEnd = false
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.IntonationUp,
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.Text,
        //     Content = "Zelda!",
        //     Length =  6,
        //     Color = Color.green,
        //     IsSentenceEnd = true
        // });
        //
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.Text,
        //     Content = "Let's flow!",
        //     Length =  11,
        //     Color = Color.magenta,
        //     IsSentenceEnd = true
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.Prominent,
        //     Content = " Which",
        //     Length =  6,
        //     Color = Color.red,
        //     IsSentenceEnd = false
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.Wrap,
        //     Content = null,
        //     Length =  1,
        //     Color = Color.magenta,
        //     IsSentenceEnd = false
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.Mask,
        //     Content = "Game",
        //     Id = 2,
        //     Length = 4,
        //     Color = Color.magenta,
        //     IsSentenceEnd = false
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.SplitChar,
        //     Length = 1,
        //     IsSentenceEnd = false
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.Text,
        //     Content = " do you like best?",
        //     Length =  18,
        //     Color = Color.green,
        //     IsSentenceEnd = true
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.Text,
        //     Content = " I ",
        //     Length =  3,
        //     Color = Color.magenta,
        //     IsSentenceEnd = false
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.IntonationDown,
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.Text,
        //     Content = "like ",
        //     Length =  5,
        //     Color = Color.magenta,
        //     IsSentenceEnd = false
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.IntonationUp,
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.Text,
        //     Content = "Zelda!",
        //     Length =  6,
        //     Color = Color.green,
        //     IsSentenceEnd = true,
        //     IsLast = true,
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.Prominent,
        //     Content = "Let's",
        //     IsLast = false,
        //     IsCoreWord = true
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.Prominent,
        //     Content = " ",
        //     IsLast = false,
        //     IsCoreWord = true
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.Prominent,
        //     Content = "Flow",
        //     IsLast = false,
        //     IsCoreWord = true
        // });
        // avatarNormalCell.AppendRichContent(new RichContent()
        // {
        //     Type = RichContentType.Prominent,
        //     Content = "!",
        //     IsLast = false,
        //     IsCoreWord = true
        // });
        avatarNormalCell.AppendRichContent(new RichContent()
        {
            Type = RichContentType.Text,
            Content = "Let's Flow!",
            IsCoreWord = true,
            StartTime = 1000,
            PlaySpeed = 0.03f,
            IsLast = false,
        });
        avatarNormalCell.AppendTransRichContent(new RichContent()
        {
            Type = RichContentType.Text,
            Content = "Let's Flow!",
            IsLast = false,
        });
        avatarNormalCell.AppendRichContent(new RichContent()
        {
            Type = RichContentType.Text,
            Content = " London on you ",
            StartTime = 4000,
            PlaySpeed = 0.03f,
            IsLast = true,
        });
        avatarNormalCell.AppendRichContent(new RichContent()
        {
            Type = RichContentType.Mask,
            Content = "want",
            // Length = 4,
            IsLast = false,
            StartTime = 5000,
            PlaySpeed = 0.03f,
            Id = 1,
        });
        avatarNormalCell.AppendRichContent(new RichContent()
        {
            Type = RichContentType.Text,
            Content = " ",
            IsLast = false,
            IsCoreWord = false
        });
        avatarNormalCell.AppendRichContent(new RichContent()
        {
            Type = RichContentType.Prominent,
            Content = "talk",
            IsLast = false,
            StartTime = 6000,
            PlaySpeed = 0.03f,
            IsCoreWord = true
        });
        avatarNormalCell.AppendRichContent(new RichContent()
        {
            Type = RichContentType.Text,
            Content = " ",
            IsLast = false,
            IsCoreWord = true
        });
        avatarNormalCell.AppendRichContent(new RichContent()
        {
            Type = RichContentType.Text,
            Content = "about want go ",
            IsLast = false,
            StartTime = 7000,
            PlaySpeed = 0.03f,
        });
        avatarNormalCell.AppendRichContent(new RichContent()
        {
            Type = RichContentType.Mask,
            Content = "want",
            // Length = 6,
            IsLast = false,
            StartTime = 5000,
            PlaySpeed = 0.03f,
            Id = 2,
        });
        avatarNormalCell.AppendRichContent(new RichContent()
        {
            Type = RichContentType.Text,
            Content = " about want talk about ",
            IsLast = true,
            StartTime = 7000,
            PlaySpeed = 0.03f,
        });

        avatarNormalCell.ChangeState(AvatarNormalCellState.Free);
        avatarNormalCell.SetBtnHideMaskCallback((A)=>
        {
            
        });
        List<MatchResult> result = new();
        // result.Add(("London", TextFieldExtension.TEXT_COLOR_GOOD));
        // result.Add(("on", TextFieldExtension.TEXT_COLOR_BAD));
        // result.Add(("want", TextFieldExtension.TEXT_COLOR_GOOD));
        // result.Add(("talk", TextFieldExtension.TEXT_COLOR_BAD));
        avatarNormalCell.TfOrigin.SetEvaluateResult(result);
        avatarNormalCell.TfOrigin.Display(ShowMode.QuestionNormal);
        avatarNormalCell.TfOrigin.PlayCollectEffect();
        avatarNormalCell.TfOrigin.ShowAnswer(new List<AnswerContent>()
        {
            new AnswerContent()
            {
                Answer = "wanted",
                AnswerType = AnswerType.Wrong,
                Id = 1,
            },
            new AnswerContent()
            {
                Answer = "Londoddd",
                AnswerType = AnswerType.Correct,
                Id = 2,
            },
        }, out var a, out var b);
        Timers.inst.Add(2,1,(i) =>
        {
            avatarNormalCell.TfOrigin.PlayAnswer(new List<AnswerContent>()
            {
                new AnswerContent()
                {
                    Answer = "want",
                    AnswerType = AnswerType.Correct,
                    Id = 1,
                },
            });
        });

        // avatarNormalCell.TfOrigin.ClearContent();
        // avatarNormalCell.TfOrigin.Play(ShowMode.Typing);
        // avatarNormalCell.TfOrigin.Play(ShowMode.Follow, 3f);
        // avatarNormalCell.TfOrigin.SetEvaluateResult(result);
        // avatarNormalCell.TfOrigin.ClearContent();
        // avatarNormalCell.TfOrigin.Play(ShowMode.Follow);
        var startTime = 2000;
        // for (var i = 0; i < avatarNormalCell.TfOrigin.GetAllContents().Count; i++)
        // {
        //     var randomSpd = Random.Range(0.03f, 1f);
        //     RichContent contentTmp = avatarNormalCell.TfOrigin.GetAllContents()[i]; 
        //     contentTmp.StartTime = startTime;
        //     var timeSpan = randomSpd * contentTmp.Length * 1000;
        //     contentTmp.EndTime = startTime + (int)timeSpan;
        //     contentTmp.PlaySpeed = randomSpd;
        //     avatarNormalCell.TfOrigin.GetAllContents()[i] = contentTmp;
        //     startTime += (int)timeSpan + 300;
        // }

        avatarNormalCell.TfOrigin.OnTypingEffectEnd += () =>
        {
            avatarNormalCell.TfOrigin.ShowAnswer(new List<AnswerContent>()
            {
                new AnswerContent() { Id = 1, Answer = "high", AnswerType = AnswerType.Correct },
                new AnswerContent() { Id = 2, Answer = "go", AnswerType = AnswerType.Wrong }
            }, out var a, out var b);
        };
        
        // avatarNormalCell.TfOrigin.Play(ShowMode.Typing);


        // var seq = DOTween.Sequence();
        // seq.AppendCallback(() =>
        // {
        //     avatarNormalCell.AppendTextAndTrans("Hello, my name is flow.", "Pas mal aujourd'hui. ", false);
        //     avatarNormalCell.PlayTypingEffect();
        // });
        // seq.AppendInterval(7.1f);
        // seq.AppendCallback(() =>
        // {
        //     avatarNormalCell.AppendTextAndTrans("I like to eat fried chicken wings and hamburgers. ",
        //         "J'ai récemment voulu apprendre à faire du café.", false);
        // });
        // seq.AppendInterval(0.1f);
        // seq.AppendCallback(() =>
        // {
        //     avatarNormalCell.AppendTextAndTrans("I often go skiing on weekends.",
        //         "J'ai récemment voulu apprendre à faire du café.", true);
        //     //avatarNormalCell.TfOrigin.OnTypingEffectEnd += OnCreatePlayerNormalCell;
        //     // avatarNormalCell.ShowMask(); 
        // });
    }

    private void OnCreatePlayerNormalCell()
    {
        PlayerNormalCell playerNormalCell = new PlayerNormalCell();
        var ctrl = ControllerManager.instance.GetController(ModelConsts.Chat) as ChatController;
        this.AddChatCellCom(playerNormalCell, ChatCellType.PlayerNormal, null);
        var pos = playerNormalCell.playerNormalCellCom.com.position;
        // playerNormalCell.playerNormalCellCom.com.position =
        //     new Vector3(this.width - playerNormalCell.playerNormalCellCom.com.width - 18, pos.y, pos.z);
        var seq = DOTween.Sequence();
        seq.AppendCallback(() => { playerNormalCell.ChangeState(PlayerNormalCellState.WaitAsr); });
        seq.AppendCallback(() =>
        {            
            var textContent = new RichContent()
            {
                Type = RichContentType.Text,
                Content = "Hello, my name is flow. ",
                IsLast = true,
            };
            playerNormalCell.AppendContent(textContent);
        });
        seq.AppendInterval(2f);
        seq.AppendCallback(() =>
        {
            var textContent = new RichContent()
            {
                Type = RichContentType.Text,
                Content = "I like to eat fried chicken wings and hamburgers. ",
                IsLast = true,
            };
            playerNormalCell.AppendContent(textContent);
        });
        seq.AppendInterval(2f);
        seq.AppendCallback(() =>
        {
            var textContent = new RichContent()
            {
                Type = RichContentType.Text,
                Content = "I often go skiing on weekends.",
                IsLast = true,
            };
            playerNormalCell.AppendContent(textContent);
        });
        seq.AppendInterval(2f);
        seq.AppendCallback(() =>
        {
            playerNormalCell.TfOrigin.Display(ShowMode.Normal);
            playerNormalCell.ChangeState(PlayerNormalCellState.Evaluate);
        });
    }

    private void OnCreatePlayerFollowCell()
    {
        PlayerFollowCell playerFollowCell = new PlayerFollowCell();
        this.AddChatCellCom(playerFollowCell, ChatCellType.PlayerFollow, null);
        var pos = playerFollowCell.playerFollowCellCom.com.position;
        // playerFollowCell.playerFollowCellCom.com.position =
        //     new Vector3(this.width - playerFollowCell.playerFollowCellCom.com.width - 18, pos.y, pos.z);
        var seq = DOTween.Sequence();
        seq.AppendCallback(() =>
        {
            var textContent = new RichContent()
            {
                Type = RichContentType.Text,
                Content = "Hello, my name is flow.",
                IsLast = true,
            };
            playerFollowCell.AppendText(textContent);
        });
        seq.AppendCallback(() =>
        {
            var textContent = new RichContent()
            {
                Type = RichContentType.Text,
                Content = "I like to eat fried chicken wings and hamburgers. ",
                IsLast = true,
            };
            playerFollowCell.AppendText(textContent);
        });
        seq.AppendCallback(() =>
        {
            var textContent = new RichContent()
            {
                Type = RichContentType.Text,
                Content = "I often go skiing on weekends.",
                IsLast = true,
            };
            playerFollowCell.AppendText(textContent);
            playerFollowCell.ChangeState(PlayerFollowCellState.Idle);
        });
        seq.AppendInterval(2f);
        seq.AppendCallback(() =>
        {
            playerFollowCell.ChangeState(PlayerFollowCellState.Follow);
            playerFollowCell.TfOrigin.OnTypingEffectEnd += () =>
            {
                playerFollowCell.ChangeState(PlayerFollowCellState.Evaluate);
            };
        });
    }

    private void OnCreatePreTalkCell()
    {
        PreTalkCell preTalkCell = new PreTalkCell();
        this.AddChatCellCom(preTalkCell, ChatCellType.PreTalk, null);
    }

    private void OnCreateSuggestCell()
    {
        SuggestCell suggestCell = new SuggestCell();
        this.AddChatCellCom(suggestCell, ChatCellType.Suggest, null);
        var pos = suggestCell.suggestCellCom.com.position;
        // suggestCell.suggestCellCom.com.position =
        //     new Vector3((this.width - suggestCell.suggestCellCom.com.width) / 2f, pos.y, pos.z);
        var seq = DOTween.Sequence();
        seq.AppendCallback(() =>
        {
            suggestCell.AppendText("I'm doing well today. I want to learn how to make coffee recently.",
                "ai récemment voulu apprendre à faire du café.", true);
            // suggestCell.TfOrigin.AddDynamicBtn();
        });
    }

    private void OnCreateToastCell()
    {
        ToastCell toastCell = new ToastCell();
        this.AddChatCellCom(toastCell, ChatCellType.Toast, null);
        var pos = toastCell.toastCellCom.com.position;
        toastCell.toastCellCom.com.position =
            new Vector3((this.width - toastCell.toastCellCom.com.width) / 2f, pos.y, pos.z);
        var seq = DOTween.Sequence();
        seq.AppendCallback(() =>
        {
            toastCell.SetIcon(ToastCell.IconType.challenge);
            toastCell.AppendText("You can practice conversations with Avatar", true);
            toastCell.StartTimer(()=>Debug.LogWarning("CML"));
        });
    }

    private void OnCreateScaffoldCell()
    {
        ScaffoldCell scaffoldCell = new ScaffoldCell();
        this.AddChatCellCom(scaffoldCell, ChatCellType.Scaffold, null);
        var pos = scaffoldCell.scaffoldCellCom.com.position;
        scaffoldCell.scaffoldCellCom.com.position =
            new Vector3((this.width - scaffoldCell.scaffoldCellCom.com.width) / 2f, pos.y, pos.z);
        var seq = DOTween.Sequence();
        seq.AppendCallback(() =>
        {
            scaffoldCell.AppendText("Hello, my name is flow.",
                "ai récemment voulu apprendre à faire du café.", true);
        });
    }
    // ************************************Test Logic End****************************************
    #endregion
    
    protected override void OnShow()
    {
        this.ui.comCellContainer.comContent.Contents.visible = true;
        ui.posCollectEnd.visible = false;
        if (_chatModel.chatMode == PB_DialogMode.Career)
        {
            ui.comCellContainer.com.position = new Vector3(ui.comCellContainer.com.position.x,
                ui.compTopic.com.position.y + ui.compTopic.com.height + 32, 0);
            this.ui.comCellContainer.comContent.com.height = _containHeight - 32 - ui.compTopic.com.height;
        }
        else
        {
            ui.comCellContainer.com.position =
                new Vector3(ui.comCellContainer.com.position.x, ui.compTopic.com.position.y, 0);
            this.ui.comCellContainer.comContent.com.height = _containHeight;
        }
        // this.AddUIEvent();
        this.ui.comCellContainer.comContent.com.scrollPane.SetPercY(1f, false);
        this.ui.testBtn2.visible = false;// AppConst.IsDebug;
        this.ui.cellType.visible = false;// AppConst.IsDebug;
        ui.comChatCombo.com.visible = false;
        ui.comChatCombo.tfCombo.visible = false;
        _emptyBubbleId = 0;
        _combo = 0;
        ui.topicShow.selectedIndex = 0;
        ui.compTopic.com.position = new Vector3(ui.comCellContainer.com.position.x, ui.compTopic.com.position.y, 0);
        HideAvatarName();
        ui.comAvatarName.com.position = new Vector3(ui.targetAvatarNamePos.position.x,
            ui.targetAvatarNamePos.position.y, ui.targetAvatarNamePos.position.z);
        //ShowAvatarName("Alexandre");
        //this.AddUIEvent(this.ui.add.onClick,Add);
        // this.AddUIEvent(this.ui.delete.onClick,Delete);
        // CheckMic();
    }

    private async void CheckMic()
    {
        Debug.LogError("请求麦克风权限 oldChat");
        //判断是否是第一次出现
        if (!Application.HasUserAuthorization(UserAuthorization.Microphone) && !_settingModel.isAppearMicPermission)
        {
            await RequestMicrophonePermissionAsync();
            _settingModel.SetMicrophonePermission(true);
            return;
        }
        
        if (!ControllerManager.instance.GetController<PermissMsgController>(ModelConsts.PermissMsg).CheckPermissMic())
        {
            return;
        }
    }
    
    private async UniTask RequestMicrophonePermissionAsync()
    {
        await Application.RequestUserAuthorization(UserAuthorization.Microphone).ToUniTask();
    }
    
    

    public void SetCellContainerVisible(bool isVisible)
    {
        if (!this.isShow) return;
        this.ui.comCellContainer.comContent.Contents.visible = isVisible;
    }

    public int AddChatCellCom(BaseChatCell baseChatCell, ChatCellType cellType, Object data)
    {
        if (_emptyBubbleId != 0)
        {
            ReturnChatCellItem(_emptyBubbleId,ChatCellType.Empty);
            _emptyBubbleId = 0;
        }
        _bubbleId++;
        GComponent gCom;
        BaseChatCell ChatCell = null;
        string resName = _chatController.GetChatCellResName(cellType);
        if (_pools.ContainsKey(cellType) && _pools[cellType].Count > 0)
        {
            gCom = _pools[cellType][0];
            _pools[cellType].RemoveAt(0);
        }
        else
            gCom = _uiPackage.CreateObject(resName).asCom;

        gCom.name = _bubbleId.ToString();
        _chatModel.SetChatCellByBubbleId(_bubbleId, baseChatCell);
        int roundId = _chatModel.curRoundId;
        baseChatCell.OnInit(gCom, cellType, _bubbleId, RefreshCellContainer,roundId);
        baseChatCell.SetData(data);
        baseChatCell.SetModel(_chatModel, _recommendCardModel,_reviewQuestionModel);
        baseChatCell.SetChatUI(GetUI<ChatUI>(UIConsts.Chat));
        baseChatCell.SetRecordUI(GetUI<RecordUI>(UIConsts.RecordUI));
        baseChatCell.SetGuideModel(GetModel<GuideModel>(ModelConsts.Guide));
        baseChatCell.SetChatController(_chatController);
        baseChatCell.SetCurrencyController(_currencyController);
        baseChatCell.SetTaskController(GetController<TaskController>(ModelConsts.Task));
        this.ui.comCellContainer.comContent.com.AddChild(gCom);
        gCom.group = this.ui.comCellContainer.comContent.Contents;
        var pos = gCom.position;
        if (cellType == ChatCellType.AvatarNormal || cellType == ChatCellType.PreTalk)
            gCom.position = new Vector3(0, pos.y, pos.z);
        // else if (cellType == ChatCellType.PlayerFollow || cellType == ChatCellType.Toast)
        //     gCom.position = new Vector3(this.ui.comCellContainer.comContent.com.width / 2 - gCom.width / 2, pos.y, pos.z);
        else
            gCom.position = new Vector3(ui.comCellContainer.com.width - gCom.width , pos.y, pos.z);
        // VFDebug.LogError("width "+width+" gCom.width "+ gCom.width+"  "+gCom.position+" cellType "+cellType);
        if (cellType == ChatCellType.Empty)
            gCom.height = 0;
        TimerManager.instance.RegisterNextFrame((a) =>
        {
            TimerManager.instance.RegisterNextFrame((a) =>
            {
                this.ui.comCellContainer.comContent.com.scrollPane.SetPercY(1, false);
            });
        });
        DotAppearBubble(cellType);
        return _bubbleId;
    }

    private void DotAppearBubble(ChatCellType cellType)
    {
        string bubbleType = "";
        switch (cellType)
        {
            case ChatCellType.AvatarNormal:
                bubbleType = "Avatar";
                break;
            case ChatCellType.PlayerFollow:
                bubbleType = "User";
                break;
            case ChatCellType.Toast:
                bubbleType = "Toast";
                break;
            case ChatCellType.PlayerNormal:
                bubbleType = "User";
                break;
            case ChatCellType.PreTalk:
                bubbleType = "Loading";
                break;
            case ChatCellType.Scaffold:
                bubbleType = "Scaffold";
                break;
            case ChatCellType.Suggest:
                bubbleType = "Suggest";
                break;
            case ChatCellType.Advice:
                bubbleType = "Advice";
                break;
            case ChatCellType.Empty:
                bubbleType = "Empty";
                break;
        }
        DataDotAppear_Dialogue_bubble dot = new DataDotAppear_Dialogue_bubble();
        dot.Dialogue_id = _chatModel.dialogId;
        dot.Bubble_type = bubbleType;
        dot.Dialogue_round = _chatModel.curRoundId;
        dot.Help_mode = _chatModel.UserAssistLevel == PB_UserAssistLevelEnum.UserAssistLevelHelpAutoOpen
            ? "Auto"
            : "Manual";
        DataDotMgr.Collect(dot);
    }

    //刷新布局
    public void RefreshCellContainer(int bubbleId)
    {
        this.ui.comCellContainer.comContent.com.scrollPane.SetPercY(1, false);
        // VFDebug.LogError("RefreshCellContainer "+bubbleId);
        // this.ui.comCellContainer.Contents.EnsureBoundsCorrect();
        // var containerBottomY = ui.comCellContainer.comContent.com.position.y + ui.comCellContainer.comContent.com.height;
        // BaseChatCell cell = _chatModel.GetChatCellByBubbleId(bubbleId);
        // if(cell == null)
        //     return;
        // VFDebug.LogError("cell.gCom.height  "+cell.gCom.height+" cell.gCom.position.y "+cell.gCom.position.y+" ui.comCellContainer.comContent.com.position.y ");
        // var cellBottomY = cell.gCom.height + cell.gCom.position.y+ui.comCellContainer.comContent.com.position.y;
        // Vector2 gloalCellPos = cell.gCom.parent.LocalToGlobal(new Vector2(cell.gCom.position.x,cell.gCom.position.y+cell.gCom.height));
        // Vector2 gloalContentPos = ui.comCellContainer.comContent.com.parent.LocalToGlobal(new Vector2(ui.comCellContainer.comContent.com.position.x,ui.comCellContainer.comContent.com.position.y+ui.comCellContainer.comContent.com.height));
        // Vector2 globalPos = cell.gCom.LocalToGlobal(Vector2.zero);
        // VFDebug.LogError("gloalCellPos "+gloalCellPos+" gloalContentPos "+gloalContentPos+"globalPos "+globalPos);
        // if (globalPos.y +cell.gCom.height  > gloalContentPos.y && cell.cellType != ChatCellType.Empty)
        // {
        //     TimerManager.instance.RegisterNextFrame((a) =>
        //     {
        //         VFDebug.LogError("111111111111");
        //         // this.ui.comCellContainer.comContent.com.scrollPane.SetPercY(1, true);
        //     });
        // }
        // // 判断内容高度是否超过显示区域高度
        // //引导时候不可以滑动
        // if((_chatModel.chatMode == PB_DialogMode.OnBoarding && _chatModel.curRoundId == _chatController.GuideOnboardingRoundIdfour))
        //     ui.comCellContainer.comContent.com.scrollPane.touchEffect = false;
        // else if (ui.comCellContainer.Contents.height > ui.comCellContainer.comContent.com.height)
        //     // 内容超过显示区域，允许滑动
        //     ui.comCellContainer.comContent.com.scrollPane.touchEffect = true;
        // else
        //     // 内容未超过显示区域，禁止滑动
        //     ui.comCellContainer.comContent.com.scrollPane.touchEffect = false;
    }

    public void ForceRefershScroll(float progressValue)
    {
        TimerManager.instance.RegisterNextFrame((a) =>
        {
            this.ui.comCellContainer.comContent.com.scrollPane.SetPercY(progressValue, true);
        });
    }

    public void Update(int interval)
    {
        RefreshBubbleVisibility();
    }
    
    private void RefreshBubbleVisibility()
    {
        var scrollY = ui.comCellContainer.comContent.com.scrollPane.scrollingPosY;
        foreach (var keyValue in _chatModel.GetAllChatCell())
        {
            if(!keyValue.Value.isInit)
                continue;
            if (keyValue.Value.gCom == null)
            {
                Debug.LogError("bindT is null " + keyValue.Value.bubbleId + " type " + keyValue.Value.cellType);
                break;
            }
            if (keyValue.Value.gCom == null)
            {
                Debug.LogError("bindT.com is null " + keyValue.Value.bubbleId + " type " + keyValue.Value.cellType);
                break;
            }
            if (!keyValue.Value.gCom.visible && keyValue.Value.gCom.y <= ui.comCellContainer.comContent.com.height + scrollY &&
                keyValue.Value.gCom.y + keyValue.Value.gCom.height >= scrollY)
            {
                keyValue.Value.gCom.visible = true;
            }

            if (keyValue.Value.gCom.visible && keyValue.Value.gCom.y > ui.comCellContainer.comContent.com.height + scrollY)
            {
                keyValue.Value.gCom.visible = false;
            }

            if (keyValue.Value.gCom.visible && keyValue.Value.gCom.y + keyValue.Value.gCom.height < scrollY)
            {
                keyValue.Value.gCom.visible = false;
            }
        }
    }

    public void ChangeChatContainHeight(float y)
    {
        // VFDebug.LogError("y  "+y);
        float contentY = ui.comCellContainer.com.LocalToGlobal(new Vector2(0, ui.comCellContainer.comContent.com.y)).y;
       
        Vector2 aaa = ui.comCellContainer.com.GlobalToLocal(new Vector2(0, contentY));
        Vector2 bbbb = ui.comCellContainer.com.GlobalToLocal(new Vector2(0, y));
        var height = (Mathf.Abs(bbbb.y - aaa.y));
        // VFDebug.LogError("height "+height+"  aaa  "+aaa+"  "+bbbb);
        if (height >= _containHeight)
        {
            ui.comCellContainer.comContent.com.height = _containHeight;
            return;
        }
        this.ui.comCellContainer.comContent.com.height = height;
        this.ui.comCellContainer.comContent.com.scrollPane.SetPercY(1, false);
        this.ui.comCellContainer.comContent.com.EnsureBoundsCorrect();
    }

    private void HideHalfBubble()
    {
        foreach (var keyValue in _chatModel.GetAllChatCell())
        {
            var scrollY = ui.comCellContainer.comContent.com.scrollPane.scrollingPosY;
            GComponent gComponent = keyValue.Value.gCom;
            Debug.Log("type "+keyValue.Value.cellType+" id "+keyValue.Value.bubbleId + " comY "+gComponent.y+" panelHeight "+ui.comCellContainer.comContent.com.height
            +" scrollY "+scrollY+" comHeight "+gComponent.height);

            if (gComponent.visible && gComponent.y < scrollY && (gComponent.y+gComponent.height) > scrollY)
            {
                if(keyValue.Value.avatarId == _bubbleId && keyValue.Value.cellType == ChatCellType.PlayerNormal)
                    continue;
                if (_emptyBubbleId == 0)
                {
                    EmptyCell emptyCell = new EmptyCell();
                    _emptyBubbleId = AddChatCellCom(emptyCell,ChatCellType.Empty,null);
                }
                BaseChatCell baseChatCel = _chatModel.GetChatCellByBubbleId(_emptyBubbleId);
                baseChatCel.gCom.height += gComponent.height +gComponent.y - scrollY ;
                this.ui.comCellContainer.comContent.com.scrollPane.SetPercY(1, true);
                break;
            }
        }
    }
    
    public void ShowAvatarName(string name,string job )
    {
       
        if (!string.IsNullOrEmpty(name))
        {
            this.ui.comAvatarName.textAvatarName.text = name;
            var num = CalculateMaxVisibleChars(this.ui.comAvatarName.textAvatarName.displayObject as RichTextField);
            this.ui.comAvatarName.textAvatarName.text = GetEllipsizedText(name, num);


            ui.comAvatarName.textAvatarJob.text = job;
            var num1 = CalculateMaxVisibleChars(this.ui.comAvatarName.textAvatarJob.displayObject as RichTextField);
            this.ui.comAvatarName.textAvatarJob.text = GetEllipsizedText(job, num1);

            this.ui.comAvatarName.ObjavatarName.visible = true;
            //ui.comAvatarName.com.position = new Vector3(32, ui.comAvatarName.com.position.y); 强制改x坐标？？
           
        }
        else
            Debug.LogError("avatarName is null");
    }
  
    public float maxWidth = 200f; // 目标宽度


    public void ShowTopic(PB_TopicInfo info)
    {
        ui.topicShow.selectedIndex = 1;
        
        ui.comCellContainer.comContent.com.EnsureBoundsCorrect();
        ui.comCellContainer.comContent.com.scrollPane.SetPercY(1, false);
        
        if (null != Cfg.T.TBEmojiIcon.Get(info.icon_file_name))
            ui.compTopic.emojiIcon.icon = Cfg.T.TBEmojiIcon.Get(info.icon_file_name).resourceURL;
        else
            ui.compTopic.emojiIcon.icon = DefaultIconUrl;
        ui.compTopic.tfTopic.text = info.topic_title;
        ui.compTopic.grpTopic.EnsureBoundsCorrect();
    }

    int CalculateMaxVisibleChars(RichTextField richTextField)
    {

        float totalWidth = 0f;
        int characterCount = 0;

        // 逐字符计算宽度
        for (int i = 0; i < richTextField.text.Length; i++)
        {
            richTextField.textField.EnableCharPositionSupport();
            float characterWidth = richTextField.textField.charPositions[i].width;

            if (totalWidth + characterWidth > maxWidth)
            {
                break;
            }

            totalWidth += characterWidth;
            characterCount++;
        }

        return characterCount;
    }

        //
    string GetEllipsizedText(string text,int maxChars)
        {
        
        if (text.Length <= maxChars)
        {
            return text;
        }

        // 截取文本并添加省略号
        string ellipsizedText = text.Substring(0, maxChars - 3) + "...";
        return ellipsizedText;
    }


    public void HideAvatarName()
    {
        this.ui.comAvatarName.ObjavatarName.visible = false;
    }

    //播放显示的动画
    public void PlayTweenShowCell(float duration,float outDuration,int moveDeltaX)
    {
        ui.comChatCombo.com.visible = false;
        foreach (var item in _chatModel.GetAllChatCell())
        {
            BaseChatCell cell = item.Value;
            if (cell.cellType == ChatCellType.AvatarNormal && false)
            {
                cell.gCom.TweenMoveX(cell.gCom.position.x +moveDeltaX, duration).SetEase(EaseType.QuadIn).OnComplete(() =>
                {
                    // 再向右移动500单位，耗时400ms
                    cell.gCom.TweenMoveX(-cell.gCom.width, outDuration).SetEase(EaseType.QuadIn);;
                });
            }
            else
            {
                cell.gCom.TweenMoveX(cell.gCom.position.x -moveDeltaX, duration).SetEase(EaseType.QuadIn).OnComplete(() =>
                {
                    // 再向右移动500单位，耗时400ms
                    cell.gCom.TweenMoveX(width+100, outDuration).SetEase(EaseType.QuadIn);;
                });
            }
        }
        ui.comAvatarName.com.TweenMoveX(ui.comAvatarName.com.position.x +moveDeltaX, duration).SetEase(EaseType.QuadIn).OnComplete(() =>
        {
            // 再向右移动500单位，耗时400ms
            ui.comAvatarName.com.TweenMoveX(-ui.comAvatarName.com.width, outDuration).SetEase(EaseType.QuadIn);;
        });
        ui.compTopic.com.TweenMoveX(ui.compTopic.com.position.x - moveDeltaX, duration).SetEase(EaseType.QuadIn)
            .OnComplete(() =>
                {
                    // 再向右移动500单位，耗时400ms
                    ui.compTopic.com.TweenMoveX(width + 100, outDuration).SetEase(EaseType.QuadIn);
                });
        //ui.ObjavatarName.asCom.TweenMoveX()
        //ui.TweenAvatarName.selectedPage = "Out";
        //ui.Out.Play();
    }
    public void ClearData()
    {
        _bubbleId = 0;
        Dictionary<int, ChatCellType> datas = new Dictionary<int, ChatCellType>();
        foreach (var item in _chatModel.GetAllChatCell())
        {
            datas[item.Key] = item.Value.cellType;
        }
        Debug.Log("ClearData  "+datas.Count);
        foreach (var item in datas)
        {
            ReturnChatCellItem(item.Key, item.Value);
        }
    }

    //还对象 清数据
    public void ReturnChatCellItem(int bubbleId, ChatCellType cellType)
    {
        BaseChatCell chatCell = _chatModel.GetChatCellByBubbleId(bubbleId);
        if (chatCell != null)
        {
            GComponent gCom = chatCell.gCom;
            if (gCom.parent != null)
                gCom.parent.RemoveChild(gCom);
            if (!_pools.ContainsKey(cellType))
                _pools[cellType] = new List<GComponent>();
            bool isRecycle = _pools[cellType].Count < MAXNUM;
           
            _chatModel.RemoveChatCellByBubbleId(bubbleId,isRecycle);
            if (!isRecycle)
            {
                gCom.Dispose(); 
            }
            else
                _pools[cellType].Add(gCom);
        }
        else
        {
            Debug.Log("不存在该数据：" + bubbleId);
        }

        //RefreshCellContainer();
    }

    public void PlayCollectEffect(List<Vector2> posList, Vector2 pos, Action cb = null, bool showIcon = false, bool disableNext = false)
    {
        if (showIcon)
            ui.posCollectEnd.visible = true;
        _spCollect.visible = true;
        Vector2 screenPos = pos;
        screenPos.y = Screen.height - screenPos.y;
        _spCollect.go.transform.Find("root/root/origin").position = StageCamera.main.ScreenToWorldPoint(screenPos);
        if (posList.Count == 1)
        {
            _spCollect.PlayWithCallback("1", () =>
            {
                ui.posCollectEnd.visible = false;
                _spCollect.visible = false;
                if (!disableNext)
                    _chatController.GetChatState().SetNextAble();
                cb?.Invoke();
            });
            
        }
        else if (posList.Count > 1)
        {
            Transform root = _spCollect.go.transform.Find("root/root");
            for (var i = 0; i < 10; i++)
            {
                if (posList.Count > i)
                {
                    Vector2 p = posList[i];
                    p.y = Screen.height - p.y; 
                    root.Find($"spot{i+1}").position = StageCamera.main.ScreenToWorldPoint(p); 
                    root.Find($"spot{i+1}").localScale = Vector3.one;
                }
                else
                {
                    root.Find($"spot{i+1}").localScale = Vector3.zero;
                }
            }
            _spCollect.PlayWithCallback("2", () =>
            {
                ui.posCollectEnd.visible = false;
                _spCollect.visible = false;
                if(!disableNext)
                    _chatController.GetChatState().SetNextAble();
                cb?.Invoke();
            });
        }
    }

    public void ShowFlow()
    {
        if (_flowAnimator == null)
        {
            ui.loader.visible = true;
            (this.ui.loader as ModelCameraLoaderExtension)?.SetModel(ResUtils.GetUIModelPath("flow"));
            var model = ui.loader as ModelCameraLoaderExtension;
            var animators = model.go.GetComponentsInChildren<Animator>();
            if (animators.Length > 0)
            {
                _flowAnimator = animators[0];
                _flowAnimator.Play("Flow_Idle_Stand_Appear_Listen");
            }  
        }
        else
        {
            ui.loader.visible = true;
            _flowAnimator.Play("Flow_Idle_Stand_Appear_Listen");
        }
    }

    public void CloseFlow()
    {
        ui.loader.visible = false;
    }

    public void PlayComboAni(string name, bool isPerfect)
    {
        ui.comChatCombo.com.visible = true;
        ui.comChatCombo.spChatCombo.spineAnimation.AnimationState.ClearListenerNotifications();
        ui.comChatCombo.tfCombo.visible = false;
        if (isPerfect && _combo == 0)
            name = "prefec2";
        ui.comChatCombo.spChatCombo.spineAnimation.AnimationState.SetAnimation(0, name, false).Complete += _ =>
        {
            if (isPerfect)
            {
                _combo++;
                ui.comChatCombo.tfCombo.visible = _combo > 1;
                ui.comChatCombo.tfCombo.asTextField.text = $"<font size=48>x</font>{_combo}";
                ui.comChatCombo.Combo.Play(() =>
                {
                    ui.comChatCombo.com.visible = false;
                });   
            }
            else
            {
                _combo = 0;
                ui.comChatCombo.com.visible = false;
            }
        };

    }

    public void ResetCombo()
    {
        _combo = 0;
    }

}