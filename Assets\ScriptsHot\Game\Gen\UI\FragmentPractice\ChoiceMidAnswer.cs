/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class ChoiceMidAnswer : AFragAnswer
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "ChoiceMidAnswer";
        public static string url => "ui://cmoz5osjz7rm3b";

        public GList list;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(ChoiceMidAnswer));
        }

        public override void ConstructFromXML(XML xml)
        {
            list = GetChildAt(0) as GList;
        }
        public override void Dispose()
        {
            list = null;

            base.Dispose();
        }
    }
}