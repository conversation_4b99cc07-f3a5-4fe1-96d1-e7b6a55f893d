/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.ReviewQuestion
{
    public partial class QuestionCardList : GComponent
    {
        public static string pkgName => "ReviewQuestion";
        public static string comName => "QuestionCardList";
        public static string url => "ui://xlh8p6j0l44c3a";

        public GList listCards;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(QuestionCardList));
        }

        public override void ConstructFromXML(XML xml)
        {
            listCards = GetChildAt(0) as GList;
        }
        public override void Dispose()
        {
            listCards = null;

            base.Dispose();
        }
    }
}