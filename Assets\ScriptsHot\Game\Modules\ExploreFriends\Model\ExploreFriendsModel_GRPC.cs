﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Cysharp.Threading.Tasks;
using Msg.explore;
using ScriptsHot.Game.Modules.Procedure;
using UnityEngine;
using UnityEngine.Networking;

public partial class ExploreFriendsModel
{

    #region 核心维护数据

        private SC_GetFriendChatPreloadDataResp _allInfo;
        public SC_GetFriendChatPreloadDataResp AllInfo => _allInfo;

        /// <summary>
        /// 预加载数据
        /// </summary>
        /// <param name="ack"></param>
        public void SetPreLoadInfo(SC_GetFriendChatPreloadDataResp ack)
        {
            _allInfo = ack;
            Notifier.instance.SendNotification(NotifyConsts.ExploreFriend_preload);
        }
        
        public PB_FriendChatPreloadData GetDataByAvatarId(int avatarId)
        {
            if (AllInfo == null) return null;
            foreach (var data in AllInfo.preloadData)
            {
                if (data.avatarId == avatarId)
                {
                    return data;
                }
            }

            return null;
        }

        public PB_FriendChatExtRoundData_Avatar GetLastAudioIdByAvatarId(long avatarId)
        {
            if (AllInfo == null) return null;
            foreach (var data in AllInfo.preloadData)
            {
                if (data.avatarId == avatarId)
                {
                    return data.dialogRound.LastOrDefault().avatarExtRoundData;
                }
            }

            return null;
        }
        private void AddSelfData(SC_FriendChatDownMsgForASR ack)
        {
            if (AllInfo == null) return;
            foreach (var data in AllInfo.preloadData)
            {
                if (data.avatarId == ack.commonData.avatarId)
                {
                    PB_Friend_DialogRoundData newData = new PB_Friend_DialogRoundData();
                    newData.msgBelong = ack.commonData.msgOwner;
                    newData.round = ack.commonData.round;
                    newData.text = ack.text;
                    newData.bubbleId = ack.commonData.bubbleId;
                    data.dialogRound.Add(newData);
                    break;
                }
            }
        }

        private void AddAvatarTxt(SC_FriendChatDownMsgForAvatarReply ack)
        {
            if (AllInfo == null) return;
            foreach (var data in AllInfo.preloadData)
            {
                if (data.avatarId == ack.commonData.avatarId)
                {
                    foreach (var roundData in data.dialogRound)
                    {
                        if (roundData.round == ack.commonData.round && roundData.bubbleId == ack.commonData.bubbleId)
                        {
                            roundData.text = ack.replyText;
                            return;
                        }
                    }
                    PB_Friend_DialogRoundData newData = new PB_Friend_DialogRoundData();
                    newData.msgBelong = ack.commonData.msgOwner;
                    newData.round = ack.commonData.round;
                    newData.text = ack.replyText;
                    newData.bubbleId = ack.commonData.bubbleId;
                    data.dialogRound.Add(newData);
                    return;
                }
            }
        }
        
        private void AddAvatarAudio(SC_FriendChatDownMsgForAvatarReplyTTS ack)
        {
            if (AllInfo == null) return;
            foreach (var data in AllInfo.preloadData)
            {
                if (data.avatarId == ack.commonData.avatarId)
                {
                    foreach (var roundData in data.dialogRound)
                    {
                        if (roundData.round == ack.commonData.round && roundData.bubbleId == ack.commonData.bubbleId)
                        {
                            roundData.avatarExtRoundData = new PB_FriendChatExtRoundData_Avatar()
                            {
                                audio = ack.audio,
                                emotionAnalysisResult = ack.emotionAnalysisResult
                            };
                            return;
                        }
                    }
                    PB_Friend_DialogRoundData newData = new PB_Friend_DialogRoundData();
                    newData.msgBelong = ack.commonData.msgOwner;
                    newData.round = ack.commonData.round;
                    newData.bubbleId = ack.commonData.bubbleId;
                    newData.avatarExtRoundData = new PB_FriendChatExtRoundData_Avatar()
                    {
                        audio = ack.audio,
                        emotionAnalysisResult = ack.emotionAnalysisResult
                    };
                    data.dialogRound.Add(newData);
                    return;
                }
            }
            
            //清空过往音频
            foreach (var data in AllInfo.preloadData)
            {
                if (data.avatarId == ack.commonData.avatarId)
                {
                    for (int i = 0; i < data.dialogRound.Count - 1; i++)
                    {
                        data.dialogRound[i].avatarExtRoundData = null;
                    }
                }
            }
        }

        #endregion
        
        private SC_FriendChatDownMsgForClosenessProgressChange _closenessInfo;
        public SC_FriendChatDownMsgForClosenessProgressChange ClosenessInfo => _closenessInfo;

        /// <summary>
        /// 亲密度变化
        /// </summary>
        /// <param name="ack"></param>
        public void SetClosenessChange(SC_FriendChatDownMsgForClosenessProgressChange ack)
        {
            _closenessInfo = ack;
            var info = GameEntry.ExFriendC.Model.GetFriendSlotDataByAvatarId(ack.commonData.avatarId);
          
            if (ack.closenessProgressInfo.isUpgrade)
            {
                info.UpdateLevel(ack.closenessProgressInfo.closenessUpgradeDetail.nextClosenessLevelId);
                info.UpdateClosenessValue(ack.closenessProgressInfo.closenessUpgradeDetail.initValue);
            }
            else
            {
                info.UpdateClosenessValue(ack.closenessProgressInfo.afterValue);
            }
            
            Notifier.instance.SendNotification(NotifyConsts.ExploreFriend_closeness_update);
        }
    
        private SC_FriendChatDownMsgForASR _selfTxtInfo;
        public SC_FriendChatDownMsgForASR SelfTxtInfo => _selfTxtInfo;

        /// <summary>
        /// 对话下行 - 用户语音识别最终结果
        /// </summary>
        /// <param name="ack"></param>
        public void SetSelfTxtInfo(SC_FriendChatDownMsgForASR ack)
        {
            _selfTxtInfo = ack;
            AddSelfData(ack);
            Notifier.instance.SendNotification(NotifyConsts.ExploreFriend_selfTxt);
        }
        
        private SC_FriendChatDownMsgForAvatarReply _avatarTxtInfo;
        public SC_FriendChatDownMsgForAvatarReply AvatarTxtInfo => _avatarTxtInfo;

        /// <summary>
        /// 对话下行 - Avatar回复
        /// </summary>
        /// <param name="ack"></param>
        public void SetAvatarTxtInfo(SC_FriendChatDownMsgForAvatarReply ack)
        {
            _avatarTxtInfo = ack;
            AddAvatarTxt(ack);
            Notifier.instance.SendNotification(NotifyConsts.ExploreFriend_avatarTxt);
        }
        
        private SC_FriendChatDownMsgForAvatarReplyTranslate _avatarTxtTranslate;
        public SC_FriendChatDownMsgForAvatarReplyTranslate AvatarTxtTranslate => _avatarTxtTranslate;

        /// <summary>
        ///  对话下行 - Avatar回复翻译
        /// </summary>
        /// <param name="ack"></param>
        public void SetAvatarTranslate(SC_FriendChatDownMsgForAvatarReplyTranslate ack)
        {
            _avatarTxtTranslate = ack;
            Notifier.instance.SendNotification(NotifyConsts.ExploreFriend_avatarTranslate);
        }
        
        private SC_FriendChatDownMsgForAvatarReplyTTS _avatarTTS;
        public SC_FriendChatDownMsgForAvatarReplyTTS AvatarTTS => _avatarTTS;

        /// <summary>
        /// * 对话下行 - Avatar回复TTS
        /// * 1. 流式下发：一个音频会出现多个结果
        /// * 2. 非流式下发：一个音频只有一个结果
        /// </summary>
        /// <param name="ack"></param>
        public void SetAvatarTTS(SC_FriendChatDownMsgForAvatarReplyTTS ack)
        {
            _avatarTTS = ack;
            AddAvatarAudio(ack);
            Notifier.instance.SendNotification(NotifyConsts.ExploreFriend_avatarTTs);
        }
        
        #region 音频处理

        private Dictionary<ulong, ExploreMp3AudioInfo> _AudioDic = new Dictionary<ulong, ExploreMp3AudioInfo>();
        private Dictionary<string, ulong> _AudioBubbleDic = new Dictionary<string, ulong>();
        private const string AUDIO_FOLDER_NAME = "ExploreAudio";
        
        public void AddStreamAudio(bool isAvatar,byte[] doByte,string bubbleId,ulong audioId,bool is_last_clip,PB_Explore_FriendChatAudioDownFrame audioInfo,bool autoPlay)
        {
            Debug.LogError($"bubbleId::AddStreamAudio:::{bubbleId}  audioId：：{audioId} is_last_clip：：{is_last_clip} ");
            _AudioBubbleDic[bubbleId] = audioId;
        
            if (_AudioDic.TryGetValue(audioId, out ExploreMp3AudioInfo audioClipData))
            {
                var data = doByte;
                if (data == null) return;
                if (audioClipData.dataFull)
                {
                    AudioOver(isAvatar, audioInfo,autoPlay);
                    return;
                }

                audioClipData.dataFull = is_last_clip;
            
                // 优化：避免频繁的Array.Resize操作，使用List动态增加
                if (audioClipData.bytesList == null)
                {
                    audioClipData.bytesList = new List<byte[]>();
                }
                audioClipData.bytesList.Add(doByte);
            
                _AudioDic[audioId] = audioClipData;

                if (audioClipData.dataFull)
                {
                    // 只有完整接收后才合并数据，减少内存操作
                    MergeAndSaveAudioData(audioId);
                    AudioOver(isAvatar, audioInfo, autoPlay);
                }
            }
            else
            {
                ExploreMp3AudioInfo info = new ExploreMp3AudioInfo();
                info.bubbleId = bubbleId;
                info.recordId = audioId;
                info.bytesList = new List<byte[]> { doByte };
                info.dataFull = is_last_clip;
                _AudioDic.Add(audioId, info);
            
                if (info.dataFull)
                {
                    MergeAndSaveAudioData(audioId);
                    AudioOver(isAvatar, audioInfo, autoPlay);
                }
            }
        }
        
        
        // 合并音频数据并保存到本地
        private void MergeAndSaveAudioData(ulong audioId)
        {
            if (_AudioDic.TryGetValue(audioId, out ExploreMp3AudioInfo audioData) && audioData.bytesList != null)
            {
                // 计算总大小
                int totalSize = audioData.bytesList.Sum(arr => arr.Length);
            
                // 创建新数组并合并
                audioData.audioBytes = new byte[totalSize];
                int offset = 0;
            
                foreach (byte[] chunk in audioData.bytesList)
                {
                    Buffer.BlockCopy(chunk, 0, audioData.audioBytes, offset, chunk.Length);
                    offset += chunk.Length;
                }
            
                // 清空列表节省内存
                audioData.bytesList.Clear();
                audioData.bytesList = null;
            
                // 保存到本地
                SaveToLocal(audioId);
            }
        }

        private void AudioOver(bool isAvatar,PB_Explore_FriendChatAudioDownFrame audioInfo,bool autoPlay)
        {
            // Debug.LogError($"音频结束:::{audioInfo.id}  isAvatar：：{isAvatar} ");
            //第一句话播放 不走这里
            if (!autoPlay) return;
            if (isAvatar)
            {
                //AVATAR 播放语音
                ProcedureParams p = new ProcedureParams();
                p.type = EProcedureType.OnExploreFriendAudioPlay;
                p.param = audioInfo.id;
                Notifier.instance.SendNotification(NotifyConsts.procedure_explore_friend_avatar_audio_play,p);
                // Notifier.instance.SendNotification(NotifyConsts.OnboardFlow_PlayAudio,audioInfo.id);
            }
        }

        private bool SaveToLocal(ulong audioId)
        {
            bool success = true;
            var data = GetAudioBytes(audioId);
            if (data == null || data.audioBytes == null || data.audioBytes.Length == 0)
            {
                return false;
            }
            try
            {
                // 确保音频文件夹存在
                string audioFolderPath = GetAudioFolderPath();
                if (!Directory.Exists(audioFolderPath))
                {
                    Directory.CreateDirectory(audioFolderPath);
                }
            
                // 修改保存路径到ExploreAudio子文件夹
                string url = Path.Combine(audioFolderPath, audioId.ToString());
            
                File.WriteAllBytes(url, data.audioBytes);
            
            }
            catch (Exception ex)
            {
                VFDebug.LogError($"Explore memory ExploreModel 写文件失败: {ex.Message}");
                success = false;
            }
            return success;
        }
        
        
        private ExploreMp3AudioInfo GetAudioBytes(ulong audioId)
        {
            if (_AudioDic.TryGetValue(audioId, out ExploreMp3AudioInfo audioClipData))
            {
                return audioClipData;
            }
            else
            {
                return null;
            }
        }
        
        /// <summary>
        /// 通过 bubbleId 获取 音频audioId
        /// </summary>
        /// <param name="bubbleId"></param>
        /// <returns></returns>
        public ulong GetAudioIdByBubbleId(string bubbleId)
        {
            if (_AudioBubbleDic.TryGetValue(bubbleId, out ulong audioId))
            {
                return audioId;
            }
            else
            {
                return 0;
            }
        }
        
        /// <summary>
        /// 获取音频文件夹路径
        /// </summary>
        private string GetAudioFolderPath()
        {
            return Path.Combine(Application.persistentDataPath, AUDIO_FOLDER_NAME);
        }
        
        public async Task<AudioClip> GetAudioByRecordId(ulong audioId)
        {
        
            if (_AudioDic.TryGetValue(audioId, out ExploreMp3AudioInfo audioClipData))
            {
                if (audioClipData.audio)
                {
                    return audioClipData.audio;
                }
                else
                {
                    if (UrlExistedForRecordId(audioId))
                    {
                        audioClipData.audio = await this.GetAudioFromData(audioClipData.recordId);
                        return audioClipData.audio;
                    }
                    else
                    {
                        SaveToLocal(audioClipData.recordId);
                        audioClipData.audio = await this.GetAudioFromData(audioClipData.recordId);
                        return audioClipData.audio;
                    }
                }
            }
            else
            {
            
                if (UrlExistedForRecordId(audioId))
                {
                    AudioClip clip = await this.GetAudioFromData(audioId);
                
                    // 创建新条目缓存此AudioClip
                    ExploreMp3AudioInfo newInfo = new ExploreMp3AudioInfo
                    {
                        recordId = audioId,
                        audio = clip,
                        dataFull = true
                    };
                    _AudioDic[audioId] = newInfo;
                
                    return clip;
                }         
                else
                {
                    return null;
                }
            }
        }
        
        private async Task<AudioClip> GetAudioFromData(ulong audioId)
        {
            ExploreController controller = GetController<ExploreController>(ModelConsts.Explore);

            UnityWebRequest req;
            if (controller.UseMp3Audio)
            {
                req = UnityWebRequestMultimedia.GetAudioClip(UrlForLoadRecordId(audioId), AudioType.MPEG);
                await req.SendWebRequest();
                return DownloadHandlerAudioClip.GetContent(req);
            }
            else
            {
                req = UnityWebRequestMultimedia.GetAudioClip(UrlForLoadRecordId(audioId), AudioType.WAV);
                await req.SendWebRequest();
                return DownloadHandlerAudioClip.GetContent(req);
            }
        }
        
        private string UrlForLoadRecordId(ulong audioId)
        {
            // 返回音频文件夹中的文件路径
            string ret = Path.Combine(GetAudioFolderPath(), audioId.ToString());
#if UNITY_IOS || UNITY_STANDALONE_OSX || UNITY_ANDROID 
            ret = "file://" + ret;
#endif         
            return ret;
        }
        
        public bool UrlExistedForRecordId(ulong audioId)
        {
            string url = UrlForSaveRecordId(audioId);
            return File.Exists(url);
        }
        
        private string UrlForSaveRecordId(ulong audioId)
        {
            // 返回音频文件夹中的文件路径
            return Path.Combine(GetAudioFolderPath(), audioId.ToString());
        }
        #endregion
        
}


