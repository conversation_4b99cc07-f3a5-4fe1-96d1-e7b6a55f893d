﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Cysharp.Threading.Tasks;
using Google.Protobuf;
using Grpc.Core;
using Msg;
using Msg.explore;
using Msg.social;
using Msg.tts;
using ScriptsHot.Game.Modules.Explore;
using ScriptsHot.Game.Modules.Procedure;
using UnityEngine;
using UnityEngine.Networking;


public partial class ExploreModel
{
    // 最大缓存音频数量
    private const int MAX_CACHED_AUDIOCLIPS = 10; 
    // 本地保存的最大音频文件数
    private const int MAX_LOCAL_AUDIO_FILES = 30; 
    // 最大缓存大小(10MB)
    private const long MAX_AUDIO_CACHE_SIZE = 5 * 1024 * 1024; 
    
    private const string AUDIO_FOLDER_NAME = "ExploreAudio";
    
    // 音频使用记录，用于缓存策略
    private List<ulong> _recentlyUsedAudio = new List<ulong>();
    private long _currentAudioCacheSize = 0;
    private bool _isCacheCleaning = false;
    
    // 添加资源卸载计时器
    private float _lastUnloadTime = 0f;
    // 卸载间隔时间（秒）
    private const float UNLOAD_INTERVAL = 40f; 
    // 已释放但未卸载的Clip数量
    private int _releasedClipsCount = 0; 
    
    public void OnInit()
    {
        InitAudioCache();
    }
    
    #region 其他数据
    
    private SC_MissionStoryChatDownMsgForASR _streamUserDialogInfo;
    public SC_MissionStoryChatDownMsgForASR UserDialogInfo => _streamUserDialogInfo;

    /// <summary>
    /// 用户自己说话数据--流返回
    /// </summary>
    /// <param name="ack"></param>
    public void SetStreamUserDialogInfo(SC_MissionStoryChatDownMsgForASR ack)
    {
        _streamUserDialogInfo = ack;
        // Debug.LogError("SC_DialogDownMsgForASR------------------------------------------");
        Notifier.instance.SendNotification(NotifyConsts.ExploreStreamUserDialogInfoUpdate);
    }

    
        
    private SC_MissionStoryChatDownMsgForAdvice _adviceInfo;
    public SC_MissionStoryChatDownMsgForAdvice AdviceInfo => _adviceInfo;

    /// <summary>
    /// advice内容
    /// </summary>
    /// <param name="ack"></param>
    public void SetAdvice(SC_MissionStoryChatDownMsgForAdvice ack)
    {
        _adviceInfo = ack;
        
        foreach (var itemData in _allMissionData)
        {
            if (itemData.Data.storyId == ack.commonData.storyId)
            {
                if (ack.commonData.round != 1 && itemData.Data.firstRoundData.isSettlement)
                    break;
                itemData.Data.taskId = ack.commonData.taskId;
                itemData.Data.firstRoundData.adviceText = ack.adviceText;
            }
        }
    }
    
    
    private SC_MissionStoryChatDownMsgForUserReplyExample _scaffoldInfo;
    public SC_MissionStoryChatDownMsgForUserReplyExample ScaffoldInfo => _scaffoldInfo;

    /// <summary>
    /// 脚手架内容
    /// </summary>
    /// <param name="ack"></param>
    public void SetScaffold(SC_MissionStoryChatDownMsgForUserReplyExample ack)
    {
        _scaffoldInfo = ack;
        
        foreach (var itemData in _allMissionData)
        {
            if (itemData.Data.storyId == ack.commonData.storyId)
            {
                if (ack.commonData.round != 1 && itemData.Data.firstRoundData.isSettlement)
                    break;
                itemData.Data.taskId = ack.commonData.taskId;
                itemData.Data.firstRoundData.exampleText = ack.exampleText;
            }
        }
    }
    
    private SC_MissionStoryChatDownMsgForUserReplyExampleTranslate _scaffoldTranslate;
    public SC_MissionStoryChatDownMsgForUserReplyExampleTranslate ScaffoldTranslate => _scaffoldTranslate;

    /// <summary>
    /// 脚手架翻译
    /// </summary>
    /// <param name="ack"></param>
    public void SetScaffoldTranslate(SC_MissionStoryChatDownMsgForUserReplyExampleTranslate ack)
    {
        _scaffoldTranslate = ack;
        
        foreach (var itemData in _allMissionData)
        {
            if (itemData.Data.storyId == ack.commonData.storyId)
            {
                if (ack.commonData.round != 1 && itemData.Data.firstRoundData.isSettlement)
                    break;
                itemData.Data.taskId = ack.commonData.taskId;
                itemData.Data.firstRoundData.exampleTranslateText = ack.exampleTranslateText;
            }
        }
    }
    
    private SC_MissionStoryChatDownMsgForUserReplyExampleTTS _scaffoldTTs;
    public SC_MissionStoryChatDownMsgForUserReplyExampleTTS ScaffoldTTs => _scaffoldTTs;

    /// <summary>
    /// 脚手架语音
    /// </summary>
    /// <param name="ack"></param>
    public void SetScaffoldTTs(SC_MissionStoryChatDownMsgForUserReplyExampleTTS msg)
    {
        _scaffoldTTs = msg;
        byte[] doByte = msg.audio.audio.ToByteArray();
        AddStreamAudio(false,doByte,msg.commonData.bubbleId,msg.audio.id,msg.audio.is_last_clip,msg.audio,false);
    }
    
    
    private SC_DialogDownMsgForFeedback _awardMsg;
    public SC_DialogDownMsgForFeedback AwardMsg => _awardMsg;

    /// <summary>
    /// 奖励数据 ---服务器不会发了
    /// </summary>
    /// <param name="ack"></param>
    public void SetAwardMsg(SC_DialogDownMsgForFeedback msg)
    {
        _awardMsg = msg;
        
        foreach (var item in _awardMsg.data)
        {
            if (item.type == PB_Explore_DialogFeedbackType.EO_FEEDBACK_XP)
            {
                // xp_bonus_amount:本次奖励的经验值
                // xp_total_amount:本次奖励后累计的经验值
                // task_id: 本条内容的id
                // dialogue_id: 本次对话的id
                // dialogue_round: 本次对话中当前对话轮次，1/2/3/4/5/6.....
                APPEAR_EXPLORE_XP_BONUS data = new APPEAR_EXPLORE_XP_BONUS();
                data.xp_bonus_amount = item.exp.changeExp;
                data.xp_total_amount = item.exp.afterExp;
                data.task_id = _awardMsg.commonData.taskId;
                data.dialogue_id = _awardMsg.commonData.dialogId;
                data.dialogue_round = _awardMsg.commonData.round;
                DataDotMgr.Collect(data);
                break;
            }
        }
        
        Notifier.instance.SendNotification(NotifyConsts.ExploreDialogDownAwardMsg);
    }
    
    private SC_MissionStoryChatDownMsgForStepProgressChange _progressChange;

    /// <summary>
    ///  Mission剧情对话下行 - 任务步骤进度变更
    /// </summary>
    /// <param name="ack"></param>
    public void SetProgressChange(SC_MissionStoryChatDownMsgForStepProgressChange ack)
    {
        // Debug.LogError($"进度更新------------------afterFinishedStepNo：：{ack.change.afterFinishedStepNo}");
        _progressChange = ack;
        foreach (var itemData in _allMissionData)
        {
            if (itemData.Data.storyId == ack.commonData.storyId) 
            {
                //有进度了 清空 Intro
                itemData.Data.taskIntro.Clear();
                itemData.Data.detail.storyProgress.currentStepDesc = ack.change.afterStepTitle;
                itemData.Data.detail.storyProgress.finishedStepNo = ack.change.afterFinishedStepNo;
            }
        }
        Notifier.instance.SendNotification(NotifyConsts.ExploreProgressChange,ack.commonData.storyId);
    }
    
    /// <summary>
    ///  Mission剧情对话下行 - 任务变更
    /// </summary>
    /// <param name="ack"></param>
    public void SetTaskChange(PB_StoryPreloadData ack)
    {
        foreach (var itemData in _allMissionData)
        {
            if (itemData.Data.storyId == ack.storyId)
            {
                // Debug.LogError($"avatar txt  任务变更  storyId: {ack.storyId} {ack.firstRoundData.replyText}");
                itemData.Data = ack;
            }
        }
    }
    
    #endregion
    
    
    #region avatar数据

    private SC_MissionStoryChatDownMsgForAvatarReply _streamAvatarDialogInfo;
    public SC_MissionStoryChatDownMsgForAvatarReply AvatarDialogInfo => _streamAvatarDialogInfo;

    // 添加新字段用于存储最近的消息
    private SC_MissionStoryChatDownMsgForAvatarReply _lastDialogInfo;
    private SC_MissionStoryChatDownMsgForAvatarReplyTranslate _lastDialogTranslate;

    /// <summary>
    /// avatar说话数据文本
    /// </summary>
    /// <param name="ack"></param>
    public void SetStreamAvatarDialogInfo(SC_MissionStoryChatDownMsgForAvatarReply ack)
    {
        _streamAvatarDialogInfo = ack;
        _lastDialogInfo = ack;
        
        foreach (var itemData in _allMissionData)
        {
            if (itemData.Data.storyId == ack.commonData.storyId)
            {
                // Debug.LogError($"avatar ack.commonData.round ::{ack.commonData.round}   itemData.Data.firstRoundData.isSettlement:{itemData.Data.firstRoundData.isSettlement}");
                
                //isSettlement 讲解：  isSettlement是用来 处理 ，客户端收到结算消息后，avatar 数据后返回会，导致数据覆盖，所以通过 isSettlement来废弃最后一轮数据 而存在
                //但是这样会出现一个新的问题，isSettlement 何时恢复为 false. 要不然当前story进入下一轮task后 数据无法更新了
                //所以 这里通过 round == 1 来重置数据
                
                if (ack.commonData.round == 1)
                {
                    itemData.Data.firstRoundData.isSettlement = false;
                }

                if (ack.commonData.round != 1 && itemData.Data.firstRoundData.isSettlement)
                    break;
                itemData.Data.taskId = ack.commonData.taskId;
                itemData.Data.firstRoundData.replyText = ack.replyText;
                
                // Debug.LogError($"avatar txt ::{ack.replyText}");
            }
        }
        // 检查是否两个消息都已收到且匹配
        CheckAndNotifyAvatarMessages();
    }
    
    private SC_MissionStoryChatDownMsgForAvatarReplyTranslate _streamAvatarDialogTranslate;
    public SC_MissionStoryChatDownMsgForAvatarReplyTranslate AvatarDialogTranslate => _streamAvatarDialogTranslate;

    /// <summary>
    /// avatar 翻译--流返回
    /// </summary>
    /// <param name="ack"></param>
    public void SetStreamAvatarDialogTranslate(SC_MissionStoryChatDownMsgForAvatarReplyTranslate ack)
    {
        _streamAvatarDialogTranslate = ack;
        _lastDialogTranslate = ack;
        
        foreach (var itemData in _allMissionData)
        {
            if (itemData.Data.storyId == ack.commonData.storyId)
            {
                if (ack.commonData.round != 1 && itemData.Data.firstRoundData.isSettlement)
                    break;
                itemData.Data.taskId = ack.commonData.taskId;
                itemData.Data.firstRoundData.replyTranslateText = ack.replyTranslateText;
            }
        }
        
        // 检查是否两个消息都已收到且匹配
        CheckAndNotifyAvatarMessages();
        
        Notifier.instance.SendNotification(NotifyConsts.ExploreStroyStreamAvatarDialogTranslate);
    }

    /// <summary>
    /// 检查并发送avatar消息通知
    /// </summary>
    private void CheckAndNotifyAvatarMessages()
    {
        if (_lastDialogInfo != null && _lastDialogTranslate != null)
        {
            if (_lastDialogInfo.commonData.storyId == _lastDialogTranslate.commonData.storyId &&
                _lastDialogInfo.commonData.taskId == _lastDialogTranslate.commonData.taskId &&
                _lastDialogInfo.commonData.round == _lastDialogTranslate.commonData.round)
            {
                Notifier.instance.SendNotification(NotifyConsts.ExploreAvatarTxt);
                
                // 清除已处理的消息
                _lastDialogInfo = null;
                _lastDialogTranslate = null;
            }
        }
    }
    
    private Dictionary<ulong, ExploreMp3AudioInfo> _AudioDic = new Dictionary<ulong, ExploreMp3AudioInfo>();
    private Dictionary<string, ulong> _AudioBubbleDic = new Dictionary<string, ulong>();
    
    private SC_MissionStoryChatDownMsgForAvatarReplyTTS _dialogDownMsgForAvatarReplyTTS;
    public SC_MissionStoryChatDownMsgForAvatarReplyTTS DialogDownMsgForAvatarReplyTTS => _dialogDownMsgForAvatarReplyTTS;
    
    /// <summary>
    /// avatar 语音--流返回
    /// </summary>
    /// <param name="ack"></param>
    public void SetStreamAvatarAudio(SC_MissionStoryChatDownMsgForAvatarReplyTTS msg)
    {
        _dialogDownMsgForAvatarReplyTTS = msg;
        byte[] doByte = msg.audio.audio.ToByteArray();
        foreach (var itemData in _allMissionData)
        {
            if (itemData.Data.storyId == msg.commonData.storyId)
            {
                if (msg.commonData.round != 1 && itemData.Data.firstRoundData.isSettlement)
                    break;
                itemData.Data.taskId = msg.commonData.taskId;
                itemData.Data.firstRoundData.replyAudio = msg.audio;
            }
        }
        AddStreamAudio(true,doByte,msg.commonData.bubbleId,msg.audio.id,msg.audio.is_last_clip,msg.audio,true);
    }

    /// <summary>
    /// 通过 bubbleId 获取 音频audioId
    /// </summary>
    /// <param name="bubbleId"></param>
    /// <returns></returns>
    public ulong GetAudioIdByBubbleId(string bubbleId)
    {
        //Debug.Log($"GetAudioIdByBubbleId:::{bubbleId}");
        if (_AudioBubbleDic.TryGetValue(bubbleId, out ulong audioId))
        {
            return audioId;
        }
        else
        {
            return 0;
        }
    }

    public void AddStreamAudio(bool isAvatar,byte[] doByte,string bubbleId,ulong audioId,bool is_last_clip,PB_Explore_MissionStoryChatAudioDownFrame audioInfo,bool autoPlay)
    {
        // Debug.LogError($"bubbleId::AddStreamAudio:::{bubbleId}  audioId：：{audioId} is_last_clip：：{is_last_clip} ");
        _AudioBubbleDic[bubbleId] = audioId;
        
        if (_AudioDic.TryGetValue(audioId, out ExploreMp3AudioInfo audioClipData))
        {
            var data = doByte;
            if (data == null) return;
            if (audioClipData.dataFull)
            {
                AudioOver(isAvatar, audioInfo,autoPlay);
                return;
            }

            audioClipData.dataFull = is_last_clip;
            
            // 优化：避免频繁的Array.Resize操作，使用List动态增加
            if (audioClipData.bytesList == null)
            {
                audioClipData.bytesList = new List<byte[]>();
            }
            audioClipData.bytesList.Add(doByte);
            
            _AudioDic[audioId] = audioClipData;

            if (audioClipData.dataFull)
            {
                // 只有完整接收后才合并数据，减少内存操作
                MergeAndSaveAudioData(audioId);
                AudioOver(isAvatar, audioInfo, autoPlay);
            }
        }
        else
        {
            ExploreMp3AudioInfo info = new ExploreMp3AudioInfo();
            info.bubbleId = bubbleId;
            info.recordId = audioId;
            info.bytesList = new List<byte[]> { doByte };
            info.dataFull = is_last_clip;
            _AudioDic.Add(audioId, info);
            
            if (info.dataFull)
            {
                MergeAndSaveAudioData(audioId);
                AudioOver(isAvatar, audioInfo, autoPlay);
            }
        }
    }

    // 合并音频数据并保存到本地
    private void MergeAndSaveAudioData(ulong audioId)
    {
        if (_AudioDic.TryGetValue(audioId, out ExploreMp3AudioInfo audioData) && audioData.bytesList != null)
        {
            // 计算总大小
            int totalSize = audioData.bytesList.Sum(arr => arr.Length);
            
            // 创建新数组并合并
            audioData.audioBytes = new byte[totalSize];
            int offset = 0;
            
            foreach (byte[] chunk in audioData.bytesList)
            {
                Buffer.BlockCopy(chunk, 0, audioData.audioBytes, offset, chunk.Length);
                offset += chunk.Length;
            }
            
            // 清空列表节省内存
            audioData.bytesList.Clear();
            audioData.bytesList = null;
            
            // 保存到本地
            SaveToLocal(audioId);
        }
    }

    private void AudioOver(bool isAvatar,PB_Explore_MissionStoryChatAudioDownFrame audioInfo,bool autoPlay)
    {
        //第一句话播放 不走这里
        if (!autoPlay) return;
        if (isAvatar)
        {
            //AVATAR 自动播放语音
            ProcedureParams p = new ProcedureParams();
            p.type = EProcedureType.ExploreAudioPlay;
            p.param = audioInfo.id;

            bool showBar = UIManager.instance.GetUI<MultiTabFrameworkUI>(UIConsts.MultiTabHomepage).CurrTabState ==
                           BottomTabState.showing;
            if (showBar) return;
            
            Notifier.instance.SendNotification(NotifyConsts.procedure_explore_entity_avatar_audio_play,p);
           
            //麦克风显示不加入队列
            // ProcedureParams p1 = new ProcedureParams();
            // p1.type = EProcedureType.ExploreShowRecordUI;
            // p1.param = audioInfo.id;
            // Notifier.instance.SendNotification(NotifyConsts.procedure_explore_entity_recordui_show,p1);
            
            //自动显示翻译 这个也通过 设置面板实现了 这里先不用了
            // ExploreController controller = GetController<ExploreController>(ModelConsts.Explore);
            // if (!controller.IsSeniorPlayer)
            // {
            //     Notifier.instance.SendNotification(NotifyConsts.procedure_explore_entity_avatar_translate_show);
            // }
        }
        else
        {
            Notifier.instance.SendNotification(NotifyConsts.ExploreScaffoldTTs,audioInfo);
        }
    }

    
    /// <summary>
    /// 初始化音频缓存目录（创建目录并清空旧文件）
    /// </summary>
    private void InitAudioCache()
    {
        try
        {
            // 获取音频文件夹路径
            string audioFolderPath = GetAudioFolderPath();
            
            // 如果文件夹存在，删除所有文件
            if (Directory.Exists(audioFolderPath))
            {
                string[] files = Directory.GetFiles(audioFolderPath);
                foreach (string file in files)
                {
                    try
                    {
                        File.Delete(file);
                    }
                    catch (Exception ex)
                    {
                        VFDebug.LogError($"Explore memory删除音频文件失败: {file}, 错误: {ex.Message}");
                    }
                }
                VFDebug.Log($"Explore memory清空音频文件夹，删除了 {files.Length} 个文件");
            }
            else
            {
                // 创建音频文件夹
                Directory.CreateDirectory(audioFolderPath);
                VFDebug.Log($"Explore memory创建音频文件夹: {audioFolderPath}");
            }
            
            // 重置缓存大小
            _currentAudioCacheSize = 0;
        }
        catch (Exception ex)
        {
            VFDebug.LogError($"初始化音频缓存出错: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 获取音频文件夹路径
    /// </summary>
    private string GetAudioFolderPath()
    {
        return Path.Combine(Application.persistentDataPath, AUDIO_FOLDER_NAME);
    }

    private bool SaveToLocal(ulong audioId)
    {
        bool success = true;
        var data = GetAudioBytes(audioId);
        if (data == null || data.audioBytes == null || data.audioBytes.Length == 0)
        {
            return false;
        }
        try
        {
            // 确保音频文件夹存在
            string audioFolderPath = GetAudioFolderPath();
            if (!Directory.Exists(audioFolderPath))
            {
                Directory.CreateDirectory(audioFolderPath);
            }
            
            // 修改保存路径到ExploreAudio子文件夹
            string url = Path.Combine(audioFolderPath, audioId.ToString());
            
            // 写入文件前检查本地缓存文件数量
            CleanupLocalAudioFiles();
            
            File.WriteAllBytes(url, data.audioBytes);
            
            // 记录缓存大小增加
            _currentAudioCacheSize += data.audioBytes.Length;
        }
        catch (Exception ex)
        {
            VFDebug.LogError($"Explore memory ExploreModel 写文件失败: {ex.Message}");
            success = false;
        }
        return success;
    }
    
    // 修改清理本地音频文件方法，使用新的音频文件夹
    private void CleanupLocalAudioFiles()
    {
        if (_isCacheCleaning) return;
        
        _isCacheCleaning = true;
        
        try
        {
            // 检查缓存大小
            if (_currentAudioCacheSize > MAX_AUDIO_CACHE_SIZE)
            {
                VFDebug.Log($"Explore memory音频缓存大小({_currentAudioCacheSize/1024/1024}MB)超过限制，开始清理");
                
                // 获取所有音频文件
                string directory = GetAudioFolderPath();
                if (!Directory.Exists(directory))
                {
                    _isCacheCleaning = false;
                    return;
                }
                
                string[] files = Directory.GetFiles(directory);
                
                // 过滤出音频文件并按修改时间排序
                var audioFiles = files
                    .Where(f => ulong.TryParse(Path.GetFileName(f), out _))
                    .OrderBy(f => new FileInfo(f).LastAccessTime)
                    .ToList();
                
                // 保留最近使用的和当前需要的文件
                int filesToDelete = Math.Max(0, audioFiles.Count - MAX_LOCAL_AUDIO_FILES);
                
                if (filesToDelete > 0)
                {
                    long freedSpace = 0;
                    
                    for (int i = 0; i < filesToDelete; i++)
                    {
                        string fileToDelete = audioFiles[i];
                        ulong audioId;
                        
                        if (ulong.TryParse(Path.GetFileName(fileToDelete), out audioId))
                        {
                            // 检查是否在最近使用列表中
                            if (!_recentlyUsedAudio.Contains(audioId))
                            {
                                long fileSize = new FileInfo(fileToDelete).Length;
                                File.Delete(fileToDelete);
                                freedSpace += fileSize;
                                
                                // 从内存中也移除
                                if (_AudioDic.ContainsKey(audioId))
                                {
                                    if (_AudioDic[audioId].audio != null)
                                    {
                                        UnityEngine.Object.Destroy(_AudioDic[audioId].audio);
                                    }
                                    _AudioDic.Remove(audioId);
                                }
                                
                                // 移除使用记录
                                _recentlyUsedAudio.Remove(audioId);
                            }
                        }
                    }
                    
                    // 更新缓存大小
                    _currentAudioCacheSize -= freedSpace;
                    VFDebug.Log($"Explore memory已清理 {filesToDelete} 个音频文件，释放 {freedSpace/1024} KB 空间");
                }
            }
        }
        catch (Exception ex)
        {
            VFDebug.LogError($"Explore memory清理音频缓存出错: {ex.Message}");
        }
        finally
        {
            _isCacheCleaning = false;
        }
    }

    private async Task<AudioClip> GetAudioFromData(ulong audioId)
    {
        ExploreController controller = GetController<ExploreController>(ModelConsts.Explore);

        UnityWebRequest req;
        if (controller.UseMp3Audio)
        {
             req = UnityWebRequestMultimedia.GetAudioClip(UrlForLoadRecordId(audioId), AudioType.MPEG);
            await req.SendWebRequest();
            return DownloadHandlerAudioClip.GetContent(req);
        }
        else
        {
            req = UnityWebRequestMultimedia.GetAudioClip(UrlForLoadRecordId(audioId), AudioType.WAV);
            await req.SendWebRequest();
            return DownloadHandlerAudioClip.GetContent(req);
        }
    }
    
    public ExploreMp3AudioInfo GetAudioBytes(ulong audioId)
    {
        if (_AudioDic.TryGetValue(audioId, out ExploreMp3AudioInfo audioClipData))
        {
            return audioClipData;
        }
        else
        {
            return null;
        }
    }
    
    private string UrlForSaveRecordId(ulong audioId)
    {
        // 返回音频文件夹中的文件路径
        return Path.Combine(GetAudioFolderPath(), audioId.ToString());
    }

    private string UrlForLoadRecordId(ulong audioId)
    {
        // 返回音频文件夹中的文件路径
        string ret = Path.Combine(GetAudioFolderPath(), audioId.ToString());
#if UNITY_IOS || UNITY_STANDALONE_OSX || UNITY_ANDROID 
        ret = "file://" + ret;
#endif         
        return ret;
    }

    public bool UrlExistedForRecordId(ulong audioId)
    {
        string url = UrlForSaveRecordId(audioId);
        return File.Exists(url);
    }

    public async Task<AudioClip> GetAudioByRecordId(ulong audioId)
    {
        VFDebug.Log($"Explore memory GetAudioByRecordId:::audioId::{audioId}");
        
        // 更新最近使用记录
        UpdateRecentlyUsed(audioId);
        
        if (_AudioDic.TryGetValue(audioId, out ExploreMp3AudioInfo audioClipData))
        {
            if (audioClipData.audio)
            {
                return audioClipData.audio;
            }
            else
            {
                // 检查内存中的AudioClip数量
                CheckAndCleanupAudioClips();
                
                if (UrlExistedForRecordId(audioId))
                {
                    audioClipData.audio = await this.GetAudioFromData(audioClipData.recordId);
                    return audioClipData.audio;
                }
                else
                {
                    SaveToLocal(audioClipData.recordId);
                    audioClipData.audio = await this.GetAudioFromData(audioClipData.recordId);
                    return audioClipData.audio;
                }
            }
        }
        else
        {
            // 检查内存中的AudioClip数量
            CheckAndCleanupAudioClips();
            
            if (UrlExistedForRecordId(audioId))
            {
                AudioClip clip = await this.GetAudioFromData(audioId);
                
                // 创建新条目缓存此AudioClip
                ExploreMp3AudioInfo newInfo = new ExploreMp3AudioInfo
                {
                    recordId = audioId,
                    audio = clip,
                    dataFull = true
                };
                _AudioDic[audioId] = newInfo;
                
                return clip;
            }         
            else
            {
                return null;
            }
        }
    }
    
    // 更新最近使用的音频记录
    private void UpdateRecentlyUsed(ulong audioId)
    {
        // 移除旧记录
        _recentlyUsedAudio.Remove(audioId);
        
        // 添加到列表最后(最近使用)
        _recentlyUsedAudio.Add(audioId);
        
        // 保持列表大小限制
        if (_recentlyUsedAudio.Count > MAX_CACHED_AUDIOCLIPS * 2)
        {
            _recentlyUsedAudio.RemoveRange(0, _recentlyUsedAudio.Count - MAX_CACHED_AUDIOCLIPS);
        }
    }
    
    // 检查并清理内存中的AudioClip
    private void CheckAndCleanupAudioClips()
    {
        int clipCount = _AudioDic.Values.Count(info => info.audio != null);
        
        if (clipCount > MAX_CACHED_AUDIOCLIPS)
        {
            VFDebug.Log($"Explore memory 内存中AudioClip数量({clipCount})超过限制，开始清理");
            
            // 获取所有有AudioClip的ID
            List<ulong> allClipIds = new List<ulong>();
            foreach (var pair in _AudioDic)
            {
                if (pair.Value.audio != null)
                {
                    allClipIds.Add(pair.Key);
                }
            }
            
            // 按最近使用的顺序排序（保留最近使用的）
            var sortedClipIds = allClipIds
                .OrderBy(id => {
                    // 如果ID不在最近使用列表中，放在最前面（优先清理）
                    int index = _recentlyUsedAudio.IndexOf(id);
                    return index == -1 ? -1 : index;
                })
                .ToList();
            
            // 计算需要清理的数量 - 修改为批量清理算法
            // 当超出限制时，一次性多清理几个而不是每次只清理一个
            int numberToClean = Math.Max(clipCount - MAX_CACHED_AUDIOCLIPS, 
                                         Math.Min(3, clipCount / 4)); // 至少清理1个，最多清理1/4的缓存数量
            
            VFDebug.Log($"Explore memory 需要清理 {numberToClean} 个AudioClip, 当前共有 {sortedClipIds.Count} 个");
            
            // 清理不需要的AudioClip（保留最近使用的）
            for (int i = 0; i < numberToClean && i < sortedClipIds.Count; i++)
            {
                ulong audioId = sortedClipIds[i];
                if (_AudioDic.ContainsKey(audioId) && _AudioDic[audioId].audio != null)
                {
                    UnityEngine.Object.Destroy(_AudioDic[audioId].audio);
                    _AudioDic[audioId].audio = null;
                    _releasedClipsCount++;
                    VFDebug.Log($"Explore memory 释放AudioClip: {audioId}");
                }
            }
            
            // 只在积累一定数量的释放或经过一定时间后才执行卸载
            float currentTime = Time.realtimeSinceStartup;
            if (_releasedClipsCount >= 5 && (currentTime - _lastUnloadTime) > UNLOAD_INTERVAL)
            {
                // 强制GC回收
                Resources.UnloadUnusedAssets();
                _lastUnloadTime = currentTime;
                _releasedClipsCount = 0;
                VFDebug.Log($"Explore memory 执行资源卸载操作, 间隔: {currentTime - _lastUnloadTime}秒");
            }
            else
            {
                VFDebug.Log($"Explore memory 已释放 {_releasedClipsCount} 个AudioClip，等待累积到5个后执行卸载");
            }
        }
    }
    
    // 清理全部音频缓存
    public void ClearAllAudioCache()
    {
        // 清理内存中的AudioClip
        foreach (var audioInfo in _AudioDic.Values)
        {
            if (audioInfo.audio != null)
            {
                UnityEngine.Object.Destroy(audioInfo.audio);
            }
        }
        
        _AudioDic.Clear();
        // _AudioBubbleDic.Clear();
        _recentlyUsedAudio.Clear();
        
        // 强制GC回收
        Resources.UnloadUnusedAssets();
        GC.Collect();
        
        VFDebug.Log("Explore memory已清理所有音频缓存");
    }
    
    #endregion


}

public class ExploreMp3AudioInfo
{
    public string bubbleId = string.Empty;
    public ulong recordId = 0;
    public byte[] audioBytes;  // 最终合并后的数据
    public List<byte[]> bytesList; // 临时存储接收的数据块
    public AudioClip audio = null;
    /// <summary>
    /// 音频数据是否结束
    /// </summary>
    public bool dataFull = false;
}
