// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/explore/base.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.explore {

  /// <summary>Holder for reflection information generated from protobuf/explore/base.proto</summary>
  public static partial class BaseReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/explore/base.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static BaseReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Chtwcm90b2J1Zi9leHBsb3JlL2Jhc2UucHJvdG8iQAoQUEJfQWxpZ25tZW50",
            "SW5mbxINCgV3b3JkcxgBIAMoCRIdChV3b3JkU3RhcnRUaW1lc1NlY29uZHMY",
            "AiADKAIikQEKGFBCX0Vtb3Rpb25BbmFseXNpc1Jlc3VsdBI4Cg1zZW50ZW5j",
            "ZV9saXN0GAEgAygLMiEuUEJfRW1vdGlvbkFuYWx5c2lzU2VudGVuY2VSZXN1",
            "bHQSEQoJbGxtUmVzdWx0GAIgASgJEigKDWFsaWdubWVudEluZm8YAyABKAsy",
            "ES5QQl9BbGlnbm1lbnRJbmZvIosBCiBQQl9FbW90aW9uQW5hbHlzaXNTZW50",
            "ZW5jZVJlc3VsdBIMCgR0ZXh0GAEgASgJEhMKC2Z1bmN0aW9uVGFnGAIgASgJ",
            "EhQKDGludGVuc2l0eVRhZxgDIAEoCRIXCg9zdGFydFRpbWVTZWNvbmQYBCAB",
            "KAISFQoNZW5kVGltZVNlY29uZBgFIAEoAireEwoTUEJfRXhwbG9yZV9Nc2dJ",
            "ZE1hcBIVChFFT19Nc2dfSWRfVW5rbm93bhAAEiUKH1NDX1JlY29tbWVuZFN3",
            "aXRjaEVudGl0eV9NYXBfSUQQm/IBEiwKJlNDX0V4cGxvcmVEb3duTXNnRm9y",
            "U2VydmVyQmFzaWNfTWFwX0lEEP/yARIqCiRTQ19FeHBsb3JlRG93bk1zZ0Zv",
            "ckhlYXJ0YmVhdF9NYXBfSUQQgPMBEisKJVNDX0RpYWxvZ0Rvd25Nc2dGb3JB",
            "dmF0YXJSZXBseV9NYXBfSUQQ4/MBEjQKLlNDX0RpYWxvZ0Rvd25Nc2dGb3JB",
            "dmF0YXJSZXBseVRyYW5zbGF0ZV9NYXBfSUQQ5PMBEi4KKFNDX0RpYWxvZ0Rv",
            "d25Nc2dGb3JBdmF0YXJSZXBseVRUU19NYXBfSUQQ5fMBEjAKKlNDX0RpYWxv",
            "Z0Rvd25Nc2dGb3JVc2VyUmVwbHlFeGFtcGxlX01hcF9JRBDm8wESOQozU0Nf",
            "RGlhbG9nRG93bk1zZ0ZvclVzZXJSZXBseUV4YW1wbGVUcmFuc2xhdGVfTWFw",
            "X0lEEOfzARIzCi1TQ19EaWFsb2dEb3duTXNnRm9yVXNlclJlcGx5RXhhbXBs",
            "ZVRUU19NYXBfSUQQ6PMBEiMKHVNDX0RpYWxvZ0Rvd25Nc2dGb3JBU1JfTWFw",
            "X0lEEOnzARIoCiJTQ19EaWFsb2dEb3duTXNnRm9yQml6RXZlbnRfTWFwX0lE",
            "EOrzARIoCiJTQ19EaWFsb2dEb3duTXNnRm9yRmVlZGJhY2tfTWFwX0lEEOvz",
            "ARI0Ci5TQ19EaWFsb2dEb3duTXNnRm9yVGFza0dvYWxTdGF0dXNDaGFuZ2Vf",
            "TWFwX0lEEOzzARImCiBTQ19EaWFsb2dEb3duTXNnRm9yQWR2aWNlX01hcF9J",
            "RBDt8wESNQovU0NfVXNlclNldHRpbmdEb3duTXNnRm9yU2F2ZVVzZXJTZXR0",
            "aW5nc19NYXBfSUQQxfQBEjEKK1NDX1VzZXJDaGF0RG93bk1zZ0ZvclVzZXJS",
            "ZWNvZ25pemluZ19NYXBfSUQQjfYBEjAKKlNDX1VzZXJDaGF0RG93bk1zZ0Zv",
            "clVzZXJSZWNvZ25pemVkX01hcF9JRBCO9gESNgowU0NfVXNlckNoYXREb3du",
            "TXNnRm9yT3RoZXJVc2VyUmVjb2duaXppbmdfTWFwX0lEEI/2ARI1Ci9TQ19V",
            "c2VyQ2hhdERvd25Nc2dGb3JPdGhlclVzZXJSZWNvZ25pemVkX01hcF9JRBCQ",
            "9gESOQozU0NfVXNlckNoYXREb3duTXNnRm9yT3RoZXJVc2VyUmVwbHlUcmFu",
            "c2xhdGVfTWFwX0lEEJH2ARIyCixTQ19Vc2VyQ2hhdERvd25Nc2dGb3JVc2Vy",
            "UmVwbHlFeGFtcGxlX01hcF9JRBCS9gESOwo1U0NfVXNlckNoYXREb3duTXNn",
            "Rm9yVXNlclJlcGx5RXhhbXBsZVRyYW5zbGF0ZV9NYXBfSUQQk/YBEioKJFND",
            "X1VzZXJDaGF0RG93bk1zZ0ZvckJpekV2ZW50X01hcF9JRBCU9gESLQonU0Nf",
            "VXNlckNoYXREb3duTXNnVXNlckNoYXRTdGF0dXNfTWFwX0lEEL72ARImCiBT",
            "Q19Vc2VyQ2hhdERvd25Nc2dGb3JFeGl0X01hcF9JRBDv9gESKAoiU0NfTWF0",
            "Y2hpbmdEb3duX01hdGNoU3RhdHVzX01hcF9JRBDz9gESKgokU0NfTWF0Y2hp",
            "bmdEb3duX01hdGNoZWRSZXN1bHRfTWFwX0lEEPT2ARIoCiJTQ19NYXRjaGlu",
            "Z0Rvd25fTWF0Y2hGYWlsZWRfTWFwX0lEEPX2ARItCidTQ19OYXRpdmVTcGVh",
            "a2VyRG93bl9NYXRjaFN0YXR1c19NYXBfSUQQ9vYBEi8KKVNDX05hdGl2ZVNw",
            "ZWFrZXJEb3duX01hdGNoZWRSZXN1bHRfTWFwX0lEEPf2ARItCidTQ19OYXRp",
            "dmVTcGVha2VyRG93bl9NYXRjaEZhaWxlZF9NYXBfSUQQ+PYBEjMKLVNDX09u",
            "Ym9hcmRpbmdDaGF0RG93bk1zZ0ZvckF2YXRhclJlcGx5X01hcF9JRBDV9wES",
            "PAo2U0NfT25ib2FyZGluZ0NoYXREb3duTXNnRm9yQXZhdGFyUmVwbHlUcmFu",
            "c2xhdGVfTWFwX0lEENb3ARI2CjBTQ19PbmJvYXJkaW5nQ2hhdERvd25Nc2dG",
            "b3JBdmF0YXJSZXBseVRUU19NYXBfSUQQ1/cBEisKJVNDX09uYm9hcmRpbmdD",
            "aGF0RG93bk1zZ0ZvckFTUl9NYXBfSUQQ2PcBEjAKKlNDX09uYm9hcmRpbmdD",
            "aGF0RG93bk1zZ0ZvckJpekV2ZW50X01hcF9JRBDZ9wESMgosU0NfT25ib2Fy",
            "ZGluZ0NoYXREb3duTXNnRm9yU2V0dGxlbWVudF9NYXBfSUQQ2vcBEiIKHFND",
            "X1NraXBPbmJvYXJkaW5nQ2hhdF9NYXBfSUQQufgBEjUKL1NDX01pc3Npb25T",
            "dG9yeUNoYXREb3duTXNnRm9yQXZhdGFyUmVwbHlfTWFwX0lEEJ35ARI+CjhT",
            "Q19NaXNzaW9uU3RvcnlDaGF0RG93bk1zZ0ZvckF2YXRhclJlcGx5VHJhbnNs",
            "YXRlX01hcF9JRBCe+QESOAoyU0NfTWlzc2lvblN0b3J5Q2hhdERvd25Nc2dG",
            "b3JBdmF0YXJSZXBseVRUU19NYXBfSUQQn/kBEjoKNFNDX01pc3Npb25TdG9y",
            "eUNoYXREb3duTXNnRm9yVXNlclJlcGx5RXhhbXBsZV9NYXBfSUQQoPkBEkMK",
            "PVNDX01pc3Npb25TdG9yeUNoYXREb3duTXNnRm9yVXNlclJlcGx5RXhhbXBs",
            "ZVRyYW5zbGF0ZV9NYXBfSUQQofkBEj0KN1NDX01pc3Npb25TdG9yeUNoYXRE",
            "b3duTXNnRm9yVXNlclJlcGx5RXhhbXBsZVRUU19NYXBfSUQQovkBEjAKKlND",
            "X01pc3Npb25TdG9yeUNoYXREb3duTXNnRm9yQWR2aWNlX01hcF9JRBCj+QES",
            "LQonU0NfTWlzc2lvblN0b3J5Q2hhdERvd25Nc2dGb3JBU1JfTWFwX0lEEKT5",
            "ARIyCixTQ19NaXNzaW9uU3RvcnlDaGF0RG93bk1zZ0ZvckJpekV2ZW50X01h",
            "cF9JRBCl+QESPAo2U0NfTWlzc2lvblN0b3J5Q2hhdERvd25Nc2dGb3JTdGVw",
            "UHJvZ3Jlc3NDaGFuZ2VfTWFwX0lEEKb5ARI0Ci5TQ19NaXNzaW9uU3RvcnlD",
            "aGF0RG93bk1zZ0ZvclNldHRsZW1lbnRfTWFwX0lEEKf5ASpjChhQQl9FeHBs",
            "b3JlX1NvdXJjZUNoYW5uZWwSEQoNRU9fU0NfVU5LTk9XThAAEg4KCkVPX0VY",
            "UExPUkUQARIRCg1FT19MRUFSTl9QQVRIEAISEQoNRU9fT05CT0FSRElORxAD",
            "KqoBChJQQl9FeHBsb3JlX0JpekNvZGUSFwoTRU9fQklaX0NPREVfVU5LTk9X",
            "ThAAEhgKE0VPX0JJWl9DT0RFX1NVQ0NFU1MQyAESHgoaRU9fQklaX0NPREVf",
            "SU5URVJOQUxfRVJST1IQARIdChlFT19CSVpfQ09ERV9JTlZBTElEX1BBUkFN",
            "EAISIgoeRU9fQklaX0NPREVfU1RUX1JFQ09HTklaRV9GQUlMEAMqjwEKFFBC",
            "X0V4cGxvcmVfQ2VmckxldmVsEhEKDUVPX0NMX1VOS05PV04QABIMCghFT19D",
            "TF9BMRABEgwKCEVPX0NMX0EyEAISDAoIRU9fQ0xfQjEQAxIMCghFT19DTF9C",
            "MhAEEgwKCEVPX0NMX0MxEAUSDAoIRU9fQ0xfQzIQBhIQCgxFT19DTF9QUkVf",
            "QTEQByp3CiRQQl9FeHBsb3JlX1VzZXJTZXR0aW5nX1NwZWFraW5nU3BlZWQS",
            "FAoQRU9fVVNfU1NfVU5LTk9XThAAEhEKDUVPX1VTX1NTX1NMT1cQARITCg9F",
            "T19VU19TU19NRURJVU0QAhIRCg1FT19VU19TU19GQVNUEAMqaQorUEJfRXhw",
            "bG9yZV9Vc2VyU2V0dGluZ19BdXRvVHJhbnNsYXRlRGlzcGxheRIVChFFT19V",
            "U19BVERfVU5LTk9XThAAEhAKDEVPX1VTX0FURF9PThABEhEKDUVPX1VTX0FU",
            "RF9PRkYQAiphCiZQQl9FeHBsb3JlX1VzZXJTZXR0aW5nX0JhY2tncm91bmRN",
            "dXNpYxIUChBFT19VU19CTV9VTktOT1dOEAASDwoLRU9fVVNfQk1fT04QARIQ",
            "CgxFT19VU19CTV9PRkYQAkIqWhp2Zl9wcm90b2J1Zi9zZXJ2ZXIvZXhwbG9y",
            "ZaoCC01zZy5leHBsb3JlYgZwcm90bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Msg.explore.PB_Explore_MsgIdMap), typeof(global::Msg.explore.PB_Explore_SourceChannel), typeof(global::Msg.explore.PB_Explore_BizCode), typeof(global::Msg.explore.PB_Explore_CefrLevel), typeof(global::Msg.explore.PB_Explore_UserSetting_SpeakingSpeed), typeof(global::Msg.explore.PB_Explore_UserSetting_AutoTranslateDisplay), typeof(global::Msg.explore.PB_Explore_UserSetting_BackgroundMusic), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_AlignmentInfo), global::Msg.explore.PB_AlignmentInfo.Parser, new[]{ "words", "wordStartTimesSeconds" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_EmotionAnalysisResult), global::Msg.explore.PB_EmotionAnalysisResult.Parser, new[]{ "sentence_list", "llmResult", "alignmentInfo" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_EmotionAnalysisSentenceResult), global::Msg.explore.PB_EmotionAnalysisSentenceResult.Parser, new[]{ "text", "functionTag", "intensityTag", "startTimeSecond", "endTimeSecond" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  /// <summary>
  ///
  /// 消息id的映射表
  /// </summary>
  public enum PB_Explore_MsgIdMap {
    [pbr::OriginalName("EO_Msg_Id_Unknown")] EO_Msg_Id_Unknown = 0,
    /// <summary>
    /// 31001~31100 推荐
    /// </summary>
    [pbr::OriginalName("SC_RecommendSwitchEntity_Map_ID")] SC_RecommendSwitchEntity_Map_ID = 31003,
    /// <summary>
    /// 31101~31200 长连接
    /// </summary>
    [pbr::OriginalName("SC_ExploreDownMsgForServerBasic_Map_ID")] SC_ExploreDownMsgForServerBasic_Map_ID = 31103,
    /// <summary>
    /// Explore服务心跳下行消息
    /// </summary>
    [pbr::OriginalName("SC_ExploreDownMsgForHeartbeat_Map_ID")] SC_ExploreDownMsgForHeartbeat_Map_ID = 31104,
    /// <summary>
    /// 31201~31300 对话
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForAvatarReply_Map_ID")] SC_DialogDownMsgForAvatarReply_Map_ID = 31203,
    /// <summary>
    /// 对话下行消息 - Avatar回复翻译
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForAvatarReplyTranslate_Map_ID")] SC_DialogDownMsgForAvatarReplyTranslate_Map_ID = 31204,
    /// <summary>
    /// 对话下行消息 - Avatar回复TTS
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForAvatarReplyTTS_Map_ID")] SC_DialogDownMsgForAvatarReplyTTS_Map_ID = 31205,
    /// <summary>
    /// 对话下行消息 - 用户回复示例
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForUserReplyExample_Map_ID")] SC_DialogDownMsgForUserReplyExample_Map_ID = 31206,
    /// <summary>
    /// 对话下行消息 - 用户回复示例翻译
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForUserReplyExampleTranslate_Map_ID")] SC_DialogDownMsgForUserReplyExampleTranslate_Map_ID = 31207,
    /// <summary>
    /// 对话下行消息 - 用户回复示例TTS
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForUserReplyExampleTTS_Map_ID")] SC_DialogDownMsgForUserReplyExampleTTS_Map_ID = 31208,
    /// <summary>
    /// 对话下行消息 - 用户语音识别最终结果
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForASR_Map_ID")] SC_DialogDownMsgForASR_Map_ID = 31209,
    /// <summary>
    /// 对话下行消息 - 业务事件
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForBizEvent_Map_ID")] SC_DialogDownMsgForBizEvent_Map_ID = 31210,
    /// <summary>
    /// 对话下行消息 - 反馈结果
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForFeedback_Map_ID")] SC_DialogDownMsgForFeedback_Map_ID = 31211,
    /// <summary>
    /// 对话下行消息 - 任务目标状态变化
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForTaskGoalStatusChange_Map_ID")] SC_DialogDownMsgForTaskGoalStatusChange_Map_ID = 31212,
    /// <summary>
    /// 对话下行消息 - Advice
    /// </summary>
    [pbr::OriginalName("SC_DialogDownMsgForAdvice_Map_ID")] SC_DialogDownMsgForAdvice_Map_ID = 31213,
    /// <summary>
    /// 31301~31400 用户设置
    /// </summary>
    [pbr::OriginalName("SC_UserSettingDownMsgForSaveUserSettings_Map_ID")] SC_UserSettingDownMsgForSaveUserSettings_Map_ID = 31301,
    /// <summary>
    /// 31501~31600 用户聊天
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForUserRecognizing_Map_ID")] SC_UserChatDownMsgForUserRecognizing_Map_ID = 31501,
    /// <summary>
    /// 用户聊天下行消息 - 用户语音识别最终结果
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForUserRecognized_Map_ID")] SC_UserChatDownMsgForUserRecognized_Map_ID = 31502,
    /// <summary>
    /// 用户聊天下行消息 - 他人语音识别过程结果
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForOtherUserRecognizing_Map_ID")] SC_UserChatDownMsgForOtherUserRecognizing_Map_ID = 31503,
    /// <summary>
    /// 用户聊天下行消息 - 他人语音识别最终结果
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForOtherUserRecognized_Map_ID")] SC_UserChatDownMsgForOtherUserRecognized_Map_ID = 31504,
    /// <summary>
    /// 用户聊天下行消息 - 他人回复翻译
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForOtherUserReplyTranslate_Map_ID")] SC_UserChatDownMsgForOtherUserReplyTranslate_Map_ID = 31505,
    /// <summary>
    /// 用户聊天下行消息 - 用户回复示例
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForUserReplyExample_Map_ID")] SC_UserChatDownMsgForUserReplyExample_Map_ID = 31506,
    /// <summary>
    /// 用户聊天下行消息 - 用户回复示例翻译
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForUserReplyExampleTranslate_Map_ID")] SC_UserChatDownMsgForUserReplyExampleTranslate_Map_ID = 31507,
    /// <summary>
    /// 用户聊天下行消息 - 业务事件
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForBizEvent_Map_ID")] SC_UserChatDownMsgForBizEvent_Map_ID = 31508,
    /// <summary>
    /// 用户聊天下行消息 - 用户聊天状态
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgUserChatStatus_Map_ID")] SC_UserChatDownMsgUserChatStatus_Map_ID = 31550,
    /// <summary>
    /// 用户聊天下行消息 - 退出聊天
    /// </summary>
    [pbr::OriginalName("SC_UserChatDownMsgForExit_Map_ID")] SC_UserChatDownMsgForExit_Map_ID = 31599,
    /// <summary>
    /// 31601~31700 撮合
    /// </summary>
    [pbr::OriginalName("SC_MatchingDown_MatchStatus_Map_ID")] SC_MatchingDown_MatchStatus_Map_ID = 31603,
    /// <summary>
    /// 撮合下行消息 - 撮合成功结果
    /// </summary>
    [pbr::OriginalName("SC_MatchingDown_MatchedResult_Map_ID")] SC_MatchingDown_MatchedResult_Map_ID = 31604,
    /// <summary>
    /// 撮合下行消息 - 撮合失败结果
    /// </summary>
    [pbr::OriginalName("SC_MatchingDown_MatchFailed_Map_ID")] SC_MatchingDown_MatchFailed_Map_ID = 31605,
    /// <summary>
    /// 寻找NativeSpeaker下行消息 - 撮合状态
    /// </summary>
    [pbr::OriginalName("SC_NativeSpeakerDown_MatchStatus_Map_ID")] SC_NativeSpeakerDown_MatchStatus_Map_ID = 31606,
    /// <summary>
    /// 寻找NativeSpeaker下行消息 - 撮合成功结果
    /// </summary>
    [pbr::OriginalName("SC_NativeSpeakerDown_MatchedResult_Map_ID")] SC_NativeSpeakerDown_MatchedResult_Map_ID = 31607,
    /// <summary>
    /// 寻找NativeSpeaker下行消息 - 撮合失败结果
    /// </summary>
    [pbr::OriginalName("SC_NativeSpeakerDown_MatchFailed_Map_ID")] SC_NativeSpeakerDown_MatchFailed_Map_ID = 31608,
    /// <summary>
    /// 31701~31800 onboarding对话
    /// </summary>
    [pbr::OriginalName("SC_OnboardingChatDownMsgForAvatarReply_Map_ID")] SC_OnboardingChatDownMsgForAvatarReply_Map_ID = 31701,
    /// <summary>
    /// onboarding对话下行消息 - Avatar回复翻译
    /// </summary>
    [pbr::OriginalName("SC_OnboardingChatDownMsgForAvatarReplyTranslate_Map_ID")] SC_OnboardingChatDownMsgForAvatarReplyTranslate_Map_ID = 31702,
    /// <summary>
    /// onboarding对话下行消息 - Avatar回复TTS
    /// </summary>
    [pbr::OriginalName("SC_OnboardingChatDownMsgForAvatarReplyTTS_Map_ID")] SC_OnboardingChatDownMsgForAvatarReplyTTS_Map_ID = 31703,
    /// <summary>
    /// onboarding对话下行消息 - 用户语音识别最终结果
    /// </summary>
    [pbr::OriginalName("SC_OnboardingChatDownMsgForASR_Map_ID")] SC_OnboardingChatDownMsgForASR_Map_ID = 31704,
    /// <summary>
    /// onboarding对话下行消息 - 业务事件
    /// </summary>
    [pbr::OriginalName("SC_OnboardingChatDownMsgForBizEvent_Map_ID")] SC_OnboardingChatDownMsgForBizEvent_Map_ID = 31705,
    /// <summary>
    /// onboarding对话下行消息 - 对话结算
    /// </summary>
    [pbr::OriginalName("SC_OnboardingChatDownMsgForSettlement_Map_ID")] SC_OnboardingChatDownMsgForSettlement_Map_ID = 31706,
    /// <summary>
    /// 31801~31900 onboarding
    /// </summary>
    [pbr::OriginalName("SC_SkipOnboardingChat_Map_ID")] SC_SkipOnboardingChat_Map_ID = 31801,
    /// <summary>
    /// 31901~32000 探索-mission剧情对话
    /// </summary>
    [pbr::OriginalName("SC_MissionStoryChatDownMsgForAvatarReply_Map_ID")] SC_MissionStoryChatDownMsgForAvatarReply_Map_ID = 31901,
    /// <summary>
    /// Mission剧情对话下行 - Avatar回复翻译
    /// </summary>
    [pbr::OriginalName("SC_MissionStoryChatDownMsgForAvatarReplyTranslate_Map_ID")] SC_MissionStoryChatDownMsgForAvatarReplyTranslate_Map_ID = 31902,
    /// <summary>
    /// Mission剧情对话下行 - Avatar回复TTS
    /// </summary>
    [pbr::OriginalName("SC_MissionStoryChatDownMsgForAvatarReplyTTS_Map_ID")] SC_MissionStoryChatDownMsgForAvatarReplyTTS_Map_ID = 31903,
    /// <summary>
    /// Mission剧情对话下行 - 用户回复示例
    /// </summary>
    [pbr::OriginalName("SC_MissionStoryChatDownMsgForUserReplyExample_Map_ID")] SC_MissionStoryChatDownMsgForUserReplyExample_Map_ID = 31904,
    /// <summary>
    /// Mission剧情对话下行 - 用户回复示例翻译
    /// </summary>
    [pbr::OriginalName("SC_MissionStoryChatDownMsgForUserReplyExampleTranslate_Map_ID")] SC_MissionStoryChatDownMsgForUserReplyExampleTranslate_Map_ID = 31905,
    /// <summary>
    /// Mission剧情对话下行 - 用户回复示例TTS
    /// </summary>
    [pbr::OriginalName("SC_MissionStoryChatDownMsgForUserReplyExampleTTS_Map_ID")] SC_MissionStoryChatDownMsgForUserReplyExampleTTS_Map_ID = 31906,
    /// <summary>
    /// Mission剧情对话下行 - Advice
    /// </summary>
    [pbr::OriginalName("SC_MissionStoryChatDownMsgForAdvice_Map_ID")] SC_MissionStoryChatDownMsgForAdvice_Map_ID = 31907,
    /// <summary>
    /// Mission剧情对话下行 - 用户语音识别最终结果
    /// </summary>
    [pbr::OriginalName("SC_MissionStoryChatDownMsgForASR_Map_ID")] SC_MissionStoryChatDownMsgForASR_Map_ID = 31908,
    /// <summary>
    /// Mission剧情对话下行 - 业务事件
    /// </summary>
    [pbr::OriginalName("SC_MissionStoryChatDownMsgForBizEvent_Map_ID")] SC_MissionStoryChatDownMsgForBizEvent_Map_ID = 31909,
    /// <summary>
    /// Mission剧情对话下行 - 任务步骤进度变更
    /// </summary>
    [pbr::OriginalName("SC_MissionStoryChatDownMsgForStepProgressChange_Map_ID")] SC_MissionStoryChatDownMsgForStepProgressChange_Map_ID = 31910,
    /// <summary>
    /// Mission剧情对话下行 - 对话结算
    /// </summary>
    [pbr::OriginalName("SC_MissionStoryChatDownMsgForSettlement_Map_ID")] SC_MissionStoryChatDownMsgForSettlement_Map_ID = 31911,
  }

  /// <summary>
  ///
  /// 来源渠道
  /// </summary>
  public enum PB_Explore_SourceChannel {
    /// <summary>
    /// 未知
    /// </summary>
    [pbr::OriginalName("EO_SC_UNKNOWN")] EO_SC_UNKNOWN = 0,
    /// <summary>
    /// 探索Tab
    /// </summary>
    [pbr::OriginalName("EO_EXPLORE")] EO_EXPLORE = 1,
    /// <summary>
    /// 学习路径
    /// </summary>
    [pbr::OriginalName("EO_LEARN_PATH")] EO_LEARN_PATH = 2,
    /// <summary>
    /// onboarding
    /// </summary>
    [pbr::OriginalName("EO_ONBOARDING")] EO_ONBOARDING = 3,
  }

  /// <summary>
  /// 业务状态码枚举
  /// </summary>
  public enum PB_Explore_BizCode {
    [pbr::OriginalName("EO_BIZ_CODE_UNKNOWN")] EO_BIZ_CODE_UNKNOWN = 0,
    /// <summary>
    /// 成功
    /// </summary>
    [pbr::OriginalName("EO_BIZ_CODE_SUCCESS")] EO_BIZ_CODE_SUCCESS = 200,
    /// <summary>
    /// 内部错误
    /// </summary>
    [pbr::OriginalName("EO_BIZ_CODE_INTERNAL_ERROR")] EO_BIZ_CODE_INTERNAL_ERROR = 1,
    /// <summary>
    /// 参数错误
    /// </summary>
    [pbr::OriginalName("EO_BIZ_CODE_INVALID_PARAM")] EO_BIZ_CODE_INVALID_PARAM = 2,
    /// <summary>
    /// 语音识别失败
    /// </summary>
    [pbr::OriginalName("EO_BIZ_CODE_STT_RECOGNIZE_FAIL")] EO_BIZ_CODE_STT_RECOGNIZE_FAIL = 3,
  }

  /// <summary>
  ///
  /// CEFR难度等级
  /// </summary>
  public enum PB_Explore_CefrLevel {
    [pbr::OriginalName("EO_CL_UNKNOWN")] EO_CL_UNKNOWN = 0,
    [pbr::OriginalName("EO_CL_A1")] EO_CL_A1 = 1,
    [pbr::OriginalName("EO_CL_A2")] EO_CL_A2 = 2,
    [pbr::OriginalName("EO_CL_B1")] EO_CL_B1 = 3,
    [pbr::OriginalName("EO_CL_B2")] EO_CL_B2 = 4,
    [pbr::OriginalName("EO_CL_C1")] EO_CL_C1 = 5,
    [pbr::OriginalName("EO_CL_C2")] EO_CL_C2 = 6,
    [pbr::OriginalName("EO_CL_PRE_A1")] EO_CL_PRE_A1 = 7,
  }

  /// <summary>
  ///
  /// 语速
  /// </summary>
  public enum PB_Explore_UserSetting_SpeakingSpeed {
    /// <summary>
    /// 未知
    /// </summary>
    [pbr::OriginalName("EO_US_SS_UNKNOWN")] EO_US_SS_UNKNOWN = 0,
    /// <summary>
    /// 慢
    /// </summary>
    [pbr::OriginalName("EO_US_SS_SLOW")] EO_US_SS_SLOW = 1,
    /// <summary>
    /// 中等
    /// </summary>
    [pbr::OriginalName("EO_US_SS_MEDIUM")] EO_US_SS_MEDIUM = 2,
    /// <summary>
    /// 快
    /// </summary>
    [pbr::OriginalName("EO_US_SS_FAST")] EO_US_SS_FAST = 3,
  }

  /// <summary>
  ///
  /// 自动显示翻译
  /// </summary>
  public enum PB_Explore_UserSetting_AutoTranslateDisplay {
    /// <summary>
    /// 未知
    /// </summary>
    [pbr::OriginalName("EO_US_ATD_UNKNOWN")] EO_US_ATD_UNKNOWN = 0,
    /// <summary>
    /// 开
    /// </summary>
    [pbr::OriginalName("EO_US_ATD_ON")] EO_US_ATD_ON = 1,
    /// <summary>
    /// 关
    /// </summary>
    [pbr::OriginalName("EO_US_ATD_OFF")] EO_US_ATD_OFF = 2,
  }

  /// <summary>
  ///
  /// 背景音乐
  /// </summary>
  public enum PB_Explore_UserSetting_BackgroundMusic {
    /// <summary>
    /// 未知
    /// </summary>
    [pbr::OriginalName("EO_US_BM_UNKNOWN")] EO_US_BM_UNKNOWN = 0,
    /// <summary>
    /// 开
    /// </summary>
    [pbr::OriginalName("EO_US_BM_ON")] EO_US_BM_ON = 1,
    /// <summary>
    /// 关
    /// </summary>
    [pbr::OriginalName("EO_US_BM_OFF")] EO_US_BM_OFF = 2,
  }

  #endregion

  #region Messages
  /// <summary>
  ///*
  /// 音频对齐信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_AlignmentInfo : pb::IMessage<PB_AlignmentInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_AlignmentInfo> _parser = new pb::MessageParser<PB_AlignmentInfo>(() => new PB_AlignmentInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_AlignmentInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.BaseReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_AlignmentInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_AlignmentInfo(PB_AlignmentInfo other) : this() {
      words_ = other.words_.Clone();
      wordStartTimesSeconds_ = other.wordStartTimesSeconds_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_AlignmentInfo Clone() {
      return new PB_AlignmentInfo(this);
    }

    /// <summary>Field number for the "words" field.</summary>
    public const int wordsFieldNumber = 1;
    private static readonly pb::FieldCodec<string> _repeated_words_codec
        = pb::FieldCodec.ForString(10);
    private readonly pbc::RepeatedField<string> words_ = new pbc::RepeatedField<string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> words {
      get { return words_; }
    }

    /// <summary>Field number for the "wordStartTimesSeconds" field.</summary>
    public const int wordStartTimesSecondsFieldNumber = 2;
    private static readonly pb::FieldCodec<float> _repeated_wordStartTimesSeconds_codec
        = pb::FieldCodec.ForFloat(18);
    private readonly pbc::RepeatedField<float> wordStartTimesSeconds_ = new pbc::RepeatedField<float>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<float> wordStartTimesSeconds {
      get { return wordStartTimesSeconds_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_AlignmentInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_AlignmentInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!words_.Equals(other.words_)) return false;
      if(!wordStartTimesSeconds_.Equals(other.wordStartTimesSeconds_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= words_.GetHashCode();
      hash ^= wordStartTimesSeconds_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      words_.WriteTo(output, _repeated_words_codec);
      wordStartTimesSeconds_.WriteTo(output, _repeated_wordStartTimesSeconds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      words_.WriteTo(ref output, _repeated_words_codec);
      wordStartTimesSeconds_.WriteTo(ref output, _repeated_wordStartTimesSeconds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += words_.CalculateSize(_repeated_words_codec);
      size += wordStartTimesSeconds_.CalculateSize(_repeated_wordStartTimesSeconds_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_AlignmentInfo other) {
      if (other == null) {
        return;
      }
      words_.Add(other.words_);
      wordStartTimesSeconds_.Add(other.wordStartTimesSeconds_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            words_.AddEntriesFrom(input, _repeated_words_codec);
            break;
          }
          case 18:
          case 21: {
            wordStartTimesSeconds_.AddEntriesFrom(input, _repeated_wordStartTimesSeconds_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            words_.AddEntriesFrom(ref input, _repeated_words_codec);
            break;
          }
          case 18:
          case 21: {
            wordStartTimesSeconds_.AddEntriesFrom(ref input, _repeated_wordStartTimesSeconds_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///*
  /// 情感分析结果
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_EmotionAnalysisResult : pb::IMessage<PB_EmotionAnalysisResult>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_EmotionAnalysisResult> _parser = new pb::MessageParser<PB_EmotionAnalysisResult>(() => new PB_EmotionAnalysisResult());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_EmotionAnalysisResult> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.BaseReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_EmotionAnalysisResult() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_EmotionAnalysisResult(PB_EmotionAnalysisResult other) : this() {
      sentence_list_ = other.sentence_list_.Clone();
      llmResult_ = other.llmResult_;
      alignmentInfo_ = other.alignmentInfo_ != null ? other.alignmentInfo_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_EmotionAnalysisResult Clone() {
      return new PB_EmotionAnalysisResult(this);
    }

    /// <summary>Field number for the "sentence_list" field.</summary>
    public const int sentence_listFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Msg.explore.PB_EmotionAnalysisSentenceResult> _repeated_sentence_list_codec
        = pb::FieldCodec.ForMessage(10, global::Msg.explore.PB_EmotionAnalysisSentenceResult.Parser);
    private readonly pbc::RepeatedField<global::Msg.explore.PB_EmotionAnalysisSentenceResult> sentence_list_ = new pbc::RepeatedField<global::Msg.explore.PB_EmotionAnalysisSentenceResult>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.explore.PB_EmotionAnalysisSentenceResult> sentence_list {
      get { return sentence_list_; }
    }

    /// <summary>Field number for the "llmResult" field.</summary>
    public const int llmResultFieldNumber = 2;
    private string llmResult_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string llmResult {
      get { return llmResult_; }
      set {
        llmResult_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "alignmentInfo" field.</summary>
    public const int alignmentInfoFieldNumber = 3;
    private global::Msg.explore.PB_AlignmentInfo alignmentInfo_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_AlignmentInfo alignmentInfo {
      get { return alignmentInfo_; }
      set {
        alignmentInfo_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_EmotionAnalysisResult);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_EmotionAnalysisResult other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!sentence_list_.Equals(other.sentence_list_)) return false;
      if (llmResult != other.llmResult) return false;
      if (!object.Equals(alignmentInfo, other.alignmentInfo)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= sentence_list_.GetHashCode();
      if (llmResult.Length != 0) hash ^= llmResult.GetHashCode();
      if (alignmentInfo_ != null) hash ^= alignmentInfo.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      sentence_list_.WriteTo(output, _repeated_sentence_list_codec);
      if (llmResult.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(llmResult);
      }
      if (alignmentInfo_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(alignmentInfo);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      sentence_list_.WriteTo(ref output, _repeated_sentence_list_codec);
      if (llmResult.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(llmResult);
      }
      if (alignmentInfo_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(alignmentInfo);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += sentence_list_.CalculateSize(_repeated_sentence_list_codec);
      if (llmResult.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(llmResult);
      }
      if (alignmentInfo_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(alignmentInfo);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_EmotionAnalysisResult other) {
      if (other == null) {
        return;
      }
      sentence_list_.Add(other.sentence_list_);
      if (other.llmResult.Length != 0) {
        llmResult = other.llmResult;
      }
      if (other.alignmentInfo_ != null) {
        if (alignmentInfo_ == null) {
          alignmentInfo = new global::Msg.explore.PB_AlignmentInfo();
        }
        alignmentInfo.MergeFrom(other.alignmentInfo);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            sentence_list_.AddEntriesFrom(input, _repeated_sentence_list_codec);
            break;
          }
          case 18: {
            llmResult = input.ReadString();
            break;
          }
          case 26: {
            if (alignmentInfo_ == null) {
              alignmentInfo = new global::Msg.explore.PB_AlignmentInfo();
            }
            input.ReadMessage(alignmentInfo);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            sentence_list_.AddEntriesFrom(ref input, _repeated_sentence_list_codec);
            break;
          }
          case 18: {
            llmResult = input.ReadString();
            break;
          }
          case 26: {
            if (alignmentInfo_ == null) {
              alignmentInfo = new global::Msg.explore.PB_AlignmentInfo();
            }
            input.ReadMessage(alignmentInfo);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///*
  /// 情感分析单句结果
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_EmotionAnalysisSentenceResult : pb::IMessage<PB_EmotionAnalysisSentenceResult>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_EmotionAnalysisSentenceResult> _parser = new pb::MessageParser<PB_EmotionAnalysisSentenceResult>(() => new PB_EmotionAnalysisSentenceResult());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_EmotionAnalysisSentenceResult> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.BaseReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_EmotionAnalysisSentenceResult() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_EmotionAnalysisSentenceResult(PB_EmotionAnalysisSentenceResult other) : this() {
      text_ = other.text_;
      functionTag_ = other.functionTag_;
      intensityTag_ = other.intensityTag_;
      startTimeSecond_ = other.startTimeSecond_;
      endTimeSecond_ = other.endTimeSecond_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_EmotionAnalysisSentenceResult Clone() {
      return new PB_EmotionAnalysisSentenceResult(this);
    }

    /// <summary>Field number for the "text" field.</summary>
    public const int textFieldNumber = 1;
    private string text_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string text {
      get { return text_; }
      set {
        text_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "functionTag" field.</summary>
    public const int functionTagFieldNumber = 2;
    private string functionTag_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string functionTag {
      get { return functionTag_; }
      set {
        functionTag_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "intensityTag" field.</summary>
    public const int intensityTagFieldNumber = 3;
    private string intensityTag_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string intensityTag {
      get { return intensityTag_; }
      set {
        intensityTag_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "startTimeSecond" field.</summary>
    public const int startTimeSecondFieldNumber = 4;
    private float startTimeSecond_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float startTimeSecond {
      get { return startTimeSecond_; }
      set {
        startTimeSecond_ = value;
      }
    }

    /// <summary>Field number for the "endTimeSecond" field.</summary>
    public const int endTimeSecondFieldNumber = 5;
    private float endTimeSecond_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public float endTimeSecond {
      get { return endTimeSecond_; }
      set {
        endTimeSecond_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_EmotionAnalysisSentenceResult);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_EmotionAnalysisSentenceResult other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (text != other.text) return false;
      if (functionTag != other.functionTag) return false;
      if (intensityTag != other.intensityTag) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(startTimeSecond, other.startTimeSecond)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.Equals(endTimeSecond, other.endTimeSecond)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (text.Length != 0) hash ^= text.GetHashCode();
      if (functionTag.Length != 0) hash ^= functionTag.GetHashCode();
      if (intensityTag.Length != 0) hash ^= intensityTag.GetHashCode();
      if (startTimeSecond != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(startTimeSecond);
      if (endTimeSecond != 0F) hash ^= pbc::ProtobufEqualityComparers.BitwiseSingleEqualityComparer.GetHashCode(endTimeSecond);
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (text.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(text);
      }
      if (functionTag.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(functionTag);
      }
      if (intensityTag.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(intensityTag);
      }
      if (startTimeSecond != 0F) {
        output.WriteRawTag(37);
        output.WriteFloat(startTimeSecond);
      }
      if (endTimeSecond != 0F) {
        output.WriteRawTag(45);
        output.WriteFloat(endTimeSecond);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (text.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(text);
      }
      if (functionTag.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(functionTag);
      }
      if (intensityTag.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(intensityTag);
      }
      if (startTimeSecond != 0F) {
        output.WriteRawTag(37);
        output.WriteFloat(startTimeSecond);
      }
      if (endTimeSecond != 0F) {
        output.WriteRawTag(45);
        output.WriteFloat(endTimeSecond);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (text.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(text);
      }
      if (functionTag.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(functionTag);
      }
      if (intensityTag.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(intensityTag);
      }
      if (startTimeSecond != 0F) {
        size += 1 + 4;
      }
      if (endTimeSecond != 0F) {
        size += 1 + 4;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_EmotionAnalysisSentenceResult other) {
      if (other == null) {
        return;
      }
      if (other.text.Length != 0) {
        text = other.text;
      }
      if (other.functionTag.Length != 0) {
        functionTag = other.functionTag;
      }
      if (other.intensityTag.Length != 0) {
        intensityTag = other.intensityTag;
      }
      if (other.startTimeSecond != 0F) {
        startTimeSecond = other.startTimeSecond;
      }
      if (other.endTimeSecond != 0F) {
        endTimeSecond = other.endTimeSecond;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            text = input.ReadString();
            break;
          }
          case 18: {
            functionTag = input.ReadString();
            break;
          }
          case 26: {
            intensityTag = input.ReadString();
            break;
          }
          case 37: {
            startTimeSecond = input.ReadFloat();
            break;
          }
          case 45: {
            endTimeSecond = input.ReadFloat();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            text = input.ReadString();
            break;
          }
          case 18: {
            functionTag = input.ReadString();
            break;
          }
          case 26: {
            intensityTag = input.ReadString();
            break;
          }
          case 37: {
            startTimeSecond = input.ReadFloat();
            break;
          }
          case 45: {
            endTimeSecond = input.ReadFloat();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
