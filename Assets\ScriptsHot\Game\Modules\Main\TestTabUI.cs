using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using CommonUI;
using FairyGUI;
using UIBind.Main;
using ScriptsHot.Game.Modules.AgoraRtc;
using ScriptsHot.Game.Modules.Scene.Level;
using ScriptsHot.Game.Modules.Setting;
using Cysharp.Threading.Tasks;
using Msg.basic;
using Modules.DataDot;

//FGUI 主状态：ChatState
//FGUI 主状态：
public class TestTabUI : BaseUI<VoiceChatTestTabPanel>
{
    private enum FGUI_Chat_State {
        notChatting,
        chatting,
    }


    private enum FGUI_MatchWindow_State
    {
        start,
        cancel,
        retry,
        confirming
    }

    public TestTabUI(string name) : base(name) { }

    public override string uiLayer => UILayerConsts.Home; //主UI层 
    protected override bool isFullScreen => true;

    private int matchTimeoutSec = 300;// 300单位s prd约定
    private int currCD;
    private string cdTimerKey = string.Empty;//单位s

    private VoiceChatManager chatMngr;

    bool cancelActionLocker = false;

    private DemoAvatar _avatar3d;
    protected override void OnInit(GComponent uiCom)
    {
        chatMngr = VoiceChatManager.instance;
        

        this.ui.chatState.selectedPage = FGUI_Chat_State.notChatting.ToString();
        this.ui.matchWindow.preMatchState.selectedIndex = 0;//start 页面

        AddUIEvent(this.ui.matchWindow.startBtn.onClick, OnStartPreMatch);
        AddUIEvent(this.ui.matchWindow.cancelBtn.onClick, OnCancelPreMatch);

        AddUIEvent(this.ui.matchWindow.retryBtn.onClick, OnStartPreMatch);//超时类事件触发后 切换retry
        AddUIEvent(this.ui.exitChattingBtn.onClick, OnSelfExitChannel);

        AddUIEvent(this.ui.micVolComp.com.onClick,OnConfigMicVol);
        AddUIEvent(this.ui.speakerVolComp.com.onClick,OnConfigSpeakerVol);
        AddUIEvent(this.ui.exitConfirmBtn.onClick,OnConfirmExit);
        
        AddUIEvent(this.ui.translateBtn.onClick,OnSwitchTranslate);
        
        AddNotify();
        AddAvatar();

    }
    protected override void OnShow()
    {
        if (!this.chatMngr.IsUserAsNativeSpeaker)
        {
            this.ui.matchWindow.startBtn.title = I18N.inst.MoStr("toNativeSpeaker_main_match");//只能手动设置
        }
        else
        {
            //老师类用户
            this.ui.matchWindow.startBtn.visible = false;
            
            AppConst.EnableDebug();
            TimerManager.instance.RegisterTimer((c) =>
            {
                this.ui.matchWindow.headerTf.y = 900;
                this.ui.matchWindow.headerTf.text = "Chat Status:" + this.chatMngr.Status.ToString();
            }, 1000, 0);
        }


        this.ui.exitConfirmBtn.title = I18N.inst.MoStr("ui_confirm_btn_1");
        
        DataDotMgr.Collect( new VoiceChat_NotChattingSimpleDot( NotChattingSimpleDotType.appear_social_live_chat_page ));
        
        
    }



    private void AddNotify()
    {
        //chat 状态类的切换通知
        Notifier.instance.RegisterNotification(NotifyConsts.VoiceChat_CancelPreMatchComplete, OnCancelPreMatchComplete);
        Notifier.instance.RegisterNotification(NotifyConsts.VoiceChat_StartPreMatchConfirming, OnPreMatchConfirming);
        Notifier.instance.RegisterNotification(NotifyConsts.VoiceChat_JoinChatting, OnMatchSuccAndJoinChatting);
        Notifier.instance.RegisterNotification(NotifyConsts.VoiceChat_RecvOtherEndExitChannel, OnRecvOtherEndExitChannel);
        Notifier.instance.RegisterNotification(NotifyConsts.VoiceChat_ExitChannel, OnExitChannel);
        

        //chat过程中的文本消息
        Notifier.instance.RegisterNotification(NotifyConsts.P2P_UserDialog, OnP2PUserDialog);
        Notifier.instance.RegisterNotification(NotifyConsts.P2P_UserDialog2, OnP2PUserDialog2);//我的识别中

        Notifier.instance.RegisterNotification(NotifyConsts.P2P_OtherDialog, OnP2POtherDialog);
        Notifier.instance.RegisterNotification(NotifyConsts.P2P_OtherDialog2, OnP2POtherDialog2);//对端的识别中
         
        Notifier.instance.RegisterNotification(NotifyConsts.P2P_OtherDialogTranslate, OnP2POtherDialogTranslate);
        
        Notifier.instance.RegisterNotification(NotifyConsts.P2P_DialogExample, OnP2PDialogExample);
        
        
        Notifier.instance.RegisterNotification(NotifyConsts.P2P_DialogExampleTranslate, OnP2PDialogExampleTranslate);
        
    }

    private void AddAvatar()
    {        
        _avatar3d = new DemoAvatar();
        _avatar3d.SetLoader(this.ui.comLoader3D); //外部只需要一个 comLoader3D
        _avatar3d.SetVisible(false);
        
    }

    private GameObject myAvatarModelGameObj;
    private async void ShowModel3d(Msg.basic.PB_GenderEnum  gender)
    {
        //PB_OnboardingChatPreloadData data = _flowController.Model.PreloadInfo.preloadData;
        
        _avatar3d.SetVisible(true);
        
        var level = ControllerManager.instance.GetController<SceneController>(ModelConsts.Scene).scene;
        
        //,2982119962383892482,Julie Lefevre,10072,205,2
        
        //外部需要一个  几个数据 ，level，1，avatarID，bgID
        if (gender == PB_GenderEnum.GNone || gender == PB_GenderEnum.NonBinary || gender == PB_GenderEnum.NotToSay)
        {
            float randomValue = Random.Range(0f, 1f);
            gender = randomValue > 0.5f ? PB_GenderEnum.Female : PB_GenderEnum.Male;
        }

        if (gender == PB_GenderEnum.Female)
        {
            myAvatarModelGameObj= await  _avatar3d.SetModel( level ,1,2982119962383892482,"cafe");
        }
        else
        {
            myAvatarModelGameObj= await  _avatar3d.SetModel( level ,1,2982119962383892481,"cafe");//男性临时角色
        }

        _avatar3d.UpdateAudioSource();
        _avatar3d.StartAudio();
    }

    private void HideModel3d()
    {
        ScriptsHot.Game.Modules.Explore.UI.ModelPool.ReleaseModel(2982119962383892482, myAvatarModelGameObj);
        _avatar3d.SetVisible(false);
        _avatar3d.StopAudio();
    }

    //private void OnInitEngine()
    //{

    //    Debug.Log("OnInitEngine");
    //    string appID = "f645bd50539a474db622874ede6e3d5c";
    //    RtcEngineManager.Instance.Initialize(appID);

    //}

    //private void OnEnterChatChannel()
    //{
    //    Debug.Log("OnEnterChatChannel");
    //    string token = "007eJxTYBBc8+Gl/bUyLsX5l28k9snMvDXD6NjdGQqf8+RtNE+8ED6mwJBmZmKalGJqYGpsmWhibpKSZGZkZGFukpqSapZqnGKa7O9hnNEQyMhwLX8iMyMDBIL4nAy5lckZiXl5qTkMDAA/wCIv";
    //    string channelName  = "mychannel";

    //    int ret=RtcEngineManager.Instance.JoinChannel(token, channelName);
    //    if (ret < 0) {
    //        Debug.LogError("JoinChannel failed, ret="+ret );
    //    }
    //}

    //private void OnExitChatChannel()
    //{
    //    Debug.Log("OnExitChatChannel");
    //    RtcEngineManager.Instance.LeaveChannel();
    //}

    #region 状态切换类的 行为与响应
    //用户点击
    private async void OnStartPreMatch()
    {
        //检查 mic
        SettingModel settingModel = GetModel<SettingModel>(ModelConsts.Setting);
        //判断是否是第一次出现
        if (!Application.HasUserAuthorization(UserAuthorization.Microphone) && ! settingModel.isAppearMicPermission)
        {
            await RequestMicrophonePermissionAsync();
            settingModel.SetMicrophonePermission(true);
        }

        if (!Application.HasUserAuthorization(UserAuthorization.Microphone) && !settingModel.isAppearMicPermission)
        {
            //授权未通过时不允许接通
            return;
        }

        //没有获取权限
        if (!GetController<PermissMsgController>(ModelConsts.PermissMsg).CheckPermissMic())
        {
            return;
        }

        if (Application.internetReachability == NetworkReachability.NotReachable)
        {
            DataDotMgr.Collect( new VoiceChat_AppearNetworkTipBubble( ));
            
            this.GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N( "ui_pay_error01",
                () => { },null,1,"common_check"); 
            return;
        }

        bool actionRet;
        if (!chatMngr.IsUserAsNativeSpeaker)
        {
            this.GetUI<MultiTabFrameworkUI>(UIConsts.MultiTabHomepage).HideTabs(TabIndex.Test,false,false);//关闭底tab, 且不显示icon
            this.ui.matchWindow.clickMatchBtn.Play();  //两个动画都是0.5s的
            this.ui.clickMatchBtn.Play(() =>
            {
                actionRet= chatMngr.StartPreMatch();
                DataDotMgr.Collect( new VoiceChat_NotChattingSimpleDot( NotChattingSimpleDotType.click_social_live_chat_page_match_button ));
                if (actionRet) {
                    //核心行为：切换页签状态
                    this.ui.matchWindow.preMatchState.selectedPage = FGUI_MatchWindow_State.cancel.ToString();// "cancel";//切到 匹配中可以 cancel的状态
                    
                    //todo-tanglei startTimer 倒计时
                    currCD = this.matchTimeoutSec;

                    this.ui.matchWindow.countDownTf.text = currCD.ToString()+"s";
                    this.ui.matchWindow.ufoIdle.Play( (int)(matchTimeoutSec/3), 0,null);//播放100次
                    
                    //匹配超时时触发cancel
                    cdTimerKey = TimerManager.instance.RegisterTimer(counter => {
                        currCD--;
                        this.ui.matchWindow.countDownTf.text = currCD.ToString() + "s";//刷新时间信息
                        if (currCD ==0 ||counter == 0)//这时的currCD也应该是0
                        {
                            Debug.Log("currCD ="+currCD + " counter="+ counter);
                            //客户端自己超时，强制cancel，这时服务器也应该超时了
                            chatMngr.CancelPreMatch();

                            this.GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N(  "toNativeSpeaker_matchtimeout",
                                () => { },null,1,"ui_confirm_btn_1");
                            
                            // OpenI18N("common_reconnect_tips", () =>
                            // {
                            //     ChatReconnection(msg);
                            // }, null, 1, null, "chat_btn_cancel");
                           
                        }
                    },1000, this.matchTimeoutSec);
                }
                else
                {
                    Debug.LogError("点击match的失败流程未定义 逻辑处理 ");
                    if (this.ui.matchWindow.ufoIdle.playing)
                    {
                        this.ui.matchWindow.ufoIdle.Stop();
                    }
                    this.ui.clickMatchBtn.PlayReverse();
                    this.ui.matchWindow.clickMatchBtn.PlayReverse();
                    this.GetUI<MultiTabFrameworkUI>(UIConsts.MultiTabHomepage).ShowTabs();
                }
            });
        }
        else
        {
            //todo0 需要允许IsUserAsNativeSpeaker 也像普通用户一样发起
            //这时，其实相当于他会临时变身为 非native speaker
            Debug.LogError("尚未开发此流程");
        }
    }



    //用户点击,非瞬间切换
    private void OnCancelPreMatch()
    {
        if (!cancelActionLocker)
        //临界保护当 用户自身已经join过channel等待对端时， cancel动作无效，从而保护 SelfJoin 到BothJoin这段中间的confirm的时间片段出现意外case
        if (!cancelActionLocker)
        {
            if( chatMngr.Status == VoiceChatRunningStatus.ChattingSelfJoin || chatMngr.Status == VoiceChatRunningStatus.ChattingBothJoin )
            {
                Debug.Log("点cancelBtn时，如果已经join。则跳过，执行保护逻辑");
                return;
            }
        
            cancelActionLocker = true;
            chatMngr.CancelPreMatch();
            DataDotMgr.Collect( new VoiceChat_NotChattingSimpleDot( NotChattingSimpleDotType.click_social_match_page_cancel_button ));
        }
        else
        {
            Debug.LogWarning("上一个cancel的处理尚未完毕");
            //todo-tanglei 
            //增加timeout机制保证 过期后还能再次触发
        }

    }
    
    //系统事件
    private void OnCancelPreMatchComplete(string name, object body)
    {
        Debug.Log("OnCancelPreMatchComplete ->notChatting");
        cancelActionLocker = false;
        this.ui.chatState.selectedPage = FGUI_Chat_State.notChatting.ToString(); //"notChatting";


        if (!string.IsNullOrEmpty(cdTimerKey))
        {
            TimerManager.instance.UnRegisterTimer(cdTimerKey);
            cdTimerKey = string.Empty;
        }

        //回退动画状态
        if (this.ui.matchWindow.ufoIdle.playing)
        {
            this.ui.matchWindow.ufoIdle.Stop();
        }
    
        this.ui.clickMatchBtn.PlayReverse();
        this.ui.matchWindow.clickMatchBtn.PlayReverse(() =>
        {
            this.ui.matchWindow.preMatchState.selectedPage = FGUI_MatchWindow_State.start.ToString();// "start";//切到 匹配中可以 cancel的状态    
            //todo 可能有时间缝隙 如果严格这里应该增加 是否已经展示了 showTabsBtn的逻辑
            this.GetUI<MultiTabFrameworkUI>(UIConsts.MultiTabHomepage).ShowTabs();//展开底tab
        }); 
       
    }

    //系统事件
    private void OnPreMatchConfirming(string name, object body)
    {

        this.ui.matchWindow.preMatchState.selectedPage = FGUI_MatchWindow_State.confirming.ToString();// "confirming";//切到 匹配中可以 cancel的状态
        //禁止切换
        
        //此时倒计时，防止后续的 join失败 
    }

    //系统事件
    //此事件对于 NS_user会触发两次，第1次是自己进房间（不展示3D角色），第二次是对方进房间
    private void OnMatchSuccAndJoinChatting(string name, object body)
    {
        if (!string.IsNullOrEmpty(cdTimerKey))
        {
            TimerManager.instance.UnRegisterTimer(cdTimerKey);
            cdTimerKey = string.Empty;
        }
        

        bool isBothJoin = this.chatMngr.Status == VoiceChatRunningStatus.ChattingBothJoin;
        if (this.chatMngr.IsUserAsNativeSpeaker)
        {
            Debug.LogError("VChat临时日志05: OnMatchSuccAndJoinChatting启动View isBoth="+isBothJoin);
        }
        else
        {
            Debug.LogError("VChat临时日志15: OnMatchSuccAndJoinChatting启动View isBoth="+isBothJoin);
        }


        InitChattingView(isBothJoin );
        
        //有可能这里已经 隐藏过了
        this.GetUI<MultiTabFrameworkUI>(UIConsts.MultiTabHomepage).HideTabs(TabIndex.Test,false,false);//关闭底tab, 且不显示icon
    }

    private void InitChattingView(bool isBothJoin)
    {
        this.ui.chatState.selectedPage = FGUI_Chat_State.chatting.ToString();// "chatting";
        if (this.chatMngr.IsUserAsNativeSpeaker)
        {
            this.ui.headerViewState.selectedPage = "NSUser";//native speaker user
            DataDotMgr.Collect( new VoiceChat_AppearChattingPage(){type="teacher" });
            
        }
        else
        {
            this.ui.headerViewState.selectedPage = "NUser";//normal user
            DataDotMgr.Collect( new VoiceChat_AppearChattingPage(){type="User" });
        }

        //重置mic状态和文本状态
        this.ui.micVolComp.toggleState.selectedPage = "On";
        chatMngr.ConfigMicVol(true);

        this.ui.NameValue.text = chatMngr.PartnerParam.NUserName;
        this.ui.TopicValue.text = chatMngr.PartnerParam.topicVal;
        this.ui.LevelValue.text = chatMngr.PartnerParam.levelVal;

        //占空白位置
        this.ui.avatarTf.text = " ";//string.Empty;
        this.ui.avatarTransTf.text = " ";// string.Empty;
        this.ui.userTf.text =" ";// string.Empty;
        this.ui.exempleTf.text =" ";// string.Empty;

        this.ui.ExampleGrp.visible = false;
        this.ui.avatarMsgGrp.visible = false;
        this.ui.UserMsgGrp.visible = false;

        if (isBothJoin)
        {
            this.ShowModel3d(chatMngr.PartnerParam.gender);
        }

        
    }

    //用户点击
    private void OnSelfExitChannel()
    {
        Debug.Log("OnSelfExitChannel");
        
        chatMngr.LeaveChannel(true);
        this.ui.ChattingExitConfirm.visible = true;
        
        //主被叫都上报
        DataDotMgr.Collect( new VoiceChat_NotChattingSimpleDot( NotChattingSimpleDotType.appear_social_call_ended_panel ));
    }

    private void OnConfirmExit()
    {
        this.ui.ChattingExitConfirm.visible = false;
        this.HideModel3d();
        
        DataDotMgr.Collect( new VoiceChat_NotChattingSimpleDot( NotChattingSimpleDotType.click_social_call_ended_panel_confirm_button ));
        
        //回退动画状态
        if (this.ui.matchWindow.ufoIdle.playing)
        {
            this.ui.matchWindow.ufoIdle.Stop();
        }
        this.ui.clickMatchBtn.PlayReverse();
        this.ui.matchWindow.clickMatchBtn.PlayReverse(() =>
        {
            Debug.Log("OnConfirmExit ->notChatting");
            this.ui.chatState.selectedPage = FGUI_Chat_State.notChatting.ToString();// "notChatting"; 
            this.ui.matchWindow.preMatchState.selectedPage = FGUI_MatchWindow_State.start.ToString();// "start";//切到 匹配中可以 cancel的状态    
            this.GetUI<MultiTabFrameworkUI>(UIConsts.MultiTabHomepage).ShowTabs();//展开底tab
        }); 
        
        
    }

    private void OnSwitchTranslate()
    {
            this.ui.avatarTransTf.visible =  !this.ui.avatarTransTf.visible;
    }

    //系统事件，对端的发生退出or离线行为时
    private void OnRecvOtherEndExitChannel(string name, object body)
    {
        Debug.Log("OnRecvOtherEndExitChannel");
        chatMngr.LeaveChannel(false);
        this.ui.ChattingExitConfirm.visible = true;
        DataDotMgr.Collect( new VoiceChat_NotChattingSimpleDot( NotChattingSimpleDotType.appear_social_call_ended_panel ));
    }

    private void OnExitChannel(string name, object body)
    {
        this.chatMngr.DisposeVoiceEngine();
        //todo-tanglei 在dispose过程中 不显示preMatch的页面 100-200ms
    }

    
    #endregion 
    #region text部分的系统通知
    private void OnP2PUserDialog(string s, object body)
    {
        //Debug.LogError("OnP2PUserDialog:" + _controller.Model.UserDialogInfo.text);
        //PtoPSelfCell cell = new PtoPSelfCell();
        //AddCellCom(cell, PtoPCellType.Player);
        //cell.SetData(_controller.Model.UserDialogInfo);

        this.ui.userTf.text = chatMngr.textModel.UserDialogInfo.text;
    }

    private void OnP2PUserDialog2(string s, object body)
    {
        this.ui.userTf.text = chatMngr.textModel.UserDialogInfo2.text;
        if (!this.ui.UserMsgGrp.visible)
        {
            this.ui.UserMsgGrp.visible = true;
        }
    }


    private void OnP2POtherDialog(string s, object body)
    {
        //Debug.LogError("OnP2POtherDialog:" + _controller.Model.OtherDialogInfo.text);
        //return;
        //PtoPTargetCell cell = new PtoPTargetCell();
        //AddCellCom(cell, PtoPCellType.Avatar);
        //cell.SetData(_controller.Model.OtherDialogInfo);

        this.ui.avatarTf.text = chatMngr.textModel.OtherDialogInfo.text;
    }
    private void OnP2POtherDialog2(string s, object body)
    {

        this.ui.avatarTf.text = chatMngr.textModel.OtherDialogInfo2.text;
        if ( !this.ui.avatarMsgGrp.visible)
        {
            this.ui.avatarMsgGrp.visible = true;
        }
    }

    private void OnP2POtherDialogTranslate(string s, object body)
    {
        this.ui.avatarTransTf.text = chatMngr.textModel.OtherDialogTranslate.replyTranslateText;
    }

    private void OnP2PDialogExample(string s, object body)
    {
        this.ui.exempleTf.text = chatMngr.textModel.DialogExample.exampleText;
        if ( !this.ui.ExampleGrp.visible )
        {
            this.ui.ExampleGrp.visible = true;
        }
    }


    private void OnP2PDialogExampleTranslate(string s, object body)
    {
        this.ui.exempleTransTf.text = chatMngr.textModel.DialogExampleTranslate.exampleTranslateText;
        //Debug.LogError("OnP2PDialogExampleTranslate:" + _controller.Model.DialogExampleTranslate.exampleTranslateText);
        return;
    }

    #endregion

    #region other functions

    void OnConfigMicVol() {
        if (this.ui.micVolComp.toggleState.selectedPage == "On")
        {
            chatMngr.ConfigMicVol(false);
            this.ui.micVolComp.toggleState.selectedPage = "Off";
            DataDotMgr.Collect( new VoiceChat_ClickChattingPageMuteBtn()
            {
                before_status = "unmuted",
                after_status = "muted",
            });
        }
        else
        {
            chatMngr.ConfigMicVol(true);
            this.ui.micVolComp.toggleState.selectedPage = "On";
             DataDotMgr.Collect( new VoiceChat_ClickChattingPageMuteBtn()
                {
                    before_status = "muted",
                    after_status = "unmuted",
                });
        }
    }
    void OnConfigSpeakerVol() {
        if (this.ui.speakerVolComp.toggleState.selectedPage == "On")
        {
            chatMngr.ConfigMicVol(false);
            this.ui.speakerVolComp.toggleState.selectedPage = "Off";
            
            DataDotMgr.Collect( new VoiceChat_ClickChattingPageSpeakerBtn()
            {
                before_status = "speaker_on",
                after_status = "speaker_off",
            });
        }
        else
        {
            chatMngr.ConfigMicVol(true);
            this.ui.speakerVolComp.toggleState.selectedPage = "On";
            DataDotMgr.Collect( new VoiceChat_ClickChattingPageSpeakerBtn()
            {
                before_status = "speaker_off",
                after_status = "speaker_on",
            });
        }
    }
    #endregion 
    
    // 待统一优化：

    
    private async UniTask RequestMicrophonePermissionAsync()
    {
        await Application.RequestUserAuthorization(UserAuthorization.Microphone).ToUniTask();
    }
}
