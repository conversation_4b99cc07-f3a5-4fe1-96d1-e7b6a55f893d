﻿using System;
using System.CodeDom;
using System.Collections.Generic;
using System.Linq;
using FairyGUI;
using UnityEngine;


//https://en.wikipedia.org/wiki/IETF_language_tag
//这里约定的LanguageCode 主体按two-letter（639-1）去约定，但允许在必要时扩

//language code 赋予的int需要后续再调整，最好和systemLanguage侧的保持一致
public enum LanguageCode
{
    uninit,
    zh  ,
    en  ,
    fr  ,
    de  ,
    ja  ,
    pt  ,
    ko  ,
    es  ,
    it  ,
    tr,
    vi,
    zh_TW,
    zh_test,
    unknown
}


public static class I18NTool
{
    public static List<LanguageCode> ShowMotherLanguage = new List<LanguageCode>();
    //要根据后端实际支持的情况填写
    private static Dictionary<SystemLanguage, LanguageCode> supportedMotherlang2CodeDict = new Dictionary<SystemLanguage, LanguageCode> {
        // 动态添加
    };

    //根据客户端所能支持的UI Language填写(有些可能不会在 欧洲版本中露出，因为欧洲版被锁定为en)
    private static Dictionary<SystemLanguage, LanguageCode> syslang2CodeDict = new Dictionary<SystemLanguage, LanguageCode> {
       { SystemLanguage.French, LanguageCode.fr },
       { SystemLanguage.English, LanguageCode.en},
       { SystemLanguage.Chinese, LanguageCode.zh},
       { SystemLanguage.ChineseSimplified, LanguageCode.zh},
       { SystemLanguage.German, LanguageCode.de},
       { SystemLanguage.Japanese, LanguageCode.ja},

       { SystemLanguage.Portuguese, LanguageCode.pt},
       { SystemLanguage.Korean, LanguageCode.ko},
       { SystemLanguage.Spanish, LanguageCode.es},
       { SystemLanguage.Italian, LanguageCode.it},
       { SystemLanguage.Turkish, LanguageCode.tr},
       { SystemLanguage.Vietnamese, LanguageCode.vi},
       { SystemLanguage.ChineseTraditional, LanguageCode.zh_TW},
    };

    private static Dictionary<LanguageCode,SystemLanguage > code2syslangDict = new Dictionary<LanguageCode,SystemLanguage > {
       { LanguageCode.fr,SystemLanguage.French },
       { LanguageCode.en,SystemLanguage.English},
       { LanguageCode.zh,SystemLanguage.ChineseSimplified},
       { LanguageCode.de,SystemLanguage.German},
       { LanguageCode.ja,SystemLanguage.Japanese},


       { LanguageCode.pt, SystemLanguage.Portuguese},
       { LanguageCode.ko,SystemLanguage.Korean},
       { LanguageCode.es,SystemLanguage.Spanish},
       { LanguageCode.it,SystemLanguage.Italian},
       { LanguageCode.tr,SystemLanguage.Turkish},
       { LanguageCode.vi,SystemLanguage.Vietnamese},
       { LanguageCode.zh_TW,SystemLanguage.ChineseTraditional},
    };

    private static Dictionary<LanguageCode, string> code2NameDict = new Dictionary<LanguageCode, string>
    {
        {LanguageCode.zh, "简体中文"},
        {LanguageCode.en, "English"},
        {LanguageCode.fr, "Français"},
        {LanguageCode.de, "Deutsch"},
        {LanguageCode.ja, "日本語"},
        {LanguageCode.pt, "português"},
        {LanguageCode.es, "español"},
        {LanguageCode.it, "Italiano"},
        {LanguageCode.ko, "한국어"},
        {LanguageCode.tr, "Türkçe"},
        {LanguageCode.vi, "Tiếng Việt"},
        {LanguageCode.zh_TW, "繁體中文"},
        {LanguageCode.zh_test, "测试语言"},
    };


    //老的debug中 切法区就是法语
    public static List<LanguageCode> DebugLangList = new List<LanguageCode>() {
        LanguageCode.zh, LanguageCode.fr, LanguageCode.en
    };

    public static string GetLangColumn(LanguageCfg cfg, LanguageCode code)
    {
        switch (code)
        {
            case LanguageCode.fr:
                return cfg.fr;
            case LanguageCode.zh:
                return cfg.zh;
            case LanguageCode.en:
                return cfg.en;
            //最晚0729更新doc后，需要解开这部分的内容
            case LanguageCode.de:
                return cfg.de;
            case LanguageCode.ja:
                return cfg.ja;
            case LanguageCode.pt:
                return cfg.pt;
            case LanguageCode.ko:
                return cfg.ko;
            case LanguageCode.es:
                return cfg.es;
            case LanguageCode.it:
                return cfg.it;
            case LanguageCode.tr:
                return cfg.tr;
            case LanguageCode.vi:
                return cfg.vi;
            case LanguageCode.zh_TW:
                return cfg.zhTW;
            default:
                //暂定的fallback均为英文
                return cfg.en;
        }
    }

    public static SystemLanguage GetSystemLangByLanguageCode(LanguageCode languageCode)
    {
        switch (languageCode)
        {
            case LanguageCode.fr:
                return SystemLanguage.French;
            case LanguageCode.zh:
                return SystemLanguage.ChineseSimplified;
            case LanguageCode.en:
                return SystemLanguage.English;
            case LanguageCode.de:
                return SystemLanguage.German;
            case LanguageCode.ja:
                return SystemLanguage.Japanese;
            case LanguageCode.pt:
                return SystemLanguage.Portuguese;
            case LanguageCode.ko:
                return SystemLanguage.Korean;
            case LanguageCode.es:
                return SystemLanguage.Spanish;
            case LanguageCode.it:
                return SystemLanguage.Italian;
            case LanguageCode.tr:
                return SystemLanguage.Turkish;
            case LanguageCode.vi:
                return SystemLanguage.Vietnamese;
            case LanguageCode.zh_TW:
                return SystemLanguage.ChineseTraditional;
            case LanguageCode.zh_test:
                return SystemLanguage.Catalan;
            default:
                return SystemLanguage.Unknown;
        }
    }
    
    public static void InitLanguageList()
    {
        InitMotherLanguageList();
    }
    
    private static void InitMotherLanguageList()
    {
        string langStr = string.Empty;
        if (AppRegionInfo.GetCurrRegionKey() == AppRegionKey.eu)
        {
            langStr = Cfg.T.TbConst.motherLanguageCodeListFr;
        }
        else if (AppRegionInfo.GetCurrRegionKey() == AppRegionKey.ch)
        {
            langStr = Cfg.T.TbConst.motherLanguageCodeListJp;
        }
        else
        {
            langStr = Cfg.T.TbConst.motherLanguageCodeListJp;
        }
        List<LanguageCode> motherLang = GetLanguageCodes(langStr);
        
        supportedMotherlang2CodeDict.Clear();
        SystemLanguage systemLanguage;
        foreach (var langCode in motherLang)
        {
            systemLanguage = GetSystemLangByLanguageCode(langCode);
            if (systemLanguage != SystemLanguage.Unknown)
            {
                supportedMotherlang2CodeDict.Add(systemLanguage , langCode);
                ShowMotherLanguage.Add(langCode);
            }
        }
    }
    
    public static List<LanguageCode> GetLanguageCodes(string languageCodes)
    {
        // 分割字符串，去除可能的空白字符
        var codes = languageCodes.Split(';', StringSplitOptions.RemoveEmptyEntries)
            .Select(code => code.Trim())
            .ToList();

        var languageCodeList = new List<LanguageCode>();

        foreach (var code in codes)
        {
            // 尝试将字符串转换为 LanguageCode 枚举值
            if (Enum.TryParse(code, true, out LanguageCode languageCode))
            {
                languageCodeList.Add(languageCode);
            }
        }

        return languageCodeList;
    }
    
    // public static SystemLanguage ConvertLanguageCodeIntoSysLang(LanguageCode languageCode)
    // {
    //     if (code2syslangDict.ContainsKey(languageCode))
    //     {
    //         return code2syslangDict[languageCode];
    //     }
    //     else
    //     {
    //         Debug.LogError("Unsupported convert1:"+ languageCode.ToString());
    //         return SystemLanguage.Unknown;
    //     }
    // }

    public static LanguageCode ConvertSupportedMotherLangIntoLanguageCode(SystemLanguage language)
    {
        if (supportedMotherlang2CodeDict.ContainsKey(language))
        {
            return supportedMotherlang2CodeDict[language];
        }
        else
        {
            Debug.Log("Fallback ConvertSupportedMotherLangIntoLanguageCode from: " + language.ToString() + " to en");
            return LanguageCode.en;
        }
    }

    // //从系统语言转化时需要兜底 fallback
    // public static LanguageCode ConvertSysLangIntoLanguageCode(SystemLanguage language)
    // {
    //     if (syslang2CodeDict.ContainsKey(language))
    //     {
    //         return syslang2CodeDict[language];
    //     }
    //     else {
    //         Debug.LogError("Fallback ConvertSysLangIntoLanguageCode from: " + language.ToString()+" to en");
    //         return LanguageCode.en;
    //     }
    // }

    // public static string ConvertSysLangIntoLanguageCodeStr(SystemLanguage language)
    // {
    //     if (syslang2CodeDict.ContainsKey(language))
    //     {
    //         return syslang2CodeDict[language].ToString();
    //     }
    //     else
    //     {
    //         Debug.LogError("Fallback ConvertSysLangIntoLanguageCodeStr from: " + language.ToString() + " to en");
            
    //         return LanguageCode.en.ToString();
    //     }
    // }
    public static LanguageCode ConvertSysLangIntoLanguageCode(SystemLanguage language)
    {
        if (syslang2CodeDict.ContainsKey(language))
        {
            return syslang2CodeDict[language];
        }
        else
        {
            Debug.Log("Fallback ConvertSysLangIntoLanguageCode from: " + language.ToString() + " to unknown");
            return LanguageCode.unknown;
        }
    }

    public static bool IsSupportedMotherLanguage(LanguageCode motherLang)
    {
        return ShowMotherLanguage.Contains(motherLang);
    }

    public static string GetLanguageName(LanguageCode languageCode)
    {
        return code2NameDict[languageCode];
    }

    public static string ToServerStr(LanguageCode languageCode)
    {
        return languageCode.ToString().Replace("_", "-");
    }

    public static bool FromServerStr(string languageStr, out LanguageCode language)
    {
        return Enum.TryParse(languageStr.Replace("-", "_"), out language);
    }

}
