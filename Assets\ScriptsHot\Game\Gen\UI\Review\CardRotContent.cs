/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.Review
{
    public partial class CardRotContent : GComponent
    {
        public static string pkgName => "Review";
        public static string comName => "CardRotContent";
        public static string url => "ui://l7zo233dl0uy1q";

        public Controller ctrlContent;
        public GGraph bg;
        public GGraph pivotObj;
        public GComponent scrollContent;
        public Stamp imgLeft;
        public Stamp imgRight;
        public Stamp imgDiscard;
        public GGroup grpCard;
        public GTextField txtContent2_1;
        public GTextField txtContent2_2;
        public GGroup grpGuide2;
        public GTextField txtContent1_1;
        public GTextField txtTimer;
        public GGroup grpGuide1;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(CardRotContent));
        }

        public override void ConstructFromXML(XML xml)
        {
            ctrlContent = GetControllerAt(0);
            bg = GetChildAt(0) as GGraph;
            pivotObj = GetChildAt(1) as GGraph;
            scrollContent = GetChildAt(2) as GComponent;
            imgLeft = GetChildAt(3) as Stamp;
            imgRight = GetChildAt(4) as Stamp;
            imgDiscard = GetChildAt(5) as Stamp;
            grpCard = GetChildAt(6) as GGroup;
            txtContent2_1 = GetChildAt(7) as GTextField;
            txtContent2_2 = GetChildAt(8) as GTextField;
            grpGuide2 = GetChildAt(9) as GGroup;
            txtContent1_1 = GetChildAt(10) as GTextField;
            txtTimer = GetChildAt(11) as GTextField;
            grpGuide1 = GetChildAt(12) as GGroup;

            SetMultiLanguageInChildren();
        }
        public override void Dispose()
        {
            ctrlContent = null;
            bg = null;
            pivotObj = null;
            scrollContent = null;
            imgLeft = null;
            imgRight = null;
            imgDiscard = null;
            grpCard = null;
            txtContent2_1 = null;
            txtContent2_2 = null;
            grpGuide2 = null;
            txtContent1_1 = null;
            txtTimer = null;
            grpGuide1 = null;

            base.Dispose();
        }
        public void SetMultiLanguageInChildren()
        {
            this.imgLeft.SetKey("review_stamp_left");  // "Got It"
            this.imgRight.SetKey("review_stamp_right");  // "Review"
            this.imgDiscard.SetKey("review_stamp_discard");  // "Never Show Up"
            this.txtContent2_1.SetKey("review_guide2_1");  // "Immediately consolidate your knowledge points"
            this.txtContent2_2.SetKey("review_guide2_2");  // "Click any area to continue"
            this.txtContent1_1.SetKey("review_guide1");  // "We have prepared 10 wrong questions for you, Start reviewing now."
            this.txtTimer.SetKey("review_guide2_2");  // "Click any area to continue"
        }
    }
}