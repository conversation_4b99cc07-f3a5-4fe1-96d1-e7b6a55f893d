/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreFriends
{
    public partial class ExploreFriendsChatPanel : UIBindT
    {
        public override string pkgName => "ExploreFriends";
        public override string comName => "ExploreFriendsChatPanel";

        public Controller ctrlList;
        public GGraph imgBG;
        public GLoader comLoaderImg;
        public GLoader comLoader3D;
        public GButton btnSetting;
        public FriendCommonBar comBar;
        public GTextField txtRelation;
        public GComponent btnBag;
        public ExploreFriendsChatCellContainer comCellContainer;
        public GButton btnDown;
        public ExploreFriendsChatTitle comTitle;
        public GGraph btnBack;
        public GGroup backGroup;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ctrlList = com.GetControllerAt(0);
            imgBG = (GGraph)com.GetChildAt(0);
            comLoaderImg = (GLoader)com.GetChildAt(1);
            comLoader3D = (GLoader)com.GetChildAt(2);
            btnSetting = (GButton)com.GetChildAt(3);
            comBar = new FriendCommonBar();
            comBar.Construct(com.GetChildAt(4).asCom);
            txtRelation = (GTextField)com.GetChildAt(5);
            btnBag = (GComponent)com.GetChildAt(6);
            comCellContainer = new ExploreFriendsChatCellContainer();
            comCellContainer.Construct(com.GetChildAt(7).asCom);
            btnDown = (GButton)com.GetChildAt(8);
            comTitle = new ExploreFriendsChatTitle();
            comTitle.Construct(com.GetChildAt(9).asCom);
            btnBack = (GGraph)com.GetChildAt(11);
            backGroup = (GGroup)com.GetChildAt(12);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ctrlList = null;
            imgBG = null;
            comLoaderImg = null;
            comLoader3D = null;
            btnSetting = null;
            comBar.Dispose();
            comBar = null;
            txtRelation = null;
            btnBag = null;
            comCellContainer.Dispose();
            comCellContainer = null;
            btnDown = null;
            comTitle.Dispose();
            comTitle = null;
            btnBack = null;
            backGroup = null;
        }
    }
}