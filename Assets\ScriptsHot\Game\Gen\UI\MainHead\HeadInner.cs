/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.MainHead
{
    public partial class HeadInner : UIBindT
    {
        public override string pkgName => "MainHead";
        public override string comName => "HeadInner";

        public GLoader ldr;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ldr = (GLoader)com.GetChildAt(1);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ldr = null;
        }
    }
}