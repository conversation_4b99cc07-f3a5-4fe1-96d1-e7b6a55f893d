﻿using System.Collections.Generic;
using System.Linq;
using FairyGUI;
using UnityEngine;

namespace UIBind.ExploreFriends
{
    public class ExploreFriendsBagPanelUI : BaseUI<ExploreFriendsBagPanel>
    {
        private bool _isDragging = false;
        private bool _isAnimating = false;
        private int _currentCenterItemIndex = -1; // 当前中心item的数据索引
        private List<FriendSlotData> _listData = new List<FriendSlotData>(); // 列表数据

        private int realSlotCnt = 0;
        
        private static int MAX_SLOT_CNT = 3;
        
        public ExploreFriendsBagPanelUI(string name) : base(name)
        {
        }

        public override string uiLayer => UILayerConsts.Top;

        protected override bool isFullScreen => true;

        protected override void OnInit(GComponent uiCom)
        {
            base.OnInit(uiCom);

            // 注册按钮点击事件
            AddUIEvent(ui.NextBtn.com.onClick, OnNextBtnClick);
            AddUIEvent(ui.ReDrawBtn.onClick, OnReDrawBtnClick);
            AddUIEvent(ui.selectList.onClickItem, OnSelectListClick);
            AddUIEvent(ui.BackBtn.onClick, OnBackBtnClick);
            // 注册滚动相关事件 - 使用ScrollPane的事件
            if (ui.selectList.scrollPane != null)
            {
                // AddUIEvent(ui.selectList.scrollPane.onScroll, OnSelectListScroll);
                // AddUIEvent(ui.selectList.scrollPane.onScrollEnd, OnSelectListScrollEnd);
            }

            InitializeList();
        }

        protected override void OnShow()
        {
            base.OnShow();

            ui.TitleTxt.text = I18N.inst.MoStr("ui_explore_friends_friend_bag_title_text");
            ui.NextBtn.SetTxt("ui_explore_friends_friend_bag_chatting_button_text");
            ui.ReDrawTxt.text = I18N.inst.MoStr("ui_explore_friends_friend_bag_switch_confirm_button_text");
            
            
            
            ui.EmptyAvatarImg.visible = false;
            _currentCenterItemIndex = -1;
            // 初始化列表数据和显示
            SetupListData();

            MoveToCenter(0);
            RefreshBtn();
        }

        protected override void OnHide()
        {
            base.OnHide();

        }


        /// <summary>
        /// 初始化列表设置
        /// </summary>
        private void InitializeList()
        {
            ui.selectList.ClearSelection();
            // 设置列表为横向布局
            ui.selectList.layout = ListLayoutType.SingleRow;
            ui.selectList.scrollItemToViewOnClick = false; // 禁用默认的滚动到视图功能

            // 设置列表渲染回调
            ui.selectList.itemRenderer = RenderListItem;
        }

        /// <summary>
        /// 设置列表数据
        /// </summary>
        private void SetupListData()
        {
            // 初始化测试数据（实际使用时替换为真实数据）
            _listData.Clear();
            _listData = GameEntry.ExFriendC.Model.SlotMapBySlotIndex.Values.ToList();
            _listData.Sort((a, b) => { return a.SlotIndex - b.SlotIndex; });

            realSlotCnt = 0;
            foreach (var data in _listData)
            {
                if (data.IsOpen)
                {
                    realSlotCnt++;
                }
            }
            if (realSlotCnt < MAX_SLOT_CNT)
            {
                realSlotCnt++;
            }
            // 设置列表item数量
            ui.selectList.numItems = realSlotCnt;
        }

        /// <summary>
        /// 列表item渲染回调
        /// </summary>
        /// <param name="index">item索引</param>
        /// <param name="item">item对象</param>
        private void RenderListItem(int index, GObject item)
        {
            if (index >= _listData.Count) return;

            item.data = index;
            GComponent itemComp = item.asCom;
            if (itemComp == null)
            {
                return;
            }
            BagListItem bagListItem = new BagListItem();
            bagListItem.Construct(itemComp);
            bagListItem.IsSelect.selectedIndex = 1;
            
            FriendSlotData data = _listData[index];
            if (!data.IsOpen)
            {
                bagListItem.IsEmpty.selectedIndex = 2;
            }
            else if (data.IsEmptySlot)
            {
                bagListItem.IsEmpty.selectedIndex = 1;
            }
            else
            {
                bagListItem.IsEmpty.selectedIndex = 0;
            }

            if (data.IsOpen && !data.IsEmptySlot)
            {
                ExploreFriendCfg cfg = GameEntry.ExFriendC.Model.GetFriendCfgByID(data.AvatarId);
                // 设置头像
                if (bagListItem.headLoader != null && !string.IsNullOrEmpty(cfg.HeadIcon))
                {
                    bagListItem.headLoader.url = cfg.HeadIcon;
                }
            }
        }

        private void OnNextBtnClick()
        {
            FriendSlotData friendSlotData = _listData[_currentCenterItemIndex];
            GameEntry.ExFriendC.EnterTalk(friendSlotData.AvatarId);
        }

        private void OnReDrawBtnClick()
        {
            FriendSlotData curSelectFriendSlotData = _listData[_currentCenterItemIndex];
            GameEntry.ExFriendC.Model.SetRedrawData(curSelectFriendSlotData);

            var introducePanelUI = 
                UIManager.instance.GetUI<ExploreFriendsIntroducePanelUI>(UIConsts.ExploreFriendsIntroducePanel);
            introducePanelUI.OpenPreUI = () =>
            {
                UIManager.instance.GetUI(UIConsts.ExploreFriendsBagPanel).Show();
            };
            introducePanelUI.Show();
            Hide();
        }
        
        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void OnCloseBtnClick()
        {
            Hide();
        }

        /// <summary>
        /// 列表item点击事件
        /// </summary>
        private void OnSelectListClick(EventContext context)
        {
            if (_isAnimating) return;
            
            // 从事件上下文获取被点击的item
            // onClickItem事件的data参数就是被点击的item对象
            GObject clickedItem = context.data as GObject;
            if (clickedItem?.data != null)
            {
                int clickedIndex = (int) clickedItem.data;
                
                FriendSlotData data = _listData[clickedIndex];
                if (data.IsOpen)
                {
                    MoveToCenter(clickedIndex);
                }
            }
        }
        
        private void OnBackBtnClick()
        {
            Hide();
        
        }

        /// <summary>
        /// 将指定索引的item移动到中心位置
        /// </summary>
        /// <param name="targetIndex">目标item的数据索引</param>
        private void MoveToCenter(int targetIndex)
        {
            if (_isAnimating || targetIndex == _currentCenterItemIndex) return;

            _isAnimating = true;

            UnselectItem(_currentCenterItemIndex);

            _currentCenterItemIndex = targetIndex;
            SelectItem(_currentCenterItemIndex);

            // 滚动到目标位置
            ui.selectList.ScrollToView(targetIndex, true, false);

            // 延迟一帧后更新显示，确保滚动完成
            GTween.DelayedCall(0.1f).OnComplete(() =>
            {
                // UpdateItemsDisplay();
                _isAnimating = false;
            });
        }

        /// <summary>
        /// 选中指定item
        /// </summary>
        /// <param name="item">要选中的item</param>
        /// <param name="itemIndex">item的数据索引</param>
        private void SelectItem(int itemIndex)
        {
            var item = ui.selectList.GetChildAt(itemIndex);
            // 设置IntroduceListItem的选中状态
            GComponent itemComp = item.asCom;
            if (itemComp != null)
            {
                // 获取IsSelect Controller (index 0)
                Controller isSelectController = itemComp.GetControllerAt(0);
                if (isSelectController != null)
                {
                    // 设置为选中状态 (假设1为选中状态)
                    isSelectController.selectedIndex = 0;
                }

                // itemComp.scale = new Vector3(1f, 1f, 1);
            }

            // 触发选中事件回调
            OnItemSelected(itemIndex);
        }

        /// <summary>
        /// 取消选中指定item
        /// </summary>
        /// <param name="item">要取消选中的item</param>
        private void UnselectItem(int index)
        {
            if (index < 0 || index >= ui.selectList.numChildren) return;
            var item = ui.selectList.GetChildAt(index);
            GComponent itemComp = item.asCom;
            if (itemComp != null)
            {
                // 获取IsSelect Controller (index 0)
                Controller isSelectController = itemComp.GetControllerAt(0);
                if (isSelectController != null)
                {
                    // 设置为非选中状态 (假设0为非选中状态)
                    isSelectController.selectedIndex = 1;
                }

                // itemComp.scale = new Vector3(0.5f, 0.5f, 1);
            }
        }

        /// <summary>
        /// item选中回调
        /// </summary>
        /// <param name="selectedIndex">选中的item索引</param>
        private void OnItemSelected(int selectedIndex)
        {
            if (selectedIndex >= 0 && selectedIndex < _listData.Count)
            {
                FriendSlotData friendSlotData = _listData[selectedIndex];

                bool isEmpty = friendSlotData.IsEmptySlot;
                ui.avatarLoader.visible = !isEmpty;
                ui.EmptyAvatarImg.visible = isEmpty;
                if (isEmpty)
                {
                    ui.SelectItemName.text = I18N.inst.MoStr("ui_explore_friends_friend_bag_empty_subtitle_text");
                    ui.NextBtn.SetEnable(false);
                }
                else
                {
                    ExploreFriendCfg selectedData = GameEntry.ExFriendC.Model.GetFriendCfgByID(friendSlotData.AvatarId);
                    ui.SelectItemName.text = selectedData.NameKey;
                    FriendsAvatarLoaderHelper.GenerateRTAsync(selectedData.AvatarModleId, ui.avatarLoader,
                        delegate(bool b) { }, 0.5f);
                    // ExploreFriendsUIHelper.SetBGImg(selectedData , ui.bgLoader);
                    ui.NextBtn.SetEnable(true);
                }
            }
        }


        private void RefreshBtn()
        {
            ui.NextBtn.SetBtnMode(FriendCommonBtn.BtnMode.Bag);
            ui.NextBtn.SetDiamondStatus(true);
            ui.NextBtn.SetLoadingStatus(false);
        }

        // protected override string[] ListNotificationInterests()
        // {
        //     return new[]
        //     {
        //         NotifyConsts.ExploreFriendsTimeLineLoaded
        //     };
        // }
        //
        // protected override void HandleNotification(string name, object body)
        // {
        //     switch (name)
        //     {
        //         case NotifyConsts.ExploreFriendsTimeLineLoaded:
        //             UIManager.instance
        //                 .GetUI<ExploreFriendsDrawCardsResultPanelUI>(UIConsts.ExploreFriendsDrawCardsResultPanel)
        //                 .Show();
        //             Hide();
        //             break;
        //
        //     }
        // }
    }
}
