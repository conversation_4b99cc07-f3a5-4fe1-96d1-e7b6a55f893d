using System;
using UnityEngine;


using FairyGUI;
using System.Collections;
using Cysharp.Threading.Tasks;
using Modules.DataDot;
using ScriptsHot.Game.Modules.Profile;
using ScriptsHot.Game.Modules.Setting;
using Msg.basic;
using ScriptsHot.Game.Modules.ReviewQuestion;
using Msg.question_process;
using ScriptsHot.Game.Modules.ChatLogicNew;
using ScriptsHot.Game.Modules.ChatLogicNew.UI.ChatAUA;
using ScriptsHot.Game.Modules.Explore;
using ScriptsHot.Game.Modules.Guide;
using ScriptsHot.Game.Modules.Guide.UI;
using UnityEditor;


public enum RecordUIStatus
{
        RecordUIStatusNone,
        RecordUIStatusRecord,
        RecordUIStatusSend,
        RecordUIStatusNextAndRepeat, //暂时没有repeat
        RecordUIStatusNext,
        RecordUIStatusFinish,
        RecordUIStatusFinishAndRepeat, //暂时没有repeat
        RecordUIStatusWaiting,
        RecordUIStatusRepeat, //暂时没有repeat
        Loading,
        NewSend,
        SocialFinishAndRepeat, //社交聊天repeat
        speakRecord, //speak收音
        speakRecordrepeart, //speak重说
        speakMic,
        TestSend, //测试时候 自动玩家语音使用
        Active,
        Lock,

}

public enum BulbStatus
{
    Hide,
    Waiting,
    Loading,
}

public class DownsideEvent {
    public string name;
}

[Obsolete]
public enum BulbStatusNew
{
    Normal,
    Loading,
    Scaffold,
    Active,
    Hide,
}

public enum BulbType
{
    Active,
    Scaffold
}

public class RecordUI : BaseUI<UIBind.Record.RecordPanel>
{
    private ChatController _chatController => this.GetController<ChatController>(ModelConsts.Chat);

    private SocialChatController _socialChatController =>
        this.GetController<SocialChatController>(ModelConsts.SocialChat);

    private SettingModel _settingModel => this.GetModel<SettingModel>(ModelConsts.Setting);

    private event Action<DownsideEvent> onRecordComplete;

    private event Action ClickMicrophoneAction;
    private event Action ClickMicrophoneCompleteAction;

    private event Action<DownsideEvent> onRecordCanceled;
    private event Action nextAction;
    private event Action endAction;
    private event Action onRecordStart;

    private event Action repeatRecordingAction;
    private event Action<bool> permissionAction;
    private event Action bulbAction;
    private event Action<BulbType, bool> bulbActionNew;
    private event Action PlayAudioAction;
    private event Action TimeOutAction;
    private event Action CloseAction;

    public RecordUI(string name) : base(name)
    {
    }

    public override string uiLayer => UILayerConsts.Top;

    protected override bool isFullScreen => true;

    private RecordUIStatus preStatus {
        get {
            if (ui != null)
                return (RecordUIStatus)ui?.ctlStatus.selectedIndex;
            return RecordUIStatus.RecordUIStatusNone;
        }
        set {
            if (ui != null) ui.ctlStatus.selectedIndex = (int)value;
        }
    }//= RecordUIStatus.RecordUIStatusNone;

    private RecordUIStatus _stashStatus;

    private bool shouldShowInNextFrame = false;

    public bool shouldCountdown = false;
    public int maxRecordingSeconds = 60; //单位秒
    private readonly float _waitBulbTime = 5f;
    public BulbStatus bulbStatus { get; private set; }

    public BulbStatusNew bulbStatusNew { get; private set; }

    private string timerKey = null;
    private int _durationAutoTime;
    private string _followTimerKey = string.Empty;
    private Coroutine _waitingCoroutine;

    private bool _isApplyNewSendStyle = false;
    private float _holdTime = 0;
    private bool _isPressed = false;
    private Coroutine _VibrateCoroutine;
    private bool _needSpeechPermiss = false;
    private bool isFollow = false;
    // private LongPressGesture _gesture;

    //用户自动说话
    private bool _autoPlayer = false;

    private Action _recordFinishAction;

    /// <summary>
    /// 是否是新的 脚手架按钮
    /// </summary>
    private bool _isNewSocial = false;

    /// <summary>
    /// 长按模式 默认关闭
    /// </summary>
    private bool _openTouchLong = false;

    private int _touchDownLong = 300;//毫秒
    private bool _isTouchBegin = false; // 是否是开始按下
    private bool _isLongPress = false;  // 是否是长按
    private bool _isClick = false;      // 是否是点击
    private float _touchBeginTime;      // 按下时间
    private Vector2 _touchBeginPos;     // 按下位置
    private string _touchTimerFlag = String.Empty;

    /// <summary>
    /// fin按钮是否可以点击
    /// </summary>
    public bool FinishBtnClick = true;

    protected override void OnInit(GComponent uiCom)
    {
        this.AddUIEvent(ui.btnMicrophone.onTouchEnd, onTouchEnd);
        this.AddUIEvent(ui.btnMicrophone.onClick, onClick);
        this.AddUIEvent(ui.btnMicrophone.onTouchBegin, onTouchBegin);

        this.AddUIEvent(ui.lockBtn.onClick, onLockClick);

        this.AddUIEvent(this.ui.btnNewCancel.com.onClick, this.ClickCancel, 1000);

        this.AddUIEvent(this.ui.btnGuideMicrophone.onClick, () =>
        {
            GetUI<GuideFreeChatUI>(UIConsts.GuideFreeChat).Hide();
            ClickRecordForRecordState();
        }, 1000);
        this.AddUIEvent(this.ui.btnSent.onClick, this.OnClickSend, 1000);
        this.AddUIEvent(this.ui.btnNewSend.onClick, this.OnClickSend, 1000);
        this.AddUIEvent(this.ui.btnCancel.com.onClick, this.ClickCancel, 1000);
        this.AddUIEvent(this.ui.btnSpeakCancel.onClick, this.ClickSpeakCancel, 1000);

        this.AddUIEvent(this.ui.btnRepeat.onClick, this.ClickRepeat, 1000);
        this.AddUIEvent(this.ui.btnBgNextForStateNR.onClick, this.ClickNext);
        this.AddUIEvent(this.ui.btnBgNext.onClick, this.ClickNext);
        this.AddUIEvent(this.ui.btnBgFin.onClick, this.ClickFinish);
        this.AddUIEvent(this.ui.btnRepeatForStateFR.onClick, this.ClickRepeat);
        this.AddUIEvent(this.ui.btnBgFinForStateFR.onClick, this.ClickFinish);
        this.AddUIEvent(this.ui.btnBulbLight.onClick, this.ClickBulb);
        this.AddUIEvent(this.ui.socialNew.onClick, this.ClickBulb);

        this.AddUIEvent(this.ui.btnSocialFinsh.onClick, ClickFinish);
        this.AddUIEvent(this.ui.btnRepeatCell.onClick, ClickRepeat, 1000);
        this.AddUIEvent(this.ui.btnSpeakReapeatBtn.onClick, ClickSpeakRepeat, 1000);
        this.AddUIEvent(this.ui.btnSpeakEndVad.onClick, OnClickSpeakEnd, 1000);
        this.AddUIEvent(this.ui.btnSpeakMicBtn.onClick, ClickSpeakRepeat, 1000);

        ;
        this.AddUIEvent(this.ui.btnPlayAudio.onClick, ClickPlayAudio);
        this.AddUIEvent(this.ui.btnClose.onClick, ClickClose);
        ui.tfCountDown.visible = false;
        //ui.tfSpeak.SetKey("ui_review_cansay");
        //ui.tfSpeakrepeat.SetKey("ui_review_tyr_again");

        // if (_gesture == null)
        // {
        //     _gesture = new LongPressGesture(ui.btnMicrophone);
        //     _gesture.trigger = 0;
        //     _gesture.onBegin.Add(OnButtonMicBegin);
        //     _gesture.onEnd.Add(OnButtonnMicEnd);
        // }

        // 引导
        var guideController = GetController<GuideController>(ModelConsts.Guide);
        guideController.RegisterMark(GuideConst.ChatFreeGuideSayGoodbye, ui.btnGuideMicrophone);
        guideController.RegisterMark(GuideConst.ChatFreeGuideSayGoodbyeParent, ui.com);
    }

    private void onLockClick()
    {
        PayWallDotHelper.LastSourcePage = PaywallEntrySourceType.explore_norecord;
        CLICK_EXPLORE_MAIN_PAGE_NORECORD_BUTTON dt1 = new CLICK_EXPLORE_MAIN_PAGE_NORECORD_BUTTON();
        DataDotMgr.Collect(dt1);
        Notifier.instance.SendNotification(NotifyConsts.OpenUI, new UIManager.UIParams() { viewName = UIConsts.SpeakPlanBottomSubscribeUI, param = 1 });
    }

    private void onTouchBegin()
    {
        if (!_openTouchLong) return;
        // Debug.LogError("开始 touch");
        _isTouchBegin = true;
        _isClick = false;
        _isLongPress = false;
        _touchBeginTime = Time.time;
        _touchBeginPos = Input.mousePosition;

        _touchTimerFlag = TimerManager.instance.RegisterTimer((a) =>
        {
            // Debug.LogError("按下");
            ClickRecordForRecordState();
        }, _touchDownLong, 1);

        VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Pop);
    }
    private void onTouchEnd()
    {

        TimerManager.instance.UnRegisterTimer(_touchTimerFlag);

        if (!_openTouchLong) return;
        float pressTime = Time.time - _touchBeginTime;

        Vector2 stagePos = Stage.inst.GetTouchPosition(0);
        Vector2 micLocalPos = ui.btnMicrophone.GlobalToLocal(stagePos);
        Vector2 cancelLocalPos = ui.btnNewCancel.com.GlobalToLocal(stagePos);

        bool isInMicButtonArea = micLocalPos.x >= 0 && micLocalPos.x <= ui.btnMicrophone.width &&
                                micLocalPos.y >= 0 && micLocalPos.y <= ui.btnMicrophone.height;

        bool isInCancelButtonArea = cancelLocalPos.x >= 0 && cancelLocalPos.x <= ui.btnNewCancel.com.width &&
                                   cancelLocalPos.y >= 0 && cancelLocalPos.y <= ui.btnNewCancel.com.height;

        // 如果在取消按钮上抬起，触发取消操作
        if (isInCancelButtonArea)
        {
            ClickCancel();
            return;
        }

        // Debug.LogError("抬起 pressTime:" + pressTime);
        // Debug.LogError("抬起 _touchDownLong:" +  _touchDownLong / 1000f);
        // Debug.LogError("抬起 _isClick:" +  _isClick);
        // 只有当触摸点在麦克风按钮区域内，且按住时间超过阈值时，才执行长按逻辑
        if (isInMicButtonArea && pressTime > _touchDownLong / 1000f && !_isClick && _isTouchBegin)
        {
            _isTouchBegin = false;
            _isLongPress = true;
            // Debug.LogError("长按抬起");
            OnClickSend();
        }
    }

    private void onClick()
    {
        // Debug.LogError("onClick  ：_isLongPress：：" + _isLongPress);
        // 如果是长按，不执行点击逻辑
        if (_isLongPress)
        {
            return;
        }

        _isClick = true;
        _isTouchBegin = false;

        // Debug.LogError("点击");
        ClickRecordForRecordState();
    }

    public void OpenNewSocial(bool value)
    {
        _isNewSocial = value;
        if (ui == null) return;
        if (_isNewSocial)
        {
            ui.recordTypeCtrl.selectedPage = "socialChatNew";
        }
        else
        {
            ui.recordTypeCtrl.selectedPage = "avatarChat";
        }
    }

    public void SetNewHandOpen(bool value)
    {
        if (ui != null)
        {
            if (value)
            {
                ui.ctrlNewHand.selectedIndex = 1;
            }
            else
            {
                ui.ctrlNewHand.selectedIndex = 0;
            }
        }

        ShowGuideTip(value);

    }

    public void SetTouchLong(bool value)
    {
        VFDebug.Log("长按开关" + value);
        _openTouchLong = value;
    }

    protected override void OnShow()
    {
        this.ui.socialNewCon.visible = false;
        this.ui.socialNewLoading.visible = false;
        ui.recordTypeCtrl.selectedIndex = 0;
        Notifier.instance.SendNotification(NotifyConsts.RecordShowReadly);
        if (GetUI<SocialChatUI>(UIConsts.SocialChatUI).isShow)
        {
            HideNewRecordText();
            VFDebug.Log("SocialChat EnterRecordIdle");
            var _socialChatCtrl = GetController<SocialChatController>(ModelConsts.SocialChat);
            _socialChatCtrl.ChangeState(SocialRecordState.RecordIdle, SocialStateMachineType.Record);
        }
        else if (GetUI<ProfileSelfUI>(UIConsts.ProfileSelfUI).isShow)
        {
            HideNewRecordText();
            VFDebug.Log("SocialCard EnterRecordIdle");
            var _socialCardCtrl = GetController<SocialCardController>(ModelConsts.SocialCard);
            _socialCardCtrl.ChangeState(IntroduceState.RecordIdle);
        }

        if (shouldShowInNextFrame)
        {
            ChangeStatus(preStatus);
        }

        RevertAfterAnimation();
        HideGuideFreeMic();
        ConfigUI();

       

        // 引导：在灯泡按钮添加标记10041
        var guideController = ControllerManager.instance.GetController<GuideController>(ModelConsts.Guide);
        guideController.RegisterMark(10041, ui.btnBulbLight);

        ui.tfTip.SetKeyArgs("explore_record_tip", "[color=#FDEA16]", "[/color]");
    }

    protected override void OnHide()
    {
        // Debug.LogError("RecordUI OnHide");
     
        preStatus = RecordUIStatus.RecordUIStatusNone;
        _stashStatus = preStatus;
        shouldShowInNextFrame = false;
        ui.tfCountDown.visible = false;
        _isPressed = true;
        this.ui.micMask.visible = false;
        _holdTime = 0f;
        
        FinishRecording(false);
        
        
        StopVibrateCoroutine();
        HideAllBulb();
        StopBulbAnim();
        ShowCompleteTips(false);

    }

    private string _checkGuideFlag = String.Empty;
    public void ShowRecord(bool show, Action recordingCallback = null, Action<DownsideEvent> sendCallback = null,
        Action<DownsideEvent> cancelCallback = null, bool shouldCountdown = false, Action clickMicrophoneAction = null,
        bool isPlayHandSpine = false, Action clickMicrophoneCompleteAction = null, bool isNewSend = false, bool needSpeechPermiss = false, bool autoPlayer = false)
    {
        if (_checkGuideFlag != null)
        {
            TimerManager.instance.UnRegisterTimer(_checkGuideFlag);
            _checkGuideFlag = String.Empty;
        }

        // 0.5s后打开的逻辑 v1.3.3版本弃用，与其他冲突，以后v1.3.4再看
        // if(!PlayerPrefs.HasKey(RecordBtnTip) )
        // {
        //     _checkGuideFlag = TimerManager.instance.RegisterTimer((c) =>
        //     {
        //         CheckGuideTip();
        //     }, 500, 1);
        // }
     
        
        
        OpenNewSocial(false);
        SetTouchLong(false);
        _isLongPress = false;
        _isApplyNewSendStyle = isNewSend;
        _needSpeechPermiss = needSpeechPermiss;
        _autoPlayer = autoPlayer;
        FinishBtnClick = true;
        CheckSelfShown();

        SetNewHandOpen(false);

        onRecordComplete = sendCallback;
        onRecordCanceled = cancelCallback;
        onRecordStart = recordingCallback;
        ClickMicrophoneAction = clickMicrophoneAction;
        ClickMicrophoneCompleteAction = clickMicrophoneCompleteAction;
        this.shouldCountdown = shouldCountdown;
        this.ui.spineHand.visible = isPlayHandSpine;
        this.ui.socialNewLoading.visible = false;
        this.ui.socialNewCon.visible = false;
        //可能是RecordUIStatusRecord, RecordUIStatusNextAndRepeat, RecordUIStatusFinishAndRepeat
        if (show)
        {
            if (preStatus != RecordUIStatus.RecordUIStatusNext && preStatus != RecordUIStatus.RecordUIStatusFinish)
            {
                ChangeStatus(RecordUIStatus.RecordUIStatusRecord);
            }
        }

    }

    public void Lock()
    {
        ChangeStatus(RecordUIStatus.Lock);
    }

    public void ShowGuideFreeMic()
    {
        ui.btnGuideMicrophone.visible = true;
    }

    public void HideGuideFreeMic()
    {
        ui.btnGuideMicrophone.visible = false;
    }

    public void HideNewRecordText()
    {
        // this.ui.grpTips.visible = false;
    }

    private IEnumerator CheckHoldTime()
    {
        while (_isPressed)
        {
            _holdTime += Time.deltaTime;

            if (_holdTime >= 2f)
            {
                // 开始震动提示
                for (int i = 0; i < 2; i++)
                {
                    VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Pop);
                    VFDebug.Log("CheckHoldTime");
                    yield return new WaitForSeconds(0.1f); // 短震动间隔
                }

                // 等待2秒再继续检查
                yield return new WaitForSeconds(2f);
            }

            yield return null; // 等待下一帧
        }
    }

    public void SetMicrophoneVisible(bool visible)
    {
        Debug.Log("SetMicrophoneVisible " + visible);

        if (visible)
        {
            ui.btnMicrophone.scale = Vector2.one;
            ui.spineHand.scale = Vector2.one;
        }
        else
        {
            ui.btnMicrophone.scale = Vector2.zero;
            ui.spineHand.scale = Vector2.zero;
        }
        // ChangeStatus(visible
        //     ? preStatus == RecordUIStatus.RecordUIStatusNone ? RecordUIStatus.RecordUIStatusRecord : preStatus
        //     : RecordUIStatus.RecordUIStatusNone);
    }

    public Vector2 getMicrophoneSize()
    {
        return ui.btnMicrophone.size;
    }

    public Vector2 getMicrophonePositon()
    {
        Vector2 pos = ui.btnMicrophone.parent.LocalToGlobal(ui.btnMicrophone.position);
        return pos;
    }


    public void ShowNext(bool show, Action nextCallback = null)
    {
        nextAction = nextCallback;

        //RecordUIStatusNext, RecordUIStatusNextAndRepeat
        if (show)
        {
            if (preStatus == RecordUIStatus.RecordUIStatusRecord || preStatus == RecordUIStatus.RecordUIStatusRepeat)
            {
                // ChangeStatus(RecordUIStatus.RecordUIStatusNextAndRepeat);
            }
            else
            {
                ChangeStatus(RecordUIStatus.RecordUIStatusNext);
            }
        }
        else
        {
            if (preStatus == RecordUIStatus.RecordUIStatusNext)
            {
                ChangeStatus(RecordUIStatus.RecordUIStatusNone);
            }
            else if (preStatus == RecordUIStatus.RecordUIStatusNextAndRepeat)
            {
                ChangeStatus(RecordUIStatus.RecordUIStatusRepeat);
            }

        }
    }

    public void ShowRepeat(Action recordCallback, bool _isFollow = false)
    {
        repeatRecordingAction = recordCallback;

        isFollow = _isFollow;

        ChangeStatus(RecordUIStatus.speakRecordrepeart);
    }

    public void ShowNextAndRepeat(Action recordCallback, Action nextCallback)
    {
        repeatRecordingAction = recordCallback;
        nextAction = nextCallback;
        ChangeStatus(RecordUIStatus.RecordUIStatusNextAndRepeat);
    }

    public void ChangeToNormal()
    {
        ChangeStatus(RecordUIStatus.RecordUIStatusRecord);
    }

    public void ShowFinish(bool show, Action finishCallback = null, bool toFinish = false)
    {
        endAction = finishCallback;
        if (toFinish)
        {
            ChangeStatus(RecordUIStatus.RecordUIStatusFinish);
            return;
        }

        //RecordUIStatusFinish, RecordUIStatusFinishAndRepeat
        if (show)
        {
            if (preStatus == RecordUIStatus.RecordUIStatusRecord || preStatus == RecordUIStatus.RecordUIStatusRepeat)
            {
                // ChangeStatus(RecordUIStatus.RecordUIStatusFinishAndRepeat);
            }
            else
            {
                ChangeStatus(RecordUIStatus.RecordUIStatusFinish);
            }
        }
        else
        {
            if (preStatus == RecordUIStatus.RecordUIStatusFinish)
            {
                ChangeStatus(RecordUIStatus.RecordUIStatusNone);
            }
            else if (preStatus == RecordUIStatus.RecordUIStatusFinishAndRepeat)
            {
                ChangeStatus(RecordUIStatus.RecordUIStatusRepeat);
            }

        }
    }

    public void ShowFinishAndRepeat(Action recordingCallback, Action finishCallback)
    {
        repeatRecordingAction = recordingCallback;
        endAction = finishCallback;
        if (ui.recordTypeCtrl.selectedPage == "socialChat" || ui.recordTypeCtrl.selectedPage == "introduce")
            ChangeStatus(RecordUIStatus.SocialFinishAndRepeat);
        else
            ChangeStatus(RecordUIStatus.RecordUIStatusFinishAndRepeat);
    }

    public void ShowRecordLoading()
    {
        ChangeStatus(RecordUIStatus.Loading);
    }

    public void ShowAutoHide(int durationTime)
    {
        ChangeStatus(RecordUIStatus.RecordUIStatusWaiting);
        _durationAutoTime = durationTime - 1;
        if (_durationAutoTime <= 5)
            ui.ctrlCountDown.selectedPage = "ShowSingleRedNum";
        else if (_durationAutoTime < 10)
            ui.ctrlCountDown.selectedPage = "ShowSingleNum";
        else
            ui.ctrlCountDown.selectedPage = "ShowDoubleNum";
        FollowCountDown(_durationAutoTime);
        _followTimerKey = this.RegisterTimer(c =>
        {
            _durationAutoTime--;
            FollowCountDown(_durationAutoTime);
        }, 1000, _durationAutoTime);
    }

    //跟读倒计时
    private void FollowCountDown(int countDown)
    {
        if (countDown <= 5)
        {
            if (countDown == 5)
                ui.ctrlCountDown.selectedPage = "ShowSingleRedNum";
            ui.singleRedNumCom.tfSingleRedNum.text = countDown.ToString();
            ui.singleRedNumCom.com.scale = new Vector2(1.2f, 1.2f);
            ui.singleRedNumCom.com.TweenScale(Vector2.one, 1f);
        }
        else if (countDown < 10)
        {
            if (countDown == 9)
                ui.ctrlCountDown.selectedPage = "ShowSingleNum";
            ui.tfSingleNum.text = countDown.ToString();
        }
        else
        {
            ui.tfDoubleNum.text = countDown.ToString();
        }
    }

    public void HideAutoHide()
    {
        ClearStatusAndTimer();
    }

    public void ClearStatusAndTimer()
    {
        ChangeStatus(RecordUIStatus.RecordUIStatusNone);
        preStatus = RecordUIStatus.RecordUIStatusNone;
        if (!string.IsNullOrEmpty(_followTimerKey))
        {
            UnRegisterTimer(_followTimerKey);
            _followTimerKey = string.Empty;
        }
    }

    public void StashState()
    {
        _stashStatus = preStatus;
    }

    public void RestoreState()
    {
        preStatus = _stashStatus;
        ChangeStatus(preStatus);
        _stashStatus = RecordUIStatus.RecordUIStatusNone;
    }

    public void CancelRecording()
    {
        if (preStatus == RecordUIStatus.RecordUIStatusSend || preStatus == RecordUIStatus.NewSend)
        {
            if (onRecordCanceled != null)
                onRecordCanceled(new DownsideEvent() { name = "CancelRecording" });
            ChangeStatus(RecordUIStatus.RecordUIStatusRecord);
        }

    }

    public RecordUIStatus FetchRecordStatus()
    {
        return preStatus;
    }

    private IEnumerator WaitAndPlayAnimation()
    {
        while (true)
        {
            // 等待 5 秒
            yield return new WaitForSeconds(_waitBulbTime);

            // 播放动画
            this.ui.spineBulb.spineAnimation.AnimationState.SetAnimation(0, "1", false);
            this.ui.spineBulb.playing = true;
            this.ui.btnBulbLight.alpha = 0.1f;
            yield return new WaitForSeconds(6.333f);
            this.ui.btnBulbLight.alpha = 1f;
        }

    }

    private void StopBulbAnim()
    {
        if (_waitingCoroutine != null)
        {
            Timers.inst.StopCoroutine(_waitingCoroutine);
            _waitingCoroutine = null;
            VFDebug.Log("111 StopBulbAnim");
        }

        VFDebug.Log("StopBulbAnim");
    }

    public void ShowBulbLight(Action action)
    {
        if (_waitingCoroutine == null)
        {
            _waitingCoroutine = Timers.inst.StartCoroutine(WaitAndPlayAnimation());
        }

        bulbAction = action;
        this.ui.socialNewCon.visible = false;
        this.ui.grpBulb.visible = true;
        this.ui.spineBulb.visible = true;
        this.ui.btnBulbLight.visible = true;
        this.ui.btnBulbLight.alpha = 1f;
        this.ui.spineBulb.loop = false;
        ui.spineBulb.playing = false;
        // this.ui.spineBulb.animationName = "1";

        // this.ui.spineBulb.spineAnimation.AnimationState.SetAnimation(0,"1",false);
        bulbStatus = BulbStatus.Waiting;
        VFDebug.Log("ShowBulbLight");
    }

    public void ShowBulbLoading()
    {
        bulbStatus = BulbStatus.Loading;
        VFDebug.Log("ShowBulbLoading");
        this.ui.socialNewCon.visible = false;
        this.ui.spineBulb.visible = false;
        this.ui.btnBulbLight.visible = false;
        this.ui.btnBulbLight.alpha = 1;
        this.ui.spineBulb.spineAnimation.AnimationState.ClearListenerNotifications();
        this.ui.spineBulb.spineAnimation.AnimationState.SetAnimation(0, "2", false).Complete +=
            (t) =>
            {
                this.ui.spineBulb.spineAnimation.AnimationState.SetAnimation(0, "3", true);
                this.ui.spineBulb.spineAnimation.AnimationState.ClearListenerNotifications();
            };
        VFDebug.Log("ShowBulbLoading");
    }

    #region 新脚手架

    /// <summary>
    /// action : 0 active  1 脚手架
    /// </summary>
    private BulbType _isScaffoldOrActive = BulbType.Active;
    /// <summary>
    /// action : 0 脚手架  1 active
    /// </summary>
    /// <param name="action"></param>
    public void ShowBulbNew(Action<BulbType, bool> action, BulbType type)
    {
        _isScaffoldOrActive = type;
        bulbActionNew = action;
        ui.socialNewLoading.visible = false;
        this.ui.socialNewCon.visible = true;
        bulbStatusNew = BulbStatusNew.Normal;
        this.ui.socialNew.spineAnimation.AnimationState.SetAnimation(0, "2", false);
    }
    public void ShowBulbLoadingNew()
    {
        bulbStatusNew = BulbStatusNew.Loading;
        this.ui.socialNewCon.visible = false;
        ui.socialNewLoading.visible = true;
    }

    public void ShowBulbScaffoldNew()
    {
        _isScaffoldOrActive = BulbType.Scaffold;
        bulbStatusNew = BulbStatusNew.Scaffold;
        ui.socialNewLoading.visible = false;
        this.ui.socialNewCon.visible = true;
        this.ui.socialNew.spineAnimation.AnimationState.SetAnimation(0, "1", false);
    }

    public void ShowBulbActiveNew()
    {
        _isScaffoldOrActive = BulbType.Active;
        bulbStatusNew = BulbStatusNew.Active;
        ui.socialNewLoading.visible = false;
        this.ui.socialNewCon.visible = true;
        this.ui.socialNew.spineAnimation.AnimationState.SetAnimation(0, "2", false);
    }

    public void HideBulbNew()
    {
        bulbStatusNew = BulbStatusNew.Hide;
        this.ui.socialNewCon.visible = false;
        ui.socialNewLoading.visible = false;
    }

    #endregion


    public void HideBulbLight()
    {
        VFDebug.Log("HideBulbLight");
        this.ui.btnBulbLight.visible = false;
        this.ui.btnBulbLight.alpha = 1;
    }

    public void HideAllBulb()
    {
        this.ui.spineBulb.animationName = "1";
        this.ui.grpBulb.visible = false;
        this.ui.spineBulb.loop = false;
        this.ui.spineBulb.spineAnimation.AnimationState.ClearListenerNotifications();
        ui.spineBulb.playing = false;
        bulbStatus = BulbStatus.Hide;
        VFDebug.Log("HideAllBulb");
    }

    public void ShowCompleteTips(bool show, string languageKey = "ui_record_finish")
    {
        ui.tfFinish.SetKey(languageKey);
        ui.grpSendCompleteTips.visible = show;
        VFDebug.Log("ShowCompleteTips " + show);
    }

    public void HideRecordButton()
    {
        ChangeStatus(RecordUIStatus.RecordUIStatusNone);
    }

    private void ChangeStatus(RecordUIStatus status)
    {
        CheckSelfShown();

        RemoveTimer();
        // Debug.LogError("[RecordUI] will change status to: " + status + Environment.StackTrace);
        if (this.isShow)
        {
            Debug.Log("[RecordUI](showing) did change status to: " + status);
            this.ui.ctlStatus.selectedIndex = (int)status;
            _recordFinishAction = null;
        }
        else
        {
            Debug.Log("[RecordUI](not show) did change status to: " + status);
        }

        if (status == RecordUIStatus.RecordUIStatusRecord)
        {
            ui.btnMicrophone.scale = Vector2.one;
            ui.spineHand.scale = Vector2.one;
        }

        if (status == RecordUIStatus.RecordUIStatusRecord)
        {
            if (this.ui.btnBulbLight.visible)
            {
                if (_waitingCoroutine == null)
                {
                    _waitingCoroutine = Timers.inst.StartCoroutine(WaitAndPlayAnimation());
                }
            }
        }

        preStatus = status;
    }

    private void ClickRecordForRecordState()
    {
        // Debug.LogError("开始录制");
        //社交点击
        if (ui.recordTypeCtrl.selectedIndex == 1)
        {
            VFDebug.Log("SocialChat RecordClick");
            var socialChatCtrl = GetController<SocialChatController>(ModelConsts.SocialChat);
            socialChatCtrl.ChangeState(SocialRecordState.Recording, SocialStateMachineType.Record);
            return;
        }
        else if (ui.recordTypeCtrl.selectedIndex == 2)
        {
            VFDebug.Log("Introduce RecordClick");
            var socialCardCtrl = GetController<SocialCardController>(ModelConsts.SocialCard);
            socialCardCtrl.ChangeState(IntroduceState.Recording);
            return;
        }

        if (bulbStatus == BulbStatus.Waiting)
        {
            StopBulbAnim();
        }

        ClickRecord();
    }

    private bool Recording()
    {
        // 移除对Microphone的直接调用
        // Debug.Log("RecordUI ClickRecordForRecordState " + Application.HasUserAuthorization(UserAuthorization.Microphone));
        Debug.Log("RecordUI ClickRecordForRecordState");

        //判断是否是第一次出现
        if (!GMicrophoneManager.instance.HasMicrophonePermission() && !_settingModel.isAppearMicPermission)
        {
            RequestMicrophonePermissionAsync();
            _settingModel.SetMicrophonePermission(true);
            return false;
        }

        if (!GetController<PermissMsgController>(ModelConsts.PermissMsg).CheckPermissMic())
        {
            return false;
        }

        if (_needSpeechPermiss)
        {
#if UNITY_IOS && !UNITY_EDITOR
            if(OsFuncAdapter.Ins.IsSpeechAuthorizationNotDetermined())
            {
                OsFuncAdapter.Ins.requestSpeechPermission();
                return false;
            }
            else if (OsFuncAdapter.Ins.HasUserDeniedSpeechPermission())
            {
                ControllerManager.instance.GetController<PermissMsgController>(ModelConsts.PermissMsg).OpenPermissSpeech();
                return false;
            }            
#endif
        }

        if (ClickMicrophoneAction != null)
        {
            ClickMicrophoneAction();
        }

        ShowGuideTip(false);
        if (onRecordStart != null)
            onRecordStart();
        return true;
    }

    private void ClickRecord()
    {
        bool result = Recording();
        if (!result) return;
        if (!_isApplyNewSendStyle)
        {
            _recordFinishAction = () =>
            {
                ChangeStatus(RecordUIStatus.RecordUIStatusSend);
                SoundManger.instance.PlayUI("audio_record_record");
                if (shouldCountdown)
                {
                    int totalSeconds = maxRecordingSeconds;
                    timerKey = this.RegisterTimer(c =>
                    {
                        totalSeconds--;
                        CountDown(totalSeconds);
                    }, 1000, maxRecordingSeconds);
                }
                else
                {
                    RemoveTimer();
                }
            };
            PlayButtonAnimation(true);

        }
        else
        {
            VFDebug.Log("SocialChat  NewSend");
            if (_autoPlayer)
            {
                ChangeStatus(RecordUIStatus.TestSend);
            }
            else
            {
                ChangeStatus(RecordUIStatus.NewSend);
            }

            SoundManger.instance.PlayUI("audio_record_record");
            if (shouldCountdown)
            {
                int totalSeconds = 0;
                Color nowColor = Color.black;
                ColorUtility.TryParseHtmlString("#111111", out nowColor);
                ui.tfTime.color = nowColor;
                ui.tfTime.text = string.Format("{0}s", totalSeconds.ToString());
                timerKey = this.RegisterTimer(c =>
                {
                    totalSeconds++;
                    NewCountDown(totalSeconds);
                }, 1000, maxRecordingSeconds);
            }
            else
            {
                RemoveTimer();
            }
        }

        ChatModel chatModel = GetModel<ChatModel>(ModelConsts.Chat);
        //新埋点：对话进入语言录入
        DataDotClickMacStart dot = new DataDotClickMacStart();
        dot.Dialogue_id = DataDotMgr.GetDialogId();
        dot.Dialogue_round = chatModel.curRoundId;
        var model = GetModel<RecommendCardModel>(ModelConsts.RecommendCard);
        if (model != null)
        {
            dot.Task_id = model.CurTaskID;
        }

        PB_DialogMode chatMode = chatModel.chatMode;
        dot.Task_type = "Task";
        if (chatMode == PB_DialogMode.WarmupPractice || chatMode == PB_DialogMode.Intensify || chatMode == PB_DialogMode.MistakeExercise)
        {
            dot.Task_type = "Warmup";
            PB_Question question = GetModel<ReviewQuestionModel>(ModelConsts.ReviewQuestionModel).CurrentQuestion().question;

            dot.question_id = question.question_id;
            dot.Knowledge_index = question.question_round_id;
            dot.Dialogue_round = GetModel<ReviewQuestionModel>(ModelConsts.ReviewQuestionModel).questionIndex;
            dot.Dialogue_type = GetModel<GuideModel>(ModelConsts.Guide).markInfo.is_question_auto ? "Auto" : "Manual";

        }
        else if (GetController<ChatLogicController>(ModelConsts.ChatLogic).CurDialogMode == PB_DialogMode.Career)
        {
            dot.Task_type = "Free_talk";
        }
        else if (GetController<ChatLogicController>(ModelConsts.ChatLogic).CurDialogMode == PB_DialogMode.Tutor)
        {
            dot.Task_type = "tutor";
        }
        else if (GetController<ChatLogicController>(ModelConsts.ChatLogic).CurDialogMode == PB_DialogMode.Challenge)
        {
            dot.Task_type = "challenge";
        }
        else if (GetController<ChatLogicController>(ModelConsts.ChatLogic).CurDialogMode == PB_DialogMode.RolePlay)
        {
            dot.Task_type = "roleplay2.0";
        }
        else if (GetController<ChatLogicController>(ModelConsts.ChatLogic).CurDialogMode == PB_DialogMode.Flash)
        {
            dot.Task_type = "aua";
        }

        dot.Help_mode = chatModel.UserAssistLevel == PB_UserAssistLevelEnum.UserAssistLevelHelpAutoOpen
            ? "Auto"
            : "Manual";


        DataDotMgr.Collect(dot);
    }

    private void CountDown(int remainTimes)
    {
        if (remainTimes <= 0)
        {
            ui.tfCountDown.visible = false;
            RemoveTimer();

            ChangeStatus(RecordUIStatus.RecordUIStatusNone);

            onRecordComplete?.Invoke(new DownsideEvent() { name = "TimeOut" });

            if (ClickMicrophoneCompleteAction != null)
            {
                ClickMicrophoneCompleteAction();
                ClickMicrophoneCompleteAction = null;
            }
        }
        else if (remainTimes <= 10)
        {
            ui.tfCountDown.visible = true;
            ui.tfCountDown.text = string.Format("{0}s", remainTimes);
        }
    }

    private void RemoveTimer()
    {
        if (timerKey != null)
        {
            this.UnRegisterTimer(timerKey);
            timerKey = null;
        }
    }


    public int CurTime = 0;
    private void NewCountDown(int times)
    {
        CurTime = times;
        Color nowColor = Color.black;
        if (times >= 0 && times <= maxRecordingSeconds - 10)
        {
            ColorUtility.TryParseHtmlString("#111111", out nowColor);
        }
        else if (times <= maxRecordingSeconds)
        {
            ColorUtility.TryParseHtmlString("#FF007A", out nowColor);
        }

        // string str = times <10?("0"+times):times.ToString();
        // ui.tfTime.text = string.Format("{0}s", str.ToString());
        ui.tfTime.text = string.Format("{0}s", times.ToString());
        ui.tfTime.color = nowColor;
        if (times == maxRecordingSeconds)
        {
            RemoveTimer();

            ChangeStatus(RecordUIStatus.RecordUIStatusNone);
            if (TimeOutAction != null)
                TimeOutAction();

            onRecordComplete?.Invoke(new DownsideEvent() { name = "NewTimeOut" });

            if (ClickMicrophoneCompleteAction != null)
            {
                ClickMicrophoneCompleteAction();
                ClickMicrophoneCompleteAction = null;
            }
        }
    }

    private void OnButtonMicBegin()
    {
        if (_isApplyNewSendStyle)
        {
            _isPressed = true;
            this.ui.micMask.visible = true;
            _holdTime = 0f;
            // 启动协程来监测按住状态
            StopVibrateCoroutine();
            _VibrateCoroutine = Timers.inst.StartCoroutine(CheckHoldTime());
        }

    }

    private void OnButtonnMicEnd()
    {
        if (_isApplyNewSendStyle)
        {
            _isPressed = false;
            this.ui.micMask.visible = false;
            StopVibrateCoroutine();
        }
    }

    private void StopVibrateCoroutine()
    {
        if (_VibrateCoroutine != null)
        {
            Timers.inst.StopCoroutine(_VibrateCoroutine);
            _VibrateCoroutine = null;
        }
    }

    private void OnClickSend()
    {
        FinishRecording(true);
        onRecordComplete?.Invoke(new DownsideEvent() { name = "OnClickSend" });
    }

    public void RefreshState()
    {
        ChangeStatus(preStatus);
        if (!GMicrophoneManager.instance.IsRecording)
        {
            FinishRecording(false);
            onRecordComplete?.Invoke(new DownsideEvent() { name = "RefreshState" });
        } else
        {
            Recording();
        }
    }

    // 纯刷界面逻辑，不处理任何上下行事件
    /***
     * isDirectlyTriggeredByUser： 有很多触发结束 rec的时候，仅用户click send的需要触发音效
     */
    private void FinishRecording( bool isDirectlyTriggeredByUser)
    {
        Debug.Log("FinishRecording _isNewSend="+_isApplyNewSendStyle);
        if (!_isApplyNewSendStyle)
        {
            ui.btnCancel.com.visible = false;
            
            _recordFinishAction = () =>
            {
                ui.btnCancel.com.visible = true;

                // ChangeStatus(RecordUIStatus.RecordUIStatusNone);
                if (isDirectlyTriggeredByUser)
                {
                    SoundManger.instance.PlayUI("audio_record_send");
                }

                
                
                RemoveTimer();
            };
            PlayButtonAnimation(false);
        }
        else
        {
            // ChangeStatus(RecordUIStatus.RecordUIStatusNone);
            ui.grpSendTips.visible = true;
            if (isDirectlyTriggeredByUser)
            {
                SoundManger.instance.PlayUI("audio_record_send");
            }
            RemoveTimer();
        }

        if (ClickMicrophoneCompleteAction != null)
        {
            ClickMicrophoneCompleteAction();
            ClickMicrophoneCompleteAction = null;
        }
        DataCollect();
    }

    private void DataCollect()
    {
        ChatModel chatModel = GetModel<ChatModel>(ModelConsts.Chat);
        //新埋点：对话语音发送
        DataDotClickMacSend dot = new DataDotClickMacSend();
        dot.Dialogue_id = DataDotMgr.GetDialogId();
        dot.Dialogue_round = chatModel.curRoundId;
        PB_DialogMode chatMode = chatModel.chatMode;
        dot.Task_type = "Task";
        if (chatMode == PB_DialogMode.WarmupPractice || chatMode == PB_DialogMode.Intensify || chatMode == PB_DialogMode.MistakeExercise)
        {
            dot.Task_type = "Warmup";
            PB_Question question = GetModel<ReviewQuestionModel>(ModelConsts.ReviewQuestionModel).CurrentQuestion().question;

            dot.question_id = question.question_id;
            dot.Knowledge_index = question.question_round_id;
            dot.Dialogue_round = GetModel<ReviewQuestionModel>(ModelConsts.ReviewQuestionModel).questionIndex;
            dot.Dialogue_type = GetModel<GuideModel>(ModelConsts.Guide).markInfo.is_question_auto ? "Auto" : "Manual";

        }
        else if (GetController<ChatLogicController>(ModelConsts.ChatLogic).CurDialogMode == PB_DialogMode.Career)
        {
            dot.Task_type = "Free_talk";
        }
        else if (GetController<ChatLogicController>(ModelConsts.ChatLogic).CurDialogMode == PB_DialogMode.Tutor)
        {
            dot.Task_type = "tutor";
        }
        else if (GetController<ChatLogicController>(ModelConsts.ChatLogic).CurDialogMode == PB_DialogMode.Challenge)
        {
            dot.Task_type = "challenge";
        }
        else if (GetController<ChatLogicController>(ModelConsts.ChatLogic).CurDialogMode == PB_DialogMode.RolePlay)
        {
            dot.Task_type = "roleplay2.0";
        }
        else if (GetController<ChatLogicController>(ModelConsts.ChatLogic).CurDialogMode == PB_DialogMode.Flash)
        {
            dot.Task_type = "aua";
        }

        dot.Help_mode = chatModel.UserAssistLevel == PB_UserAssistLevelEnum.UserAssistLevelHelpAutoOpen
            ? "Auto"
            : "Manual";

        DataDotMgr.Collect(dot);
    }


    public void SetSendTipsVisible(bool value)
    {
        ui.grpSendTips.visible = value;
    }

    private void ClickSpeakCancel()
    {
        onRecordCanceled?.Invoke(new DownsideEvent() { name = "ClickSpeakCancel" });
        ChangeStatus(RecordUIStatus.speakMic);
    }

    public void ClickCancel()
    {
        if (!_isApplyNewSendStyle)
        {
            ui.btnCancel.com.visible = false;

            _recordFinishAction = () =>
            {
                ui.btnCancel.com.visible = true;

                ChangeStatus(RecordUIStatus.RecordUIStatusNone);
                onRecordCanceled?.Invoke(new DownsideEvent() { name = "ClickCancel" });
                RemoveTimer();
            };
            PlayButtonAnimation(false);
        }
        else
        {
            ChangeStatus(RecordUIStatus.RecordUIStatusNone);
            onRecordCanceled?.Invoke(new DownsideEvent() { name = "ClickCancel" });
            RemoveTimer();
        }

        VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);

        //新埋点：对话录音取消
        ChatModel chatModel = GetModel<ChatModel>(ModelConsts.Chat);
        DataDotClickMacCancel dot = new DataDotClickMacCancel();
        dot.Dialogue_id = DataDotMgr.GetDialogId();
        dot.Dialogue_round = chatModel.curRoundId;
        var model = GetModel<RecommendCardModel>(ModelConsts.RecommendCard);
        if (model != null)
        {
            dot.Task_id = model.CurTaskID;
        }
        PB_DialogMode chatMode = chatModel.chatMode;
        dot.Task_type = "Task";
        if (chatMode == PB_DialogMode.WarmupPractice)
        {
            dot.Task_type = "Warmup";
            PB_Question question = GetModel<ReviewQuestionModel>(ModelConsts.ReviewQuestionModel).CurrentQuestion().question;
            dot.question_id = question.question_id;
            dot.Knowledge_index = question.question_round_id;

        }
        else if (GetController<ChatLogicController>(ModelConsts.ChatLogic).CurDialogMode == PB_DialogMode.Career)
        {
            dot.Task_type = "Free_talk";
        }
        else if (GetController<ChatLogicController>(ModelConsts.ChatLogic).CurDialogMode == PB_DialogMode.Tutor)
        {
            dot.Task_type = "tutor";
        }
        else if (GetController<ChatLogicController>(ModelConsts.ChatLogic).CurDialogMode == PB_DialogMode.Challenge)
        {
            dot.Task_type = "challenge";
        }
        else if (GetController<ChatLogicController>(ModelConsts.ChatLogic).CurDialogMode == PB_DialogMode.RolePlay)
        {
            dot.Task_type = "roleplay2.0";
        }
        else if (GetController<ChatLogicController>(ModelConsts.ChatLogic).CurDialogMode == PB_DialogMode.Flash)
        {
            dot.Task_type = "aua";
        }

        dot.Help_mode = chatModel.UserAssistLevel == PB_UserAssistLevelEnum.UserAssistLevelHelpAutoOpen
            ? "Auto"
            : "Manual";
        DataDotMgr.Collect(dot);
    }


    private void Repeating()
    {
        Debug.Log("RecordUI ClickRepeat " + Application.HasUserAuthorization(UserAuthorization.Microphone));
        //判断是否是第一次出现
        if (!GMicrophoneManager.instance.HasMicrophonePermission() && !_settingModel.isAppearMicPermission)
        {
            RequestMicrophonePermissionAsync();
            _settingModel.SetMicrophonePermission(true);
            return;
        }

        if (!GetController<PermissMsgController>(ModelConsts.PermissMsg).CheckPermissMic())
        {
            return;
        }
        SoundManger.instance.PlayUI("audio_record_record");
    }
    //点击重说按钮
    private void ClickRepeat()
    {
        Repeating();

        ChangeStatus(RecordUIStatus.RecordUIStatusNone);

        if (repeatRecordingAction != null)
        {
            repeatRecordingAction();
        }

        //新埋点：warmup中重说
        DataDotDialogueRepeat dot = new DataDotDialogueRepeat();
        dot.Dialogue_id = DataDotMgr.GetDialogId();
        DataDotMgr.Collect(dot);
    }


    private void OnClickSpeakEnd()
    {
        var evt = new DownsideEvent() { name = "OnClickSpeakEnd" };
        FinishRecording(true);
        onRecordComplete?.Invoke(evt);
    }

    private void ClickSpeakRepeat()
    {
        //
        ChangeStatus(RecordUIStatus.speakRecord);
        if (isFollow)
        {
            Recording();//follow时tryagain也要进行录因

        }
        else
        {
            Recording();
        }

        if (repeatRecordingAction != null)
        {
            repeatRecordingAction();
        }
    }


    private void ClickNext()
    {
        ChangeStatus(RecordUIStatus.RecordUIStatusNone);
        SoundManger.instance.PlayUI("audio_record_record");
        if (nextAction != null)
        {
            nextAction();
        }

        //新埋点：点击next
        DataDotDialogueNext dot = new DataDotDialogueNext();
        dot.Dialogue_id = DataDotMgr.GetDialogId();
        var model = GetModel<RecommendCardModel>(ModelConsts.RecommendCard);
        if (model != null)
        {
            dot.Task_id = model.CurTaskID;
        }
        PB_DialogMode chatMode = this.GetModel<ChatModel>(ModelConsts.Chat).chatMode;
        dot.Task_type = "Task";
        if (chatMode == PB_DialogMode.WarmupPractice || chatMode == PB_DialogMode.Intensify || chatMode == PB_DialogMode.MistakeExercise)
        {
            dot.Task_type = "Warmup";
            PB_Question question = GetModel<ReviewQuestionModel>(ModelConsts.ReviewQuestionModel).CurrentQuestion().question;
            dot.Knowledge_id = question.question_id;
            dot.Knowledge_index = question.question_round_id;
            dot.Dialogue_round = GetModel<ReviewQuestionModel>(ModelConsts.ReviewQuestionModel).questionIndex;
            dot.Dialogue_type = GetModel<GuideModel>(ModelConsts.Guide).markInfo.is_question_auto ? "Auto" : "Manual";
        }
        else if (chatMode == PB_DialogMode.Career)
        {
            dot.Task_type = "Free_talk";
        }
        DataDotMgr.Collect(dot);
    }


    private void ClickFinish()
    {
        if (!FinishBtnClick) return;
        ChangeStatus(RecordUIStatus.RecordUIStatusNone);
        SoundManger.instance.PlayUI("audio_record_record");
        if (endAction != null)
        {
            endAction();
        }

        //新埋点：点击end
        DataDotDialogueEnd dot = new DataDotDialogueEnd();
        dot.Dialogue_id = DataDotMgr.GetDialogId();
        dot.Dialogue_round = GetModel<ChatModel>(ModelConsts.Chat).curRoundId;
        DataDotMgr.Collect(dot);
    }

    private void ClickBulb()
    {
        if (_isNewSocial)
        {
            // if (bulbStatusNew != BulbStatusNew.Normal) return;
            // ShowBulbLoadingNew();

            if (bulbActionNew != null)
            {
                if (_isScaffoldOrActive == BulbType.Active)
                {
                    _isScaffoldOrActive = BulbType.Scaffold;
                }
                else
                {
                    _isScaffoldOrActive = BulbType.Active;
                }
                bulbActionNew(_isScaffoldOrActive, true);
            }
        }
        else
        {
            ShowBulbLoading();
        }


        if (bulbAction != null)
        {
            bulbAction();
        }

        StopBulbAnim();

        ChatModel chatModel = GetModel<ChatModel>(ModelConsts.Chat);
        DataDotClick_Help dot = new DataDotClick_Help();
        dot.Dialogue_id = DataDotMgr.GetDialogId();
        dot.Dialogue_round = GetModel<ChatModel>(ModelConsts.Chat).curRoundId;
        dot.AUA_dialogue_round = GetModel<ChatLogicModel>(ModelConsts.ChatLogic).CurQuestionRoundId;
        dot.AUA_question_id = GetModel<ChatLogicModel>(ModelConsts.ChatLogic).GetCurSectionId();
        dot.Help_mode = chatModel.UserAssistLevel == PB_UserAssistLevelEnum.UserAssistLevelHelpAutoOpen
            ? "Auto"
            : "Manual";
        DataDotMgr.Collect(dot);
    }


    private void CheckSelfShown()
    {
        if (!this.isShow)
        {
            shouldShowInNextFrame = true;
            this.Show();
        }
    }

    public void SetHeightUI(int num)
    {
        ui.com.height += num;
    }

    public void DismissWithAnimation()
    {
        if (isShow)
            ui.Out.Play(() => { });
    }

    public void RevertAfterAnimation()
    {
        ui.Reset.Play(() => { });
    }

    public void StopBulbWaitAnim()
    {
        // VFDebug.LogError("ui.spineBulb.animationName "+ui.spineBulb.animationName);
        if (bulbStatus == BulbStatus.Waiting)
        {
            ui.spineBulb.playing = false;
            ui.btnBulbLight.alpha = 1;
            VFDebug.Log("StopBulbWaitAnim");
        }
    }

    private IEnumerator CheckRecordPermission1()
    {
        // 通过GMicrophoneManager检查权限
        yield return GMicrophoneManager.instance.RequestMicrophonePermission();
        GetController<PermissMsgController>(ModelConsts.PermissMsg).CheckPermissMic();
    }

    private async UniTask RequestMicrophonePermissionAsync()
    {
        // 通过GMicrophoneManager请求权限
        await GMicrophoneManager.instance.RequestMicrophonePermissionAsync();
    }

    private bool CheckRecordPermission()
    {
        bool retVal = true;

        if (GMicrophoneManager.instance.HasMicrophonePermission())
        {
            if (permissionAction != null)
            {
                permissionAction(true);
            }
            retVal = true;
        }
        else
        {
            RequestMicrophonePermission();

            if (GMicrophoneManager.instance.HasMicrophonePermission())
            {
                if (permissionAction != null)
                {
                    permissionAction(true);
                }

                retVal = true;
            }
            else
            {
                if (permissionAction != null)
                {
                    permissionAction(false);
                }

                //PopupPrivacyAlert();
                GetController<PermissMsgController>(ModelConsts.PermissMsg).CheckPermissMic();

                retVal = false;
            }
        }

        return retVal;
    }

    private IEnumerator RequestMicrophonePermission()
    {
        yield return GMicrophoneManager.instance.RequestMicrophonePermission();
    }

    private void PopupPrivacyAlert()
    {
        this.GetUI<CommConfirmUI>(UIConsts.CommConfirm)
            .OpenI18N("ios_permission_mic", ChooseAlertCancel, ChooseGoSettings, 2, "alert_cancel", "alert_setting");
    }

    private void ChooseAlertCancel()
    {
        this.GetUI<CommConfirmUI>(UIConsts.CommConfirm).Hide();
    }

    private void ChooseGoSettings()
    {
        int a = DeviceAdapter.OpenSystemSettings();
        Debug.Log("ChooseGoSettings: " + a);
    }

    private void ConfigUI()
    {
        ui.tfNext.SetKey("avatar_talk_button_next");
        ui.tfFin.SetKey("avatar_talk_button_end");
    }

    private void PlayButtonAnimation(bool isExtension)
    {
        var spineLoader = this.ui.spineLoader as SpinePanelExtension;
        spineLoader.SetModel(ResUtils.GetSpinePath("chatbutton"), () => { spineLoader.loop = false; });
        string animationName = isExtension ? "1" : "3";
        spineLoader.PlayWithCallback(animationName, () =>
        {
            Debug.Log("PlayWithCallback");
            spineLoader.ClearModel();

            if (_recordFinishAction != null)
            {
                _recordFinishAction();
            }
        });
    }

    //--------------------------------------------------------Social Chat----------------------------------------------------------------

    public void EnterSocialRecord()
    {
        VFDebug.Log("SocialChat RecordChangeStatus");
        ui.recordTypeCtrl.selectedIndex = 1;
        ChangeStatus(RecordUIStatus.RecordUIStatusRecord);
        ui.playerContent.text = "...";
    }

    public void ExitSocialRecord()
    {
        ui.recordTypeCtrl.selectedIndex = 0;
        ChangeStatus(RecordUIStatus.RecordUIStatusRecord);
        Hide();
    }

    public void SocialChatRegisterRecord(Action recordingCallback = null, Action<DownsideEvent> sendCallback = null,
        Action<DownsideEvent> cancelCallback = null, bool shouldCountdown = false, Action clickMicrophoneAction = null,
        Action clickMicrophoneCompleteAction = null, bool isNewSend = false,
        Action timeOutAction = null)
    {
        onRecordComplete = sendCallback;
        onRecordCanceled = cancelCallback;
        onRecordStart = recordingCallback;
        ClickMicrophoneAction = clickMicrophoneAction;
        ClickMicrophoneCompleteAction = clickMicrophoneCompleteAction;
        TimeOutAction = timeOutAction;
        this.shouldCountdown = shouldCountdown;
        _isApplyNewSendStyle = isNewSend;
        VFDebug.Log("SocialChat RegisterRecord");
        ClickRecord();
    }

    public void SetPlayerContent(string content)
    {
        ui.playerContent.text = content + "...";
    }

    public void SetAudioLength(string content)
    {
        ui.socialAudioTime.text = content + "’’";
    }

    //--------------------------------------------------------introduce----------------------------------------------------------------
    public void EnterIntroduceRecord()
    {
        VFDebug.Log("EnterIntroduceRecord ui.recordTypeCtrl.selectedIndex = 2");
        ui.recordTypeCtrl.selectedIndex = 2;
        maxRecordingSeconds = 30;
        ChangeStatus(RecordUIStatus.RecordUIStatusRecord);
        ui.playerContent.text = "...";
    }

    public void ExitIntroduceRecord()
    {
        VFDebug.Log("ExitIntroduceRecord");
        ui.recordTypeCtrl.selectedIndex = 0;
        maxRecordingSeconds = 60;
        ChangeStatus(RecordUIStatus.RecordUIStatusRecord);
        CloseAction = null;
        Hide();
    }

    public void IntroduceRegisterRecord(Action recordingCallback = null, Action<DownsideEvent> sendCallback = null,
        Action<DownsideEvent> cancelCallback = null, bool shouldCountdown = false,
        bool isNewSend = false, Action playAudioAction = null,
        Action timeOutAction = null, Action closeAction = null)
    {
        onRecordComplete = sendCallback;
        onRecordCanceled = cancelCallback;
        onRecordStart = recordingCallback;
        PlayAudioAction = playAudioAction;
        TimeOutAction = timeOutAction;
        CloseAction = closeAction;
        this.shouldCountdown = shouldCountdown;
        _isApplyNewSendStyle = isNewSend;
        VFDebug.Log("SocialChat RegisterRecord");
        ClickRecord();
    }

    public void SetAudioTime(string timeLength)
    {
        ui.socialAudioTime.text = timeLength;
    }

    private void ClickPlayAudio()
    {
        if (PlayAudioAction != null)
            PlayAudioAction();
    }

    public void PlayAudioAni(bool isPlay)
    {
        ui.socialAudioAni.playing = isPlay;
        ui.socialAudioAni.frame = 1;
    }

    public void ClickClose()
    {
        CloseAction?.Invoke();
        ExitIntroduceRecord();
    }

    public override void OnBackBtnClick()
    {
        // var mainHeaderUI = GetUI<MainHeaderUI>(UIConsts.MainHeader);
        // if (mainHeaderUI.isShow)
        // {
        //     mainHeaderUI.OnBtnLeftTopClick();
        // }
    }
    
    protected override void HandleNotification(string name, object body)
    {
        base.HandleNotification(name, body);
        switch (name)
        {
            case NotifyConsts.DismissWithAnimation:
                DismissWithAnimation();
                break;
        }
    }

    protected override string[] ListNotificationInterests()
    {
        return new[]
        {
            NotifyConsts.DismissWithAnimation,
        };
    }

    #region 录音按钮引导tip
    // public const string RecordBtnTip = "RecordUI.ExploreRecord";
    // public void CheckGuideTip()
    // {
    //     ui.grpTip.visible = !PlayerPrefs.HasKey(RecordBtnTip) ;//&& ui.ctlStatus.selectedPage == "record";
    //     Debug.Log("GuideTip -- check::" + ui.grpTip.visible);
    // }
    public void ShowGuideTip(bool enable)
    {
        ui.grpTip.visible = enable && ui.ctlStatus.selectedPage == "record";
        Debug.Log("GuideTip -- show::" + ui.grpTip.visible);
    }
    
    public void OnSendRecord()
    {
        //这个一次性引导设置的流程 v1.3.3调整后，后续设计待定
        RecordGuideTip();
    }

    private void RecordGuideTip()
    {
        //PlayerPrefs.SetInt(RecordBtnTip, 1);
    }
    #endregion


}