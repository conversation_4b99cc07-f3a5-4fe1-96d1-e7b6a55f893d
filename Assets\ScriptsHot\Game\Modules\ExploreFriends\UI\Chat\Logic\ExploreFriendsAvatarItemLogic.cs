﻿using ScriptsHot.Game.Modules.Common;
using UIBind.ExploreFriends.Component;
using UnityEngine;

namespace UIBind.ExploreFriends
{
    public class ExploreFriendsAvatarItemLogic:ItemComponentLogicBase
    {
        private string _txtStr;
        public TextFieldExtension TfOrigin =>  _com.tfOrigin as TextFieldExtension;

        private ExploreFriendsAvatarItem _com;
        
        public override void UIReady()
        {
            _com = new ExploreFriendsAvatarItem();
            _com.Construct(GetUI());
            TfOrigin.SetFormat(Color.white, 30);
        }

        public void SetData(long avatarId,string bubbleId ,long dialogId)
        {
            ExploreNewWordData newWordData = new ExploreNewWordData();
            newWordData.ui = _ui;
            newWordData.avatarId = avatarId;
            newWordData.dialogId = dialogId;
            newWordData.bubbleId = bubbleId;
            _translateWordComponent.SetData(newWordData);
        }

        public void SetTxt(string value)
        {
            _txtStr = value;
            
            var textContent = new RichContent()
            {
                Content = _txtStr,
                IsLast = true,
            };
            
            TfOrigin.Reset();
            TfOrigin.OnClickNewWord += _translateWordComponent.ReqNewWordTrans;
            TfOrigin.AppendContent(textContent).Display(ShowMode.Normal);
        }
    }
}