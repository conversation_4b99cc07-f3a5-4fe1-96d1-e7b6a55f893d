using System.Collections;
using System.Collections.Generic;
using Google.Protobuf.Collections;
using Msg.explore;
using UnityEngine;

public partial class ExploreFriendsModel
{
    //免费次数
    private int freeTimes = -1;
    //抽奖价格
    private int drawConsumeDiamondCount = -1;
    //重抽价格
    private int switchConsumeDiamondCount = -1;
    //抽奖avatarId
    private long drawAvatarId = -1;
    //抽奖wRecordId
    private long drawRecordId = -1;
    
    private int drawSlotIndex = 1;
    private int curSelectSlotIndex = -1;
    
    public bool IsFree => freeTimes > 0;
    public int DrawConsumeDiamondCount => drawConsumeDiamondCount;
    public int SwitchConsumeDiamondCount => switchConsumeDiamondCount;
    public long DrawAvatarId => drawAvatarId;
    public long DrawRecordId => drawRecordId;
    public int DrawSlotIndex => drawSlotIndex;
    
    /// <summary>
    /// key = avatarId
    /// </summary>
    private Dictionary<long,FriendSlotData> slotMapByAvatarId = new Dictionary<long, FriendSlotData>();
    
    /// <summary>
    /// key = slotIndex
    /// </summary>
    private Dictionary<long,FriendSlotData> slotMapBySlotIndex = new Dictionary<long, FriendSlotData>();

    public Dictionary<long, FriendSlotData> SlotMapByAvatarId => slotMapByAvatarId;

    public Dictionary<long, FriendSlotData> SlotMapBySlotIndex => slotMapBySlotIndex;

    private int MAX_SLOT_CNT = 3;

    private void InitSlotData()
    {
        slotMapBySlotIndex.Clear();
        for (int i = 1; i <= MAX_SLOT_CNT; i++)
        {
            var pb = new PB_SlotDetailData();
            pb.slotIndex = i;
            slotMapBySlotIndex.Add(i , new FriendSlotData(pb));
        }
    }

    public bool HasFriend()
    {
        foreach (var kv in slotMapByAvatarId)
        {
            if (kv.Value.AvatarId != 0)
            {
                return true;
            }
        }

        return false;
    }

    public int GetConsumeDiamondBySlot(FriendSlotData data)
    {
        if (data != null && data.AvatarId != 0)
        {
            return SwitchConsumeDiamondCount;
        }

        return DrawConsumeDiamondCount;
    }
    
    /// <summary>
    /// avatarId获取SlotData
    /// </summary>
    /// <param name="avatarId"></param>
    /// <returns></returns>
    public FriendSlotData GetFriendSlotDataByAvatarId(long avatarId)
    {
        FriendSlotData data;
        if (slotMapByAvatarId.TryGetValue(avatarId , out data))
        {
            return data;
        }
        return null;
    }
    
    /// <summary>
    /// slotIndex获取SlotData
    /// </summary>
    /// <param name="slotIndex"></param>
    /// <returns></returns>
    public FriendSlotData GetFriendSlotDataBySlotIndex(int slotIndex)
    {
        FriendSlotData data;
        if (slotMapBySlotIndex.TryGetValue(slotIndex , out data))
        {
            return data;
        }
        return null;
    }
    
    
    public void UpdateFriendInfo(SC_GetFriendSlotListResp msg)
    {
        freeTimes = msg.remainsFreeDrawTimes;
        drawConsumeDiamondCount = msg.drawConsumeDiamondCount;
        switchConsumeDiamondCount = msg.switchConsumeDiamondCount;
        curSelectSlotIndex = msg.selectedSlotIndex;
        var slotDatas = msg.slotList;
        foreach (var slotData in slotDatas)
        {
            long avatarId = slotData.avatarId;
            int slotIndex = slotData.slotIndex;
            FriendSlotData data;
            if (slotMapByAvatarId.TryGetValue(avatarId , out data))
            {
                slotMapByAvatarId[avatarId].UpdateData(slotData);
            }
            else
            {
                data = new FriendSlotData(slotData);
                slotMapByAvatarId.Add(avatarId,data);
            }

            if (slotMapBySlotIndex.TryGetValue(slotIndex , out data))
            {
                data.UpdateData(slotData);
            }
            else
            {
                data = new FriendSlotData(slotData);
                slotMapBySlotIndex.Add(slotIndex,data);                
            }
        }
        SendNotification(NotifyConsts.ExploreFriendsFriendInfoUpdate);
    }

    public void SetDrawNewFriendResp(SC_DrawNewFriendResp msg)
    {
        ClearFreeTimes();
        var data = msg.data;
        drawAvatarId = data.avatarId;
        drawRecordId = data.drawRecordId;
        SendNotification(NotifyConsts.ExploreFriendsPlayTimeLine,drawAvatarId);
    }

    /// <summary>
    /// 只有一次免费次数，所以只要不是首次抽卡，就得花钻石 by 李思睿
    /// </summary>
    public void ClearFreeTimes()
    {
        freeTimes = 0;
    }
    
    public FriendSlotData GetEmptySlotIndex()
    {
        foreach (var kv in slotMapBySlotIndex)
        {
            if (kv.Value.IsOpen && kv.Value.AvatarId <= 0)
            {
                return kv.Value;
            }
        }
        return null;
    }

    #region 亲密度等级数据
    private SC_GetClosenessLevelListResp _allClosenessLevelList;
    public SC_GetClosenessLevelListResp AllClosenessLevelList => _allClosenessLevelList;

    /// <summary>
    /// 亲密度等级 所有数据
    /// </summary>
    /// <param name="ack"></param>
    public void SetClosenessLevelList(SC_GetClosenessLevelListResp ack)
    {
        _allClosenessLevelList = ack;
    }
    
    /// <summary>
    /// 当前亲密度等级 所有数据
    /// </summary>
    /// <param name="ack"></param>
    public RepeatedField<global::Msg.explore.PB_GetClosenessLevelMemoryData> GetCurClosenessLevelList(int levelid)
    {
        for (int i = 0; i < _allClosenessLevelList.levelList.Count; i++)
        {
            if (_allClosenessLevelList.levelList[i].levelId == levelid)
            {
                return _allClosenessLevelList.levelList[i].levelMemoryList;
            }
        }
        return null;
    }
    
    /// <summary>
    /// 亲密度等级最大数据
    /// </summary>
    /// <param name="ack"></param>
    public RepeatedField<global::Msg.explore.PB_GetClosenessLevelMemoryData> GetMaxClosenessLevelList()
    {
        int count = _allClosenessLevelList.levelList.Count;
        if (count > 0)
        {
            return _allClosenessLevelList.levelList[count - 1].levelMemoryList;
        }
       
        return null;
    }
    
    /// <summary>
    /// 数据是否解锁
    /// </summary>
    /// <param name="curlevelid"></param>
    public bool GetClosenessItemIsLock(int curlevelid, int index)
    {
        RepeatedField<PB_GetClosenessLevelMemoryData> cur = GetCurClosenessLevelList(curlevelid);
        return index < cur.Count;
    }
    
    /// <summary>
    /// 指定亲密度等级数据
    /// </summary>
    /// <param name="ack"></param>
    public PB_GetClosenessLevelData GetClosenessLevelData(long level)
    {
        foreach (var levelData in _allClosenessLevelList.levelList)
        {
            if(levelData.levelId == level)
                return levelData;
        }

        return null;
    }
    

    #endregion

}


