/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.MainPath3D
{
    public partial class CompNodeStart : GComponent
    {
        public static string pkgName => "MainPath3D";
        public static string comName => "CompNodeStart";
        public static string url => "ui://fm51gjasui4u1y";

        public Controller pos;
        public Controller state;
        public GTextField title1;
        public BtnStart btnStart;
        public GButton btnJump;
        public GTextField tfUnlock;
        public GGroup grpLock;
        public GGroup grp;
        public GTextField tfReward;
        public GTextField tfExp;
        public GGroup grp0;
        public GGroup grpAll;
        public Transition t0;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(CompNodeStart));
        }

        public override void ConstructFromXML(XML xml)
        {
            pos = GetControllerAt(0);
            state = GetControllerAt(1);
            title1 = GetChildAt(2) as GTextField;
            btnStart = new BtnStart();
            btnStart.Construct(GetChildAt(3).asCom);
            btnJump = GetChildAt(4) as GButton;
            tfUnlock = GetChildAt(6) as GTextField;
            grpLock = GetChildAt(7) as GGroup;
            grp = GetChildAt(8) as GGroup;
            tfReward = GetChildAt(9) as GTextField;
            tfExp = GetChildAt(10) as GTextField;
            grp0 = GetChildAt(12) as GGroup;
            grpAll = GetChildAt(13) as GGroup;
            t0 = GetTransitionAt(0);
        }
        public override void Dispose()
        {
            pos = null;
            state = null;
            title1 = null;
            btnStart.Dispose();
            btnStart = null;
            btnJump = null;
            tfUnlock = null;
            grpLock = null;
            grp = null;
            tfReward = null;
            tfExp = null;
            grp0 = null;
            grpAll = null;
            t0 = null;

            base.Dispose();
        }
    }
}