/* 
****************************************************
* 作者：CuiMengLin
* 创建时间：2024/03/19 10:57:47 星期二
* 功能：Nothing
****************************************************
*/

using Msg.basic;
using ScriptsHot.Game.Modules.ChatLogicNew;
using ScriptsHot.Game.Modules.Scene.Level;
using ScriptsHot.Game.Modules.Scene.Level.Component;
using ScriptsHot.Game.Modules.Setting;
using ScriptsHot.Game.Modules.Settlement;
using ScriptsHot.Game.Modules.Settlement.SettlementUI;
using ScriptsHot.Game.Modules.Task;
using UnityEngine;

public class ChatStateEndProgress : BaseChatMachineState
{
    public ChatStateEndProgress() : base(ChatState.EndProgress)
    {
    }
    
    private string timerName = "";
    private const int TWEENTIME = 500;
    private ChatController _chatController  => owner.GetController<ChatController>(ModelConsts.Chat);
    private ChatModel _chatModel => owner.GetModel<ChatModel>(ModelConsts.Chat);
    public override void OnEnter(params object[] args)
    {
        base.OnEnter(args);
        if (!string.IsNullOrEmpty(timerName))
        {
            TimerManager.instance.UnRegisterTimer(timerName);
            timerName = "";
        }

        //add by raybit11-07 for auto quick update learnpath result @each chat end, 目前依旧会因为Send没有收到ack时数据未被刷新到最新而出现bug

        //var goalRecId = ModelManager.instance.GetModel<LearnPathModel>(ModelConsts.LearnPath).GetGoalId();
        // ControllerManager.instance.GetController<LearnPathController>(ModelConsts.LearnPath).SendGetUserGoalNodeOnEntergame();

        

        timerName = TimerManager.instance.RegisterTimer((a)=>PlayTween(), 500, 1);

        ControllerManager.instance.GetController<SettlementController>(ModelConsts.Settlement)
            .RequestDialogResult(_chatModel.dialogId, _chatModel.chatMode, _chatModel.curRoundId,
                _chatModel.task_record_id,null, _chatModel.avatarId, _chatController.curTaskId, _chatModel.topicId);
    }

    private void PlayTween()
    {
        if (!string.IsNullOrEmpty(timerName))
        {
            TimerManager.instance.UnRegisterTimer(timerName);
            timerName = "";
        }
        this.owner.GetUI<ChatUI>(UIConsts.Chat).PlayTweenShowCell(0.1f,0.4f,50);
        this.owner.GetUI<RecordUI>(UIConsts.RecordUI).DismissWithAnimation();
        this.owner.GetController<TaskController>(ModelConsts.Task).DismissWithAnimation();
        // this.owner.GetUI<MainHeaderUI>(UIConsts.MainHeader).SetVisible(false, true);


        // this.owner.GetUI<MainHeaderUI>(UIConsts.MainHeader).ChangeTweenHeaderState(true);
        timerName = TimerManager.instance.RegisterTimer((a)=>TweenOver(), TWEENTIME, 1);
    }

    private void TweenOver()
    {
        //tweenOver时UI刚消失
        Notifier.instance.SendNotification(NotifyConsts.ExitDialog);

        if (!string.IsNullOrEmpty(timerName))
        {
            TimerManager.instance.UnRegisterTimer(timerName);
            timerName = "";
        }
        if (this.owner.GetModel<ChatModel>(ModelConsts.Chat).chatMode == PB_DialogMode.OnBoarding)
        {
            this.owner.GetController<MainController>(ModelConsts.Main).PlayNpcToMeet();
        }
        else
        {
            TimerManager.instance.RegisterTimer((a) => { PlayAction("clapping"); },500, 1);//500ms
           
        }

        timerName = TimerManager.instance.RegisterTimer((a)=>PlayActionOver(), 3500, 1);
        
    }

    private void PlayAction(string actionName)
    {
        SceneController sceneController = this.owner.GetController<SceneController>(ModelConsts.Scene);
        Level scene = sceneController.scene;
        if (scene.sceneType != ESceneType.HomepageRoom)
        {
            Unit unit = scene.GetComponent<UnitComponent>().GetUnitByAvatarTid(_chatController.curAvatarId);
            if (unit == null)
            {
                Debug.LogError("modAvatar  unit.uid is error ");
                PlayActionOver();
                return;
            }

            Avatar avatar = scene.GetComponent<AvatarComponent>().GetAvatar(unit.uid);
            if (avatar != null)
                avatar.PlayAction(actionName);
        }
        else
        {
            var hpLevel =  scene as ScriptsHot.Game.Modules.Scene.Level.Homepage.CenterHomeLevel;
            var avatar = hpLevel.GetComponent<AvatarComponent>().GetHomePageChatAvatar();
            if (avatar != null)
                avatar.PlayAction(actionName);
        }
        
    }

    private void PlayActionOver()
    {
        this.owner.GetUI(UIConsts.Chat).Hide();
        this.owner.GetController<TaskController>(ModelConsts.Task).HideProgressTaskUI();
        _chatController.CloseChatSocreUI();
        this.owner.GetModel<TaskModel>(ModelConsts.Task).ClearAllData();
        if (!string.IsNullOrEmpty(timerName))
        {
            TimerManager.instance.UnRegisterTimer(timerName);
            timerName = "";
        }
        _chatController.ChangeState(ChatState.Settlement);
    }

    public override void OnExit(string nextState)
    {
        base.OnExit(nextState);
        if (this.owner.GetModel<ChatModel>(ModelConsts.Chat).chatMode != PB_DialogMode.OnBoarding)
            PlayAction("Idle");
        if (!string.IsNullOrEmpty(timerName))
        {
            TimerManager.instance.UnRegisterTimer(timerName);
            timerName = "";
        }
        // this.owner.GetUI<MainHeaderUI>(UIConsts.MainHeader).ChangeTweenHeaderState(false);
    }
    public override bool OnHandleMsg(MsgData msg)
    {
        DealHandleMsgData(msg); 
        return false;
    }


    private void DealHandleMsgData(MsgData msg)
    {
    }

    public override void OnHandleError(params object[] args)
    {
        base.OnHandleError();

    }
}