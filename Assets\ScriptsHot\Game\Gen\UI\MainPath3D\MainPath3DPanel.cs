/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.MainPath3D
{
    public partial class MainPath3DPanel : UIBindT
    {
        public override string pkgName => "MainPath3D";
        public override string comName => "MainPath3DPanel";

        public GLoader closeLdr;
        public CompSectionHead compSectionHead;
        public GList pathList;
        public GList mainListLeft;
        public GList mainListRight;
        public GGraph targetY;
        public CompNodeStart compNodeStart;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            closeLdr = (GLoader)com.GetChildAt(0);
            compSectionHead = new CompSectionHead();
            compSectionHead.Construct(com.GetChildAt(1).asCom);
            pathList = (GList)com.GetChildAt(2);
            mainListLeft = (GList)com.GetChildAt(3);
            mainListRight = (GList)com.GetChildAt(4);
            targetY = (GGraph)com.GetChildAt(5);
            compNodeStart = (CompNodeStart)com.GetChildAt(6);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            closeLdr = null;
            compSectionHead.Dispose();
            compSectionHead = null;
            pathList = null;
            mainListLeft = null;
            mainListRight = null;
            targetY = null;
            compNodeStart = null;
        }
    }
}