﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FairyGUI;
using UnityEngine;
using Object = System.Object;

public class UIManager
{
    public const int UIDelayGCTime = 30000;//延迟GC时间,ms

    private static UIManager _instance = null;
    private Dictionary<string, GComponent> _layers = new Dictionary<string, GComponent>();
    private Dictionary<string, int> _pkgRef = new Dictionary<string, int>();
    private Dictionary<string, int> _pkgWaitGC = new Dictionary<string, int>();
    private Dictionary<string, bool> _pkgPreload = new Dictionary<string, bool>();
    private Dictionary<string, IBaseUI> _uis = new Dictionary<string, IBaseUI>();
    private OrderedDictionary<string, IBaseUI> _uisShow = new ();
    private Dictionary<string, IBaseUIUpdate> _uisUpdate = new Dictionary<string, IBaseUIUpdate>();
    private Dictionary<string, IDragAbleUI> _uisDragAbleUI = new Dictionary<string, IDragAbleUI>();
    private Dictionary<string, int> _uisWaitGC = new Dictionary<string, int>();
    private Dictionary<string, List<Action>> _packageLoadingMap = new Dictionary<string, List<Action>>();
    private List<UIPkgItemExtensionVO> _uiExtensionList = new List<UIPkgItemExtensionVO>();

    private int _safeAreaTopHeight = 0;
    private int _safeAreaBottomHeight = 0;

    public static string GetUIClassName<T>() {
          
        var name = typeof(T).Name;
        var pos = name.IndexOf("UI");//刻意去尾部
        if (pos > 0)
        {
            return name.Substring(0, pos - 1);
        }
        return name;
    }

    public static UIManager instance
    {
        get
        {
            if (_instance == null)
                _instance = new UIManager();
            return _instance;
        }
    }

    public void Init()
    {
        this.CalcSafeArea();
        this.InitLayers();
        this.InitPreloadPkg();
        GHeadBarManager.instance.InitRoot();
        FairyGUI.GRoot.inst.onSizeChanged.Add(this.OnResize);

        if (AppConst.IsDebug)
            GRoot.inst.onRightClick.Add(this.OnDebugHit);

        AddEvent();
    }

    /**
     * 初始化所有Layer
     */
    private void InitLayers()
    {
        this.AddLayer(UILayerConsts.HeadBar);
        this.AddLayer(UILayerConsts.Scene);
        this.AddLayer(UILayerConsts.Touch);
        this.AddLayer(UILayerConsts.UnderBottom, true);
        this.AddLayer(UILayerConsts.Bottom, true);
        this.AddLayer(UILayerConsts.HomeUnSafe);
        this.AddLayer(UILayerConsts.HomePage, true);
        this.AddLayer(UILayerConsts.FeaturePage, true);
        this.AddLayer(UILayerConsts.Home, true);
        this.AddLayer(UILayerConsts.Module, true);
        this.AddLayer(UILayerConsts.Top, true);
        this.AddLayer(UILayerConsts.Float, true);
        this.AddLayer(UILayerConsts.Guide, true);

        this.AddLayer(UILayerConsts.Loading);
    }

    //判断UILayerConsts层级正在显示界面数量
    public bool HasLayerShow(string LayerConsts ,List<string> except)
    {
        foreach (var item in this._uisShow)
        {
            if (except.Contains(item.Value.name))
            {
                continue;
            }
            if (item.Value.uiLayer == LayerConsts)
            {
                return true;
            }
        }
        return false;
    }
    
    
    
    

    
    private void AddEvent()
    {
        Notifier.instance.RegisterNotification(NotifyConsts.OpenUI,OnOpenUIEvent);
        Notifier.instance.RegisterNotification(NotifyConsts.CloseUI,OnCloseUIEvent);
    }

    private void RemoveEvent()
    {
        Notifier.instance.UnRegisterNotification(NotifyConsts.OpenUI,OnOpenUIEvent);
        Notifier.instance.UnRegisterNotification(NotifyConsts.CloseUI,OnCloseUIEvent);
    }
    
    private void OnOpenUIEvent(string name, object body)
    {
        string viewName = String.Empty;
        if (body is string)
        {
            viewName = (string)body;
            GetUI(viewName).Show();
        }
        else
        {
            viewName = (body as UIParams).viewName;
            GetUI(viewName).Show((body as UIParams).param);
        }
    }
    private void OnCloseUIEvent(string name, object body)
    {
        string viewName = body.ToString();
        GetUI(viewName).Hide();
    }

    public float width
    {
        get
        {
            return FairyGUI.GRoot.inst.width;
        }
    }

    public float height
    {
        get
        {
            return FairyGUI.GRoot.inst.height;
        }
    }

    public float FullScreenHeight {
        get
        {
            return FairyGUI.GRoot.inst.height + this.safeAreaTopHeight + this.safeAreaBottomHeight;
        }
    }
    /**
     * 获取所有UI
     */
    public Dictionary<string, IBaseUI> GetAllUI()
    {
        return this._uis;
    }

    public OrderedDictionary<string, IBaseUI> GetShowUI()
    {
        return this._uisShow;
    }

  

    /**
     * 添加一个UI
     */
    public void AddUI(IBaseUI ui)
    {
        this._uis.Add(ui.name, ui);
    }

    /**
     * 获取UI
     */
    public IBaseUI GetUI(string name)
    {
        IBaseUI ui;
        this._uis.TryGetValue(name, out ui);
        return ui;
    }

    public T GetUI<T>(string name) where T: IBaseUI
    {
        IBaseUI ui;
        this._uis.TryGetValue(name, out ui);
        return (T)ui;
    }

    ///// <summary>
    ///// 新接口自动 根据T的类名定义
    ///// </summary>
    ///// <typeparam name="T"></typeparam>
    ///// <returns></returns>
    //public T GetUI<T>() where T : IBaseUI
    //{
    //    IBaseUI ui;

    //    this._uis.TryGetValue(UIManager.GetUIClassName<T>(), out ui);
    //    if (ui == null) {
    //        Debug.LogError("Err in GetUI by nullName"+ typeof(T).Name);
    //    }
    //    return (T)ui;
    //}


    /**
     * 添加Layder
     */
    public void AddLayer(string layerName, bool useSafeArea = false)
    {
        if (this._layers.ContainsKey(layerName))
        {
            VFDebug.LogError("Has Same Name Layer!!" + layerName);
            return;
        }
        FairyGUI.GComponent layer = new FairyGUI.GComponent();
        layer.name = layerName;
        layer.gameObjectName = layerName;
        FairyGUI.GRoot.inst.AddChild(layer);
        //
        if (useSafeArea)
        {
            layer.x = 0;
            layer.y = this._safeAreaTopHeight;
            layer.width = GRoot.inst.width;
            layer.height = GRoot.inst.height - this._safeAreaTopHeight - this._safeAreaBottomHeight;
            layer.AddRelation(GRoot.inst, RelationType.Width);
            layer.AddRelation(GRoot.inst, RelationType.Top_Top);
            layer.AddRelation(GRoot.inst, RelationType.BottomExt_Bottom);
        }
        else
        {
            layer.x = 0;
            layer.y = 0;
            layer.AddRelation(GRoot.inst, RelationType.Width);
            layer.AddRelation(GRoot.inst, RelationType.Height);
        }
        this._layers.Add(layerName, layer);
    }

    /**
     * 取Layer
     */
    public FairyGUI.GComponent GetLayer(string layerName)
    {
        return this._layers[layerName];
    }

    /**
     * 隐藏指定Layer
     */
    public void HideLayer(string layerName)
    {
        if (this._layers.ContainsKey(layerName))
            this._layers[layerName].visible = false;
    }

    /**
     * 隐藏指定Layer外的所有Layer
     */
    public void HideLayerBeyond(params string[] param)
    {
        List<string> keys = new List<string>(this._layers.Keys);
        for (int i = 0; i < keys.Count; i++)
        {
            string name = keys[i];
            bool hasFind = false;
            for (int j = 0; j < param.Length; j++)
            {
                if (name == param[j])
                {
                    hasFind = true;
                    break;
                }
            }
            if (!hasFind)
                this.HideLayer(name);
        }
    }

    /**
     * 恢复显示所有Layer
     */
    public void ShowAllLayer()
    {
        List<string> keys = new List<string>(this._layers.Keys);
        for (int i = 0; i < keys.Count; i++)
        {
            FairyGUI.GComponent layer = this._layers[keys[i]];
            if (!layer.visible)
                layer.visible = true;
        }
    }

    public void SwitchLayer(string uiName, string layerName)
    {
        var ui = this.GetUI(uiName);
        if (ui != null)
        {
            this.GetLayer(layerName).AddChild(ui.uiComponent);
        }
    }

    /**
     * update
     */
    public void Update(int interval)
    {
        List<string> keys = new List<string>(this._uisUpdate.Keys);
        for (int i = 0; i < keys.Count; i++)
        {
            IBaseUIUpdate ui = this._uisUpdate[keys[i]];
            ui.Update(interval);
        }
        this.CheckUnloadPackage();
        this.CheckUIGC();

        if (Input.GetKey(KeyCode.Escape) && escBtnInterval <= 0)
        {
            VFDebug.Log("On KeyCode.Escape Click");
            OnEscClick();
            escBtnInterval = 500;
        }
        escBtnInterval -= interval;
    }

    private int escBtnInterval = 0;
    
    /**
 * 加载Package
 */
    public void LoadPackage(string pkgName, Action callback)
    {
        // 使用 HashSet 记录已经处理过的包，避免递归依赖导致的引用计数错误
        LoadPackageInternal(pkgName, callback, new HashSet<string>());
    }

    private void LoadPackageInternal(string pkgName, Action callback, HashSet<string> processedPackages)
    {
        // 如果包已经被处理过，直接返回
        if (processedPackages.Contains(pkgName))
        {
            VFDebug.LogWarning($"Package {pkgName} has already been processed, skipping to avoid infinite recursion.");
            callback?.Invoke();
            return;
        }

        // 标记当前包为已处理
        processedPackages.Add(pkgName);

        if (!this._pkgRef.ContainsKey(pkgName))
            this._pkgRef.Add(pkgName, 0);

        FairyGUI.UIPackage uiPackage = FairyGUI.UIPackage.GetByName(pkgName);
        if (uiPackage != null)
        {
            this._pkgRef[pkgName]++;
            callback?.Invoke();
            return;
        }

        if (!this._packageLoadingMap.ContainsKey(pkgName))
            this._packageLoadingMap[pkgName] = new List<Action>();

        this._packageLoadingMap[pkgName].Add(callback);
        if (this._packageLoadingMap[pkgName].Count > 1) return;

        GResManager.instance.LoadUIPackage(pkgName, uiPackage =>
        {
            LoadDependencies(uiPackage, () =>
            {
                this.CheckPkgItemExtension(pkgName);
                if (this._packageLoadingMap.ContainsKey(pkgName))
                {
                    this._pkgRef[pkgName] = this._pkgRef[pkgName] + this._packageLoadingMap[pkgName].Count;
                    foreach (var _callback in this._packageLoadingMap[pkgName])
                    {
                        if (_callback != null)
                            _callback();
                    }
                    this._packageLoadingMap.Remove(pkgName);
                }
            }, processedPackages);
        });
    }

    private const string PckNameKey = "name";

    private void LoadDependencies(FairyGUI.UIPackage uiPackage, Action onComplete, HashSet<string> processedPackages)
    {
        var dependencies = uiPackage.dependencies;
        if (dependencies == null || dependencies.Length == 0)
        {
            onComplete?.Invoke();
            return;
        }

        int loadedCount = 0;
        foreach (var kv in dependencies)
        {
            if (kv.TryGetValue(PckNameKey, out string depPkgName))
            {
                VFDebug.Log($"{uiPackage.name} ---- 通过依赖加载了{depPkgName}");
                LoadPackageInternal(depPkgName, () =>
                {
                    loadedCount++;
                    if (loadedCount >= dependencies.Length)
                    {
                        onComplete?.Invoke();
                    }
                }, processedPackages);
            }
            else
            {
                loadedCount++;
                if (loadedCount >= dependencies.Length)
                {
                    onComplete?.Invoke();
                }
            }
        }
    }

    /**
     * 卸载Package
     */
    public void UnloadPackage(string pkgName)
    {
        // 使用 HashSet 记录已经处理过的包，避免递归依赖导致的无限循环
        UnloadPackageInternal(pkgName, new HashSet<string>());
    }

    private void UnloadPackageInternal(string pkgName, HashSet<string> processedPackages)
    {
        // 如果包已经被处理过，直接返回
        if (processedPackages.Contains(pkgName))
        {
            VFDebug.LogWarning($"Package {pkgName} has already been processed, skipping to avoid infinite recursion.");
            return;
        }

        // 标记当前包为已处理
        processedPackages.Add(pkgName);

        VFDebug.Log("ready UnloadPackage:" + pkgName);
        if (!this._pkgRef.ContainsKey(pkgName))
        {
            VFDebug.Log("Error:UIPackage Ref Error." + pkgName);
            return;
        }

        // 减少当前包的引用计数
        this._pkgRef[pkgName]--;
        if (this._pkgRef[pkgName] <= 0 && !this._pkgPreload.ContainsKey(pkgName))
        {
            // 获取当前包的依赖包
            var uiPackage = UIPackage.GetByName(pkgName);
            if (uiPackage != null)
            {
                var dependencies = uiPackage.dependencies;
                if (dependencies != null && dependencies.Length > 0)
                {
                    foreach (var kv in dependencies)
                    {
                        if (kv.TryGetValue(PckNameKey, out string depPkgName))
                        {
                            // 检查依赖包的引用计数
                            if (this._pkgRef.ContainsKey(depPkgName))
                            {
                                this._pkgRef[depPkgName]--;
                                if (this._pkgRef[depPkgName] <= 0 && !this._pkgPreload.ContainsKey(depPkgName))
                                {
                                    // 递归卸载依赖包，并传递已处理包的记录
                                    UnloadPackageInternal(depPkgName, processedPackages);
                                }
                            }
                        }
                    }
                }
            }

            // 卸载当前包
            this._pkgRef.Remove(pkgName);
            this._pkgWaitGC[pkgName] = TimeExt.currTime;
            VFDebug.Log("WaitGC UnloadPackage:" + pkgName);
        }
    }

    private void OnResize()
    {
        List<string> keys = new List<string>(this._uisShow.Keys);
        for (int i = 0; i < keys.Count; i++)
        {
            IBaseUI ui = this._uisShow[keys[i]];
            ui.OnResize();
        }
    }

    public void HideAllUI()
    {
        List<string> keys = new List<string>(this._uisShow.Keys);
        for (int i = 0; i < keys.Count; i++)
        {
            IBaseUI ui = this._uisShow[keys[i]];
            ui.Hide();
        }
    }

    public void OnUIShow(IBaseUI ui)
    {
        this._uisShow[ui.name] = ui;
        if (ui is IBaseUIUpdate)
        {
            this._uisUpdate[ui.name] = ui as IBaseUIUpdate;
        }

        if (ui is IDragAbleUI)
        {
            _uisDragAbleUI[ui.name] = ui as IDragAbleUI;
            var dragAbleUI = _uisDragAbleUI[ui.name];
            dragAbleUI.GetDragInputPanel().touchable = true;
            dragAbleUI.GetDragInputPanel().onTouchBegin.Add(context => context.CaptureTouch());
            dragAbleUI.GetDragInputPanel().onTouchMove.Add(context =>
            {
                if (dragAbleUI.IsDragAble())
                    dragAbleUI.OnDrag(MoveCmp(context, dragAbleUI.GetDragTarget()));
            });
            dragAbleUI.GetDragInputPanel().onTouchEnd.Add(context =>
            {
                OnMoveCmpEnd(context, dragAbleUI.GetDragTarget(), dragAbleUI, dragAbleUI.GetAutoScrollRatio());
            });
        }

        this._uisWaitGC.Remove(ui.name);
    }

    #region DragAbleUI
    private Vector2 MoveCmp(EventContext context, GComponent cmp)
    {
        if (context.inputEvent.holdTime <= 0.2f)
            return cmp.position;
        var bottom = cmp.parent.GlobalToLocal(new Vector2(0, Screen.height));
        var top = new Vector2(0, bottom.y - cmp.size.y);
        var pointerPos = cmp.parent.GlobalToLocal(new Vector2(0, context.inputEvent.position.y));
        var clickPos = cmp.parent.GlobalToLocal(context.inputEvent.position);
        var newPos = new Vector2(cmp.position.x, bottom.y - clickPos.y >= cmp.size.y ? top.y : pointerPos.y);
        cmp.position = newPos;
        return newPos;
    }

    private void OnMoveCmpEnd(EventContext context, GComponent cmp, IDragAbleUI ui, float ratio)
    {
        var bottom = cmp.parent.GlobalToLocal(new Vector2(0, Screen.height));
        var top = new Vector2(0, bottom.y - cmp.size.y);
        var split = cmp.parent.GlobalToLocal(new Vector2(0, Screen.height * ratio));
        var clickPos = cmp.parent.GlobalToLocal(context.inputEvent.position);
        if (ui.IsDragAble() && context.inputEvent.holdTime > 0.05f)
        {
            if (clickPos.y < split.y)
            {
                cmp.TweenMoveY(top.y, 0.1f).OnUpdate(g => { ui.OnDrag(g.value.vec2); }).OnComplete(ui.OnDragIn);
            }
            else
            {
                cmp.TweenMoveY(bottom.y, 0.1f).OnUpdate(g => { ui.OnDrag(g.value.vec2); }).OnComplete(ui.OnDragOut);
            }
        }
    }

    private void RemoveTouchEvents(IDragAbleUI ui)
    {
        ui.GetDragInputPanel().onTouchBegin.Clear();
        ui.GetDragInputPanel().onTouchMove.Clear();
        ui.GetDragInputPanel().onTouchEnd.Clear();
    }
    #endregion

    public void OnUIHide(IBaseUI ui)
    {
        this._uisShow.Remove(ui.name);
        this._uisUpdate.Remove(ui.name);
        if (_uisDragAbleUI.ContainsKey(ui.name))
        {
            RemoveTouchEvents(_uisDragAbleUI[ui.name]);
            _uisDragAbleUI.Remove(ui.name);
        }
        //
        if (ui.parentUI != null)
            return;
        if (ui.deleteRule == EUIDeleteRule.RightNow)
        {
            this._uisWaitGC[ui.name] = (int)(UnityEngine.Time.realtimeSinceStartup * 1000);
        }
        else if (ui.deleteRule == EUIDeleteRule.Auto)
        {
            this._uisWaitGC[ui.name] = (int)(UnityEngine.Time.realtimeSinceStartup * 1000) + UIManager.UIDelayGCTime;
        }
    }

    /**
     * 检查互斥面板
     */
    public void CheckMutexGroup(IBaseUI ui)
    {
        int mutuexGroup = ui.mutexGroup;
        if (mutuexGroup <= 0)
            return;
        List<string> keys = new List<string>(this._uisShow.Keys);
        for (int i = 0; i < keys.Count; i++)
        {
            IBaseUI showUI = this._uisShow[keys[i]];
            if (showUI != ui && showUI.mutexGroup == mutuexGroup)
            {
                showUI.Hide();
            }
        }
    }

    private void CheckUnloadPackage()
    {
        int now = (int)(UnityEngine.Time.realtimeSinceStartup * 1000);
        List<string> keys = new List<string>(this._pkgWaitGC.Keys);
        for (int i = 0; i < keys.Count; i++)
        {
            string pkgName = keys[i];
            int startTime = this._pkgWaitGC[pkgName];
            if (now - startTime > 5000)
            {
                if (this._pkgRef.ContainsKey(pkgName))
                {
                    this._pkgWaitGC.Remove(pkgName);
                }
                else
                {
                    GResManager.instance.UnLoadUIPackage(pkgName);
                    VFDebug.Log("UnloadPackage:" + pkgName);
                    this._pkgWaitGC.Remove(pkgName);
                }
            }
        }
    }


    /**
     * 检查UI GC
     */
    public void CheckUIGC()
    {
        int now = (int)(UnityEngine.Time.realtimeSinceStartup * 1000);
        List<string> keys = new List<string>(this._uisWaitGC.Keys);
        for (int i = 0; i < keys.Count; i++)
        {
            string name = keys[i];
            int startTime = this._uisWaitGC[name];
            if (now - startTime > 5000)
            {
                IBaseUI ui = this.GetUI(name);
                if (ui != null && !ui.isShow)
                {
                    ui.DeleteUI();
                }
                this._uisWaitGC.Remove(name);
            }
        }
    }

    //计算设备的安全区高度
    private void CalcSafeArea()
    {
        var safeArea = Screen.safeArea;
        this._safeAreaTopHeight = Mathf.FloorToInt((Screen.height - safeArea.yMax) / GRoot.contentScaleFactor);
        if (_safeAreaTopHeight<=0)
        {
            this._safeAreaTopHeight = 20;
        }
        this._safeAreaBottomHeight = Mathf.FloorToInt( safeArea.y/ GRoot.contentScaleFactor);
    }

    public int safeAreaTopHeight { get { return this._safeAreaTopHeight; } }
    public int safeAreaBottomHeight { get { return this._safeAreaBottomHeight; } }

    private void InitPreloadPkg()
    {
        this.AddPreloadPkg("common");
        this.AddPreloadPkg("common_bg");

        this.AddPreloadPkg("HeadBar");
        this.AddPreloadPkg("CommonUI");
        this.AddPreloadPkg("Record");
    
        //首页tab必须内置才能提速
        this.AddPreloadPkg("MainPath3D");
        this.AddPreloadPkg("Rank");
        this.AddPreloadPkg("Explore");
        this.AddPreloadPkg("Main"); //目前包含了 voicechat
        this.AddPreloadPkg("Friends"); 
        this.AddPreloadPkg("AtlasCommon");
        
    }

    public void AddPreloadPkg(string pkgName)
    {
        this._pkgPreload[pkgName] = true;
    }

    public void Preload(Action preloadAllCb)
    {
        if (_pkgPreload.Count <= 0)
        {
            preloadAllCb?.Invoke();
            return;
        }
        
        _preloadAllCb = preloadAllCb;
        preloadComplateCnt = 0;
        List<string> keys = new List<string>(this._pkgPreload.Keys);
        for (int i = 0; i < keys.Count; i++)
        {
            string pkgName = keys[i];
            this.LoadPackage(pkgName, PreloadCb);
        }
    }

    private Action _preloadAllCb;
    private int preloadComplateCnt;
    private void PreloadCb()
    {
        preloadComplateCnt++;
        if (preloadComplateCnt >= _pkgPreload.Count)
        {
            _preloadAllCb?.Invoke();
        }
    }

    public void RegisterItemExtension(string pkgName, string resName, Type type)
    {
        string itemUrl = UIPackage.GetItemURL(pkgName, resName);
        if (!string.IsNullOrEmpty(itemUrl))
        {
            UIObjectFactory.SetPackageItemExtension(itemUrl, type);
        }
        else
        {
            this._uiExtensionList.Add(new UIPkgItemExtensionVO { pkgName = pkgName, resName = resName, type = type });
        }
    }

    private void CheckPkgItemExtension(string pkgName)
    {
        foreach(var vo in this._uiExtensionList)
        {
            if(vo.pkgName == pkgName)
            {
                string itemUrl = UIPackage.GetItemURL(pkgName, vo.resName);
                if (!string.IsNullOrEmpty(itemUrl))
                    UIObjectFactory.SetPackageItemExtension(itemUrl, vo.type);
            }
        }
    }

    public void RefreshLanguageComs()
    {
        List<string> keys = new List<string>(this._uisShow.Keys);
        for (int i = 0; i < keys.Count; i++)
        {
            IBaseUI ui = this._uisShow[keys[i]];
            ui.RefreshLanguageComs();
        }
    }

    private void OnDebugHit(EventContext context)
    {
        var gObject = (context.initiator as DisplayObject).gOwner;
        List<string> list = new List<string>();
        while (gObject != null)
        {
            string name = gObject.name;
            if (string.IsNullOrEmpty(name))
                name = gObject.gameObjectName;
            list.Insert(0, name);
            gObject = gObject.parent;
        }
        VFDebug.Log(string.Join("/", list.ToArray()));
    }

    internal class UIPkgItemExtensionVO
    {
        public string pkgName;
        public string resName;
        public Type type; 
    }
    
        
    public class UIParams
    {
        public string viewName;
        public object param;
    }
    


    #region android 返回键

    private List<string> HolderUIs = new List<string>()
    {
        // UIConsts.MainHeader,
        UIConsts.SceneLocate,
        UIConsts.LittleMapUI,
        UIConsts.CenterHome,
        UIConsts.SceneLoading,
        UIConsts.Login,
        UIConsts.LoginCN,
        UIConsts.SceneLocate,    
        UIConsts.MultiTabHomepage,
        UIConsts.CommBusy,
        UIConsts.CommonToast,        
        UIConsts.CommonGetGold,        
        UIConsts.CommonGetDiamond,        
        UIConsts.CommonPush,       
        
        
        UIConsts.ProgressTask,   
        UIConsts.ProgressCareer,   
        UIConsts.ProgressStrengthen,   
        UIConsts.ProgressChallengeTask,   
        UIConsts.ProgressFreeTalkTask,   
        
        
        UIConsts.ProgressToast,   
        UIConsts.ChatGuide,
        UIConsts.Guide,   
        UIConsts.CurrencyUI,         
    };
    
    StringBuilder sb = new StringBuilder();
    private void OnEscClick()
    {
        foreach (var kv in _uisShow)
        {
            sb.AppendLine($"{kv.Key}:isShow={kv.Value.isShow}  ||");
        }

        var topUI = _uisShow.LastOrDefault(kv => !HolderUIs.Contains(kv.Key));
        VFDebug.Log($"topUI={topUI.Key} queue = {sb}");
        sb.Clear();
        
        if (!EqualityComparer<KeyValuePair<string, IBaseUI>>.Default.Equals(topUI, default))
        {
            topUI.Value.OnBackBtnClick();
        }
        else
        {
            VFDebug.LogError($"topUI={topUI.Key} value = null");
        }
    }
    

    #endregion
}
