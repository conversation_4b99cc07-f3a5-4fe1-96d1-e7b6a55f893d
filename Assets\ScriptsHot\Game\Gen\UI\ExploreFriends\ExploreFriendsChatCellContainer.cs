/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreFriends
{
    public partial class ExploreFriendsChatCellContainer : UIBindT
    {
        public override string pkgName => "ExploreFriends";
        public override string comName => "ExploreFriendsChatCellContainer";

        public Controller ctrlMask;
        public GGraph comMask;
        public GGroup Content;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ctrlMask = com.GetControllerAt(0);
            comMask = (GGraph)com.GetChildAt(0);
            Content = (GGroup)com.GetChildAt(2);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ctrlMask = null;
            comMask = null;
            Content = null;
        }
    }
}