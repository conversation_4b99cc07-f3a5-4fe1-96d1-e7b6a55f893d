// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/explore/mission_story.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.explore {

  /// <summary>Holder for reflection information generated from protobuf/explore/mission_story.proto</summary>
  public static partial class MissionStoryReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/explore/mission_story.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static MissionStoryReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CiRwcm90b2J1Zi9leHBsb3JlL21pc3Npb25fc3RvcnkucHJvdG8aG3Byb3Rv",
            "YnVmL2V4cGxvcmUvYmFzZS5wcm90bxobcHJvdG9idWYvYmFzaWMvZGlhbG9n",
            "LnByb3RvIv4CChhQQl9NaXNzaW9uU3RvcnlDaGF0VXBNc2cSEQoJbWlzc2lv",
            "bklkGAEgASgDEg8KB3N0b3J5SWQYAiABKAMSDgoGdGFza0lkGAMgASgDEkIK",
            "DnVzZXJJbnB1dEF1ZGlvGAQgASgLMiguUEJfRXhwbG9yZV9NaXNzaW9uU3Rv",
            "cnlDaGF0QXVkaW9VcEZyYW1lSAASPAoKdXBCaXpFdmVudBgFIAEoDjImLlBC",
            "X0V4cGxvcmVfTWlzc2lvblN0b3J5Q2hhdFVwQml6RXZlbnRIABJQChNnZXRV",
            "c2VyRXhhbXBsZUF1ZGlvGAYgASgLMjEuUEJfRXhwbG9yZV9NaXNzaW9uU3Rv",
            "cnlDaGF0VXBHZXRVc2VyRXhhbXBsZUF1ZGlvSAASTgoSZGlmZmljdWx0eUZl",
            "ZWRiYWNrGAcgASgLMjAuUEJfRXhwbG9yZV9NaXNzaW9uU3RvcnlDaGF0VXBE",
            "aWZmaWN1bHR5RmVlZGJhY2tIAEIKCgh1cEJpek1zZyJjCidQQl9FeHBsb3Jl",
            "X01pc3Npb25TdG9yeUNoYXRBdWRpb1VwRnJhbWUSDQoFYXVkaW8YASABKAwS",
            "EwoLc2FtcGxlX3JhdGUYAiABKA0SFAoMbnVtX2NoYW5uZWxzGAMgASgNIksK",
            "MFBCX0V4cGxvcmVfTWlzc2lvblN0b3J5Q2hhdFVwR2V0VXNlckV4YW1wbGVB",
            "dWRpbxIXCg9leGFtcGxlQnViYmxlSWQYASABKAkiqQEKL1BCX0V4cGxvcmVf",
            "TWlzc2lvblN0b3J5Q2hhdFVwRGlmZmljdWx0eUZlZWRiYWNrEhEKCW1pc3Np",
            "b25JZBgBIAEoAxIPCgdzdG9yeUlkGAIgASgDEg4KBnRhc2tJZBgDIAEoAxJC",
            "CgpkaWZmaWN1bHR5GAQgASgOMi4uUEJfRXhwbG9yZV9NaXNzaW9uU3RvcnlD",
            "aGF0RGlmZmljdWx0eUZlZWRiYWNrIpoBCilQQl9FeHBsb3JlX01pc3Npb25T",
            "dG9yeUNoYXRBdWRpb0Rvd25GcmFtZRIKCgJpZBgBIAEoBBINCgVhdWRpbxgC",
            "IAEoDBITCgtzYW1wbGVfcmF0ZRgDIAEoDRIUCgxudW1fY2hhbm5lbHMYBCAB",
            "KA0SEQoJYnViYmxlX2lkGAUgASgJEhQKDGlzX2xhc3RfY2xpcBgGIAEoCCK0",
            "AQouUEJfTWlzc2lvblN0b3J5Q2hhdENvbW1vbkRhdGFGaWVsZEZvckRhdGFD",
            "bGFzcxIQCghkaWFsb2dJZBgBIAEoAxINCgVtc2dJZBgCIAEoAxIQCghidWJi",
            "bGVJZBgDIAEoCRINCgVyb3VuZBgEIAEoBRIfCghtc2dPd25lchgFIAEoDjIN",
            "LlBCX01zZ0JlbG9uZxIOCgZ0YXNrSWQYBiABKAMSDwoHc3RvcnlJZBgHIAEo",
            "AyK3AQooU0NfTWlzc2lvblN0b3J5Q2hhdERvd25Nc2dGb3JBdmF0YXJSZXBs",
            "eRIhCgRjb2RlGAEgASgOMhMuUEJfRXhwbG9yZV9CaXpDb2RlEkMKCmNvbW1v",
            "bkRhdGEYAiABKAsyLy5QQl9NaXNzaW9uU3RvcnlDaGF0Q29tbW9uRGF0YUZp",
            "ZWxkRm9yRGF0YUNsYXNzEhEKCXJlcGx5VGV4dBgDIAEoCRIQCghpc0Zpbmlz",
            "aBgEIAEoCCK3AQoxU0NfTWlzc2lvblN0b3J5Q2hhdERvd25Nc2dGb3JBdmF0",
            "YXJSZXBseVRyYW5zbGF0ZRIhCgRjb2RlGAEgASgOMhMuUEJfRXhwbG9yZV9C",
            "aXpDb2RlEkMKCmNvbW1vbkRhdGEYAiABKAsyLy5QQl9NaXNzaW9uU3RvcnlD",
            "aGF0Q29tbW9uRGF0YUZpZWxkRm9yRGF0YUNsYXNzEhoKEnJlcGx5VHJhbnNs",
            "YXRlVGV4dBgDIAEoCSKKAgorU0NfTWlzc2lvblN0b3J5Q2hhdERvd25Nc2dG",
            "b3JBdmF0YXJSZXBseVRUUxIhCgRjb2RlGAEgASgOMhMuUEJfRXhwbG9yZV9C",
            "aXpDb2RlEkMKCmNvbW1vbkRhdGEYAiABKAsyLy5QQl9NaXNzaW9uU3RvcnlD",
            "aGF0Q29tbW9uRGF0YUZpZWxkRm9yRGF0YUNsYXNzEjkKBWF1ZGlvGAMgASgL",
            "MiouUEJfRXhwbG9yZV9NaXNzaW9uU3RvcnlDaGF0QXVkaW9Eb3duRnJhbWUS",
            "OAoVZW1vdGlvbkFuYWx5c2lzUmVzdWx0GAQgASgLMhkuUEJfRW1vdGlvbkFu",
            "YWx5c2lzUmVzdWx0IqwBCi1TQ19NaXNzaW9uU3RvcnlDaGF0RG93bk1zZ0Zv",
            "clVzZXJSZXBseUV4YW1wbGUSIQoEY29kZRgBIAEoDjITLlBCX0V4cGxvcmVf",
            "Qml6Q29kZRJDCgpjb21tb25EYXRhGAIgASgLMi8uUEJfTWlzc2lvblN0b3J5",
            "Q2hhdENvbW1vbkRhdGFGaWVsZEZvckRhdGFDbGFzcxITCgtleGFtcGxlVGV4",
            "dBgDIAEoCSK+AQo2U0NfTWlzc2lvblN0b3J5Q2hhdERvd25Nc2dGb3JVc2Vy",
            "UmVwbHlFeGFtcGxlVHJhbnNsYXRlEiEKBGNvZGUYASABKA4yEy5QQl9FeHBs",
            "b3JlX0JpekNvZGUSQwoKY29tbW9uRGF0YRgCIAEoCzIvLlBCX01pc3Npb25T",
            "dG9yeUNoYXRDb21tb25EYXRhRmllbGRGb3JEYXRhQ2xhc3MSHAoUZXhhbXBs",
            "ZVRyYW5zbGF0ZVRleHQYAyABKAki1QEKMFNDX01pc3Npb25TdG9yeUNoYXRE",
            "b3duTXNnRm9yVXNlclJlcGx5RXhhbXBsZVRUUxIhCgRjb2RlGAEgASgOMhMu",
            "UEJfRXhwbG9yZV9CaXpDb2RlEkMKCmNvbW1vbkRhdGEYAiABKAsyLy5QQl9N",
            "aXNzaW9uU3RvcnlDaGF0Q29tbW9uRGF0YUZpZWxkRm9yRGF0YUNsYXNzEjkK",
            "BWF1ZGlvGAMgASgLMiouUEJfRXhwbG9yZV9NaXNzaW9uU3RvcnlDaGF0QXVk",
            "aW9Eb3duRnJhbWUioQEKI1NDX01pc3Npb25TdG9yeUNoYXREb3duTXNnRm9y",
            "QWR2aWNlEiEKBGNvZGUYASABKA4yEy5QQl9FeHBsb3JlX0JpekNvZGUSQwoK",
            "Y29tbW9uRGF0YRgCIAEoCzIvLlBCX01pc3Npb25TdG9yeUNoYXRDb21tb25E",
            "YXRhRmllbGRGb3JEYXRhQ2xhc3MSEgoKYWR2aWNlVGV4dBgDIAEoCSKYAQog",
            "U0NfTWlzc2lvblN0b3J5Q2hhdERvd25Nc2dGb3JBU1ISIQoEY29kZRgBIAEo",
            "DjITLlBCX0V4cGxvcmVfQml6Q29kZRJDCgpjb21tb25EYXRhGAIgASgLMi8u",
            "UEJfTWlzc2lvblN0b3J5Q2hhdENvbW1vbkRhdGFGaWVsZEZvckRhdGFDbGFz",
            "cxIMCgR0ZXh0GAMgASgJIskBCiVTQ19NaXNzaW9uU3RvcnlDaGF0RG93bk1z",
            "Z0ZvckJpekV2ZW50EiEKBGNvZGUYASABKA4yEy5QQl9FeHBsb3JlX0JpekNv",
            "ZGUSQwoKY29tbW9uRGF0YRgCIAEoCzIvLlBCX01pc3Npb25TdG9yeUNoYXRD",
            "b21tb25EYXRhRmllbGRGb3JEYXRhQ2xhc3MSOAoIYml6RXZlbnQYAyABKA4y",
            "Ji5QQl9FeHBsb3JlX01pc3Npb25TdG9yeUNoYXRVcEJpekV2ZW50InsKJlBC",
            "X01pc3Npb25TdG9yeUNoYXRfU3RlcFByb2dyZXNzQ2hhbmdlEhwKFGJlZm9y",
            "ZUZpbmlzaGVkU3RlcE5vGAEgASgFEhsKE2FmdGVyRmluaXNoZWRTdGVwTm8Y",
            "AiABKAUSFgoOYWZ0ZXJTdGVwVGl0bGUYAyABKAki0gEKL1NDX01pc3Npb25T",
            "dG9yeUNoYXREb3duTXNnRm9yU3RlcFByb2dyZXNzQ2hhbmdlEiEKBGNvZGUY",
            "ASABKA4yEy5QQl9FeHBsb3JlX0JpekNvZGUSQwoKY29tbW9uRGF0YRgCIAEo",
            "CzIvLlBCX01pc3Npb25TdG9yeUNoYXRDb21tb25EYXRhRmllbGRGb3JEYXRh",
            "Q2xhc3MSNwoGY2hhbmdlGAMgASgLMicuUEJfTWlzc2lvblN0b3J5Q2hhdF9T",
            "dGVwUHJvZ3Jlc3NDaGFuZ2Uq4gEKJVBCX0V4cGxvcmVfTWlzc2lvblN0b3J5",
            "Q2hhdFVwQml6RXZlbnQSIQodRU9fTVNDX1VOS05PV05fQklaX0VWRU5UX1RZ",
            "UEUQABIcChhFT19NU0NfVVNFUl9NQU5VQUxfU1RBUlQQARIdChlFT19NU0Nf",
            "VVNFUl9NQU5VQUxfU1VCTUlUEAISHQoZRU9fTVNDX1VTRVJfU1dJVENIX01B",
            "TlVBTBADEhsKF0VPX01TQ19VU0VSX1NXSVRDSF9BVVRPEAQSHQoZRU9fTVND",
            "X1VTRVJfQ0FOQ0VMX1VQTE9BRBAFKoQBCi1QQl9FeHBsb3JlX01pc3Npb25T",
            "dG9yeUNoYXREaWZmaWN1bHR5RmVlZGJhY2sSFQoRRU9fTVNDX0RGX1VOS05P",
            "V04QABISCg5FT19NU0NfREZfRUFTWRABEhQKEEVPX01TQ19ERl9OT1JNQUwQ",
            "AhISCg5FT19NU0NfREZfSEFSRBADKv4DCipQQl9FeHBsb3JlX01pc3Npb25T",
            "dG9yeUNoYXREb3duQml6RGF0YVR5cGUSIAocRU9fTVNDX1VOS05PV05fQkla",
            "X0RBVEFfVFlQRRAAEhQKEEVPX01TQ19CSVpfRVZFTlQQARIlCiFFT19NU0Nf",
            "U1BFRUNIX1RPX1RFWFRfUkVDT0dOSVpJTkcQAhIkCiBFT19NU0NfU1BFRUNI",
            "X1RPX1RFWFRfUkVDT0dOSVpFRBADEhkKFUVPX01TQ19URVhUX1RPX1NQRUVD",
            "SBAEEhcKE0VPX01TQ19BVkFUQVJfUkVQTFkQBRIhCh1FT19NU0NfQVZBVEFS",
            "X1JFUExZX1RSQU5TTEFURRAGEh0KGUVPX01TQ19VU0VSX1JFUExZX0VYQU1Q",
            "TEUQBxInCiNFT19NU0NfVVNFUl9SRVBMWV9FWEFNUExFX1RSQU5TTEFURRAI",
            "EhMKD0VPX01TQ19MTE1fVFRGQhAJEhgKFEVPX01TQ19DVVJSRU5UX1JPVU5E",
            "EAoSGwoXRU9fTVNDX0lTX0ZJTklTSF9ESUFMT0cQCxIRCg1FT19NU0NfQURW",
            "SUNFEAwSGwoXRU9fTVNDX0FEVklDRV9UUkFOU0xBVEUQDRIYChRFT19NU0Nf",
            "VEFTS19QUk9HUkVTUxAOEhYKEkVPX01TQ19UQVNLX1JFU1VMVBAPQipaGnZm",
            "X3Byb3RvYnVmL3NlcnZlci9leHBsb3JlqgILTXNnLmV4cGxvcmViBnByb3Rv",
            "Mw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Msg.explore.BaseReflection.Descriptor, global::Msg.basic.DialogReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Msg.explore.PB_Explore_MissionStoryChatUpBizEvent), typeof(global::Msg.explore.PB_Explore_MissionStoryChatDifficultyFeedback), typeof(global::Msg.explore.PB_Explore_MissionStoryChatDownBizDataType), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_MissionStoryChatUpMsg), global::Msg.explore.PB_MissionStoryChatUpMsg.Parser, new[]{ "missionId", "storyId", "taskId", "userInputAudio", "upBizEvent", "getUserExampleAudio", "difficultyFeedback" }, new[]{ "upBizMsg" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Explore_MissionStoryChatAudioUpFrame), global::Msg.explore.PB_Explore_MissionStoryChatAudioUpFrame.Parser, new[]{ "audio", "sample_rate", "num_channels" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Explore_MissionStoryChatUpGetUserExampleAudio), global::Msg.explore.PB_Explore_MissionStoryChatUpGetUserExampleAudio.Parser, new[]{ "exampleBubbleId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Explore_MissionStoryChatUpDifficultyFeedback), global::Msg.explore.PB_Explore_MissionStoryChatUpDifficultyFeedback.Parser, new[]{ "missionId", "storyId", "taskId", "difficulty" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Explore_MissionStoryChatAudioDownFrame), global::Msg.explore.PB_Explore_MissionStoryChatAudioDownFrame.Parser, new[]{ "id", "audio", "sample_rate", "num_channels", "bubble_id", "is_last_clip" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass), global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass.Parser, new[]{ "dialogId", "msgId", "bubbleId", "round", "msgOwner", "taskId", "storyId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_MissionStoryChatDownMsgForAvatarReply), global::Msg.explore.SC_MissionStoryChatDownMsgForAvatarReply.Parser, new[]{ "code", "commonData", "replyText", "isFinish" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_MissionStoryChatDownMsgForAvatarReplyTranslate), global::Msg.explore.SC_MissionStoryChatDownMsgForAvatarReplyTranslate.Parser, new[]{ "code", "commonData", "replyTranslateText" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_MissionStoryChatDownMsgForAvatarReplyTTS), global::Msg.explore.SC_MissionStoryChatDownMsgForAvatarReplyTTS.Parser, new[]{ "code", "commonData", "audio", "emotionAnalysisResult" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_MissionStoryChatDownMsgForUserReplyExample), global::Msg.explore.SC_MissionStoryChatDownMsgForUserReplyExample.Parser, new[]{ "code", "commonData", "exampleText" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_MissionStoryChatDownMsgForUserReplyExampleTranslate), global::Msg.explore.SC_MissionStoryChatDownMsgForUserReplyExampleTranslate.Parser, new[]{ "code", "commonData", "exampleTranslateText" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_MissionStoryChatDownMsgForUserReplyExampleTTS), global::Msg.explore.SC_MissionStoryChatDownMsgForUserReplyExampleTTS.Parser, new[]{ "code", "commonData", "audio" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_MissionStoryChatDownMsgForAdvice), global::Msg.explore.SC_MissionStoryChatDownMsgForAdvice.Parser, new[]{ "code", "commonData", "adviceText" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_MissionStoryChatDownMsgForASR), global::Msg.explore.SC_MissionStoryChatDownMsgForASR.Parser, new[]{ "code", "commonData", "text" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_MissionStoryChatDownMsgForBizEvent), global::Msg.explore.SC_MissionStoryChatDownMsgForBizEvent.Parser, new[]{ "code", "commonData", "bizEvent" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_MissionStoryChat_StepProgressChange), global::Msg.explore.PB_MissionStoryChat_StepProgressChange.Parser, new[]{ "beforeFinishedStepNo", "afterFinishedStepNo", "afterStepTitle" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_MissionStoryChatDownMsgForStepProgressChange), global::Msg.explore.SC_MissionStoryChatDownMsgForStepProgressChange.Parser, new[]{ "code", "commonData", "change" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  /// <summary>
  ///
  /// Mission剧情对话上行 - 业务事件枚举
  /// </summary>
  public enum PB_Explore_MissionStoryChatUpBizEvent {
    /// <summary>
    /// 无意义
    /// </summary>
    [pbr::OriginalName("EO_MSC_UNKNOWN_BIZ_EVENT_TYPE")] EO_MSC_UNKNOWN_BIZ_EVENT_TYPE = 0,
    /// <summary>
    /// 用户手动开始
    /// </summary>
    [pbr::OriginalName("EO_MSC_USER_MANUAL_START")] EO_MSC_USER_MANUAL_START = 1,
    /// <summary>
    /// 用户手动提交
    /// </summary>
    [pbr::OriginalName("EO_MSC_USER_MANUAL_SUBMIT")] EO_MSC_USER_MANUAL_SUBMIT = 2,
    /// <summary>
    /// 切换手动模式
    /// </summary>
    [pbr::OriginalName("EO_MSC_USER_SWITCH_MANUAL")] EO_MSC_USER_SWITCH_MANUAL = 3,
    /// <summary>
    /// 切换自动模式
    /// </summary>
    [pbr::OriginalName("EO_MSC_USER_SWITCH_AUTO")] EO_MSC_USER_SWITCH_AUTO = 4,
    /// <summary>
    /// 用户取消收音
    /// </summary>
    [pbr::OriginalName("EO_MSC_USER_CANCEL_UPLOAD")] EO_MSC_USER_CANCEL_UPLOAD = 5,
  }

  /// <summary>
  /// 难度反馈枚举
  /// </summary>
  public enum PB_Explore_MissionStoryChatDifficultyFeedback {
    /// <summary>
    /// 无意义
    /// </summary>
    [pbr::OriginalName("EO_MSC_DF_UNKNOWN")] EO_MSC_DF_UNKNOWN = 0,
    /// <summary>
    /// 简单
    /// </summary>
    [pbr::OriginalName("EO_MSC_DF_EASY")] EO_MSC_DF_EASY = 1,
    /// <summary>
    /// 适中
    /// </summary>
    [pbr::OriginalName("EO_MSC_DF_NORMAL")] EO_MSC_DF_NORMAL = 2,
    /// <summary>
    /// 困难
    /// </summary>
    [pbr::OriginalName("EO_MSC_DF_HARD")] EO_MSC_DF_HARD = 3,
  }

  /// <summary>
  ///
  /// Mission剧情对话下行 - 业务数据类型枚举
  /// </summary>
  public enum PB_Explore_MissionStoryChatDownBizDataType {
    /// <summary>
    /// 无意义
    /// </summary>
    [pbr::OriginalName("EO_MSC_UNKNOWN_BIZ_DATA_TYPE")] EO_MSC_UNKNOWN_BIZ_DATA_TYPE = 0,
    /// <summary>
    /// 业务事件
    /// </summary>
    [pbr::OriginalName("EO_MSC_BIZ_EVENT")] EO_MSC_BIZ_EVENT = 1,
    /// <summary>
    /// 语音识别过程结果
    /// </summary>
    [pbr::OriginalName("EO_MSC_SPEECH_TO_TEXT_RECOGNIZING")] EO_MSC_SPEECH_TO_TEXT_RECOGNIZING = 2,
    /// <summary>
    /// 语音识别最终结果
    /// </summary>
    [pbr::OriginalName("EO_MSC_SPEECH_TO_TEXT_RECOGNIZED")] EO_MSC_SPEECH_TO_TEXT_RECOGNIZED = 3,
    /// <summary>
    /// 语音合成结果
    /// </summary>
    [pbr::OriginalName("EO_MSC_TEXT_TO_SPEECH")] EO_MSC_TEXT_TO_SPEECH = 4,
    /// <summary>
    /// Avatar回复文本
    /// </summary>
    [pbr::OriginalName("EO_MSC_AVATAR_REPLY")] EO_MSC_AVATAR_REPLY = 5,
    /// <summary>
    /// Avatar回复文本翻译
    /// </summary>
    [pbr::OriginalName("EO_MSC_AVATAR_REPLY_TRANSLATE")] EO_MSC_AVATAR_REPLY_TRANSLATE = 6,
    /// <summary>
    /// 用户回复示例
    /// </summary>
    [pbr::OriginalName("EO_MSC_USER_REPLY_EXAMPLE")] EO_MSC_USER_REPLY_EXAMPLE = 7,
    /// <summary>
    /// 用户回复示例翻译
    /// </summary>
    [pbr::OriginalName("EO_MSC_USER_REPLY_EXAMPLE_TRANSLATE")] EO_MSC_USER_REPLY_EXAMPLE_TRANSLATE = 8,
    /// <summary>
    /// LLM首包时延（TTFB 代表 "Time to First Byte"（首字节时间））
    /// </summary>
    [pbr::OriginalName("EO_MSC_LLM_TTFB")] EO_MSC_LLM_TTFB = 9,
    /// <summary>
    /// 当前轮次
    /// </summary>
    [pbr::OriginalName("EO_MSC_CURRENT_ROUND")] EO_MSC_CURRENT_ROUND = 10,
    /// <summary>
    /// 是否结束对话
    /// </summary>
    [pbr::OriginalName("EO_MSC_IS_FINISH_DIALOG")] EO_MSC_IS_FINISH_DIALOG = 11,
    /// <summary>
    /// 用户建议
    /// </summary>
    [pbr::OriginalName("EO_MSC_ADVICE")] EO_MSC_ADVICE = 12,
    /// <summary>
    /// 用户建议翻译
    /// </summary>
    [pbr::OriginalName("EO_MSC_ADVICE_TRANSLATE")] EO_MSC_ADVICE_TRANSLATE = 13,
    /// <summary>
    /// 任务进度
    /// </summary>
    [pbr::OriginalName("EO_MSC_TASK_PROGRESS")] EO_MSC_TASK_PROGRESS = 14,
    /// <summary>
    /// 任务完成结果
    /// </summary>
    [pbr::OriginalName("EO_MSC_TASK_RESULT")] EO_MSC_TASK_RESULT = 15,
  }

  #endregion

  #region Messages
  /// <summary>
  ///
  /// Mission剧情对话功能上行消息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_MissionStoryChatUpMsg : pb::IMessage<PB_MissionStoryChatUpMsg>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_MissionStoryChatUpMsg> _parser = new pb::MessageParser<PB_MissionStoryChatUpMsg>(() => new PB_MissionStoryChatUpMsg());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_MissionStoryChatUpMsg> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MissionStoryReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MissionStoryChatUpMsg() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MissionStoryChatUpMsg(PB_MissionStoryChatUpMsg other) : this() {
      missionId_ = other.missionId_;
      storyId_ = other.storyId_;
      taskId_ = other.taskId_;
      switch (other.upBizMsgCase) {
        case upBizMsgOneofCase.userInputAudio:
          userInputAudio = other.userInputAudio.Clone();
          break;
        case upBizMsgOneofCase.upBizEvent:
          upBizEvent = other.upBizEvent;
          break;
        case upBizMsgOneofCase.getUserExampleAudio:
          getUserExampleAudio = other.getUserExampleAudio.Clone();
          break;
        case upBizMsgOneofCase.difficultyFeedback:
          difficultyFeedback = other.difficultyFeedback.Clone();
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MissionStoryChatUpMsg Clone() {
      return new PB_MissionStoryChatUpMsg(this);
    }

    /// <summary>Field number for the "missionId" field.</summary>
    public const int missionIdFieldNumber = 1;
    private long missionId_;
    /// <summary>
    /// missionId
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long missionId {
      get { return missionId_; }
      set {
        missionId_ = value;
      }
    }

    /// <summary>Field number for the "storyId" field.</summary>
    public const int storyIdFieldNumber = 2;
    private long storyId_;
    /// <summary>
    /// storyId
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long storyId {
      get { return storyId_; }
      set {
        storyId_ = value;
      }
    }

    /// <summary>Field number for the "taskId" field.</summary>
    public const int taskIdFieldNumber = 3;
    private long taskId_;
    /// <summary>
    /// taskId
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long taskId {
      get { return taskId_; }
      set {
        taskId_ = value;
      }
    }

    /// <summary>Field number for the "userInputAudio" field.</summary>
    public const int userInputAudioFieldNumber = 4;
    /// <summary>
    /// 用户输入音频
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_MissionStoryChatAudioUpFrame userInputAudio {
      get { return upBizMsgCase_ == upBizMsgOneofCase.userInputAudio ? (global::Msg.explore.PB_Explore_MissionStoryChatAudioUpFrame) upBizMsg_ : null; }
      set {
        upBizMsg_ = value;
        upBizMsgCase_ = value == null ? upBizMsgOneofCase.None : upBizMsgOneofCase.userInputAudio;
      }
    }

    /// <summary>Field number for the "upBizEvent" field.</summary>
    public const int upBizEventFieldNumber = 5;
    /// <summary>
    /// 上行业务事件
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_MissionStoryChatUpBizEvent upBizEvent {
      get { return HasupBizEvent ? (global::Msg.explore.PB_Explore_MissionStoryChatUpBizEvent) upBizMsg_ : global::Msg.explore.PB_Explore_MissionStoryChatUpBizEvent.EO_MSC_UNKNOWN_BIZ_EVENT_TYPE; }
      set {
        upBizMsg_ = value;
        upBizMsgCase_ = upBizMsgOneofCase.upBizEvent;
      }
    }
    /// <summary>Gets whether the "upBizEvent" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasupBizEvent {
      get { return upBizMsgCase_ == upBizMsgOneofCase.upBizEvent; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "upBizEvent" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearupBizEvent() {
      if (HasupBizEvent) {
        ClearupBizMsg();
      }
    }

    /// <summary>Field number for the "getUserExampleAudio" field.</summary>
    public const int getUserExampleAudioFieldNumber = 6;
    /// <summary>
    /// 获取用户例句音频
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_MissionStoryChatUpGetUserExampleAudio getUserExampleAudio {
      get { return upBizMsgCase_ == upBizMsgOneofCase.getUserExampleAudio ? (global::Msg.explore.PB_Explore_MissionStoryChatUpGetUserExampleAudio) upBizMsg_ : null; }
      set {
        upBizMsg_ = value;
        upBizMsgCase_ = value == null ? upBizMsgOneofCase.None : upBizMsgOneofCase.getUserExampleAudio;
      }
    }

    /// <summary>Field number for the "difficultyFeedback" field.</summary>
    public const int difficultyFeedbackFieldNumber = 7;
    /// <summary>
    /// 难度反馈
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_MissionStoryChatUpDifficultyFeedback difficultyFeedback {
      get { return upBizMsgCase_ == upBizMsgOneofCase.difficultyFeedback ? (global::Msg.explore.PB_Explore_MissionStoryChatUpDifficultyFeedback) upBizMsg_ : null; }
      set {
        upBizMsg_ = value;
        upBizMsgCase_ = value == null ? upBizMsgOneofCase.None : upBizMsgOneofCase.difficultyFeedback;
      }
    }

    private object upBizMsg_;
    /// <summary>Enum of possible cases for the "upBizMsg" oneof.</summary>
    public enum upBizMsgOneofCase {
      None = 0,
      userInputAudio = 4,
      upBizEvent = 5,
      getUserExampleAudio = 6,
      difficultyFeedback = 7,
    }
    private upBizMsgOneofCase upBizMsgCase_ = upBizMsgOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public upBizMsgOneofCase upBizMsgCase {
      get { return upBizMsgCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearupBizMsg() {
      upBizMsgCase_ = upBizMsgOneofCase.None;
      upBizMsg_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_MissionStoryChatUpMsg);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_MissionStoryChatUpMsg other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (missionId != other.missionId) return false;
      if (storyId != other.storyId) return false;
      if (taskId != other.taskId) return false;
      if (!object.Equals(userInputAudio, other.userInputAudio)) return false;
      if (upBizEvent != other.upBizEvent) return false;
      if (!object.Equals(getUserExampleAudio, other.getUserExampleAudio)) return false;
      if (!object.Equals(difficultyFeedback, other.difficultyFeedback)) return false;
      if (upBizMsgCase != other.upBizMsgCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (missionId != 0L) hash ^= missionId.GetHashCode();
      if (storyId != 0L) hash ^= storyId.GetHashCode();
      if (taskId != 0L) hash ^= taskId.GetHashCode();
      if (upBizMsgCase_ == upBizMsgOneofCase.userInputAudio) hash ^= userInputAudio.GetHashCode();
      if (HasupBizEvent) hash ^= upBizEvent.GetHashCode();
      if (upBizMsgCase_ == upBizMsgOneofCase.getUserExampleAudio) hash ^= getUserExampleAudio.GetHashCode();
      if (upBizMsgCase_ == upBizMsgOneofCase.difficultyFeedback) hash ^= difficultyFeedback.GetHashCode();
      hash ^= (int) upBizMsgCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (missionId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(missionId);
      }
      if (storyId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(storyId);
      }
      if (taskId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(taskId);
      }
      if (upBizMsgCase_ == upBizMsgOneofCase.userInputAudio) {
        output.WriteRawTag(34);
        output.WriteMessage(userInputAudio);
      }
      if (HasupBizEvent) {
        output.WriteRawTag(40);
        output.WriteEnum((int) upBizEvent);
      }
      if (upBizMsgCase_ == upBizMsgOneofCase.getUserExampleAudio) {
        output.WriteRawTag(50);
        output.WriteMessage(getUserExampleAudio);
      }
      if (upBizMsgCase_ == upBizMsgOneofCase.difficultyFeedback) {
        output.WriteRawTag(58);
        output.WriteMessage(difficultyFeedback);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (missionId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(missionId);
      }
      if (storyId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(storyId);
      }
      if (taskId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(taskId);
      }
      if (upBizMsgCase_ == upBizMsgOneofCase.userInputAudio) {
        output.WriteRawTag(34);
        output.WriteMessage(userInputAudio);
      }
      if (HasupBizEvent) {
        output.WriteRawTag(40);
        output.WriteEnum((int) upBizEvent);
      }
      if (upBizMsgCase_ == upBizMsgOneofCase.getUserExampleAudio) {
        output.WriteRawTag(50);
        output.WriteMessage(getUserExampleAudio);
      }
      if (upBizMsgCase_ == upBizMsgOneofCase.difficultyFeedback) {
        output.WriteRawTag(58);
        output.WriteMessage(difficultyFeedback);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (missionId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(missionId);
      }
      if (storyId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(storyId);
      }
      if (taskId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(taskId);
      }
      if (upBizMsgCase_ == upBizMsgOneofCase.userInputAudio) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(userInputAudio);
      }
      if (HasupBizEvent) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) upBizEvent);
      }
      if (upBizMsgCase_ == upBizMsgOneofCase.getUserExampleAudio) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(getUserExampleAudio);
      }
      if (upBizMsgCase_ == upBizMsgOneofCase.difficultyFeedback) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(difficultyFeedback);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_MissionStoryChatUpMsg other) {
      if (other == null) {
        return;
      }
      if (other.missionId != 0L) {
        missionId = other.missionId;
      }
      if (other.storyId != 0L) {
        storyId = other.storyId;
      }
      if (other.taskId != 0L) {
        taskId = other.taskId;
      }
      switch (other.upBizMsgCase) {
        case upBizMsgOneofCase.userInputAudio:
          if (userInputAudio == null) {
            userInputAudio = new global::Msg.explore.PB_Explore_MissionStoryChatAudioUpFrame();
          }
          userInputAudio.MergeFrom(other.userInputAudio);
          break;
        case upBizMsgOneofCase.upBizEvent:
          upBizEvent = other.upBizEvent;
          break;
        case upBizMsgOneofCase.getUserExampleAudio:
          if (getUserExampleAudio == null) {
            getUserExampleAudio = new global::Msg.explore.PB_Explore_MissionStoryChatUpGetUserExampleAudio();
          }
          getUserExampleAudio.MergeFrom(other.getUserExampleAudio);
          break;
        case upBizMsgOneofCase.difficultyFeedback:
          if (difficultyFeedback == null) {
            difficultyFeedback = new global::Msg.explore.PB_Explore_MissionStoryChatUpDifficultyFeedback();
          }
          difficultyFeedback.MergeFrom(other.difficultyFeedback);
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            missionId = input.ReadInt64();
            break;
          }
          case 16: {
            storyId = input.ReadInt64();
            break;
          }
          case 24: {
            taskId = input.ReadInt64();
            break;
          }
          case 34: {
            global::Msg.explore.PB_Explore_MissionStoryChatAudioUpFrame subBuilder = new global::Msg.explore.PB_Explore_MissionStoryChatAudioUpFrame();
            if (upBizMsgCase_ == upBizMsgOneofCase.userInputAudio) {
              subBuilder.MergeFrom(userInputAudio);
            }
            input.ReadMessage(subBuilder);
            userInputAudio = subBuilder;
            break;
          }
          case 40: {
            upBizMsg_ = input.ReadEnum();
            upBizMsgCase_ = upBizMsgOneofCase.upBizEvent;
            break;
          }
          case 50: {
            global::Msg.explore.PB_Explore_MissionStoryChatUpGetUserExampleAudio subBuilder = new global::Msg.explore.PB_Explore_MissionStoryChatUpGetUserExampleAudio();
            if (upBizMsgCase_ == upBizMsgOneofCase.getUserExampleAudio) {
              subBuilder.MergeFrom(getUserExampleAudio);
            }
            input.ReadMessage(subBuilder);
            getUserExampleAudio = subBuilder;
            break;
          }
          case 58: {
            global::Msg.explore.PB_Explore_MissionStoryChatUpDifficultyFeedback subBuilder = new global::Msg.explore.PB_Explore_MissionStoryChatUpDifficultyFeedback();
            if (upBizMsgCase_ == upBizMsgOneofCase.difficultyFeedback) {
              subBuilder.MergeFrom(difficultyFeedback);
            }
            input.ReadMessage(subBuilder);
            difficultyFeedback = subBuilder;
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            missionId = input.ReadInt64();
            break;
          }
          case 16: {
            storyId = input.ReadInt64();
            break;
          }
          case 24: {
            taskId = input.ReadInt64();
            break;
          }
          case 34: {
            global::Msg.explore.PB_Explore_MissionStoryChatAudioUpFrame subBuilder = new global::Msg.explore.PB_Explore_MissionStoryChatAudioUpFrame();
            if (upBizMsgCase_ == upBizMsgOneofCase.userInputAudio) {
              subBuilder.MergeFrom(userInputAudio);
            }
            input.ReadMessage(subBuilder);
            userInputAudio = subBuilder;
            break;
          }
          case 40: {
            upBizMsg_ = input.ReadEnum();
            upBizMsgCase_ = upBizMsgOneofCase.upBizEvent;
            break;
          }
          case 50: {
            global::Msg.explore.PB_Explore_MissionStoryChatUpGetUserExampleAudio subBuilder = new global::Msg.explore.PB_Explore_MissionStoryChatUpGetUserExampleAudio();
            if (upBizMsgCase_ == upBizMsgOneofCase.getUserExampleAudio) {
              subBuilder.MergeFrom(getUserExampleAudio);
            }
            input.ReadMessage(subBuilder);
            getUserExampleAudio = subBuilder;
            break;
          }
          case 58: {
            global::Msg.explore.PB_Explore_MissionStoryChatUpDifficultyFeedback subBuilder = new global::Msg.explore.PB_Explore_MissionStoryChatUpDifficultyFeedback();
            if (upBizMsgCase_ == upBizMsgOneofCase.difficultyFeedback) {
              subBuilder.MergeFrom(difficultyFeedback);
            }
            input.ReadMessage(subBuilder);
            difficultyFeedback = subBuilder;
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Mission剧情对话上行 - 音频框架（用户录音）
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Explore_MissionStoryChatAudioUpFrame : pb::IMessage<PB_Explore_MissionStoryChatAudioUpFrame>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Explore_MissionStoryChatAudioUpFrame> _parser = new pb::MessageParser<PB_Explore_MissionStoryChatAudioUpFrame>(() => new PB_Explore_MissionStoryChatAudioUpFrame());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Explore_MissionStoryChatAudioUpFrame> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MissionStoryReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_MissionStoryChatAudioUpFrame() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_MissionStoryChatAudioUpFrame(PB_Explore_MissionStoryChatAudioUpFrame other) : this() {
      audio_ = other.audio_;
      sample_rate_ = other.sample_rate_;
      num_channels_ = other.num_channels_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_MissionStoryChatAudioUpFrame Clone() {
      return new PB_Explore_MissionStoryChatAudioUpFrame(this);
    }

    /// <summary>Field number for the "audio" field.</summary>
    public const int audioFieldNumber = 1;
    private pb::ByteString audio_ = pb::ByteString.Empty;
    /// <summary>
    /// 音频数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString audio {
      get { return audio_; }
      set {
        audio_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "sample_rate" field.</summary>
    public const int sample_rateFieldNumber = 2;
    private uint sample_rate_;
    /// <summary>
    /// 采样率
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint sample_rate {
      get { return sample_rate_; }
      set {
        sample_rate_ = value;
      }
    }

    /// <summary>Field number for the "num_channels" field.</summary>
    public const int num_channelsFieldNumber = 3;
    private uint num_channels_;
    /// <summary>
    /// 声道数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint num_channels {
      get { return num_channels_; }
      set {
        num_channels_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Explore_MissionStoryChatAudioUpFrame);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Explore_MissionStoryChatAudioUpFrame other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (audio != other.audio) return false;
      if (sample_rate != other.sample_rate) return false;
      if (num_channels != other.num_channels) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (audio.Length != 0) hash ^= audio.GetHashCode();
      if (sample_rate != 0) hash ^= sample_rate.GetHashCode();
      if (num_channels != 0) hash ^= num_channels.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (audio.Length != 0) {
        output.WriteRawTag(10);
        output.WriteBytes(audio);
      }
      if (sample_rate != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(sample_rate);
      }
      if (num_channels != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(num_channels);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (audio.Length != 0) {
        output.WriteRawTag(10);
        output.WriteBytes(audio);
      }
      if (sample_rate != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(sample_rate);
      }
      if (num_channels != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(num_channels);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (audio.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(audio);
      }
      if (sample_rate != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(sample_rate);
      }
      if (num_channels != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(num_channels);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Explore_MissionStoryChatAudioUpFrame other) {
      if (other == null) {
        return;
      }
      if (other.audio.Length != 0) {
        audio = other.audio;
      }
      if (other.sample_rate != 0) {
        sample_rate = other.sample_rate;
      }
      if (other.num_channels != 0) {
        num_channels = other.num_channels;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            audio = input.ReadBytes();
            break;
          }
          case 16: {
            sample_rate = input.ReadUInt32();
            break;
          }
          case 24: {
            num_channels = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            audio = input.ReadBytes();
            break;
          }
          case 16: {
            sample_rate = input.ReadUInt32();
            break;
          }
          case 24: {
            num_channels = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Mission剧情对话上行 - 获取用户例句音频
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Explore_MissionStoryChatUpGetUserExampleAudio : pb::IMessage<PB_Explore_MissionStoryChatUpGetUserExampleAudio>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Explore_MissionStoryChatUpGetUserExampleAudio> _parser = new pb::MessageParser<PB_Explore_MissionStoryChatUpGetUserExampleAudio>(() => new PB_Explore_MissionStoryChatUpGetUserExampleAudio());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Explore_MissionStoryChatUpGetUserExampleAudio> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MissionStoryReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_MissionStoryChatUpGetUserExampleAudio() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_MissionStoryChatUpGetUserExampleAudio(PB_Explore_MissionStoryChatUpGetUserExampleAudio other) : this() {
      exampleBubbleId_ = other.exampleBubbleId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_MissionStoryChatUpGetUserExampleAudio Clone() {
      return new PB_Explore_MissionStoryChatUpGetUserExampleAudio(this);
    }

    /// <summary>Field number for the "exampleBubbleId" field.</summary>
    public const int exampleBubbleIdFieldNumber = 1;
    private string exampleBubbleId_ = "";
    /// <summary>
    /// 用户回复示例气泡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string exampleBubbleId {
      get { return exampleBubbleId_; }
      set {
        exampleBubbleId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Explore_MissionStoryChatUpGetUserExampleAudio);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Explore_MissionStoryChatUpGetUserExampleAudio other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (exampleBubbleId != other.exampleBubbleId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (exampleBubbleId.Length != 0) hash ^= exampleBubbleId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (exampleBubbleId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(exampleBubbleId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (exampleBubbleId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(exampleBubbleId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (exampleBubbleId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(exampleBubbleId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Explore_MissionStoryChatUpGetUserExampleAudio other) {
      if (other == null) {
        return;
      }
      if (other.exampleBubbleId.Length != 0) {
        exampleBubbleId = other.exampleBubbleId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            exampleBubbleId = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            exampleBubbleId = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Mission剧情对话上行 - 难度反馈
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Explore_MissionStoryChatUpDifficultyFeedback : pb::IMessage<PB_Explore_MissionStoryChatUpDifficultyFeedback>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Explore_MissionStoryChatUpDifficultyFeedback> _parser = new pb::MessageParser<PB_Explore_MissionStoryChatUpDifficultyFeedback>(() => new PB_Explore_MissionStoryChatUpDifficultyFeedback());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Explore_MissionStoryChatUpDifficultyFeedback> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MissionStoryReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_MissionStoryChatUpDifficultyFeedback() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_MissionStoryChatUpDifficultyFeedback(PB_Explore_MissionStoryChatUpDifficultyFeedback other) : this() {
      missionId_ = other.missionId_;
      storyId_ = other.storyId_;
      taskId_ = other.taskId_;
      difficulty_ = other.difficulty_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_MissionStoryChatUpDifficultyFeedback Clone() {
      return new PB_Explore_MissionStoryChatUpDifficultyFeedback(this);
    }

    /// <summary>Field number for the "missionId" field.</summary>
    public const int missionIdFieldNumber = 1;
    private long missionId_;
    /// <summary>
    /// missionId
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long missionId {
      get { return missionId_; }
      set {
        missionId_ = value;
      }
    }

    /// <summary>Field number for the "storyId" field.</summary>
    public const int storyIdFieldNumber = 2;
    private long storyId_;
    /// <summary>
    /// storyId
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long storyId {
      get { return storyId_; }
      set {
        storyId_ = value;
      }
    }

    /// <summary>Field number for the "taskId" field.</summary>
    public const int taskIdFieldNumber = 3;
    private long taskId_;
    /// <summary>
    /// taskId
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long taskId {
      get { return taskId_; }
      set {
        taskId_ = value;
      }
    }

    /// <summary>Field number for the "difficulty" field.</summary>
    public const int difficultyFieldNumber = 4;
    private global::Msg.explore.PB_Explore_MissionStoryChatDifficultyFeedback difficulty_ = global::Msg.explore.PB_Explore_MissionStoryChatDifficultyFeedback.EO_MSC_DF_UNKNOWN;
    /// <summary>
    /// 难度反馈枚举
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_MissionStoryChatDifficultyFeedback difficulty {
      get { return difficulty_; }
      set {
        difficulty_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Explore_MissionStoryChatUpDifficultyFeedback);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Explore_MissionStoryChatUpDifficultyFeedback other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (missionId != other.missionId) return false;
      if (storyId != other.storyId) return false;
      if (taskId != other.taskId) return false;
      if (difficulty != other.difficulty) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (missionId != 0L) hash ^= missionId.GetHashCode();
      if (storyId != 0L) hash ^= storyId.GetHashCode();
      if (taskId != 0L) hash ^= taskId.GetHashCode();
      if (difficulty != global::Msg.explore.PB_Explore_MissionStoryChatDifficultyFeedback.EO_MSC_DF_UNKNOWN) hash ^= difficulty.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (missionId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(missionId);
      }
      if (storyId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(storyId);
      }
      if (taskId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(taskId);
      }
      if (difficulty != global::Msg.explore.PB_Explore_MissionStoryChatDifficultyFeedback.EO_MSC_DF_UNKNOWN) {
        output.WriteRawTag(32);
        output.WriteEnum((int) difficulty);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (missionId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(missionId);
      }
      if (storyId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(storyId);
      }
      if (taskId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(taskId);
      }
      if (difficulty != global::Msg.explore.PB_Explore_MissionStoryChatDifficultyFeedback.EO_MSC_DF_UNKNOWN) {
        output.WriteRawTag(32);
        output.WriteEnum((int) difficulty);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (missionId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(missionId);
      }
      if (storyId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(storyId);
      }
      if (taskId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(taskId);
      }
      if (difficulty != global::Msg.explore.PB_Explore_MissionStoryChatDifficultyFeedback.EO_MSC_DF_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) difficulty);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Explore_MissionStoryChatUpDifficultyFeedback other) {
      if (other == null) {
        return;
      }
      if (other.missionId != 0L) {
        missionId = other.missionId;
      }
      if (other.storyId != 0L) {
        storyId = other.storyId;
      }
      if (other.taskId != 0L) {
        taskId = other.taskId;
      }
      if (other.difficulty != global::Msg.explore.PB_Explore_MissionStoryChatDifficultyFeedback.EO_MSC_DF_UNKNOWN) {
        difficulty = other.difficulty;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            missionId = input.ReadInt64();
            break;
          }
          case 16: {
            storyId = input.ReadInt64();
            break;
          }
          case 24: {
            taskId = input.ReadInt64();
            break;
          }
          case 32: {
            difficulty = (global::Msg.explore.PB_Explore_MissionStoryChatDifficultyFeedback) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            missionId = input.ReadInt64();
            break;
          }
          case 16: {
            storyId = input.ReadInt64();
            break;
          }
          case 24: {
            taskId = input.ReadInt64();
            break;
          }
          case 32: {
            difficulty = (global::Msg.explore.PB_Explore_MissionStoryChatDifficultyFeedback) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Mission剧情对话音频下行框架（TTS）
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Explore_MissionStoryChatAudioDownFrame : pb::IMessage<PB_Explore_MissionStoryChatAudioDownFrame>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Explore_MissionStoryChatAudioDownFrame> _parser = new pb::MessageParser<PB_Explore_MissionStoryChatAudioDownFrame>(() => new PB_Explore_MissionStoryChatAudioDownFrame());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Explore_MissionStoryChatAudioDownFrame> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MissionStoryReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_MissionStoryChatAudioDownFrame() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_MissionStoryChatAudioDownFrame(PB_Explore_MissionStoryChatAudioDownFrame other) : this() {
      id_ = other.id_;
      audio_ = other.audio_;
      sample_rate_ = other.sample_rate_;
      num_channels_ = other.num_channels_;
      bubble_id_ = other.bubble_id_;
      is_last_clip_ = other.is_last_clip_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_MissionStoryChatAudioDownFrame Clone() {
      return new PB_Explore_MissionStoryChatAudioDownFrame(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int idFieldNumber = 1;
    private ulong id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "audio" field.</summary>
    public const int audioFieldNumber = 2;
    private pb::ByteString audio_ = pb::ByteString.Empty;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString audio {
      get { return audio_; }
      set {
        audio_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "sample_rate" field.</summary>
    public const int sample_rateFieldNumber = 3;
    private uint sample_rate_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint sample_rate {
      get { return sample_rate_; }
      set {
        sample_rate_ = value;
      }
    }

    /// <summary>Field number for the "num_channels" field.</summary>
    public const int num_channelsFieldNumber = 4;
    private uint num_channels_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint num_channels {
      get { return num_channels_; }
      set {
        num_channels_ = value;
      }
    }

    /// <summary>Field number for the "bubble_id" field.</summary>
    public const int bubble_idFieldNumber = 5;
    private string bubble_id_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string bubble_id {
      get { return bubble_id_; }
      set {
        bubble_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "is_last_clip" field.</summary>
    public const int is_last_clipFieldNumber = 6;
    private bool is_last_clip_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is_last_clip {
      get { return is_last_clip_; }
      set {
        is_last_clip_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Explore_MissionStoryChatAudioDownFrame);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Explore_MissionStoryChatAudioDownFrame other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (id != other.id) return false;
      if (audio != other.audio) return false;
      if (sample_rate != other.sample_rate) return false;
      if (num_channels != other.num_channels) return false;
      if (bubble_id != other.bubble_id) return false;
      if (is_last_clip != other.is_last_clip) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (id != 0UL) hash ^= id.GetHashCode();
      if (audio.Length != 0) hash ^= audio.GetHashCode();
      if (sample_rate != 0) hash ^= sample_rate.GetHashCode();
      if (num_channels != 0) hash ^= num_channels.GetHashCode();
      if (bubble_id.Length != 0) hash ^= bubble_id.GetHashCode();
      if (is_last_clip != false) hash ^= is_last_clip.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(id);
      }
      if (audio.Length != 0) {
        output.WriteRawTag(18);
        output.WriteBytes(audio);
      }
      if (sample_rate != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(sample_rate);
      }
      if (num_channels != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(num_channels);
      }
      if (bubble_id.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(bubble_id);
      }
      if (is_last_clip != false) {
        output.WriteRawTag(48);
        output.WriteBool(is_last_clip);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(id);
      }
      if (audio.Length != 0) {
        output.WriteRawTag(18);
        output.WriteBytes(audio);
      }
      if (sample_rate != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(sample_rate);
      }
      if (num_channels != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(num_channels);
      }
      if (bubble_id.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(bubble_id);
      }
      if (is_last_clip != false) {
        output.WriteRawTag(48);
        output.WriteBool(is_last_clip);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (id != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(id);
      }
      if (audio.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(audio);
      }
      if (sample_rate != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(sample_rate);
      }
      if (num_channels != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(num_channels);
      }
      if (bubble_id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(bubble_id);
      }
      if (is_last_clip != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Explore_MissionStoryChatAudioDownFrame other) {
      if (other == null) {
        return;
      }
      if (other.id != 0UL) {
        id = other.id;
      }
      if (other.audio.Length != 0) {
        audio = other.audio;
      }
      if (other.sample_rate != 0) {
        sample_rate = other.sample_rate;
      }
      if (other.num_channels != 0) {
        num_channels = other.num_channels;
      }
      if (other.bubble_id.Length != 0) {
        bubble_id = other.bubble_id;
      }
      if (other.is_last_clip != false) {
        is_last_clip = other.is_last_clip;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            id = input.ReadUInt64();
            break;
          }
          case 18: {
            audio = input.ReadBytes();
            break;
          }
          case 24: {
            sample_rate = input.ReadUInt32();
            break;
          }
          case 32: {
            num_channels = input.ReadUInt32();
            break;
          }
          case 42: {
            bubble_id = input.ReadString();
            break;
          }
          case 48: {
            is_last_clip = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            id = input.ReadUInt64();
            break;
          }
          case 18: {
            audio = input.ReadBytes();
            break;
          }
          case 24: {
            sample_rate = input.ReadUInt32();
            break;
          }
          case 32: {
            num_channels = input.ReadUInt32();
            break;
          }
          case 42: {
            bubble_id = input.ReadString();
            break;
          }
          case 48: {
            is_last_clip = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Mission剧情对话下行 - 通用数据字段（数据类）
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_MissionStoryChatCommonDataFieldForDataClass : pb::IMessage<PB_MissionStoryChatCommonDataFieldForDataClass>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_MissionStoryChatCommonDataFieldForDataClass> _parser = new pb::MessageParser<PB_MissionStoryChatCommonDataFieldForDataClass>(() => new PB_MissionStoryChatCommonDataFieldForDataClass());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_MissionStoryChatCommonDataFieldForDataClass> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MissionStoryReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MissionStoryChatCommonDataFieldForDataClass() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MissionStoryChatCommonDataFieldForDataClass(PB_MissionStoryChatCommonDataFieldForDataClass other) : this() {
      dialogId_ = other.dialogId_;
      msgId_ = other.msgId_;
      bubbleId_ = other.bubbleId_;
      round_ = other.round_;
      msgOwner_ = other.msgOwner_;
      taskId_ = other.taskId_;
      storyId_ = other.storyId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MissionStoryChatCommonDataFieldForDataClass Clone() {
      return new PB_MissionStoryChatCommonDataFieldForDataClass(this);
    }

    /// <summary>Field number for the "dialogId" field.</summary>
    public const int dialogIdFieldNumber = 1;
    private long dialogId_;
    /// <summary>
    /// 对话id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long dialogId {
      get { return dialogId_; }
      set {
        dialogId_ = value;
      }
    }

    /// <summary>Field number for the "msgId" field.</summary>
    public const int msgIdFieldNumber = 2;
    private long msgId_;
    /// <summary>
    /// 消息id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long msgId {
      get { return msgId_; }
      set {
        msgId_ = value;
      }
    }

    /// <summary>Field number for the "bubbleId" field.</summary>
    public const int bubbleIdFieldNumber = 3;
    private string bubbleId_ = "";
    /// <summary>
    /// 气泡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string bubbleId {
      get { return bubbleId_; }
      set {
        bubbleId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "round" field.</summary>
    public const int roundFieldNumber = 4;
    private int round_;
    /// <summary>
    /// 对话轮次
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int round {
      get { return round_; }
      set {
        round_ = value;
      }
    }

    /// <summary>Field number for the "msgOwner" field.</summary>
    public const int msgOwnerFieldNumber = 5;
    private global::Msg.basic.PB_MsgBelong msgOwner_ = global::Msg.basic.PB_MsgBelong.BNone;
    /// <summary>
    /// 消息归属方
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_MsgBelong msgOwner {
      get { return msgOwner_; }
      set {
        msgOwner_ = value;
      }
    }

    /// <summary>Field number for the "taskId" field.</summary>
    public const int taskIdFieldNumber = 6;
    private long taskId_;
    /// <summary>
    /// 任务id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long taskId {
      get { return taskId_; }
      set {
        taskId_ = value;
      }
    }

    /// <summary>Field number for the "storyId" field.</summary>
    public const int storyIdFieldNumber = 7;
    private long storyId_;
    /// <summary>
    /// story_id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long storyId {
      get { return storyId_; }
      set {
        storyId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_MissionStoryChatCommonDataFieldForDataClass);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_MissionStoryChatCommonDataFieldForDataClass other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (dialogId != other.dialogId) return false;
      if (msgId != other.msgId) return false;
      if (bubbleId != other.bubbleId) return false;
      if (round != other.round) return false;
      if (msgOwner != other.msgOwner) return false;
      if (taskId != other.taskId) return false;
      if (storyId != other.storyId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (dialogId != 0L) hash ^= dialogId.GetHashCode();
      if (msgId != 0L) hash ^= msgId.GetHashCode();
      if (bubbleId.Length != 0) hash ^= bubbleId.GetHashCode();
      if (round != 0) hash ^= round.GetHashCode();
      if (msgOwner != global::Msg.basic.PB_MsgBelong.BNone) hash ^= msgOwner.GetHashCode();
      if (taskId != 0L) hash ^= taskId.GetHashCode();
      if (storyId != 0L) hash ^= storyId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (dialogId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(dialogId);
      }
      if (msgId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(msgId);
      }
      if (bubbleId.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(bubbleId);
      }
      if (round != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(round);
      }
      if (msgOwner != global::Msg.basic.PB_MsgBelong.BNone) {
        output.WriteRawTag(40);
        output.WriteEnum((int) msgOwner);
      }
      if (taskId != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(taskId);
      }
      if (storyId != 0L) {
        output.WriteRawTag(56);
        output.WriteInt64(storyId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (dialogId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(dialogId);
      }
      if (msgId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(msgId);
      }
      if (bubbleId.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(bubbleId);
      }
      if (round != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(round);
      }
      if (msgOwner != global::Msg.basic.PB_MsgBelong.BNone) {
        output.WriteRawTag(40);
        output.WriteEnum((int) msgOwner);
      }
      if (taskId != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(taskId);
      }
      if (storyId != 0L) {
        output.WriteRawTag(56);
        output.WriteInt64(storyId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (dialogId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(dialogId);
      }
      if (msgId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(msgId);
      }
      if (bubbleId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(bubbleId);
      }
      if (round != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(round);
      }
      if (msgOwner != global::Msg.basic.PB_MsgBelong.BNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) msgOwner);
      }
      if (taskId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(taskId);
      }
      if (storyId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(storyId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_MissionStoryChatCommonDataFieldForDataClass other) {
      if (other == null) {
        return;
      }
      if (other.dialogId != 0L) {
        dialogId = other.dialogId;
      }
      if (other.msgId != 0L) {
        msgId = other.msgId;
      }
      if (other.bubbleId.Length != 0) {
        bubbleId = other.bubbleId;
      }
      if (other.round != 0) {
        round = other.round;
      }
      if (other.msgOwner != global::Msg.basic.PB_MsgBelong.BNone) {
        msgOwner = other.msgOwner;
      }
      if (other.taskId != 0L) {
        taskId = other.taskId;
      }
      if (other.storyId != 0L) {
        storyId = other.storyId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            dialogId = input.ReadInt64();
            break;
          }
          case 16: {
            msgId = input.ReadInt64();
            break;
          }
          case 26: {
            bubbleId = input.ReadString();
            break;
          }
          case 32: {
            round = input.ReadInt32();
            break;
          }
          case 40: {
            msgOwner = (global::Msg.basic.PB_MsgBelong) input.ReadEnum();
            break;
          }
          case 48: {
            taskId = input.ReadInt64();
            break;
          }
          case 56: {
            storyId = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            dialogId = input.ReadInt64();
            break;
          }
          case 16: {
            msgId = input.ReadInt64();
            break;
          }
          case 26: {
            bubbleId = input.ReadString();
            break;
          }
          case 32: {
            round = input.ReadInt32();
            break;
          }
          case 40: {
            msgOwner = (global::Msg.basic.PB_MsgBelong) input.ReadEnum();
            break;
          }
          case 48: {
            taskId = input.ReadInt64();
            break;
          }
          case 56: {
            storyId = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Mission剧情对话下行 - Avatar回复
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_MissionStoryChatDownMsgForAvatarReply : pb::IMessage<SC_MissionStoryChatDownMsgForAvatarReply>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_MissionStoryChatDownMsgForAvatarReply> _parser = new pb::MessageParser<SC_MissionStoryChatDownMsgForAvatarReply>(() => new SC_MissionStoryChatDownMsgForAvatarReply());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_MissionStoryChatDownMsgForAvatarReply> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MissionStoryReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForAvatarReply() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForAvatarReply(SC_MissionStoryChatDownMsgForAvatarReply other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      replyText_ = other.replyText_;
      isFinish_ = other.isFinish_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForAvatarReply Clone() {
      return new SC_MissionStoryChatDownMsgForAvatarReply(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "replyText" field.</summary>
    public const int replyTextFieldNumber = 3;
    private string replyText_ = "";
    /// <summary>
    /// Avatar回复文本
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string replyText {
      get { return replyText_; }
      set {
        replyText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "isFinish" field.</summary>
    public const int isFinishFieldNumber = 4;
    private bool isFinish_;
    /// <summary>
    /// 对话是否结束
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool isFinish {
      get { return isFinish_; }
      set {
        isFinish_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_MissionStoryChatDownMsgForAvatarReply);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_MissionStoryChatDownMsgForAvatarReply other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (replyText != other.replyText) return false;
      if (isFinish != other.isFinish) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (replyText.Length != 0) hash ^= replyText.GetHashCode();
      if (isFinish != false) hash ^= isFinish.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (replyText.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(replyText);
      }
      if (isFinish != false) {
        output.WriteRawTag(32);
        output.WriteBool(isFinish);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (replyText.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(replyText);
      }
      if (isFinish != false) {
        output.WriteRawTag(32);
        output.WriteBool(isFinish);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (replyText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(replyText);
      }
      if (isFinish != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_MissionStoryChatDownMsgForAvatarReply other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.replyText.Length != 0) {
        replyText = other.replyText;
      }
      if (other.isFinish != false) {
        isFinish = other.isFinish;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            replyText = input.ReadString();
            break;
          }
          case 32: {
            isFinish = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            replyText = input.ReadString();
            break;
          }
          case 32: {
            isFinish = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Mission剧情对话下行 - Avatar回复翻译
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_MissionStoryChatDownMsgForAvatarReplyTranslate : pb::IMessage<SC_MissionStoryChatDownMsgForAvatarReplyTranslate>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_MissionStoryChatDownMsgForAvatarReplyTranslate> _parser = new pb::MessageParser<SC_MissionStoryChatDownMsgForAvatarReplyTranslate>(() => new SC_MissionStoryChatDownMsgForAvatarReplyTranslate());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_MissionStoryChatDownMsgForAvatarReplyTranslate> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MissionStoryReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForAvatarReplyTranslate() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForAvatarReplyTranslate(SC_MissionStoryChatDownMsgForAvatarReplyTranslate other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      replyTranslateText_ = other.replyTranslateText_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForAvatarReplyTranslate Clone() {
      return new SC_MissionStoryChatDownMsgForAvatarReplyTranslate(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "replyTranslateText" field.</summary>
    public const int replyTranslateTextFieldNumber = 3;
    private string replyTranslateText_ = "";
    /// <summary>
    /// Avatar回复文本翻译
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string replyTranslateText {
      get { return replyTranslateText_; }
      set {
        replyTranslateText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_MissionStoryChatDownMsgForAvatarReplyTranslate);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_MissionStoryChatDownMsgForAvatarReplyTranslate other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (replyTranslateText != other.replyTranslateText) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (replyTranslateText.Length != 0) hash ^= replyTranslateText.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (replyTranslateText.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(replyTranslateText);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (replyTranslateText.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(replyTranslateText);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (replyTranslateText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(replyTranslateText);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_MissionStoryChatDownMsgForAvatarReplyTranslate other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.replyTranslateText.Length != 0) {
        replyTranslateText = other.replyTranslateText;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            replyTranslateText = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            replyTranslateText = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Mission剧情对话下行 - Avatar回复TTS
  /// 1. 流式下发：一个音频会出现多个结果
  /// 2. 非流式下发：一个音频只有一个结果
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_MissionStoryChatDownMsgForAvatarReplyTTS : pb::IMessage<SC_MissionStoryChatDownMsgForAvatarReplyTTS>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_MissionStoryChatDownMsgForAvatarReplyTTS> _parser = new pb::MessageParser<SC_MissionStoryChatDownMsgForAvatarReplyTTS>(() => new SC_MissionStoryChatDownMsgForAvatarReplyTTS());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_MissionStoryChatDownMsgForAvatarReplyTTS> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MissionStoryReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForAvatarReplyTTS() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForAvatarReplyTTS(SC_MissionStoryChatDownMsgForAvatarReplyTTS other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      audio_ = other.audio_ != null ? other.audio_.Clone() : null;
      emotionAnalysisResult_ = other.emotionAnalysisResult_ != null ? other.emotionAnalysisResult_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForAvatarReplyTTS Clone() {
      return new SC_MissionStoryChatDownMsgForAvatarReplyTTS(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "audio" field.</summary>
    public const int audioFieldNumber = 3;
    private global::Msg.explore.PB_Explore_MissionStoryChatAudioDownFrame audio_;
    /// <summary>
    /// 语音合成结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_MissionStoryChatAudioDownFrame audio {
      get { return audio_; }
      set {
        audio_ = value;
      }
    }

    /// <summary>Field number for the "emotionAnalysisResult" field.</summary>
    public const int emotionAnalysisResultFieldNumber = 4;
    private global::Msg.explore.PB_EmotionAnalysisResult emotionAnalysisResult_;
    /// <summary>
    /// 情感分析结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_EmotionAnalysisResult emotionAnalysisResult {
      get { return emotionAnalysisResult_; }
      set {
        emotionAnalysisResult_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_MissionStoryChatDownMsgForAvatarReplyTTS);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_MissionStoryChatDownMsgForAvatarReplyTTS other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (!object.Equals(audio, other.audio)) return false;
      if (!object.Equals(emotionAnalysisResult, other.emotionAnalysisResult)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (audio_ != null) hash ^= audio.GetHashCode();
      if (emotionAnalysisResult_ != null) hash ^= emotionAnalysisResult.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (audio_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(audio);
      }
      if (emotionAnalysisResult_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(emotionAnalysisResult);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (audio_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(audio);
      }
      if (emotionAnalysisResult_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(emotionAnalysisResult);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (audio_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(audio);
      }
      if (emotionAnalysisResult_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(emotionAnalysisResult);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_MissionStoryChatDownMsgForAvatarReplyTTS other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.audio_ != null) {
        if (audio_ == null) {
          audio = new global::Msg.explore.PB_Explore_MissionStoryChatAudioDownFrame();
        }
        audio.MergeFrom(other.audio);
      }
      if (other.emotionAnalysisResult_ != null) {
        if (emotionAnalysisResult_ == null) {
          emotionAnalysisResult = new global::Msg.explore.PB_EmotionAnalysisResult();
        }
        emotionAnalysisResult.MergeFrom(other.emotionAnalysisResult);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            if (audio_ == null) {
              audio = new global::Msg.explore.PB_Explore_MissionStoryChatAudioDownFrame();
            }
            input.ReadMessage(audio);
            break;
          }
          case 34: {
            if (emotionAnalysisResult_ == null) {
              emotionAnalysisResult = new global::Msg.explore.PB_EmotionAnalysisResult();
            }
            input.ReadMessage(emotionAnalysisResult);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            if (audio_ == null) {
              audio = new global::Msg.explore.PB_Explore_MissionStoryChatAudioDownFrame();
            }
            input.ReadMessage(audio);
            break;
          }
          case 34: {
            if (emotionAnalysisResult_ == null) {
              emotionAnalysisResult = new global::Msg.explore.PB_EmotionAnalysisResult();
            }
            input.ReadMessage(emotionAnalysisResult);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Mission剧情对话下行 - 用户回复示例（user_reply_example: 也叫事前脚手架）
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_MissionStoryChatDownMsgForUserReplyExample : pb::IMessage<SC_MissionStoryChatDownMsgForUserReplyExample>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_MissionStoryChatDownMsgForUserReplyExample> _parser = new pb::MessageParser<SC_MissionStoryChatDownMsgForUserReplyExample>(() => new SC_MissionStoryChatDownMsgForUserReplyExample());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_MissionStoryChatDownMsgForUserReplyExample> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MissionStoryReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForUserReplyExample() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForUserReplyExample(SC_MissionStoryChatDownMsgForUserReplyExample other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      exampleText_ = other.exampleText_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForUserReplyExample Clone() {
      return new SC_MissionStoryChatDownMsgForUserReplyExample(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "exampleText" field.</summary>
    public const int exampleTextFieldNumber = 3;
    private string exampleText_ = "";
    /// <summary>
    /// 用户回复示例文本
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string exampleText {
      get { return exampleText_; }
      set {
        exampleText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_MissionStoryChatDownMsgForUserReplyExample);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_MissionStoryChatDownMsgForUserReplyExample other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (exampleText != other.exampleText) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (exampleText.Length != 0) hash ^= exampleText.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (exampleText.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(exampleText);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (exampleText.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(exampleText);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (exampleText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(exampleText);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_MissionStoryChatDownMsgForUserReplyExample other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.exampleText.Length != 0) {
        exampleText = other.exampleText;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            exampleText = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            exampleText = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Mission剧情对话下行 - 用户回复示例翻译
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_MissionStoryChatDownMsgForUserReplyExampleTranslate : pb::IMessage<SC_MissionStoryChatDownMsgForUserReplyExampleTranslate>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_MissionStoryChatDownMsgForUserReplyExampleTranslate> _parser = new pb::MessageParser<SC_MissionStoryChatDownMsgForUserReplyExampleTranslate>(() => new SC_MissionStoryChatDownMsgForUserReplyExampleTranslate());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_MissionStoryChatDownMsgForUserReplyExampleTranslate> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MissionStoryReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForUserReplyExampleTranslate() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForUserReplyExampleTranslate(SC_MissionStoryChatDownMsgForUserReplyExampleTranslate other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      exampleTranslateText_ = other.exampleTranslateText_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForUserReplyExampleTranslate Clone() {
      return new SC_MissionStoryChatDownMsgForUserReplyExampleTranslate(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "exampleTranslateText" field.</summary>
    public const int exampleTranslateTextFieldNumber = 3;
    private string exampleTranslateText_ = "";
    /// <summary>
    /// 用户回复示例文本翻译
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string exampleTranslateText {
      get { return exampleTranslateText_; }
      set {
        exampleTranslateText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_MissionStoryChatDownMsgForUserReplyExampleTranslate);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_MissionStoryChatDownMsgForUserReplyExampleTranslate other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (exampleTranslateText != other.exampleTranslateText) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (exampleTranslateText.Length != 0) hash ^= exampleTranslateText.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (exampleTranslateText.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(exampleTranslateText);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (exampleTranslateText.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(exampleTranslateText);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (exampleTranslateText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(exampleTranslateText);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_MissionStoryChatDownMsgForUserReplyExampleTranslate other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.exampleTranslateText.Length != 0) {
        exampleTranslateText = other.exampleTranslateText;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            exampleTranslateText = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            exampleTranslateText = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Mission剧情对话下行 - 用户回复示例TTS
  /// 1. 流式下发：一个音频会出现多个结果
  /// 2. 非流式下发：一个音频只有一个结果
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_MissionStoryChatDownMsgForUserReplyExampleTTS : pb::IMessage<SC_MissionStoryChatDownMsgForUserReplyExampleTTS>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_MissionStoryChatDownMsgForUserReplyExampleTTS> _parser = new pb::MessageParser<SC_MissionStoryChatDownMsgForUserReplyExampleTTS>(() => new SC_MissionStoryChatDownMsgForUserReplyExampleTTS());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_MissionStoryChatDownMsgForUserReplyExampleTTS> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MissionStoryReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForUserReplyExampleTTS() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForUserReplyExampleTTS(SC_MissionStoryChatDownMsgForUserReplyExampleTTS other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      audio_ = other.audio_ != null ? other.audio_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForUserReplyExampleTTS Clone() {
      return new SC_MissionStoryChatDownMsgForUserReplyExampleTTS(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "audio" field.</summary>
    public const int audioFieldNumber = 3;
    private global::Msg.explore.PB_Explore_MissionStoryChatAudioDownFrame audio_;
    /// <summary>
    /// 语音合成结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_MissionStoryChatAudioDownFrame audio {
      get { return audio_; }
      set {
        audio_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_MissionStoryChatDownMsgForUserReplyExampleTTS);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_MissionStoryChatDownMsgForUserReplyExampleTTS other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (!object.Equals(audio, other.audio)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (audio_ != null) hash ^= audio.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (audio_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(audio);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (audio_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(audio);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (audio_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(audio);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_MissionStoryChatDownMsgForUserReplyExampleTTS other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.audio_ != null) {
        if (audio_ == null) {
          audio = new global::Msg.explore.PB_Explore_MissionStoryChatAudioDownFrame();
        }
        audio.MergeFrom(other.audio);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            if (audio_ == null) {
              audio = new global::Msg.explore.PB_Explore_MissionStoryChatAudioDownFrame();
            }
            input.ReadMessage(audio);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            if (audio_ == null) {
              audio = new global::Msg.explore.PB_Explore_MissionStoryChatAudioDownFrame();
            }
            input.ReadMessage(audio);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Mission剧情对话下行 - Advice
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_MissionStoryChatDownMsgForAdvice : pb::IMessage<SC_MissionStoryChatDownMsgForAdvice>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_MissionStoryChatDownMsgForAdvice> _parser = new pb::MessageParser<SC_MissionStoryChatDownMsgForAdvice>(() => new SC_MissionStoryChatDownMsgForAdvice());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_MissionStoryChatDownMsgForAdvice> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MissionStoryReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForAdvice() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForAdvice(SC_MissionStoryChatDownMsgForAdvice other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      adviceText_ = other.adviceText_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForAdvice Clone() {
      return new SC_MissionStoryChatDownMsgForAdvice(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "adviceText" field.</summary>
    public const int adviceTextFieldNumber = 3;
    private string adviceText_ = "";
    /// <summary>
    /// 提示文本
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string adviceText {
      get { return adviceText_; }
      set {
        adviceText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_MissionStoryChatDownMsgForAdvice);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_MissionStoryChatDownMsgForAdvice other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (adviceText != other.adviceText) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (adviceText.Length != 0) hash ^= adviceText.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (adviceText.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(adviceText);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (adviceText.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(adviceText);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (adviceText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(adviceText);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_MissionStoryChatDownMsgForAdvice other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.adviceText.Length != 0) {
        adviceText = other.adviceText;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            adviceText = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            adviceText = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Mission剧情对话下行 - 用户语音识别最终结果
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_MissionStoryChatDownMsgForASR : pb::IMessage<SC_MissionStoryChatDownMsgForASR>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_MissionStoryChatDownMsgForASR> _parser = new pb::MessageParser<SC_MissionStoryChatDownMsgForASR>(() => new SC_MissionStoryChatDownMsgForASR());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_MissionStoryChatDownMsgForASR> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MissionStoryReflection.Descriptor.MessageTypes[13]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForASR() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForASR(SC_MissionStoryChatDownMsgForASR other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      text_ = other.text_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForASR Clone() {
      return new SC_MissionStoryChatDownMsgForASR(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "text" field.</summary>
    public const int textFieldNumber = 3;
    private string text_ = "";
    /// <summary>
    /// 语音识别结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string text {
      get { return text_; }
      set {
        text_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_MissionStoryChatDownMsgForASR);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_MissionStoryChatDownMsgForASR other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (text != other.text) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (text.Length != 0) hash ^= text.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (text.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(text);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (text.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(text);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (text.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(text);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_MissionStoryChatDownMsgForASR other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.text.Length != 0) {
        text = other.text;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            text = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            text = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Mission剧情对话下行 - 业务事件
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_MissionStoryChatDownMsgForBizEvent : pb::IMessage<SC_MissionStoryChatDownMsgForBizEvent>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_MissionStoryChatDownMsgForBizEvent> _parser = new pb::MessageParser<SC_MissionStoryChatDownMsgForBizEvent>(() => new SC_MissionStoryChatDownMsgForBizEvent());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_MissionStoryChatDownMsgForBizEvent> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MissionStoryReflection.Descriptor.MessageTypes[14]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForBizEvent() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForBizEvent(SC_MissionStoryChatDownMsgForBizEvent other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      bizEvent_ = other.bizEvent_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForBizEvent Clone() {
      return new SC_MissionStoryChatDownMsgForBizEvent(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "bizEvent" field.</summary>
    public const int bizEventFieldNumber = 3;
    private global::Msg.explore.PB_Explore_MissionStoryChatUpBizEvent bizEvent_ = global::Msg.explore.PB_Explore_MissionStoryChatUpBizEvent.EO_MSC_UNKNOWN_BIZ_EVENT_TYPE;
    /// <summary>
    /// 业务事件
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_MissionStoryChatUpBizEvent bizEvent {
      get { return bizEvent_; }
      set {
        bizEvent_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_MissionStoryChatDownMsgForBizEvent);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_MissionStoryChatDownMsgForBizEvent other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (bizEvent != other.bizEvent) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (bizEvent != global::Msg.explore.PB_Explore_MissionStoryChatUpBizEvent.EO_MSC_UNKNOWN_BIZ_EVENT_TYPE) hash ^= bizEvent.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (bizEvent != global::Msg.explore.PB_Explore_MissionStoryChatUpBizEvent.EO_MSC_UNKNOWN_BIZ_EVENT_TYPE) {
        output.WriteRawTag(24);
        output.WriteEnum((int) bizEvent);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (bizEvent != global::Msg.explore.PB_Explore_MissionStoryChatUpBizEvent.EO_MSC_UNKNOWN_BIZ_EVENT_TYPE) {
        output.WriteRawTag(24);
        output.WriteEnum((int) bizEvent);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (bizEvent != global::Msg.explore.PB_Explore_MissionStoryChatUpBizEvent.EO_MSC_UNKNOWN_BIZ_EVENT_TYPE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) bizEvent);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_MissionStoryChatDownMsgForBizEvent other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.bizEvent != global::Msg.explore.PB_Explore_MissionStoryChatUpBizEvent.EO_MSC_UNKNOWN_BIZ_EVENT_TYPE) {
        bizEvent = other.bizEvent;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 24: {
            bizEvent = (global::Msg.explore.PB_Explore_MissionStoryChatUpBizEvent) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 24: {
            bizEvent = (global::Msg.explore.PB_Explore_MissionStoryChatUpBizEvent) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///*
  /// Mission剧情对话下行 - 任务步骤进度变更
  /// 1. 只有进度发生变化才会发送此消息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_MissionStoryChat_StepProgressChange : pb::IMessage<PB_MissionStoryChat_StepProgressChange>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_MissionStoryChat_StepProgressChange> _parser = new pb::MessageParser<PB_MissionStoryChat_StepProgressChange>(() => new PB_MissionStoryChat_StepProgressChange());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_MissionStoryChat_StepProgressChange> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MissionStoryReflection.Descriptor.MessageTypes[15]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MissionStoryChat_StepProgressChange() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MissionStoryChat_StepProgressChange(PB_MissionStoryChat_StepProgressChange other) : this() {
      beforeFinishedStepNo_ = other.beforeFinishedStepNo_;
      afterFinishedStepNo_ = other.afterFinishedStepNo_;
      afterStepTitle_ = other.afterStepTitle_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MissionStoryChat_StepProgressChange Clone() {
      return new PB_MissionStoryChat_StepProgressChange(this);
    }

    /// <summary>Field number for the "beforeFinishedStepNo" field.</summary>
    public const int beforeFinishedStepNoFieldNumber = 1;
    private int beforeFinishedStepNo_;
    /// <summary>
    /// 变更前已完成的step_no（从0开始）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int beforeFinishedStepNo {
      get { return beforeFinishedStepNo_; }
      set {
        beforeFinishedStepNo_ = value;
      }
    }

    /// <summary>Field number for the "afterFinishedStepNo" field.</summary>
    public const int afterFinishedStepNoFieldNumber = 2;
    private int afterFinishedStepNo_;
    /// <summary>
    /// 变更后已完成的step_no（从0开始）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int afterFinishedStepNo {
      get { return afterFinishedStepNo_; }
      set {
        afterFinishedStepNo_ = value;
      }
    }

    /// <summary>Field number for the "afterStepTitle" field.</summary>
    public const int afterStepTitleFieldNumber = 3;
    private string afterStepTitle_ = "";
    /// <summary>
    /// 变更后的step标题
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string afterStepTitle {
      get { return afterStepTitle_; }
      set {
        afterStepTitle_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_MissionStoryChat_StepProgressChange);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_MissionStoryChat_StepProgressChange other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (beforeFinishedStepNo != other.beforeFinishedStepNo) return false;
      if (afterFinishedStepNo != other.afterFinishedStepNo) return false;
      if (afterStepTitle != other.afterStepTitle) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (beforeFinishedStepNo != 0) hash ^= beforeFinishedStepNo.GetHashCode();
      if (afterFinishedStepNo != 0) hash ^= afterFinishedStepNo.GetHashCode();
      if (afterStepTitle.Length != 0) hash ^= afterStepTitle.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (beforeFinishedStepNo != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(beforeFinishedStepNo);
      }
      if (afterFinishedStepNo != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(afterFinishedStepNo);
      }
      if (afterStepTitle.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(afterStepTitle);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (beforeFinishedStepNo != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(beforeFinishedStepNo);
      }
      if (afterFinishedStepNo != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(afterFinishedStepNo);
      }
      if (afterStepTitle.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(afterStepTitle);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (beforeFinishedStepNo != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(beforeFinishedStepNo);
      }
      if (afterFinishedStepNo != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(afterFinishedStepNo);
      }
      if (afterStepTitle.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(afterStepTitle);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_MissionStoryChat_StepProgressChange other) {
      if (other == null) {
        return;
      }
      if (other.beforeFinishedStepNo != 0) {
        beforeFinishedStepNo = other.beforeFinishedStepNo;
      }
      if (other.afterFinishedStepNo != 0) {
        afterFinishedStepNo = other.afterFinishedStepNo;
      }
      if (other.afterStepTitle.Length != 0) {
        afterStepTitle = other.afterStepTitle;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            beforeFinishedStepNo = input.ReadInt32();
            break;
          }
          case 16: {
            afterFinishedStepNo = input.ReadInt32();
            break;
          }
          case 26: {
            afterStepTitle = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            beforeFinishedStepNo = input.ReadInt32();
            break;
          }
          case 16: {
            afterFinishedStepNo = input.ReadInt32();
            break;
          }
          case 26: {
            afterStepTitle = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///*
  /// Mission剧情对话下行 - 任务步骤进度变更
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_MissionStoryChatDownMsgForStepProgressChange : pb::IMessage<SC_MissionStoryChatDownMsgForStepProgressChange>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_MissionStoryChatDownMsgForStepProgressChange> _parser = new pb::MessageParser<SC_MissionStoryChatDownMsgForStepProgressChange>(() => new SC_MissionStoryChatDownMsgForStepProgressChange());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_MissionStoryChatDownMsgForStepProgressChange> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.MissionStoryReflection.Descriptor.MessageTypes[16]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForStepProgressChange() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForStepProgressChange(SC_MissionStoryChatDownMsgForStepProgressChange other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      change_ = other.change_ != null ? other.change_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForStepProgressChange Clone() {
      return new SC_MissionStoryChatDownMsgForStepProgressChange(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "change" field.</summary>
    public const int changeFieldNumber = 3;
    private global::Msg.explore.PB_MissionStoryChat_StepProgressChange change_;
    /// <summary>
    /// 任务步骤进度变更信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_MissionStoryChat_StepProgressChange change {
      get { return change_; }
      set {
        change_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_MissionStoryChatDownMsgForStepProgressChange);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_MissionStoryChatDownMsgForStepProgressChange other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (!object.Equals(change, other.change)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (change_ != null) hash ^= change.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (change_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(change);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (change_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(change);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (change_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(change);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_MissionStoryChatDownMsgForStepProgressChange other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.change_ != null) {
        if (change_ == null) {
          change = new global::Msg.explore.PB_MissionStoryChat_StepProgressChange();
        }
        change.MergeFrom(other.change);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            if (change_ == null) {
              change = new global::Msg.explore.PB_MissionStoryChat_StepProgressChange();
            }
            input.ReadMessage(change);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            if (change_ == null) {
              change = new global::Msg.explore.PB_MissionStoryChat_StepProgressChange();
            }
            input.ReadMessage(change);
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
