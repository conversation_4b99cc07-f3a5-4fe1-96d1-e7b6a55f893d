/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.VideoLessonTask
{
    public partial class CompVideoCtrl : GComponent
    {
        public static string pkgName => "VideoLessonTask";
        public static string comName => "CompVideoCtrl";
        public static string url => "ui://shj5g74gr0o81";

        public Controller pause;
        public GLoader btnTouch;
        public GGraph initPos;
        public BtnPlay btnPlay;
        public GButton btnFast;
        public GButton btnBack;
        public GTextField tfCurTime;
        public GTextField tfTotalTime;
        public GSlider sldProgress;
        public GButton btnSwitchLang;
        public GGroup grpPlay;
        public Transition hide;
        public Transition show;
        public Transition doLoad;
        public Transition noLoad;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(CompVideoCtrl));
        }

        public override void ConstructFromXML(XML xml)
        {
            pause = GetControllerAt(0);
            btnTouch = GetChildAt(0) as GLoader;
            initPos = GetChildAt(2) as GGraph;
            btnPlay = new BtnPlay();
            btnPlay.Construct(GetChildAt(4).asCom);
            btnFast = GetChildAt(5) as GButton;
            btnBack = GetChildAt(6) as GButton;
            tfCurTime = GetChildAt(7) as GTextField;
            tfTotalTime = GetChildAt(8) as GTextField;
            sldProgress = GetChildAt(9) as GSlider;
            btnSwitchLang = GetChildAt(10) as GButton;
            grpPlay = GetChildAt(11) as GGroup;
            hide = GetTransitionAt(0);
            show = GetTransitionAt(1);
            doLoad = GetTransitionAt(2);
            noLoad = GetTransitionAt(3);
        }
        public override void Dispose()
        {
            pause = null;
            btnTouch = null;
            initPos = null;
            btnPlay.Dispose();
            btnPlay = null;
            btnFast = null;
            btnBack = null;
            tfCurTime = null;
            tfTotalTime = null;
            sldProgress = null;
            btnSwitchLang = null;
            grpPlay = null;
            hide = null;
            show = null;
            doLoad = null;
            noLoad = null;

            base.Dispose();
        }
    }
}