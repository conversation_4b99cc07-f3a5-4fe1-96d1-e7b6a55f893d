/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.Debugger
{
    public partial class StarLevelEditor : ExtendedComponent
    {
        public static string pkgName => "Debugger";
        public static string comName => "StarLevelEditor";
        public static string url => "ui://hwralbtzrxt1uvptct";

        public GGraph imgBG;
        public GTextInput inputFragId;
        public GButton btnStart;
        public GButton btnClose;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(StarLevelEditor));
        }

        public override void ConstructFromXML(XML xml)
        {
            imgBG = GetChildAt(0) as GGraph;
            inputFragId = GetChildAt(2) as GTextInput;
            btnStart = GetChildAt(3) as GButton;
            btnClose = GetChildAt(4) as GButton;

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();

            imgBG = null;
            inputFragId = null;
            btnStart = null;
            btnClose = null;

            base.Dispose();
        }
    }
}