/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreFriends
{
    public partial class ExploreFriendsDrawCardsResultPanel : UIBindT
    {
        public override string pkgName => "ExploreFriends";
        public override string comName => "ExploreFriendsDrawCardsResultPanel";

        public Controller IsSelect;
        public GGraph imgBG;
        public GGraph bgImg;
        public GLoader bgLoader;
        public GLoader avatarLoader;
        public GTextField Content1Txt;
        public GGroup Introduce;
        public FriendCommonBtn NextBtn;
        public GTextField ReDrawTxt;
        public GTextField ReDrawDiamondCnt;
        public GGraph ReDrawBtn;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            IsSelect = com.GetControllerAt(0);
            imgBG = (GGraph)com.GetChildAt(0);
            bgImg = (GGraph)com.GetChildAt(1);
            bgLoader = (GLoader)com.GetChildAt(2);
            avatarLoader = (GLoader)com.GetChildAt(3);
            Content1Txt = (GTextField)com.GetChildAt(4);
            Introduce = (GGroup)com.GetChildAt(9);
            NextBtn = new FriendCommonBtn();
            NextBtn.Construct(com.GetChildAt(10).asCom);
            ReDrawTxt = (GTextField)com.GetChildAt(11);
            ReDrawDiamondCnt = (GTextField)com.GetChildAt(13);
            ReDrawBtn = (GGraph)com.GetChildAt(14);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            IsSelect = null;
            imgBG = null;
            bgImg = null;
            bgLoader = null;
            avatarLoader = null;
            Content1Txt = null;
            Introduce = null;
            NextBtn.Dispose();
            NextBtn = null;
            ReDrawTxt = null;
            ReDrawDiamondCnt = null;
            ReDrawBtn = null;
        }
    }
}