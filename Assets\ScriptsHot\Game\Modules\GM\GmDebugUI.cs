﻿using FairyGUI;
using System.Collections.Generic;
using LitJson;
using Msg.GM;
using ScriptsHot.Game.UGUI.WebView;
using ScriptsN.UI.WebView;
using UIBind.GM;
using UnityEngine;
using Talkit.Framework.DesignControl;
#if UNITY_EDITOR
using UnityEditor;
#endif

public class GmDebugUI : BaseUI<UIBind.GM.GMDebugPanel>
{
    List<LanguageCode> lans = I18NTool.DebugLangList;
    private List<string> _debugLanName = new List<string> { "中文", "法语","英文"};
    private List<string> _debugSrvName = new List<string> {
        "日本",
        "QA",
        "dev网",
        ".5网",
        "法国",
        "Pre",
        "中国",
     };
    private List<string> _debugSrvList = new List<string> {
        "https://login-jp-uat.talkit.ai:8888",//jp-UAT
        "https://login-qa-test.talkit.ai:8888",//jp-qa

        "https://login-jp-dev.talkit.ai:8888",//dev
        "http://*********:8888",//0.5

        "https://login-fr-prod.talkit.ai:8888",//fr
        "https://login-fr-pre.talkit.ai:8888",//fr-pre
        "https://login.mudiyongxian.com",//cn
    };

    public GmDebugUI(string name) : base(name) { }

    public override string uiLayer => UILayerConsts.Top;
    private MainModel _mainModel => this.GetModel<MainModel>(ModelConsts.Main);
    private LoginOnBoardingModel BoardingModel => GetModel<LoginOnBoardingModel>(ModelConsts.Login);
    protected override bool isFullScreen => true;

    protected override void OnInit(GComponent uiCom)
    {
        this.AddUIEvent(this.ui.btnClose.onClick, this.OnBtnCloseClick);
        this.AddUIEvent(this.ui.gmDebugCom.btnReset.onClick, this.OnBtnResetClick);
        this.AddUIEvent(this.ui.gmDebugCom.btnSave.onClick, this.OnBtnSaveClick);
        this.AddUIEvent(this.ui.gmDebugCom.comServer.ctrlServer.onChanged, this.OnCtrlServerChanged);
        this.AddUIEvent(this.ui.ctrlLanguage.onChanged, this.OnCtrlLanguageChanged);

        this.AddUIEvent(this.ui.gmDebugCom.comDevice.com.onClick, this.OnBtnDeviceClick);
        this.AddUIEvent(this.ui.gmDebugCom.comUser.com.onClick, this.OnBtnUIDClick);
        if (AppConst.IsDebugDevice)
        {
            this.AddUIEvent(this.ui.gmDebugCom.comDevice.icon.onClick, OnBtnRefreshClick);
        }
        
        

        this.AddUIEvent(ui.gmDebugCom.btnLoginUp.onClick, OnBtnLoginUpClicked);
        this.AddUIEvent(ui.gmDebugCom.btnTestUrl.onClick, OnBtnTestUrlClicked);
        this.AddUIEvent(ui.gmDebugCom.btnDelLearnPath.onClick, OnDelFrameLearnPathClicked);
        this.AddUIEvent(ui.gmDebugCom.btnDIYChat.onClick, OnDIYChatClicked);
        this.AddUIEvent(ui.gmDebugCom.btnFrame1.onClick, (a) =>
        {
            OnBtnFrameClicked(a,1);
        });
        this.AddUIEvent(ui.gmDebugCom.btnFrame2.onClick, (a) =>
        {
            OnBtnFrameClicked(a,2);
        });
        this.AddUIEvent(ui.gmDebugCom.btnFrame3.onClick, (a) =>
        {
            OnBtnFrameClicked(a,3);
        });
        this.AddUIEvent(ui.btnSecret.onClick, OnBtnDesignControlClick);
    }



    protected override void OnShow()
    {
        this.GetUI<CommConfirmUI>(UIConsts.CommConfirm).CloseAll();
        //
        this.ShowLanguageInfo();
        this.ShowSrvInfo();
        this.ui.gmDebugCom.btnSave.enabled = false;
        ui.gmDebugCom.comDevice.tfID.text = BoardingModel.DeviceID;
        ui.gmDebugCom.comUser.tfID.text = _mainModel.userID.ToString();
    }

    private void ShowLanguageInfo()
    {
        string debugLanguageHost = I18N.inst.MotherLanguage.ToString();
        int debugLanIndex = this.GetLanguegeIndex(debugLanguageHost);
        List<GmLanguageButton> btnList = new List<GmLanguageButton> { this.ui.gmDebugCom.btnSelectChinese, this.ui.gmDebugCom.btnSelectFrench, this.ui.gmDebugCom.btnSelectEnglish };
        for(int i = 0; i < btnList.Count; i++)
        {
            GmLanguageButton button = btnList[i];
            if ( i >= _debugLanName.Count)
            {
                button.com.visible = false;
                continue;
            }
            button.com.visible = true;
            button.lan.text = this._debugLanName[i];
            if (i == debugLanIndex)
            {
                this.ui.ctrlLanguage.selectedIndex = i;
                this.ui.gmDebugCom.tfLanTips.text = $"当前语言：{this._debugLanName[i]}";
            }
        }
    }
    
    private void ShowSrvInfo()
    {
        string debugSrvHost = LocalCfgMgr.instance.GetGlobal("debugSrvHost");
        debugSrvHost = string.IsNullOrEmpty(debugSrvHost) ? AppConst.LoginSrv : debugSrvHost;//保存本地配置
        int debugSrvIndex = this.GetSrvIndex(debugSrvHost);
        List<GmServerButton> btnList = new List<GmServerButton> {
            this.ui.gmDebugCom.comServer.btnSrvJP_UAT,
            this.ui.gmDebugCom.comServer.btnSrvJP_QA,
            this.ui.gmDebugCom.comServer.btnSrvDEV,
            this.ui.gmDebugCom.comServer.btnSrvDEV05,
            this.ui.gmDebugCom.comServer.btnSrvFR,
            this.ui.gmDebugCom.comServer.btnSrvFR_PRE,
            this.ui.gmDebugCom.comServer.btnSrvCN
                
        };
        for(int i = 0; i < btnList.Count; i++)
        {
            GmServerButton button = btnList[i];
            if ( i >= _debugSrvName.Count)
            {
                button.com.visible = false;
                continue;
            }
            button.com.visible = true;
            button.tfServer.text = this._debugSrvName[i];
            button.tfURL.text = this._debugSrvList[i];
            if (i == debugSrvIndex)
            {
                this.ui.gmDebugCom.comServer.ctrlServer.selectedIndex = i;
                this.ui.gmDebugCom.tfSrvTips.text = $"当前服务器：{this._debugSrvName[i]}";
            }
        }
    }

    private int GetSrvIndex(string srvHost)
    {
        for(int i = 0; i < this._debugSrvList.Count; i++)
        {
            if (this._debugSrvList[i] == srvHost)
                return i;
        }
        return 0;
    }

    private int GetLanguegeIndex(string lan)
    {
        
        for(int i = 0; i < lans.Count; i++)
        {
            if (lans[i].ToString() == lan)
                return i;
        }
        return 0;
    }
    
    private void OnCtrlServerChanged()
    {
        this.ui.gmDebugCom.btnSave.enabled = true;
    }

    private void OnCtrlLanguageChanged()
    {
        this.ui.gmDebugCom.btnSave.enabled = true;
    }
    
    private void OnBtnResetClick()
    {
        this.ui.gmDebugCom.btnSave.enabled = true;
        this.ui.gmDebugCom.comServer.ctrlServer.selectedIndex = 0;
        PlayerPrefs.DeleteKey("debugSrvHost");
    }

    private void OnBtnSaveClick()
    {
        this.GetUI<CommConfirmUI>(UIConsts.CommConfirm).Open("保存操作需要退出应用再重新进入，您确认吗？", 
            (() =>
            {
                SaveSetting();
            }), null, 2, "确认", "取消");
    }

    async void SaveSetting()
    {
        //语言设置
        // Cfg.SetFirstLanguage(_debugLanName[this.ui.ctrlLanguage.selectedIndex]);

        //服务器设置
        int index = this.ui.gmDebugCom.comServer.ctrlServer.selectedIndex;
        if (index < 0 || index >= this._debugSrvList.Count) return;
        string debugSrvHost = this._debugSrvList[index];
        LocalCfgMgr.instance.SetGlobal("debugSrvHost", debugSrvHost);
        
        await this.GetController<LoginController>(ModelConsts.Login).SetSrvFirstLanguage(lans[this.ui.ctrlLanguage.selectedIndex], _mainModel.userID);

        //清理Token
        LocalCfgMgr.instance.SetGlobal(LoginConst.Key_User_Token, "");
        LocalCfgMgr.instance.SetGlobal(LoginConst.Key_User_Id, "");
#if UNITY_EDITOR
        EditorApplication.isPlaying = false;
#else
        Application.Quit();
#endif
    }

    private void OnBtnCloseClick()
    {
        this.Hide();
    }
    
    private void OnBtnUIDClick()
    {
        UnityEngine.GUIUtility.systemCopyBuffer =  ui.gmDebugCom.comUser.tfID.text;
        this.GetUI<CommConfirmUI>(UIConsts.CommConfirm).Open("您已成功复制！", 
        null, null, 1, "确认", null);
    }
        
    private void OnBtnDeviceClick()
    {
        UnityEngine.GUIUtility.systemCopyBuffer = ui.gmDebugCom.comDevice.tfID.text;
        this.GetUI<CommConfirmUI>(UIConsts.CommConfirm).Open("您已成功复制！", 
            null, null, 1, "确认", null);
    }

    //使用systemInfo时不太需要此接口
    private void OnBtnRefreshClick()
    {
        BoardingModel.GenDebugDeviceID();
        ui.gmDebugCom.comDevice.tfID.text = BoardingModel.DeviceID;
        //清理Token
        LocalCfgMgr.instance.SetGlobal(LoginConst.Key_User_Token, "");
        LocalCfgMgr.instance.SetGlobal(LoginConst.Key_User_Id, "");
#if UNITY_EDITOR
        EditorApplication.isPlaying = false;
#else
        Application.Quit();
#endif
    }

    private async void OnBtnLoginUpClicked()
    {
        // AFHelper.Click_Quick_start();
        // var controller = GameEntry.LoginC.GetController<LoginController>(ModelConsts.Login);
        // var loginUI = GameEntry.LoginC.GetUI<LoginUI>(UIConsts.Login);
        // if (loginUI.isShow)
        // {
        //     var phone = loginUI.GetInputPhone();
        //     if (AppConst.AppChannel == LoginChannel.debug)
        //     {
        //         controller.DoOnBoardLoginFromUI(phone);
        //         LocalCfgMgr.instance.SetGlobal("debug_account", phone);
        //     }
        //     else
        //     {
        //         controller.DoOnBoardLoginFromUI();
        //     }   
        // }
        GetUI<CommConfirmUI>(UIConsts.CommConfirm).Open("确认删除账号？后果自负",
            ClearAccount, null,2,"确认", "取消", false, 2
        );
    }

    private async void ClearAccount()
    {
        var data = JsonMapper.ToJson(new DeleteAccountReq
        {
            user_id = _mainModel.userID,
        });
        await GHttpManager.instance.PostAsync(AppConst.LoginSrv + LoginConst.ClearAccountPath, data);
#if UNITY_EDITOR
        EditorApplication.isPlaying = false;
#else
                Application.Quit();
#endif
    }

    private void OnBtnTestUrlClicked()
    {
        MainModel _mainModel = GetModel<MainModel>(ModelConsts.Main);

        // GameObject ctlPrefab = Resources.Load<GameObject>("Prefabs/WebViewCtl");
        GameObject ctlPrefab = GResManager.instance.LoadPrefab("WebViewCtl");     
        GameObject newCtl = Object.Instantiate(ctlPrefab);
            
        WebViewCtl ctl = newCtl.GetComponent<WebViewCtl>();
        if (ctl == null)
        {
            ctl = newCtl.AddComponent<WebViewCtl>();
        }
        ctl.Init(10f, I18N.inst.MotherLanguage.ToString(), I18N.inst.ForeignLanguage.ToString(), _mainModel.toKen, I18N.inst.MotherLanguage.ToString(),
            true,
            true,
            () =>
            { 
                GetController<CurrencyController>(ModelConsts.CurrencyController).SendGetEconomicInfoReqAsync(GameEventName.GameEnter, () =>
                {
                    SendNotification(NotifyConsts.MainHeadRefreshEvent);
                });
            },() =>
            {
                GetUI<CommBusyUI>(UIConsts.CommBusy).LazyShow();
            },
            () =>
            {
                GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
            }
        );
        ctl.LoadTestUrl("https://jp-uat-speech.talkit.ai/web/h5_index.html");
    }

    //帧率设置
    private void OnBtnFrameClicked(EventContext eventContext,int index)
    {
        Application.targetFrameRate = AppConst.FrameRate * index;
        LocalCfgMgr.instance.SetGlobal(LoginConst.FrameRate, AppConst.FrameRate * index);
        this.GetUI<CommConfirmUI>(UIConsts.CommConfirm).Open("您已成功设置！",
        null, null, 1, "确认", null);
    }
    
    //清除学习路径
    private void OnDelFrameLearnPathClicked()
    {
        CS_DelLearnPathDataReq msg = new CS_DelLearnPathDataReq();
        MsgManager.instance.SendMsg(msg);
    }
    
    private void OnDIYChatClicked()
    {
        MainModel _mainModel = GetModel<MainModel>(ModelConsts.Main);
        // GameObject ctlPrefab = Resources.Load<GameObject>("Prefabs/WebViewCtl");
        GameObject ctlPrefab = GResManager.instance.LoadPrefab("WebViewCtl");           
        GameObject newCtl = Object.Instantiate(ctlPrefab);
        
        WebViewCtl ctl = newCtl.GetComponent<WebViewCtl>();
        if (ctl == null)
        {
            ctl = newCtl.AddComponent<WebViewCtl>();
        }
        ctl.Init(5f, I18N.inst.MotherLanguage.ToString(), I18N.inst.ForeignLanguage.ToString(), _mainModel.toKen, I18N.inst.TempUILanguageStr,
            false,
            false,
            () =>
            { 
                GetController<CurrencyController>(ModelConsts.CurrencyController).SendGetEconomicInfoReqAsync(GameEventName.GameEnter, () =>
                {
                    SendNotification(NotifyConsts.MainHeadRefreshEvent);
                });
            },() =>
            {
                GetUI<CommBusyUI>(UIConsts.CommBusy).LazyShow();
            },
            () =>
            {
                GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
            }
        );
        ctl.LoadUrl("https://jp-uat-pg.talkit.ai/h5-app/talkit/free-talk/");
    
    }

    private void OnBtnDesignControlClick()
    {
        DesignControl.instance.Show();
    }
}
