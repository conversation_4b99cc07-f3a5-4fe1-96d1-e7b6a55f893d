/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class BtnAudio : ABtnAudio
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "BtnAudio";
        public static string url => "ui://cmoz5osjz7rm35";

        public Controller ctrlPlaying;
        public GGraph state0Bg;
        public GGraph state1Bg;
        public Transition playing;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(BtnAudio));
        }

        public override void ConstructFromXML(XML xml)
        {
            ctrlPlaying = GetControllerAt(0);
            state0Bg = GetChildAt(0) as GGraph;
            state1Bg = GetChildAt(1) as GGraph;
            playing = GetTransitionAt(0);
        }
        public override void Dispose()
        {
            ctrlPlaying = null;
            state0Bg = null;
            state1Bg = null;
            playing = null;

            base.Dispose();
        }
    }
}