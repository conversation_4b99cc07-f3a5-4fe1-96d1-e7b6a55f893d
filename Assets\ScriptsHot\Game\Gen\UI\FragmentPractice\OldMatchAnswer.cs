/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class OldMatchAnswer : AFragAnswer
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "OldMatchAnswer";
        public static string url => "ui://cmoz5osjz7rm3k";

        public GList leftList;
        public GList rightList;
        public GGroup grpMatch;
        public Transition t0;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(OldMatchAnswer));
        }

        public override void ConstructFromXML(XML xml)
        {
            leftList = GetChildAt(1) as GList;
            rightList = GetChildAt(2) as GList;
            grpMatch = GetChildAt(3) as GGroup;
            t0 = GetTransitionAt(0);
        }
        public override void Dispose()
        {
            leftList = null;
            rightList = null;
            grpMatch = null;
            t0 = null;

            base.Dispose();
        }
    }
}