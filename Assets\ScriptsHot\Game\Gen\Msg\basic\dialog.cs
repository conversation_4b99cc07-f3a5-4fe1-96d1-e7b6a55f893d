// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/basic/dialog.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.basic {

  /// <summary>Holder for reflection information generated from protobuf/basic/dialog.proto</summary>
  public static partial class DialogReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/basic/dialog.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static DialogReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Chtwcm90b2J1Zi9iYXNpYy9kaWFsb2cucHJvdG8iQAoPQ2hhdE1lc3NhZ2VJ",
            "dGVtEgwKBFJvbGUYASABKAkSDwoHQ29udGVudBgCIAEoCRIOCgZtc2dfaWQY",
            "AyABKAMi0gEKFVBCX0RpYWxvZ1Byb2dyZXNzSW5mbxIUCgxjdXJfcHJvZ3Jl",
            "c3MYASABKAUSFgoOdG90YWxfcHJvZ3Jlc3MYAiABKAUSEQoJY3VyX2xldmVs",
            "GAMgASgFEhIKCm5leHRfbGV2ZWwYBCABKAUSFgoOaXNfdXBwZXJfbGltaXQY",
            "BSABKAgSGAoQdXBwZXJfbGltaXRfdGV4dBgGIAEoCRIbChNpc19uZXh0X3Vw",
            "cGVyX2xpbWl0GAcgASgIEhUKDWluY3JfcHJvZ3Jlc3MYCCABKAUihAEKElBC",
            "X0tub3dsZWRnZVBvaW50cxIRCglrbm93bGVkZ2UYASABKAkSEwoLZXhwbGFu",
            "YXRpb24YAiABKAkSHQoVYWRkaXRpb25hbF9leHBlcmllbmNlGAMgASgFEg4K",
            "BnR0c19pZBgEIAEoAxIXCg90cmFuc19rbm93bGVkZ2UYBSABKAki1gEKDFBC",
            "X1RvcGljSW5mbxIQCgh0b3BpY19pZBgBIAEoAxITCgt0b3BpY190aXRsZRgC",
            "IAEoCRIVCg10b3BpY19jb250ZW50GAMgASgJEg8KB2lzX2xvY2sYBCABKAgS",
            "EAoIdGFsa19jbnQYBSABKAUSGgoSdG9waWNfdW5sb2NrX2xldmVsGAYgASgF",
            "Eg8KB3Rhc2tfaWQYByABKAMSDgoGaXNfbmV3GAggASgIEhYKDmljb25fZmls",
            "ZV9uYW1lGAkgASgJEhAKCGlzX2ZvY3VzGAogASgIIjYKD1BCX01zZ093bmVy",
            "SW5mbxIRCglhdmF0YXJfaWQYASABKAMSEAoIaGVhZF91cmwYAiABKAkqXAoM",
            "UEJfTXNnQmVsb25nEgkKBUJOb25lEAASCgoGQXZhdGFyEAESCAoEVXNlchAC",
            "Eg0KCU5hcnJhdGlvbhADEg0KCU90aGVyVXNlchAEEg0KCVN5c3RlbU1zZxAF",
            "KtECCg1QQl9EaWFsb2dNb2RlEgkKBU1Ob25lEAASCAoET3BlbhABEgwKCEV4",
            "ZXJjaXNlEAISCgoGQ2FyZWVyEAMSDQoJQ2hhbGxlbmdlEAQSDQoJSW50ZW5z",
            "aWZ5EAUSDgoKT25Cb2FyZGluZxAGEhIKDkRhaWx5SW50ZW5zaWZ5EAcSDQoJ",
            "RW1vdGlvbmFsEAgSCQoFVHV0b3IQCRISCg5XYXJtdXBQcmFjdGljZRAKEhEK",
            "DVF1aWNrUHJhY3RpY2UQCxINCglSZXdhcmRCb3gQDBIRCg1Ub3BpY0V4ZXJj",
            "aXNlEA0SEwoPTWlzdGFrZUV4ZXJjaXNlEA4SDgoKV29ybGRTdG9yeRAPEgcK",
            "A0RJWRAQEgwKCFJvbGVQbGF5EBESCAoEUXVpehASEgkKBUZsYXNoEBMSCQoF",
            "VmlkZW8QFBIQCgxNaXNzaW9uU3RvcnkQFSqBAQoPUEJfVXBNc2dCaXpUeXBl",
            "EgkKBVVOb25lEAASDQoJTmV4dFJvdW5kEAESCwoHVXNlclNheRACEhoKFlF1",
            "ZXN0aW9uUmVzdWx0QnlTcGVlY2gQAxIQCgxGbG93U2NhZmZvbGQQBBILCgdV",
            "QWR2aWNlEAUSDAoIVXBSZXBlYXQQBiq/AwoRUEJfRG93bk1zZ0JpelR5cGUS",
            "CQoFRE5vbmUQABIPCgtBdmF0YXJSZXBseRABEhIKDlF1ZXN0aW9uUmVzdWx0",
            "EAISEwoPQXZhdGFyUmVjb21tZW5kEAMSDAoIU2NhZmZvbGQQBBILCgdEQWR2",
            "aWNlEAUSEAoMUXVlc3Rpb25JbmZvEAYSBwoDRXh0EAcSEAoMRWNvbm9taWNJ",
            "bmZvEAgSHQoZRG93bkJpelR5cGVUYXNrU3RlcFN0YXR1cxAJEhkKFURvd25C",
            "aXpUeXBlVGFza1Jlc3VsdBAKEh4KGkRvd25CaXpUeXBlVGFza1N0ZXBDb250",
            "ZW50EAsSEwoPQXNzZXRDaGFuZ2VJbmZvEAwSFQoRVGFza1Byb2Nlc3NTdGF0",
            "dXMQDRIUChBUYXNrU3RhclByb2dyZXNzEA4SEgoORnJlZXRhbGtBZHZpY2UQ",
            "DxITCg9UdXRvclRhc2tSZXN1bHQQEBIPCgtCdXR0b25HdWlkZRAREhAKDERp",
            "YWxvZ1Jlc3VsdBASEhMKD0RpYWxvZ05hcnJhdGlvbhATEg8KC0RpYWxvZ0lt",
            "YWdlEBQSDwoLUmVwbHlOb3RpY2UQFSo6ChFQQl9TY29yZUxldmVsRW51bRIJ",
            "CgVTTm9uZRAAEgcKA0xvdxABEgcKA01pZBACEggKBEhpZ2gQAyrRAQoVUEJf",
            "Rmx1ZW5jeUNvbnRlbnRFbnVtEg8KC0ZsdWVuY3lOb25lEAASDgoKRmx1ZW5j",
            "eU9uZRABEg4KCkZsdWVuY3lUd28QAhIQCgxGbHVlbmN5VGhyZWUQAxIPCgtG",
            "bHVlbmN5Rm91chAEEg8KC0ZsdWVuY3lGaXZlEAUSDgoKRmx1ZW5jeVNpeBAG",
            "EhAKDEZsdWVuY3lTZXZlbhAHEhAKDEZsdWVuY3lFaWdodBAIEg8KC0ZsdWVu",
            "Y3lOaW5lEAkSDgoKRmx1ZW5jeVRlbhAKKmgKG1BCX0NvbXByZWhlbnNpdmVD",
            "b250ZW50RW51bRIVChFDb21wcmVoZW5zaXZlTm9uZRAAEhkKFUNvbXByZWhl",
            "bnNpdmVTY2FmZm9sZBABEhcKE0NvbXByZWhlbnNpdmVSZXBlYXQQAio1Cg1Q",
            "Ql9Nb2RlU3RhdHVzEhIKDk1vZGVJbmNvbXBsZXRlEAASEAoMTW9kZUNvbXBs",
            "ZXRlEAEqVwoXUEJfRGlhbG9nRGlmZmljdWx0eUVudW0SEgoORGlmZmljdWx0",
            "eU5vbmUQABIUChBEaWZmaWN1bHR5Tm9ybWFsEAESEgoORGlmZmljdWx0eUhl",
            "cm8QAiqTAgoYUEJfTWFya09wZXJhdGlvblR5cGVFbnVtEgoKBk9wTm9uZRAA",
            "EgwKCE9wV2FybVVwEAESDwoLT3BDaGFsbGVuZ2UQAhIKCgZPcEZyZWUQAxIQ",
            "CgxPcENsaWNrU2NvcmUQBBIUChBPcEV4ZXJjaXNlTm90aWNlEAUSHQoZT3BD",
            "aGFsbGVuZ2VLbm93bGVkZ2VHdWlkZRAGEhwKGE9wQ2hhbGxlbmdlRXhlcmNp",
            "c2VHdWlkZRAHEhQKEE9wQXNzaXN0TGV2ZWxQb3AQCBIUChBPcENsaWNrV29y",
            "ZEd1aWRlEAkSFgoST3BRdWVzdGlvbkF1dG9PcGVuEAoSFwoTT3BRdWVzdGlv",
            "bkF1dG9DbG9zZRALKsABChZQQl9Vc2VyQXNzaXN0TGV2ZWxFbnVtEhcKE1Vz",
            "ZXJBc3Npc3RMZXZlbE5vbmUQABIWChJVc2VyQXNzaXN0TGV2ZWxMb3cQARIZ",
            "ChVVc2VyQXNzaXN0TGV2ZWxNZWRpdW0QAhIXChNVc2VyQXNzaXN0TGV2ZWxI",
            "aWdoEAMSHwobVXNlckFzc2lzdExldmVsSGVscEF1dG9PcGVuEAQSIAocVXNl",
            "ckFzc2lzdExldmVsSGVscEF1dG9DbG9zZRAFKpoCChNQQl9EaWFsb2dTb3Vy",
            "Y2VFbnVtEhQKEERpYWxvZ1NvdXJjZU5vbmUQABITCg9EaWFsb2dTb3VyY2VN",
            "YXAQARIZChVEaWFsb2dTb3VyY2VMZWFyblBhdGgQAhIhCh1EaWFsb2dTb3Vy",
            "Y2VNZW51QmFyRm9yU3RhbWluYRADEiMKH0RpYWxvZ1NvdXJjZU1hcEV4ZXJj",
            "aXNlRW50cmFuY2UQBBIcChhEaWFsb2dTb3VyY2VVc2VyQ29udGFjdHMQBRIc",
            "ChhEaWFsb2dTb3VyY2VVc2VyUXVlc3Rpb24QBhIZChVEaWFsb2dTb3VyY2VL",
            "bm93bGVkZ2UQBxIeChpEaWFsb2dTb3VyY2VSZVNpZ25FbnRyYW5jZRAIKnUK",
            "F1BCX0RpYWxvZ1Jlc3VsdFR5cGVFbnVtEhQKEERpYWxvZ1Jlc3VsdE5vbmUQ",
            "ABIWChJEaWFsb2dSZXN1bHRDb21tb24QARIVChFEaWFsb2dSZXN1bHRUdXRv",
            "chACEhUKEURpYWxvZ1Jlc3VsdFRyYWluEANCJloYdmZfcHJvdG9idWYvc2Vy",
            "dmVyL2Jhc2ljqgIJTXNnLmJhc2ljYgZwcm90bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Msg.basic.PB_MsgBelong), typeof(global::Msg.basic.PB_DialogMode), typeof(global::Msg.basic.PB_UpMsgBizType), typeof(global::Msg.basic.PB_DownMsgBizType), typeof(global::Msg.basic.PB_ScoreLevelEnum), typeof(global::Msg.basic.PB_FluencyContentEnum), typeof(global::Msg.basic.PB_ComprehensiveContentEnum), typeof(global::Msg.basic.PB_ModeStatus), typeof(global::Msg.basic.PB_DialogDifficultyEnum), typeof(global::Msg.basic.PB_MarkOperationTypeEnum), typeof(global::Msg.basic.PB_UserAssistLevelEnum), typeof(global::Msg.basic.PB_DialogSourceEnum), typeof(global::Msg.basic.PB_DialogResultTypeEnum), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.basic.ChatMessageItem), global::Msg.basic.ChatMessageItem.Parser, new[]{ "Role", "Content", "msg_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.basic.PB_DialogProgressInfo), global::Msg.basic.PB_DialogProgressInfo.Parser, new[]{ "cur_progress", "total_progress", "cur_level", "next_level", "is_upper_limit", "upper_limit_text", "is_next_upper_limit", "incr_progress" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.basic.PB_KnowledgePoints), global::Msg.basic.PB_KnowledgePoints.Parser, new[]{ "knowledge", "explanation", "additional_experience", "tts_id", "trans_knowledge" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.basic.PB_TopicInfo), global::Msg.basic.PB_TopicInfo.Parser, new[]{ "topic_id", "topic_title", "topic_content", "is_lock", "talk_cnt", "topic_unlock_level", "task_id", "is_new", "icon_file_name", "is_focus" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.basic.PB_MsgOwnerInfo), global::Msg.basic.PB_MsgOwnerInfo.Parser, new[]{ "avatar_id", "head_url" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  /// <summary>
  /// 消息归属方
  /// </summary>
  public enum PB_MsgBelong {
    /// <summary>
    /// 无意义
    /// </summary>
    [pbr::OriginalName("BNone")] BNone = 0,
    /// <summary>
    /// avatar
    /// </summary>
    [pbr::OriginalName("Avatar")] Avatar = 1,
    /// <summary>
    /// 用户
    /// </summary>
    [pbr::OriginalName("User")] User = 2,
    /// <summary>
    /// 旁白
    /// </summary>
    [pbr::OriginalName("Narration")] Narration = 3,
    /// <summary>
    /// 其他用户
    /// </summary>
    [pbr::OriginalName("OtherUser")] OtherUser = 4,
    /// <summary>
    /// 系统消息
    /// </summary>
    [pbr::OriginalName("SystemMsg")] SystemMsg = 5,
  }

  /// <summary>
  /// 任务型对话模式
  /// </summary>
  public enum PB_DialogMode {
    /// <summary>
    /// 无意义
    /// </summary>
    [pbr::OriginalName("MNone")] MNone = 0,
    /// <summary>
    /// Open 开放模式（纯自由）
    /// </summary>
    [pbr::OriginalName("Open")] Open = 1,
    /// <summary>
    /// Exercise 练习模式（热身练习）
    /// </summary>
    [pbr::OriginalName("Exercise")] Exercise = 2,
    /// <summary>
    /// Career 职业模式（与职业Avatar对话）
    /// </summary>
    [pbr::OriginalName("Career")] Career = 3,
    /// <summary>
    /// Challenge 挑战模式
    /// </summary>
    [pbr::OriginalName("Challenge")] Challenge = 4,
    /// <summary>
    /// Intensify 强化模式
    /// </summary>
    [pbr::OriginalName("Intensify")] Intensify = 5,
    /// <summary>
    /// on boarding
    /// </summary>
    [pbr::OriginalName("OnBoarding")] OnBoarding = 6,
    /// <summary>
    /// 每日强化
    /// </summary>
    [pbr::OriginalName("DailyIntensify")] DailyIntensify = 7,
    /// <summary>
    /// emotional 高情商模式
    /// </summary>
    [pbr::OriginalName("Emotional")] Emotional = 8,
    /// <summary>
    /// tutor模式
    /// </summary>
    [pbr::OriginalName("Tutor")] Tutor = 9,
    /// <summary>
    /// warmup练习关卡
    /// </summary>
    [pbr::OriginalName("WarmupPractice")] WarmupPractice = 10,
    /// <summary>
    /// 多邻国练习关卡
    /// </summary>
    [pbr::OriginalName("QuickPractice")] QuickPractice = 11,
    /// <summary>
    /// RewardBox 奖励关卡
    /// </summary>
    [pbr::OriginalName("RewardBox")] RewardBox = 12,
    /// <summary>
    /// 知识点复习
    /// </summary>
    [pbr::OriginalName("TopicExercise")] TopicExercise = 13,
    /// <summary>
    /// 错题复习
    /// </summary>
    [pbr::OriginalName("MistakeExercise")] MistakeExercise = 14,
    /// <summary>
    /// 世界剧情
    /// </summary>
    [pbr::OriginalName("WorldStory")] WorldStory = 15,
    /// <summary>
    /// DIY模式
    /// </summary>
    [pbr::OriginalName("DIY")] DIY = 16,
    /// <summary>
    /// role play
    /// </summary>
    [pbr::OriginalName("RolePlay")] RolePlay = 17,
    /// <summary>
    /// quiz
    /// </summary>
    [pbr::OriginalName("Quiz")] Quiz = 18,
    /// <summary>
    /// speak aua/flash
    /// </summary>
    [pbr::OriginalName("Flash")] Flash = 19,
    /// <summary>
    /// video
    /// </summary>
    [pbr::OriginalName("Video")] Video = 20,
    /// <summary>
    /// Mission剧情对话（Explore-Mission-Story）
    /// </summary>
    [pbr::OriginalName("MissionStory")] MissionStory = 21,
  }

  /// <summary>
  /// 对话上行消息业务类型
  /// </summary>
  public enum PB_UpMsgBizType {
    /// <summary>
    /// 无意义
    /// </summary>
    [pbr::OriginalName("UNone")] UNone = 0,
    /// <summary>
    /// NextRound 下一轮（所有对话模式通用）
    /// </summary>
    [pbr::OriginalName("NextRound")] NextRound = 1,
    /// <summary>
    /// UserSay 自由发言（纯自由对话）
    /// </summary>
    [pbr::OriginalName("UserSay")] UserSay = 2,
    /// <summary>
    /// QuestionResultBySpeech 题目作答结果提交（语音输入方式）
    /// </summary>
    [pbr::OriginalName("QuestionResultBySpeech")] QuestionResultBySpeech = 3,
    /// <summary>
    /// FlowScaffold Flow脚手架（向Flow提问需求帮助）
    /// </summary>
    [pbr::OriginalName("FlowScaffold")] FlowScaffold = 4,
    /// <summary>
    /// Advice 改进建议
    /// </summary>
    [pbr::OriginalName("UAdvice")] UAdvice = 5,
    /// <summary>
    /// repeat 重录
    /// </summary>
    [pbr::OriginalName("UpRepeat")] UpRepeat = 6,
  }

  /// <summary>
  /// 对话下行消息业务类型
  /// </summary>
  public enum PB_DownMsgBizType {
    /// <summary>
    /// 无意义
    /// </summary>
    [pbr::OriginalName("DNone")] DNone = 0,
    /// <summary>
    /// AvatarReply Avatar回复
    /// </summary>
    [pbr::OriginalName("AvatarReply")] AvatarReply = 1,
    /// <summary>
    /// QuestionResult 题目作答结果
    /// </summary>
    [pbr::OriginalName("QuestionResult")] QuestionResult = 2,
    /// <summary>
    /// AvatarRecommend Avatar推荐
    /// </summary>
    [pbr::OriginalName("AvatarRecommend")] AvatarRecommend = 3,
    /// <summary>
    /// Scaffold 脚手架
    /// </summary>
    [pbr::OriginalName("Scaffold")] Scaffold = 4,
    /// <summary>
    /// Advice 改进建议
    /// </summary>
    [pbr::OriginalName("DAdvice")] DAdvice = 5,
    /// <summary>
    /// QuestionInfo 题目详情
    /// </summary>
    [pbr::OriginalName("QuestionInfo")] QuestionInfo = 6,
    /// <summary>
    /// extItem 扩展业务
    /// </summary>
    [pbr::OriginalName("Ext")] Ext = 7,
    /// <summary>
    /// EconomicInfo 经济详情
    /// </summary>
    [pbr::OriginalName("EconomicInfo")] EconomicInfo = 8,
    /// <summary>
    /// 任务节点进展
    /// </summary>
    [pbr::OriginalName("DownBizTypeTaskStepStatus")] DownBizTypeTaskStepStatus = 9,
    /// <summary>
    /// 任务结算
    /// </summary>
    [pbr::OriginalName("DownBizTypeTaskResult")] DownBizTypeTaskResult = 10,
    /// <summary>
    /// 任务节点展示
    /// </summary>
    [pbr::OriginalName("DownBizTypeTaskStepContent")] DownBizTypeTaskStepContent = 11,
    /// <summary>
    /// 经济变动详情
    /// </summary>
    [pbr::OriginalName("AssetChangeInfo")] AssetChangeInfo = 12,
    /// <summary>
    /// 任务进展状态数据
    /// </summary>
    [pbr::OriginalName("TaskProcessStatus")] TaskProcessStatus = 13,
    /// <summary>
    /// 任务星级进展
    /// </summary>
    [pbr::OriginalName("TaskStarProgress")] TaskStarProgress = 14,
    /// <summary>
    /// 自由对话新advice
    /// </summary>
    [pbr::OriginalName("FreetalkAdvice")] FreetalkAdvice = 15,
    /// <summary>
    /// tutor任务结算
    /// </summary>
    [pbr::OriginalName("TutorTaskResult")] TutorTaskResult = 16,
    /// <summary>
    /// 按钮引导
    /// </summary>
    [pbr::OriginalName("ButtonGuide")] ButtonGuide = 17,
    /// <summary>
    /// 对话结算
    /// </summary>
    [pbr::OriginalName("DialogResult")] DialogResult = 18,
    /// <summary>
    /// 对话旁白
    /// </summary>
    [pbr::OriginalName("DialogNarration")] DialogNarration = 19,
    /// <summary>
    /// 对话图片
    /// </summary>
    [pbr::OriginalName("DialogImage")] DialogImage = 20,
    /// <summary>
    /// 提示回复
    /// </summary>
    [pbr::OriginalName("ReplyNotice")] ReplyNotice = 21,
  }

  /// <summary>
  /// 分数等级
  /// </summary>
  public enum PB_ScoreLevelEnum {
    /// <summary>
    /// 无意义
    /// </summary>
    [pbr::OriginalName("SNone")] SNone = 0,
    /// <summary>
    /// 低级
    /// </summary>
    [pbr::OriginalName("Low")] Low = 1,
    /// <summary>
    /// 中级
    /// </summary>
    [pbr::OriginalName("Mid")] Mid = 2,
    /// <summary>
    /// 高级
    /// </summary>
    [pbr::OriginalName("High")] High = 3,
  }

  /// <summary>
  /// 流利度分数等级文案内容枚举
  /// </summary>
  public enum PB_FluencyContentEnum {
    /// <summary>
    /// 无意义
    /// </summary>
    [pbr::OriginalName("FluencyNone")] FluencyNone = 0,
    /// <summary>
    /// 0 - 10分
    /// </summary>
    [pbr::OriginalName("FluencyOne")] FluencyOne = 1,
    /// <summary>
    /// 11 - 20分
    /// </summary>
    [pbr::OriginalName("FluencyTwo")] FluencyTwo = 2,
    /// <summary>
    /// 21 - 30分
    /// </summary>
    [pbr::OriginalName("FluencyThree")] FluencyThree = 3,
    /// <summary>
    /// 31 - 40分
    /// </summary>
    [pbr::OriginalName("FluencyFour")] FluencyFour = 4,
    /// <summary>
    /// 41 - 50分
    /// </summary>
    [pbr::OriginalName("FluencyFive")] FluencyFive = 5,
    /// <summary>
    /// 51 - 60分
    /// </summary>
    [pbr::OriginalName("FluencySix")] FluencySix = 6,
    /// <summary>
    /// 61 - 70分
    /// </summary>
    [pbr::OriginalName("FluencySeven")] FluencySeven = 7,
    /// <summary>
    /// 71 - 80分
    /// </summary>
    [pbr::OriginalName("FluencyEight")] FluencyEight = 8,
    /// <summary>
    /// 81 - 90分
    /// </summary>
    [pbr::OriginalName("FluencyNine")] FluencyNine = 9,
    /// <summary>
    /// 91 - 100分
    /// </summary>
    [pbr::OriginalName("FluencyTen")] FluencyTen = 10,
  }

  /// <summary>
  /// 综合分数等级文案内容枚举
  /// </summary>
  public enum PB_ComprehensiveContentEnum {
    /// <summary>
    /// 无意义
    /// </summary>
    [pbr::OriginalName("ComprehensiveNone")] ComprehensiveNone = 0,
    /// <summary>
    /// 脚手架使用
    /// </summary>
    [pbr::OriginalName("ComprehensiveScaffold")] ComprehensiveScaffold = 1,
    /// <summary>
    /// 重录
    /// </summary>
    [pbr::OriginalName("ComprehensiveRepeat")] ComprehensiveRepeat = 2,
  }

  /// <summary>
  /// 模式状态
  /// </summary>
  public enum PB_ModeStatus {
    /// <summary>
    /// 未完成
    /// </summary>
    [pbr::OriginalName("ModeIncomplete")] ModeIncomplete = 0,
    /// <summary>
    /// 完成
    /// </summary>
    [pbr::OriginalName("ModeComplete")] ModeComplete = 1,
  }

  /// <summary>
  /// 对话难度
  /// </summary>
  public enum PB_DialogDifficultyEnum {
    /// <summary>
    /// 无意义
    /// </summary>
    [pbr::OriginalName("DifficultyNone")] DifficultyNone = 0,
    /// <summary>
    /// 普通
    /// </summary>
    [pbr::OriginalName("DifficultyNormal")] DifficultyNormal = 1,
    /// <summary>
    /// 英雄
    /// </summary>
    [pbr::OriginalName("DifficultyHero")] DifficultyHero = 2,
  }

  /// <summary>
  /// 标识变更操作类型枚举
  /// </summary>
  public enum PB_MarkOperationTypeEnum {
    /// <summary>
    /// 无意义
    /// </summary>
    [pbr::OriginalName("OpNone")] OpNone = 0,
    /// <summary>
    /// 热身练习
    /// </summary>
    [pbr::OriginalName("OpWarmUp")] OpWarmUp = 1,
    /// <summary>
    /// 挑战
    /// </summary>
    [pbr::OriginalName("OpChallenge")] OpChallenge = 2,
    /// <summary>
    /// 自由对话
    /// </summary>
    [pbr::OriginalName("OpFree")] OpFree = 3,
    /// <summary>
    /// 评分点击
    /// </summary>
    [pbr::OriginalName("OpClickScore")] OpClickScore = 4,
    /// <summary>
    /// 跟读提示
    /// </summary>
    [pbr::OriginalName("OpExerciseNotice")] OpExerciseNotice = 5,
    /// <summary>
    /// 挑战模式-知识点引导
    /// </summary>
    [pbr::OriginalName("OpChallengeKnowledgeGuide")] OpChallengeKnowledgeGuide = 6,
    /// <summary>
    /// 挑战模式-跟读练习引导
    /// </summary>
    [pbr::OriginalName("OpChallengeExerciseGuide")] OpChallengeExerciseGuide = 7,
    /// <summary>
    /// 自由对话-援助等级气泡
    /// </summary>
    [pbr::OriginalName("OpAssistLevelPop")] OpAssistLevelPop = 8,
    /// <summary>
    /// 点词翻译引导
    /// </summary>
    [pbr::OriginalName("OpClickWordGuide")] OpClickWordGuide = 9,
    /// <summary>
    /// 批量答题 自动
    /// </summary>
    [pbr::OriginalName("OpQuestionAutoOpen")] OpQuestionAutoOpen = 10,
    [pbr::OriginalName("OpQuestionAutoClose")] OpQuestionAutoClose = 11,
  }

  /// <summary>
  /// 用户援助等级
  /// </summary>
  public enum PB_UserAssistLevelEnum {
    /// <summary>
    /// 无意义
    /// </summary>
    [pbr::OriginalName("UserAssistLevelNone")] UserAssistLevelNone = 0,
    /// <summary>
    /// 低
    /// </summary>
    [pbr::OriginalName("UserAssistLevelLow")] UserAssistLevelLow = 1,
    /// <summary>
    /// 中
    /// </summary>
    [pbr::OriginalName("UserAssistLevelMedium")] UserAssistLevelMedium = 2,
    /// <summary>
    /// 高
    /// </summary>
    [pbr::OriginalName("UserAssistLevelHigh")] UserAssistLevelHigh = 3,
    /// <summary>
    /// help auto打开
    /// </summary>
    [pbr::OriginalName("UserAssistLevelHelpAutoOpen")] UserAssistLevelHelpAutoOpen = 4,
    /// <summary>
    /// help auto关闭
    /// </summary>
    [pbr::OriginalName("UserAssistLevelHelpAutoClose")] UserAssistLevelHelpAutoClose = 5,
  }

  /// <summary>
  /// 对话来源
  /// </summary>
  public enum PB_DialogSourceEnum {
    /// <summary>
    /// 无意义
    /// </summary>
    [pbr::OriginalName("DialogSourceNone")] DialogSourceNone = 0,
    /// <summary>
    /// 地图
    /// </summary>
    [pbr::OriginalName("DialogSourceMap")] DialogSourceMap = 1,
    /// <summary>
    /// 学习路径
    /// </summary>
    [pbr::OriginalName("DialogSourceLearnPath")] DialogSourceLearnPath = 2,
    /// <summary>
    /// 菜单栏-体力值
    /// </summary>
    [pbr::OriginalName("DialogSourceMenuBarForStamina")] DialogSourceMenuBarForStamina = 3,
    /// <summary>
    /// 地图右下角-碎片化练习入口
    /// </summary>
    [pbr::OriginalName("DialogSourceMapExerciseEntrance")] DialogSourceMapExerciseEntrance = 4,
    /// <summary>
    /// 通讯录
    /// </summary>
    [pbr::OriginalName("DialogSourceUserContacts")] DialogSourceUserContacts = 5,
    /// <summary>
    /// 错题复习
    /// </summary>
    [pbr::OriginalName("DialogSourceUserQuestion")] DialogSourceUserQuestion = 6,
    /// <summary>
    /// 知识点复习
    /// </summary>
    [pbr::OriginalName("DialogSourceKnowledge")] DialogSourceKnowledge = 7,
    /// <summary>
    /// 补签入口
    /// </summary>
    [pbr::OriginalName("DialogSourceReSignEntrance")] DialogSourceReSignEntrance = 8,
  }

  /// <summary>
  /// 对话结算类型(老)
  /// </summary>
  public enum PB_DialogResultTypeEnum {
    /// <summary>
    /// 无意义
    /// </summary>
    [pbr::OriginalName("DialogResultNone")] DialogResultNone = 0,
    /// <summary>
    /// 通用
    /// </summary>
    [pbr::OriginalName("DialogResultCommon")] DialogResultCommon = 1,
    /// <summary>
    /// tutor结算
    /// </summary>
    [pbr::OriginalName("DialogResultTutor")] DialogResultTutor = 2,
    /// <summary>
    /// 训练中心
    /// </summary>
    [pbr::OriginalName("DialogResultTrain")] DialogResultTrain = 3,
  }

  #endregion

  #region Messages
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ChatMessageItem : pb::IMessage<ChatMessageItem>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ChatMessageItem> _parser = new pb::MessageParser<ChatMessageItem>(() => new ChatMessageItem());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ChatMessageItem> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.basic.DialogReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ChatMessageItem() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ChatMessageItem(ChatMessageItem other) : this() {
      Role_ = other.Role_;
      Content_ = other.Content_;
      msg_id_ = other.msg_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ChatMessageItem Clone() {
      return new ChatMessageItem(this);
    }

    /// <summary>Field number for the "Role" field.</summary>
    public const int RoleFieldNumber = 1;
    private string Role_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Role {
      get { return Role_; }
      set {
        Role_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "Content" field.</summary>
    public const int ContentFieldNumber = 2;
    private string Content_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string Content {
      get { return Content_; }
      set {
        Content_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "msg_id" field.</summary>
    public const int msg_idFieldNumber = 3;
    private long msg_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long msg_id {
      get { return msg_id_; }
      set {
        msg_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ChatMessageItem);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ChatMessageItem other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Role != other.Role) return false;
      if (Content != other.Content) return false;
      if (msg_id != other.msg_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Role.Length != 0) hash ^= Role.GetHashCode();
      if (Content.Length != 0) hash ^= Content.GetHashCode();
      if (msg_id != 0L) hash ^= msg_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Role.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Role);
      }
      if (Content.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Content);
      }
      if (msg_id != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(msg_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Role.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(Role);
      }
      if (Content.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(Content);
      }
      if (msg_id != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(msg_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Role.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Role);
      }
      if (Content.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(Content);
      }
      if (msg_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(msg_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ChatMessageItem other) {
      if (other == null) {
        return;
      }
      if (other.Role.Length != 0) {
        Role = other.Role;
      }
      if (other.Content.Length != 0) {
        Content = other.Content;
      }
      if (other.msg_id != 0L) {
        msg_id = other.msg_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            Role = input.ReadString();
            break;
          }
          case 18: {
            Content = input.ReadString();
            break;
          }
          case 24: {
            msg_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            Role = input.ReadString();
            break;
          }
          case 18: {
            Content = input.ReadString();
            break;
          }
          case 24: {
            msg_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 对话进度信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_DialogProgressInfo : pb::IMessage<PB_DialogProgressInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_DialogProgressInfo> _parser = new pb::MessageParser<PB_DialogProgressInfo>(() => new PB_DialogProgressInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_DialogProgressInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.basic.DialogReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_DialogProgressInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_DialogProgressInfo(PB_DialogProgressInfo other) : this() {
      cur_progress_ = other.cur_progress_;
      total_progress_ = other.total_progress_;
      cur_level_ = other.cur_level_;
      next_level_ = other.next_level_;
      is_upper_limit_ = other.is_upper_limit_;
      upper_limit_text_ = other.upper_limit_text_;
      is_next_upper_limit_ = other.is_next_upper_limit_;
      incr_progress_ = other.incr_progress_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_DialogProgressInfo Clone() {
      return new PB_DialogProgressInfo(this);
    }

    /// <summary>Field number for the "cur_progress" field.</summary>
    public const int cur_progressFieldNumber = 1;
    private int cur_progress_;
    /// <summary>
    /// 当前进度
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int cur_progress {
      get { return cur_progress_; }
      set {
        cur_progress_ = value;
      }
    }

    /// <summary>Field number for the "total_progress" field.</summary>
    public const int total_progressFieldNumber = 2;
    private int total_progress_;
    /// <summary>
    /// 总进度
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int total_progress {
      get { return total_progress_; }
      set {
        total_progress_ = value;
      }
    }

    /// <summary>Field number for the "cur_level" field.</summary>
    public const int cur_levelFieldNumber = 3;
    private int cur_level_;
    /// <summary>
    /// 当前等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int cur_level {
      get { return cur_level_; }
      set {
        cur_level_ = value;
      }
    }

    /// <summary>Field number for the "next_level" field.</summary>
    public const int next_levelFieldNumber = 4;
    private int next_level_;
    /// <summary>
    /// 下一个等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int next_level {
      get { return next_level_; }
      set {
        next_level_ = value;
      }
    }

    /// <summary>Field number for the "is_upper_limit" field.</summary>
    public const int is_upper_limitFieldNumber = 5;
    private bool is_upper_limit_;
    /// <summary>
    /// 亲密度上限标识
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is_upper_limit {
      get { return is_upper_limit_; }
      set {
        is_upper_limit_ = value;
      }
    }

    /// <summary>Field number for the "upper_limit_text" field.</summary>
    public const int upper_limit_textFieldNumber = 6;
    private string upper_limit_text_ = "";
    /// <summary>
    /// 上限文本
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string upper_limit_text {
      get { return upper_limit_text_; }
      set {
        upper_limit_text_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "is_next_upper_limit" field.</summary>
    public const int is_next_upper_limitFieldNumber = 7;
    private bool is_next_upper_limit_;
    /// <summary>
    /// 下一个等级是否是满级状态
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is_next_upper_limit {
      get { return is_next_upper_limit_; }
      set {
        is_next_upper_limit_ = value;
      }
    }

    /// <summary>Field number for the "incr_progress" field.</summary>
    public const int incr_progressFieldNumber = 8;
    private int incr_progress_;
    /// <summary>
    /// 增加进度
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int incr_progress {
      get { return incr_progress_; }
      set {
        incr_progress_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_DialogProgressInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_DialogProgressInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (cur_progress != other.cur_progress) return false;
      if (total_progress != other.total_progress) return false;
      if (cur_level != other.cur_level) return false;
      if (next_level != other.next_level) return false;
      if (is_upper_limit != other.is_upper_limit) return false;
      if (upper_limit_text != other.upper_limit_text) return false;
      if (is_next_upper_limit != other.is_next_upper_limit) return false;
      if (incr_progress != other.incr_progress) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (cur_progress != 0) hash ^= cur_progress.GetHashCode();
      if (total_progress != 0) hash ^= total_progress.GetHashCode();
      if (cur_level != 0) hash ^= cur_level.GetHashCode();
      if (next_level != 0) hash ^= next_level.GetHashCode();
      if (is_upper_limit != false) hash ^= is_upper_limit.GetHashCode();
      if (upper_limit_text.Length != 0) hash ^= upper_limit_text.GetHashCode();
      if (is_next_upper_limit != false) hash ^= is_next_upper_limit.GetHashCode();
      if (incr_progress != 0) hash ^= incr_progress.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (cur_progress != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(cur_progress);
      }
      if (total_progress != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(total_progress);
      }
      if (cur_level != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(cur_level);
      }
      if (next_level != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(next_level);
      }
      if (is_upper_limit != false) {
        output.WriteRawTag(40);
        output.WriteBool(is_upper_limit);
      }
      if (upper_limit_text.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(upper_limit_text);
      }
      if (is_next_upper_limit != false) {
        output.WriteRawTag(56);
        output.WriteBool(is_next_upper_limit);
      }
      if (incr_progress != 0) {
        output.WriteRawTag(64);
        output.WriteInt32(incr_progress);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (cur_progress != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(cur_progress);
      }
      if (total_progress != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(total_progress);
      }
      if (cur_level != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(cur_level);
      }
      if (next_level != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(next_level);
      }
      if (is_upper_limit != false) {
        output.WriteRawTag(40);
        output.WriteBool(is_upper_limit);
      }
      if (upper_limit_text.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(upper_limit_text);
      }
      if (is_next_upper_limit != false) {
        output.WriteRawTag(56);
        output.WriteBool(is_next_upper_limit);
      }
      if (incr_progress != 0) {
        output.WriteRawTag(64);
        output.WriteInt32(incr_progress);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (cur_progress != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(cur_progress);
      }
      if (total_progress != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(total_progress);
      }
      if (cur_level != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(cur_level);
      }
      if (next_level != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(next_level);
      }
      if (is_upper_limit != false) {
        size += 1 + 1;
      }
      if (upper_limit_text.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(upper_limit_text);
      }
      if (is_next_upper_limit != false) {
        size += 1 + 1;
      }
      if (incr_progress != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(incr_progress);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_DialogProgressInfo other) {
      if (other == null) {
        return;
      }
      if (other.cur_progress != 0) {
        cur_progress = other.cur_progress;
      }
      if (other.total_progress != 0) {
        total_progress = other.total_progress;
      }
      if (other.cur_level != 0) {
        cur_level = other.cur_level;
      }
      if (other.next_level != 0) {
        next_level = other.next_level;
      }
      if (other.is_upper_limit != false) {
        is_upper_limit = other.is_upper_limit;
      }
      if (other.upper_limit_text.Length != 0) {
        upper_limit_text = other.upper_limit_text;
      }
      if (other.is_next_upper_limit != false) {
        is_next_upper_limit = other.is_next_upper_limit;
      }
      if (other.incr_progress != 0) {
        incr_progress = other.incr_progress;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            cur_progress = input.ReadInt32();
            break;
          }
          case 16: {
            total_progress = input.ReadInt32();
            break;
          }
          case 24: {
            cur_level = input.ReadInt32();
            break;
          }
          case 32: {
            next_level = input.ReadInt32();
            break;
          }
          case 40: {
            is_upper_limit = input.ReadBool();
            break;
          }
          case 50: {
            upper_limit_text = input.ReadString();
            break;
          }
          case 56: {
            is_next_upper_limit = input.ReadBool();
            break;
          }
          case 64: {
            incr_progress = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            cur_progress = input.ReadInt32();
            break;
          }
          case 16: {
            total_progress = input.ReadInt32();
            break;
          }
          case 24: {
            cur_level = input.ReadInt32();
            break;
          }
          case 32: {
            next_level = input.ReadInt32();
            break;
          }
          case 40: {
            is_upper_limit = input.ReadBool();
            break;
          }
          case 50: {
            upper_limit_text = input.ReadString();
            break;
          }
          case 56: {
            is_next_upper_limit = input.ReadBool();
            break;
          }
          case 64: {
            incr_progress = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 知识点
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_KnowledgePoints : pb::IMessage<PB_KnowledgePoints>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_KnowledgePoints> _parser = new pb::MessageParser<PB_KnowledgePoints>(() => new PB_KnowledgePoints());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_KnowledgePoints> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.basic.DialogReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_KnowledgePoints() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_KnowledgePoints(PB_KnowledgePoints other) : this() {
      knowledge_ = other.knowledge_;
      explanation_ = other.explanation_;
      additional_experience_ = other.additional_experience_;
      tts_id_ = other.tts_id_;
      trans_knowledge_ = other.trans_knowledge_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_KnowledgePoints Clone() {
      return new PB_KnowledgePoints(this);
    }

    /// <summary>Field number for the "knowledge" field.</summary>
    public const int knowledgeFieldNumber = 1;
    private string knowledge_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string knowledge {
      get { return knowledge_; }
      set {
        knowledge_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "explanation" field.</summary>
    public const int explanationFieldNumber = 2;
    private string explanation_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string explanation {
      get { return explanation_; }
      set {
        explanation_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "additional_experience" field.</summary>
    public const int additional_experienceFieldNumber = 3;
    private int additional_experience_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int additional_experience {
      get { return additional_experience_; }
      set {
        additional_experience_ = value;
      }
    }

    /// <summary>Field number for the "tts_id" field.</summary>
    public const int tts_idFieldNumber = 4;
    private long tts_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long tts_id {
      get { return tts_id_; }
      set {
        tts_id_ = value;
      }
    }

    /// <summary>Field number for the "trans_knowledge" field.</summary>
    public const int trans_knowledgeFieldNumber = 5;
    private string trans_knowledge_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string trans_knowledge {
      get { return trans_knowledge_; }
      set {
        trans_knowledge_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_KnowledgePoints);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_KnowledgePoints other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (knowledge != other.knowledge) return false;
      if (explanation != other.explanation) return false;
      if (additional_experience != other.additional_experience) return false;
      if (tts_id != other.tts_id) return false;
      if (trans_knowledge != other.trans_knowledge) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (knowledge.Length != 0) hash ^= knowledge.GetHashCode();
      if (explanation.Length != 0) hash ^= explanation.GetHashCode();
      if (additional_experience != 0) hash ^= additional_experience.GetHashCode();
      if (tts_id != 0L) hash ^= tts_id.GetHashCode();
      if (trans_knowledge.Length != 0) hash ^= trans_knowledge.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (knowledge.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(knowledge);
      }
      if (explanation.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(explanation);
      }
      if (additional_experience != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(additional_experience);
      }
      if (tts_id != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(tts_id);
      }
      if (trans_knowledge.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(trans_knowledge);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (knowledge.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(knowledge);
      }
      if (explanation.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(explanation);
      }
      if (additional_experience != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(additional_experience);
      }
      if (tts_id != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(tts_id);
      }
      if (trans_knowledge.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(trans_knowledge);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (knowledge.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(knowledge);
      }
      if (explanation.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(explanation);
      }
      if (additional_experience != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(additional_experience);
      }
      if (tts_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(tts_id);
      }
      if (trans_knowledge.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(trans_knowledge);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_KnowledgePoints other) {
      if (other == null) {
        return;
      }
      if (other.knowledge.Length != 0) {
        knowledge = other.knowledge;
      }
      if (other.explanation.Length != 0) {
        explanation = other.explanation;
      }
      if (other.additional_experience != 0) {
        additional_experience = other.additional_experience;
      }
      if (other.tts_id != 0L) {
        tts_id = other.tts_id;
      }
      if (other.trans_knowledge.Length != 0) {
        trans_knowledge = other.trans_knowledge;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            knowledge = input.ReadString();
            break;
          }
          case 18: {
            explanation = input.ReadString();
            break;
          }
          case 24: {
            additional_experience = input.ReadInt32();
            break;
          }
          case 32: {
            tts_id = input.ReadInt64();
            break;
          }
          case 42: {
            trans_knowledge = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            knowledge = input.ReadString();
            break;
          }
          case 18: {
            explanation = input.ReadString();
            break;
          }
          case 24: {
            additional_experience = input.ReadInt32();
            break;
          }
          case 32: {
            tts_id = input.ReadInt64();
            break;
          }
          case 42: {
            trans_knowledge = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 话题信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_TopicInfo : pb::IMessage<PB_TopicInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_TopicInfo> _parser = new pb::MessageParser<PB_TopicInfo>(() => new PB_TopicInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_TopicInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.basic.DialogReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_TopicInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_TopicInfo(PB_TopicInfo other) : this() {
      topic_id_ = other.topic_id_;
      topic_title_ = other.topic_title_;
      topic_content_ = other.topic_content_;
      is_lock_ = other.is_lock_;
      talk_cnt_ = other.talk_cnt_;
      topic_unlock_level_ = other.topic_unlock_level_;
      task_id_ = other.task_id_;
      is_new_ = other.is_new_;
      icon_file_name_ = other.icon_file_name_;
      is_focus_ = other.is_focus_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_TopicInfo Clone() {
      return new PB_TopicInfo(this);
    }

    /// <summary>Field number for the "topic_id" field.</summary>
    public const int topic_idFieldNumber = 1;
    private long topic_id_;
    /// <summary>
    /// topic id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long topic_id {
      get { return topic_id_; }
      set {
        topic_id_ = value;
      }
    }

    /// <summary>Field number for the "topic_title" field.</summary>
    public const int topic_titleFieldNumber = 2;
    private string topic_title_ = "";
    /// <summary>
    /// topic标题
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string topic_title {
      get { return topic_title_; }
      set {
        topic_title_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "topic_content" field.</summary>
    public const int topic_contentFieldNumber = 3;
    private string topic_content_ = "";
    /// <summary>
    /// topic内容
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string topic_content {
      get { return topic_content_; }
      set {
        topic_content_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "is_lock" field.</summary>
    public const int is_lockFieldNumber = 4;
    private bool is_lock_;
    /// <summary>
    /// 解锁状态，false：解锁，true：未解锁
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is_lock {
      get { return is_lock_; }
      set {
        is_lock_ = value;
      }
    }

    /// <summary>Field number for the "talk_cnt" field.</summary>
    public const int talk_cntFieldNumber = 5;
    private int talk_cnt_;
    /// <summary>
    /// 话题次数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int talk_cnt {
      get { return talk_cnt_; }
      set {
        talk_cnt_ = value;
      }
    }

    /// <summary>Field number for the "topic_unlock_level" field.</summary>
    public const int topic_unlock_levelFieldNumber = 6;
    private int topic_unlock_level_;
    /// <summary>
    /// topic解锁等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int topic_unlock_level {
      get { return topic_unlock_level_; }
      set {
        topic_unlock_level_ = value;
      }
    }

    /// <summary>Field number for the "task_id" field.</summary>
    public const int task_idFieldNumber = 7;
    private long task_id_;
    /// <summary>
    /// 任务id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long task_id {
      get { return task_id_; }
      set {
        task_id_ = value;
      }
    }

    /// <summary>Field number for the "is_new" field.</summary>
    public const int is_newFieldNumber = 8;
    private bool is_new_;
    /// <summary>
    /// new标签，true：是，false：否
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is_new {
      get { return is_new_; }
      set {
        is_new_ = value;
      }
    }

    /// <summary>Field number for the "icon_file_name" field.</summary>
    public const int icon_file_nameFieldNumber = 9;
    private string icon_file_name_ = "";
    /// <summary>
    /// icon文件名
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string icon_file_name {
      get { return icon_file_name_; }
      set {
        icon_file_name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "is_focus" field.</summary>
    public const int is_focusFieldNumber = 10;
    private bool is_focus_;
    /// <summary>
    /// 选中标识，true：是，false：否
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is_focus {
      get { return is_focus_; }
      set {
        is_focus_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_TopicInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_TopicInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (topic_id != other.topic_id) return false;
      if (topic_title != other.topic_title) return false;
      if (topic_content != other.topic_content) return false;
      if (is_lock != other.is_lock) return false;
      if (talk_cnt != other.talk_cnt) return false;
      if (topic_unlock_level != other.topic_unlock_level) return false;
      if (task_id != other.task_id) return false;
      if (is_new != other.is_new) return false;
      if (icon_file_name != other.icon_file_name) return false;
      if (is_focus != other.is_focus) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (topic_id != 0L) hash ^= topic_id.GetHashCode();
      if (topic_title.Length != 0) hash ^= topic_title.GetHashCode();
      if (topic_content.Length != 0) hash ^= topic_content.GetHashCode();
      if (is_lock != false) hash ^= is_lock.GetHashCode();
      if (talk_cnt != 0) hash ^= talk_cnt.GetHashCode();
      if (topic_unlock_level != 0) hash ^= topic_unlock_level.GetHashCode();
      if (task_id != 0L) hash ^= task_id.GetHashCode();
      if (is_new != false) hash ^= is_new.GetHashCode();
      if (icon_file_name.Length != 0) hash ^= icon_file_name.GetHashCode();
      if (is_focus != false) hash ^= is_focus.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (topic_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(topic_id);
      }
      if (topic_title.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(topic_title);
      }
      if (topic_content.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(topic_content);
      }
      if (is_lock != false) {
        output.WriteRawTag(32);
        output.WriteBool(is_lock);
      }
      if (talk_cnt != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(talk_cnt);
      }
      if (topic_unlock_level != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(topic_unlock_level);
      }
      if (task_id != 0L) {
        output.WriteRawTag(56);
        output.WriteInt64(task_id);
      }
      if (is_new != false) {
        output.WriteRawTag(64);
        output.WriteBool(is_new);
      }
      if (icon_file_name.Length != 0) {
        output.WriteRawTag(74);
        output.WriteString(icon_file_name);
      }
      if (is_focus != false) {
        output.WriteRawTag(80);
        output.WriteBool(is_focus);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (topic_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(topic_id);
      }
      if (topic_title.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(topic_title);
      }
      if (topic_content.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(topic_content);
      }
      if (is_lock != false) {
        output.WriteRawTag(32);
        output.WriteBool(is_lock);
      }
      if (talk_cnt != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(talk_cnt);
      }
      if (topic_unlock_level != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(topic_unlock_level);
      }
      if (task_id != 0L) {
        output.WriteRawTag(56);
        output.WriteInt64(task_id);
      }
      if (is_new != false) {
        output.WriteRawTag(64);
        output.WriteBool(is_new);
      }
      if (icon_file_name.Length != 0) {
        output.WriteRawTag(74);
        output.WriteString(icon_file_name);
      }
      if (is_focus != false) {
        output.WriteRawTag(80);
        output.WriteBool(is_focus);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (topic_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(topic_id);
      }
      if (topic_title.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(topic_title);
      }
      if (topic_content.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(topic_content);
      }
      if (is_lock != false) {
        size += 1 + 1;
      }
      if (talk_cnt != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(talk_cnt);
      }
      if (topic_unlock_level != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(topic_unlock_level);
      }
      if (task_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(task_id);
      }
      if (is_new != false) {
        size += 1 + 1;
      }
      if (icon_file_name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(icon_file_name);
      }
      if (is_focus != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_TopicInfo other) {
      if (other == null) {
        return;
      }
      if (other.topic_id != 0L) {
        topic_id = other.topic_id;
      }
      if (other.topic_title.Length != 0) {
        topic_title = other.topic_title;
      }
      if (other.topic_content.Length != 0) {
        topic_content = other.topic_content;
      }
      if (other.is_lock != false) {
        is_lock = other.is_lock;
      }
      if (other.talk_cnt != 0) {
        talk_cnt = other.talk_cnt;
      }
      if (other.topic_unlock_level != 0) {
        topic_unlock_level = other.topic_unlock_level;
      }
      if (other.task_id != 0L) {
        task_id = other.task_id;
      }
      if (other.is_new != false) {
        is_new = other.is_new;
      }
      if (other.icon_file_name.Length != 0) {
        icon_file_name = other.icon_file_name;
      }
      if (other.is_focus != false) {
        is_focus = other.is_focus;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            topic_id = input.ReadInt64();
            break;
          }
          case 18: {
            topic_title = input.ReadString();
            break;
          }
          case 26: {
            topic_content = input.ReadString();
            break;
          }
          case 32: {
            is_lock = input.ReadBool();
            break;
          }
          case 40: {
            talk_cnt = input.ReadInt32();
            break;
          }
          case 48: {
            topic_unlock_level = input.ReadInt32();
            break;
          }
          case 56: {
            task_id = input.ReadInt64();
            break;
          }
          case 64: {
            is_new = input.ReadBool();
            break;
          }
          case 74: {
            icon_file_name = input.ReadString();
            break;
          }
          case 80: {
            is_focus = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            topic_id = input.ReadInt64();
            break;
          }
          case 18: {
            topic_title = input.ReadString();
            break;
          }
          case 26: {
            topic_content = input.ReadString();
            break;
          }
          case 32: {
            is_lock = input.ReadBool();
            break;
          }
          case 40: {
            talk_cnt = input.ReadInt32();
            break;
          }
          case 48: {
            topic_unlock_level = input.ReadInt32();
            break;
          }
          case 56: {
            task_id = input.ReadInt64();
            break;
          }
          case 64: {
            is_new = input.ReadBool();
            break;
          }
          case 74: {
            icon_file_name = input.ReadString();
            break;
          }
          case 80: {
            is_focus = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 对话消息所有者信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_MsgOwnerInfo : pb::IMessage<PB_MsgOwnerInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_MsgOwnerInfo> _parser = new pb::MessageParser<PB_MsgOwnerInfo>(() => new PB_MsgOwnerInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_MsgOwnerInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.basic.DialogReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MsgOwnerInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MsgOwnerInfo(PB_MsgOwnerInfo other) : this() {
      avatar_id_ = other.avatar_id_;
      head_url_ = other.head_url_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MsgOwnerInfo Clone() {
      return new PB_MsgOwnerInfo(this);
    }

    /// <summary>Field number for the "avatar_id" field.</summary>
    public const int avatar_idFieldNumber = 1;
    private long avatar_id_;
    /// <summary>
    /// avatar id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long avatar_id {
      get { return avatar_id_; }
      set {
        avatar_id_ = value;
      }
    }

    /// <summary>Field number for the "head_url" field.</summary>
    public const int head_urlFieldNumber = 2;
    private string head_url_ = "";
    /// <summary>
    /// 头像url
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string head_url {
      get { return head_url_; }
      set {
        head_url_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_MsgOwnerInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_MsgOwnerInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (avatar_id != other.avatar_id) return false;
      if (head_url != other.head_url) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (avatar_id != 0L) hash ^= avatar_id.GetHashCode();
      if (head_url.Length != 0) hash ^= head_url.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (avatar_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(avatar_id);
      }
      if (head_url.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(head_url);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (avatar_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(avatar_id);
      }
      if (head_url.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(head_url);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (avatar_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(avatar_id);
      }
      if (head_url.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(head_url);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_MsgOwnerInfo other) {
      if (other == null) {
        return;
      }
      if (other.avatar_id != 0L) {
        avatar_id = other.avatar_id;
      }
      if (other.head_url.Length != 0) {
        head_url = other.head_url;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            avatar_id = input.ReadInt64();
            break;
          }
          case 18: {
            head_url = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            avatar_id = input.ReadInt64();
            break;
          }
          case 18: {
            head_url = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
