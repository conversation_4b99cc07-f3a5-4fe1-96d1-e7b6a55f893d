﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using ScriptsHot.Game.Modules.ChatLogicNew;
using cn.sharesdk.unity3d;
using ScriptsHot.Game.Modules.Sign;
using ScriptsHot.Game.UGUI.iOSWidget;
using ScriptsHot.Game.UGUI.WebView;
using Game;
using Game.Modules.WhatsApp;
using LitJson;
using ScriptsHot.Game.Modules.Shop;
using UnityEngine;
using UnityEngine.Purchasing;
using UnityEngine.Purchasing.Extension;
using UnityEngine.UI;
using Random = UnityEngine.Random;
using FairyGUI;

#if UNITY_IOS
using Unity.Advertisement.IosSupport;
#endif

public partial class GMFuncClass
{
    [GMFunc("Restore")]
    public static void Restore()
    {
        PurchasingManager.instance.Restore((result, msg) =>
        {
            Debug.LogError($"==result = {result}");
            Debug.LogError($"==msg = {msg}");
        });
    }
    
    [GMFunc("CheckAllSubscriptionStatus")]
    public static void CheckAllSubscriptionStatus()
    {
        PurchasingManager.instance.CheckAllSubscriptionStatus(() =>
        {
            if (GameEntry.MainC.MainModel.IsMember())
            {
                UIManager.instance.GetUI<SubscribeSuccessUI>(UIConsts.SubscribeSuccessUI).prefix = "Premium";
                UIManager.instance.GetUI<SubscribeSuccessUI>(UIConsts.SubscribeSuccessUI).Show();
            }
        });
    }
    
    [GMFunc("PresentCodeRedemptionSheet")]
    public static void PresentCodeRedemptionSheet()
    {
        PurchasingManager.instance.PresentCodeRedemptionSheet();
    }
    [GMFunc("RefreshAppReceipt")]
    public static void RefreshAppReceipt()
    {
        PurchasingManager.instance.RefreshAppReceipt();
    }
    
    [GMFunc("clear PlayerPrefs")]
    public static void PlayerPrefs_DeleteAll()
    {
        PlayerPrefs.DeleteAll();
    }
    [GMFunc("弹窗C")]
    public static void errorPopC()
    {
        var PurchaseFailureDescription = new PurchaseFailureDescription("productId" , PurchaseFailureReason.Unknown , "SKERRORSTOREPRODUCTNOTAVAILABLE");
        PaymentFailureHandler.HandlePaymentFailure(PurchaseFailureDescription, "productId", true);
    }  
    
    [GMFunc("弹窗D")]
    public static void errorPopD()
    {
        var PurchaseFailureDescription = new PurchaseFailureDescription("productId" , PurchaseFailureReason.Unknown , "ACCOUNT");
        PaymentFailureHandler.HandlePaymentFailure(PurchaseFailureDescription, "productId", true); 
    }  
    [GMFunc("whatsappTest")]
    public static void whatsappTest()
    {
        UIManager.instance.GetUI<WhatsappGuiderUI>(UIConsts.WhatsappGuider).Show();
    }  
    [GMFunc("whatsapp2Test")]
    public static void whatsapp2Test()
    {
        UIManager.instance.GetUI<WhatsappGuider2UI>(UIConsts.WhatsappGuider2).Show();
    }  
    [GMFunc("RefreshMainBtns")]
    public static void RefreshMainBtns()
    {
        Notifier.instance.SendNotification(NotifyConsts.RefreshMainBtns);
    }  
    
    [GMFunc("用户私聊")]
    public static void MessageUI()
    {
        ControllerManager.instance.GetController<MessagesController>(ModelConsts.Messages).EnterMessages();
    }   
    
    [GMFunc("NoticeUI")]
    public static void NoticeUI()
    {
        ControllerManager.instance.GetController<NoticeController>(ModelConsts.Notice).TryShowNotice();
    } 

    [GMFunc("题目测试")]
    public static void ShowStarLevelPanel()
    {
        UIManager.instance.GetUI<ScriptsHot.Game.Modules.Debugger.DebugPracticePanelUI>(UIConsts.DebugPracticePanelUI).Show();
        Close();
    }

    private static void Close()
    { 
        GRoot.inst.enabled = true;
        GMUI_ugui.Hide();
    }

    [GMFunc("文本测试")]
    public static void ShowTextTest()
    {
        UIManager.instance.GetUI<ScriptsHot.Game.Modules.Debugger.TestTextPanelUI>(UIConsts.TestTextPanelUI).Show();
        Close();
    }
        
    [GMFunc("Pop ATT")]
    public static void PopATT()
    {
#if UNITY_IOS
        Debug.Log("AFHelper: Checking ATT status...");
        var trackingStatus = ATTrackingStatusBinding.GetAuthorizationTrackingStatus();
        Debug.Log($"AFHelper: Current ATT status: {trackingStatus}");

        ATTrackingStatusBinding.RequestAuthorizationTracking();
#endif
    }
            
    [GMFunc("清理已读公告列表")]
    public static void ClearNoticeReadRecord()
    {
        GameEntry.NoticeC.ClearReadRecord();
    }  
    [GMFunc("TestStartupTime")]
    public static void TestStartupTime()
    {
        var sb = TimerLogHelper.Ins.GetSb();
        GUIUtility.systemCopyBuffer = sb;
    }   
    
    [GMFunc("testShop")]
    public static void testShop()
    {
        UIManager.instance.GetUI(UIConsts.PlanBottomSubscribeUI).Show();
    }   
    
    [GMFunc("TestSystemAlert")]
    public static void TestSystemAlert()
    {
        SDKManager.callback_delegate_showAlert_cancel cancelCb = () => {            Debug.LogError("SDKManager.ShowAlert - cancelCallback"); };
        SDKManager.callback_delegate_showAlert_confirm confirmCb = () => {             Debug.LogError("SDKManager.ShowAlert - confirmCallback");};
        SDKManager.ShowAlert("this is title" , "this is message"  , "cancel", "confirm", true, cancelCb, confirmCb);
    }   

    
    [GMFunc("TestPopManager")]
    public static void TestPopManager()
    {
        UIManager.instance.GetUI(UIConsts.PlanBottomSubscribeUI).PopShow(PopUIManager.Priority.Business);
        UIManager.instance.GetUI<HalfScreenMsgUI>(UIConsts.HalfScreenMsgUI).PopShow(PopUIManager.Priority.Push, new HalfScreenMsgVO()
        {
            iconType = 1,
            content1 = I18N.inst.MoStr("push_guider_02"),
            content2 = I18N.inst.MoStr("push_guider_03"),
            confirmLabel = I18N.inst.MoStr("push_guider_btn_txt_01"),
            cancelLabel = I18N.inst.MoStr("push_guider_btn_txt_02"),
            confirmFunc = () => { }
        });
        UIManager.instance.GetUI(UIConsts.PlanCanceledUI).PopShow();
        UIManager.instance.GetUI(UIConsts.PlanRetryUI).PopShow();
    }   
    
    [GMFunc("LogPkgList")]
    public static void LogPkgList()
    {
        var list = FairyGUI.UIPackage.GetPackages();
        Debug.LogError("========当前内存中的package");
        foreach (var uiPackage in list)
        {
            Debug.LogError($"=={uiPackage.name}");
            

        }
        Debug.LogError("========");
    }
    [GMFunc("CheckUIGC")]
    public static void CheckUIGC()
    {
        UIManager.instance.CheckUIGC();
    }
    
    [GMFunc("TestAppRatingUI")]
    public static void TestAppRatingUI()
    {
        UIManager.instance.GetUI<AppRatingUI>(UIConsts.AppRatingUI).Show();
        //关闭gm界面
        GMUI_ugui.Hide();
    }
    
    [GMFunc("TestSafeArea")]
    public static void TestSafeArea()
    {
        List<string> list = new List<string>()
        {
            UIConsts.SettlementGrowthUI,
            UIConsts.SettlementCommon,
            UIConsts.SettlementFriendsUI
        };
        foreach (var ui in list)
        {
            try
            {
                UIManager.instance.GetUI(ui).PopShow();
            }
            catch (Exception e)
            {
            }

        }
    }

    [GMFunc("RecordTest")]
    public static void ShowRecordBullets()
    {
        UIManager.instance.GetUI<SpeechBulletUI>(UIConsts.SpeechBulletUI).Show();
    }

    [GMFunc("测试有向图")]
    public static void TestVertices()
    {
        VerticesMatcher.VertexMatcher.Test();
    }

    [GMFunc("GetStartupPostTime")]
    public static void GetStartupPostTime()
    {
        var sb = TimerLogHelper.Ins.GetOtherTime();
        GUIUtility.systemCopyBuffer = sb;
    }
    
    [GMFunc("ClearLockIPA")]
    public static void ClearLockIPA()
    {
        PurchasingManager.instance.ClearLockIPA();
    }
    
    private static int VibrationType = 0;
    [GMFunc("Vibration")]
    public static void Vibration()
    {
        if (VibrationType >= (int) VibrationManager.VibrationType.NUM)
        {
            VibrationType = 0;
        }
        VibrationManager.Ins.Vibrate((VibrationManager.VibrationType)VibrationType);
        Debug.Log((VibrationManager.VibrationType)VibrationType);
        VibrationType++;
    }   
    
 
    [GMFunc("ClearLogDirectory")]
    public static void ClearBI()
    {
        BIEventLogger.ClearBI();
    }

#if UNITY_IPHONE
    [GMFunc("CopyBi_appflyer")]
    public static void CopyBi_appflyer()
    {
        BIEventLogger.CopyFileContentToClipboard_appflyer();
    }
    [GMFunc("CopyBi_amazon")]
    public static void CopyBi_amazon()
    {
        BIEventLogger.CopyFileContentToClipboard_amazon();
    }
#endif
    [GMFunc("OpenLogDirectory")]
    public static void OpenLogDirectory()
    {
        BIEventLogger.OpenLogDirectory();
    } 
    
    [GMFunc("ShareUI")]
    public static void ShareUI()
    {
        UIManager.instance.GetUI<ShareUI>(UIConsts.ShareUI).Show();
    }

    [GMFunc("XShareTest")]
    public static void XShareTest()
    {
        string url = "https://f1.webshare.mob.com/code/demo/img/1.jpg";
        ShareManager.instance.ShareImage(PlatformType.Twitter,url , () =>
        {
            Debug.Log("XShareTest over");
        });
    }   
    [GMFunc("DiscardTest")]
    public static void DiscardTest()
    {
        string url = "https://f1.webshare.mob.com/code/demo/img/1.jpg";
        ShareManager.instance.ShareImage(PlatformType.Discord,url , () =>
        {
            Debug.Log("DiscardTest over");
        });
    }  
    
    [GMFunc("RequestAudioPermission")]
    public static void RequestAudioPermission()
    {
        AndroidAdapter.Ins.RequestAudioPermission();
    }  
    [GMFunc("OpenSettingAndroid")]
    public static void OpenSettingAndroid()
    {
        AndroidAdapter.Ins.OpenSetting();
    }

    [GMFunc("TestWebviewOpenUrl")]
    public static void TestWebviewOpenUrl()
    {
        MainModel mainModel = ModelManager.instance.GetModel<MainModel>(ModelConsts.Main);

        // GameObject ctlPrefab = Resources.Load<GameObject>("Prefabs/WebViewCtl");
        GameObject ctlPrefab = GResManager.instance.LoadPrefab("WebViewCtl");     
        GameObject newCtl = GameObject.Instantiate(ctlPrefab);
            
        WebViewCtl ctl = newCtl.GetComponent<WebViewCtl>();
        if (ctl == null)
        {
            ctl = newCtl.AddComponent<WebViewCtl>();
        }
        ctl.Init(10f, I18N.inst.MotherLanguageStr, I18N.inst.ForeignLanguageStr, mainModel.toKen, I18N.inst.TempUILanguageStr,
            true,
            true,
            () =>
            {
                ControllerManager.instance.GetController<CurrencyController>(ModelConsts.CurrencyController).SendGetEconomicInfoReqAsync(GameEventName.GameEnter, () =>
                {
                    Notifier.instance.SendNotification(NotifyConsts.MainHeadRefreshEvent);
                });
            },() =>
            {
                UIManager.instance.GetUI<CommBusyUI>(UIConsts.CommBusy).LazyShow();
            },
            () =>
            {
                UIManager.instance.GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
            }
        );
        ctl.LoadTestUrl("https://jp-uat-pg.talkit.ai/h5-app/demo/");
    }     
    
    private static void OnShowNetErrorConfirmUIOpen(string content, Action confirmFunc, Action cancelFunc = null, int type = 2,
        string confirmLabel = "", string cancelLabel = "", bool block = false,int iconType = 0)
    {
        MainLoadingUI.Ins.OnShowNetErrorConfirmUIOpen(content,confirmFunc,cancelFunc,type,confirmLabel,cancelLabel,block,iconType);
    }
    
    [GMFunc("小组件guiderUI-limit")]
    public static void widgetGuiderUI2()
    {
        if (ControllerManager.instance.GetController<WidgetController>(ModelConsts.Widget).CanShowGuiderUI)
        {
            UIManager.instance.GetUI<WidgetGuiderUI>(UIConsts.WidgetGuiderUI).Show();
        }
    }  
    [GMFunc("小组件guiderUI")]
    public static void widgetGuiderUI()
    {
        UIManager.instance.GetUI<WidgetGuiderUI>(UIConsts.WidgetGuiderUI).Show();
    }  
    
    [GMFunc("ClearLocalTimes")]
    public static void ClearLocalTimes()
    {
        LocalTimeIntervalManager.Ins.ClearAll();
    } 
    
    [GMFunc("半屏确认框")]
    public static void halfScreenUI()
    {
        HalfScreenMsgUI ui = UIManager.instance.GetUI(UIConsts.HalfScreenMsgUI) as HalfScreenMsgUI;
        ui.Open(new HalfScreenMsgVO()
        {
            iconType = 1,
            content1 = "content1",
            content2 = "content2",
            cancelFunc = () => { Debug.Log("cancelFunc");},
            cancelLabel = "cancel",
            confirmFunc = () => { Debug.Log("confirmFunc");},
            confirmLabel = "confirmLabel"
        });
        GMUI_ugui.Hide();
    }  
    [GMFunc("全屏确认框")]
    public static void FullScreenUI()
    {
        FullScreenMsgUI ui = UIManager.instance.GetUI(UIConsts.FullScreenMsgUI) as FullScreenMsgUI;
        ui.Open(new FullScreenMsgVO()
        {
            content1 = "content1",
            cancelFunc = () => { Debug.Log("cancelFunc");},
            cancelLabel = "cancel",
            confirmFunc = () => { Debug.Log("confirmFunc");},
            confirmLabel = "confirmLabel"
        });
        GMUI_ugui.Hide();
    }  
    
    [GMFunc("push_test")]
    public static void push_test()
    {
        if (ControllerManager.instance.GetController<PermissMsgController>(ModelConsts.PermissMsg).CanShowPushGuiderUI)
        {
            FullScreenMsgUI ui = UIManager.instance.GetUI(UIConsts.FullScreenMsgUI) as FullScreenMsgUI;
            ui.Open(new FullScreenMsgVO()
            {
                content1 = "content1",
                cancelFunc = () => { Debug.Log("cancelFunc");},
                cancelLabel = "cancel",
                confirmFunc = () => { Debug.Log("confirmFunc");},
                confirmLabel = "confirmLabel"
            });
            ControllerManager.instance.GetController<PermissMsgController>(ModelConsts.PermissMsg).OnPushGuiderComplate();
            GMUI_ugui.Hide();
        }
    }   
    
    [GMFunc("小组件test01")]
    public static void Widget01()
    {
        WidgetUtils.Test111();
    }    
    
    [GMFunc("签到+1")]
    public static void gmSign()
    {
        ModelManager.instance.GetModel<SignModel>(ModelConsts.Sign).signInfo.continue_days++;
    } 
    
    [GMFunc("小组件test02")]
    public static void Widget02()
    {
        WidgetUtils.Test222();
    } 
    [GMFunc("小组件test03")]
    public static void Widget03()
    {
        WidgetUtils.Test3333();
    } 
    [GMFunc("小组件test04")]
    public static void Widget04()
    {
        WidgetUtils.Test4444();
    } 
    [GMFunc("小组件test05")]
    public static void Widget05()
    {
        WidgetUtils.Test555();
    }     
    
    [GMFunc("小组件真实数据")]
    public static void Widget06()
    {
        WidgetUtils.UpdateAndRefreshWidget();
    }  
    [GMFunc("小组件刷新")]
    public static void Widget07()
    {
        WidgetUtils.RefreshWidget();
    } 
    
    [GMFunc("TestPop")]
    public static void TestPop()
    {
        //网络彻底断开,断开逻辑处理; 返回登录
        OnShowNetErrorConfirmUIOpen(I18N.inst.MoStr("common_reconnect"),
            () => { NetManager.instance.ForceReConnect(); }, null, 1);
    }     

    [GMFunc("ClearGC")]
    public static void ClearGC()
    {
        GC.Collect();
    }

    [GMFunc("0.5倍速")]
    public static void speedhalf()
    {
        Time.timeScale = 0.5f;
    }
    
    [GMFunc("倍速体验-1.5倍")]
    public static void speed1_5()
    {
        Time.timeScale = 1.5f;
    }

    [GMFunc("10倍速")]
    public static void speed10()
    {
        Time.timeScale = 4;
    }
    [GMFunc("SpeechTotext")]
    public static void SpeechTotext()
    {
        SpeechToTextManager.instance.Startup();
        GameEntry.LoginC.GetUI<ExpectUI>(UIConsts.ExpectUI).Show();
    }

    private static byte[] _ENCRYPT_KEYS = new byte[] {3, 5, 2, 4};

    [GMFunc("加密解密测试", GMQuickType.Test)]
    public static void TestJiaMiJieMi()
    {
        string testTxt = File.ReadAllText("E://ttt.txt");
        string sessionID = "c6f1i4mbpjp9d4d6m220";
        //加密
        byte[] httpData = System.Text.Encoding.UTF8.GetBytes(testTxt);

        int datamark = 0;
        string datatoken = sessionID;
        if (sessionID != null && sessionID.Length > 4 && httpData != null)
        {
            int datalen = httpData.Length;
            int keylen = sessionID.Length;
            byte keyIdx;
            char[] keyChars = sessionID.ToCharArray();
            for (int i = 0; i < datalen; i++)
            {
                keyIdx = (byte) (i % 4);
                datamark += (httpData[i] ^ keyChars[i % keylen]);
                httpData[i] = (byte) (httpData[i] ^ (keyChars[keyIdx] + _ENCRYPT_KEYS[keyIdx]));
            }

            datalen = datalen % 10;
            datatoken = sessionID.Substring(0, 4) + "m+1" + datalen.ToString() + datamark.ToString();
        }

        string jiamiData = System.Text.Encoding.UTF8.GetString(httpData);

        string jiemiData = "";

        //解密
        if (jiamiData.Length > 9 && jiamiData[4] == 'm' && jiamiData[5] == '+')
        {
            int tokenPos = jiamiData.IndexOf('|');
            if (tokenPos > 9)
            {
                string token = jiamiData.Substring(0, tokenPos);
                string dataCyped = jiamiData.Substring(tokenPos + 1);
                StringBuilder dataSrcBuffer = new StringBuilder(dataCyped.Length);
                int dataSrcLen = dataCyped.Length;
                byte keyIdx;
                for (int i = 0; i < dataSrcLen; i++)
                {
                    keyIdx = (byte) (i % 4);
                    dataSrcBuffer.Append((char) (dataCyped[i] ^ (token[keyIdx] + _ENCRYPT_KEYS[keyIdx])));
                }

                jiemiData = dataSrcBuffer.ToString();

                string srcMarkvStr = token.Substring(8);
                int srcMarkv = 0;
                if (!int.TryParse(srcMarkvStr, out srcMarkv))
                {
                    srcMarkv = 0;
                }

                int sessionLen = sessionID.Length;
                int dataMarkv = 0;
                for (int i = 0; i < dataSrcLen; i++)
                {
                    dataMarkv += (int) (jiamiData[i] ^ sessionID[i % sessionLen]);
                }

                if (dataMarkv != srcMarkv)
                {
                    //-5
                    Debug.LogError("解密失败-5");
                }
            }
            else
            {
                //-4
                Debug.LogError("解密失败-4");
            }
        }
        else
        {
            //-3
            Debug.LogError("解密失败-3");
        }

        if (jiemiData != testTxt)
        {
            Debug.LogError("文本不一致！");
        }
        else
        {
            Debug.LogError("文本一致！");
        }
    }

    
    [GMFunc("内存-主动GC", GMQuickType.Test)]
    public static void GMRunGC()
    {
        System.GC.Collect();
    }
    
    [GMFunc("ClearLocalRecord")]
    public static void ClearLocalRecord()
    {
        PersistentDataCleaner.Instance.ClearSpecificFolder("record");
    }
    
    [GMFunc("内存-卸载无用资源", GMQuickType.Test)]
    public static void UpLoadUnUesedRes()
    {
        Resources.UnloadUnusedAssets();
    }
    public static string GetRandomCharacters(int n = 10, bool Number = true, bool Lowercase = false, bool Capital = false)  // 生成随机字符串
    {
        StringBuilder tmp = new StringBuilder();
        System.Random rand = new System.Random();
        string characters = (Capital ? "ABCDEFGHIJKLMNOPQRSTUVWXYZ" : null) + (Number ? "0123456789" : null) + (Lowercase ? "abcdefghijklmnopqrstuvwxyz" : null);
        if (characters.Length < 1)
        {
            return (null);
        }
        for (int i = 0; i < n; i++)
        {
            tmp.Append(characters[rand.Next(0, characters.Length)].ToString());
        }
        return (tmp.ToString());
    }

    [GMFunc("测试小数点后两位", GMQuickType.Test)]
    public static void BombFollowMainerss4444()
    {
        Debug.Log(1.23.ToString("F2").TrimEnd('0').TrimEnd('.'));
        Debug.Log(1.00.ToString("F2").TrimEnd('0').TrimEnd('.'));
        Debug.Log(1.ToString("F2").TrimEnd('0').TrimEnd('.'));
    }
    
    [GMFunc("清理rolePlay缓存")]
    public static void ClearRolePlayCache()
    {
        ChatLogicController con = ControllerManager.instance.GetController<ChatLogicController>(ModelConsts.ChatLogic) as ChatLogicController;
        con.Model.TestCleanLocal();
    }
    
    [GMFunc("切换 AutoPlayer")]
    public static void ChangeAutoPlayer()
    {
        ChatController.AutoPlayer = !ChatController.AutoPlayer;
        Debug.Log($"AutoPlayer：{ChatController.AutoPlayer}");
    }

    
    [GMFunc("测试STT")]
    public static void TestSpeechToText()
    {
        SpeechToTextManager.RunTests();
    }

    class stringData
    {
        public int id { get; set; }
        public string content { get; set; }
    }

    static void WriteCsvFile(string filePath, List<stringData> people)
    {
        using (StreamWriter writer = new StreamWriter(filePath))
        {
            foreach (stringData person in people)
            {
                writer.WriteLine("{0},{1}", person.id, person.content);
            }
        }
    }
    
    static long GetByteCount(string str)
    {
        return Encoding.Unicode.GetByteCount(str); 
    }

}
