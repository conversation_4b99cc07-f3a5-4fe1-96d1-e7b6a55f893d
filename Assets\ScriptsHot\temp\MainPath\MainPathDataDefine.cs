﻿using System;
using UnityEngine;

namespace UIBind.MainPath3D
{
    /// <summary>
    /// 切换场景,无需关心切换冷却问题。(滑动的过程中一直调用就行)
    /// </summary>
    public struct ChangeSceneParams
    {
        /// <summary>
        /// 场景id
        /// </summary>
        public string sceneId;

        /// <summary>
        /// 角色id
        /// </summary>
        public long avatarId;

        /// <summary>
        /// 家具数量
        /// </summary>
        public int furNum;

        /// <summary>
        /// 永久点亮
        /// </summary>
        public bool isLightForever;

        /// <summary>
        /// 今日点亮
        /// </summary>
        public bool isLightToday;
        
        /// <summary>
        /// 场景是否已经解锁了?
        /// </summary>
        public bool isUnlocked;
    }

    /// <summary>
    /// 点亮场景,与是否曾经已经点亮过无关。 && 路径节点完成也用这个结构体
    /// </summary>
    public struct LightUpParams
    {
        /// <summary>
        /// 场景id
        /// </summary>
        public string sceneId;

        /// <summary>
        /// x y scale
        /// </summary>
        public Vector2 lightScale;

        /// <summary>
        /// 永久点亮。
        /// </summary>
        public bool isLightForever;

        /// <summary>
        /// 回调函数(可以滑动时触发)
        /// </summary>
        public Action callback;
    }
    
    public struct CompleteLevel
    {
        /// <summary>
        /// 家具数量
        /// </summary>
        public int furNum;
        
        /// <summary>
        /// 是哪个场景新增了家具？如果不是当前场景，那么家具不会显示。
        /// </summary>
        public string sceneId;
        
        /// <summary>
        /// 今日点亮
        /// </summary>
        public bool isLightToday;
        
        /// <summary>
        /// 永久点亮
        /// </summary>
        public bool isLightForever;
    }
}