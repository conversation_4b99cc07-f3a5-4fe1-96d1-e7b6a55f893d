
using UnityEngine;
using System.Collections.Generic;
using Msg.basic;
using Msg.question;



public class DataDotUserSession
{
    public string access_token;
    public long user_id;
}

//点开app，目前实际触发点为首次展示登录
public class DataDotStartupUser : DataDotBase
{
    public override string Event_name => "Startup_User";
    public long User_id;
}

//回家
public class DataDotClickHome : DataDotBase
{
    public override string Event_name => "Click_Home";
    public long User_id;
}

//EEA合规
public class DataDotEeaAuthorization: DataDotBase
{
    public override string Event_name => "eea_authorization";
    public bool accept;
}

//点击avatar
public class DataDotAvatar : DataDotBase
{
    public override string Event_name => "Click_Avatar";
    
    public long Avatar_id;
    public string Background; //Map_outside（地图模式_室外）/Map_inside（地图模式_室内)
}

//点击建筑物
public class DataDotBuilding : DataDotBase
{
    public override string Event_name => "Click_Building";
    
    public long Building_id;
}

//Flow avatar推荐接受
public class DataDotAvatarMeet : DataDotBase
{
    public override string Event_name => "Click_Flow_avatar_meet";
    public long Avatar_id;
}

//室内场景退出
public class DataDotInsideQuit : DataDotBase
{
    public override string Event_name => "Click_Inside_Quit";
    public long Build_id;
}

//对话退出
public class DataDotDialogQuit: DataDotBase
{
    public override string Event_name => "Click_Dialogue_quit";
    public long Dialogue_id;
    public int Dialogue_round;
    public long Knowledge_id;
    public int Knowledge_index;
    public int Knowledge_num;
    public int Practice_index;
    public int Practice_num;
    public string Dialogue_type;
}

//点击挑战模式
public class DataDotClickChallengingModel : DataDotBase
{
    public override string Event_name => "Click_Challenging_model";
    public long Dialogue_id;
}

//点击热身模式
public class DataDotClickWarmupModel : DataDotBase
{
    public override string Event_name => "Click_Warmup_model";
    public long Dialogue_id;
}

//任务开启
public class DataDotCutDialogueStart : DataDotBase
{
    public override string Event_name => "Cut_Dialogue_start";
    public string Task_type; // Should be "Task", "Free_talk", or "Warm up"
    public long Dialogue_id;
    public long Task_id;
}

//todo 任务结束
public class DataDotCutDialogueFinish : DataDotBase
{
    public override string Event_name => "Cut_Dialogue_finish";
    public long Dialogue_id;
}

//点击next
public class DataDotDialogueNext : DataDotBase
{
    public override string Event_name => "Click_Dialogue_next";
    public long Dialogue_id;
    public int Dialogue_round;
    public long Task_id;
    public string Task_type; // Should be "Task", "Free_talk", or "Warm up"
    public long Knowledge_id;
    public int Knowledge_index;
    public int Knowledge_num;
    public int Practice_index;
    public int Practice_num;
    public string Dialogue_type;
}

//点击end
public class DataDotDialogueEnd : DataDotBase
{
    public override string Event_name => "Click_Dialogue_end";
    public long Dialogue_id;
    public int Dialogue_round;
}

//warmup中重说
public class DataDotDialogueRepeat : DataDotBase
{
    public override string Event_name => "Click_Dialogue_repeat";
    public long Dialogue_id;
}

//对话点赞
public class DataDotClickDialogueUp : DataDotBase
{
    public override string Event_name => "Click_Dialogue_up";
    public long Dialogue_id;
    public long Task_id;
    public string Task_type; // Should be "Task", "Free_talk", or "Warm up"
    public long Knowledge_id;
    public int Knowledge_index;
    public int Knowledge_num;
    public int Practice_index;
    public int Practice_num;
}

//对话点踩
public class DataDotClickDialogueDown : DataDotBase
{
    public override string Event_name => "Click_Dialogue_down";
    public long Dialogue_id;
    public long Task_id;
    public string Task_type; // Should be "Task", "Free_talk", or "Warm up"
    public long Knowledge_id;
    public int Knowledge_index;
    public int Knowledge_num;
    public int Practice_index;
    public int Practice_num;
}

//结算页点赞
public class DataDotClickResultUp : DataDotBase
{
    public override string Event_name => "Click_Dialogue_result_up";
    public long Dialogue_id;
}
//结算页点踩
public class DataDotClickResultDown : DataDotBase
{
    public override string Event_name => "Click_Dialogue_result_down";
    public long Dialogue_id;
}
//结算页进入强化练习
public class DataDotClickDialogueResultPractice : DataDotBase
{
    public override string Event_name => "Click_Dialogue_result_practice";
    public long Dialogue_id;
    public long Task_id;
    public string Task_type;
}

//对话录音取消
public class DataDotClickMacCancel : DataDotBase
{
    public override string Event_name => "Click_Mac_cancel";
    public long Dialogue_id;
    public long Dialogue_round;
    public long question_id;
    public long question_record_id;//针对warmup
    public long Task_id;
    public string Task_type; // Should be "Task", "Free_talk", or "Warm up"
    public int Knowledge_index;
    public int Knowledge_num;
    public int Practice_index;
    public int Practice_num; 
    public string Dialogue_type;
    public string Help_mode;
}

//点击展示下方目标
public class DataDotClickTaskGoalShow : DataDotBase
{
    public override string Event_name => "Click_Task_goal_show";
    public long Dialogue_id;
}

//点击隐藏下方目标
public class DataDotClickTaskGoalHide : DataDotBase
{
    public override string Event_name => "Click_Task_goal_hide";
    public long Dialogue_id;
}

//滑动下方目标展示
public class DataDotSwtichTaskGoalShow : DataDotBase
{
    public override string Event_name => "Switch_Task_goal_show";
    public long Dialogue_id;
}


//滑动下方目标隐藏
public class DataDotSwtichTaskGoalHide : DataDotBase
{
    public override string Event_name => "Switch_Task_goal_hide";
    public long Dialogue_id;
}

//对话文本播放
public class DataDotClickDialoguePlay : DataDotBase
{
    public override string Event_name => "Click_Dialogue_play";
    public long Dialogue_id;
    public int Dialogue_round;
    public long Task_id;
    public string Task_type; // Should be "Task", "Free_talk", or "Warm up"
    public long Knowledge_id;
    public int Knowledge_index;
    public int Knowledge_num;
    public int Practice_index;
    public int Practice_num; 
    public string Dialogue_type;
    public string Help_mode;
}

//对话中重听
public class DataDotClickDialogueRelisten : DataDotBase
{
    public override string Event_name => "Click_Dialogue_relisten";
    public long Dialogue_id;
    public int Dialogue_round;
    public long Task_id;
    public string Task_type; // Should be "Task", "Free_talk", or "Warm up"
    public long Knowledge_id;
    public int Knowledge_index;
    public int Knowledge_num;
    public int Practice_index;
    public int Practice_num;
    public string Dialogue_type;
}

//对话文本翻译打开
public class DataDotClickDialogueTranslateOn : DataDotBase
{
    public override string Event_name => "Click_Dialogue_translate_on";
    public long Dialogue_id;
    public int Dialogue_round;
    public string Help_mode;
}

//生词点击
public class DataDotClickDialogueNewWords : DataDotBase
{
    public override string Event_name => "Click_Dialogue_new_words";
    public long Dialogue_id;
}


//对话进入语言录入
public class DataDotClickMacStart : DataDotBase
{
    public override string Event_name => "Click_Mac_start";
    public long Dialogue_id;
    public int Dialogue_round;
    public long question_id;
    public long question_record_id;//针对warmup
    public long Task_id;
    public string Task_type; // Should be "Task", "Free_talk", or "Warm up"
    public int Knowledge_index;
    public int Knowledge_num;
    public int Practice_index;
    public int Practice_num;
    public string Dialogue_type;
    public string Help_mode;
}

public class DataDotClickTry_again : DataDotBase
{
    public override string Event_name => "Click_Try_again";
    public long Dialogue_id;
    public int Dialogue_round;
    public long Knowledge_id;
    public int Knowledge_index;
    public int Knowledge_num;
    public int Practice_index;
    public int Practice_num;
    public string Dialogue_type;
}


public class DataDotClickType_change : DataDotBase
{
    public override string Event_name => "Click_Try_change";
    public long Dialogue_id;
    public int Dialogue_round;
    public long Knowledge_id;
    public int Knowledge_index;
    public int Knowledge_num;
    public int Practice_index;
    public int Practice_num;
    public string Dialogue_type;
}


//对话语音发送
public class DataDotClickMacSend : DataDotBase
{
    public override string Event_name => "Click_Mac_send";
    public long Dialogue_id;
    public long Dialogue_round;
    public long question_id;
    public long question_record_id;//针对warmup
    public long Task_id;
    public string Task_type; // Should be "Task", "Free_talk", or "Warm up"
    public int Knowledge_index;
    public int Knowledge_num;
    public int Practice_index;
    public int Practice_num;
    public string Dialogue_type;
    public string Help_mode;
}

//对话气泡中点击分数展示评分
public class DataDotClickDialogueScoreShow: DataDotBase
{
    public override string Event_name => "Click_Dialogue_score_show";
    public long Dialogue_id;
}

//对话气泡中点击分数收起评分
public class DataDotClickDialogueScoreHide : DataDotBase
{
    public override string Event_name => "Click_Dialogue_score_hide";
    public long Dialogue_id;
}

//评分窗口下展示发音评分
public class DataDotClickDialoguePronounceShow : DataDotBase
{
    public override string Event_name => "Click_Dialogue_pronounce_show";
    public long Dialogue_id;
}

//todo Suggestion收藏


//Suggestion播放
public class DataDotClickSuggestionPlay : DataDotBase
{
    public override string Event_name => "Click_Suggestion_play";
    public long Dialogue_id;
}

//Suggestion翻译展开
public class DataDotClickSuggestionTranslateOn : DataDotBase
{
    public override string Event_name => "Click_Suggestion_translate_on";
    public long Dialogue_id;
}

//Suggestion翻译收起
public class DataDotClickSuggestionTranslateOff : DataDotBase
{
    public override string Event_name => "Click_Suggestion_translate_off";
    public long Dialogue_id;
}

//Suggestion点赞
public class DataDotClickSuggestionUp : DataDotBase
{
    public override string Event_name => "Click_Suggestion_up";
    public long Dialogue_id;
}

//Suggestion点踩
public class DataDotClickSuggestionDown : DataDotBase
{
    public override string Event_name => "Click_Suggestion_down";
    public long Dialogue_id;
}

//点击蒙层关闭脚手架
public class DataDotClickFlowHelpAreaHide : DataDotBase
{
    public override string Event_name => "Click_Flow_help_area_hide";
    public long Dialogue_id;
}

//说话情况下点击Flow发送
public class DataDotClickFlowHelpSend : DataDotBase
{
    public override string Event_name => "Click_Flow_help_send";
    public long Dialogue_id;
}

//未说话情况下点击Flow关闭脚手架
public class DataDotClickFlowHelpHide : DataDotBase
{
    public override string Event_name => "Click_Flow_help_hide";
    public long Dialogue_id;
}

//点击Flow唤醒脚手架
public class DataDotClickFlowHelpShow : DataDotBase
{
    public override string Event_name => "Click_Flow_help_show";
    public long Dialogue_id;

}

//Warmup中对话中打开设置
public class DataDotClickDialogueOptionShow : DataDotBase
{
    public override string Event_name => "Click_Dialogue_option_show";
    public long Dialogue_id;
}


//Warmup中对话中关闭设置
public class DataDotClickDialogueOptionHide : DataDotBase
{
    public override string Event_name => "Click_Dialogue_option_hide";
    public long Dialogue_id;
}

//回声模式打开
public class DataDotClickDialogueEchoOn : DataDotBase
{
    public override string Event_name => "Click_Dialogue_echo_on";
    public long Dialogue_id;
}

//回声模式关闭
public class DataDotClickDialogueEchoOff : DataDotBase
{
    public override string Event_name => "Click_Dialogue_echo_off";
    public long Dialogue_id;
}

//原音模式打开
public class DataDotClickDialogueShadowReadingOn : DataDotBase
{
    public override string Event_name => "Click_Dialogue_shadow_reading_on";
    public long Dialogue_id;
}

//原音模式关闭
public class DataDotClickDialogueShadowReadingOff : DataDotBase
{
    public override string Event_name => "Click_Dialogue_shadow_reading_off";
    public long Dialogue_id;
}

//next打开
public class DataDotClickDialogueNextOn : DataDotBase
{
    public override string Event_name => "Click_Dialogue_next_on";
    public long Dialogue_id;
}

//next关闭
public class DataDotClickDialogueNextOff : DataDotBase
{
    public override string Event_name => "Click_Dialogue_next_off";
    public long Dialogue_id;
}

//选择简单模式
public class DataDotClickDialogueEasy : DataDotBase
{
    public override string Event_name => "Click_Dialogue_easy";
    public long Dialogue_id;
}

//选择中等模式
public class DataDotClickDialogueMedium : DataDotBase
{
    public override string Event_name => "Click_Dialogue_medium";
    public long Dialogue_id;
}

//选择困难模式
public class DataDotClickDialogueDifficult : DataDotBase
{
    public override string Event_name => "Click_Dialogue_difficult";
    public long Dialogue_id;
}

//结算页跳过（不点踩不点赞，倔强）
public class DataDotBillingSkip : DataDotBase
{
    public override string Event_name => "Click_Dialogue_billing_skip";
    public long Dialogue_id;
}

//APP切换到APP后台
public class DataDotCutBackground : DataDotBase
{
    public override string Event_name => "cut_background";
    public string usage_id;
    
}

//APP切换回APP
public class DataDotCutForeground : DataDotBase
{
    public override string Event_name => "cut_foreground";
    public string usage_id;
}

//APP启动 在StartUpAmazonBI里 那部分不是热更域

//APP被杀死
public class DataDotCutExit : DataDotBase
{
    public override string Event_name => "cut_exit";
    public string usage_id;
}

public class DataDotCutEnter : DataDotBase
{
    public override string Event_name => "cut_enter";
}

//点击左下角任务卡片
public class DataDotClickTaskCard : DataDotBase
{
    public override string Event_name => "Click_Task_card";
}

//任务卡片切换
public class DataDotClickTaskCardSwitch : DataDotBase
{
    public override string Event_name => "Click_Task_card_switch";
    public long Avatar_id;
    public long Task_id;
}




public class DataDotFlowTaskStart : DataDotBase
{
    public override string Event_name => "Click_Flow_task_start";
    public long Avatar_id;
    public long Sub_task_id;
    public string Background; //Map_outside（地图模式_室外）/Map_inside（地图模式_室内)
}



public class DataDotTaskSwitch : DataDotBase
{
    public override string Event_name => "Click_task_switch";
    public long Avatar_id;
    public long Sub_task_id;
    public string Background; //Map_outside（地图模式_室外）/Map_inside（地图模式_室内)
}

public class DataDotFlowDialog : DataDotBase
{
    public override string Event_name => "Click_Flow_dialogue";
    public long Dialogue_id;
}

public class DataDotFlowDialogPlay : DataDotBase
{
    public override string Event_name => "Click_Flow_dialogue_play";
    public long Dialogue_id;

}

public class DataDotFlowDialogTranslateOn : DataDotBase
{
    public override string Event_name => "Click_Flow_dialoguer_translate_on";
    public long Dialogue_id;
}

public class DataDotFlowDialogSend : DataDotBase
{
    public override string Event_name => "Click_Flow_dialogue_send";
    public long Dialogue_id;
}

public class DataDotFlowDialogCancell : DataDotBase
{
    public override string Event_name => "Click_Flow_dialogue_cancell";
    public long Dialogue_id;
}


public class DataDotAppearTaskGoalShow : DataDotBase
{
    public override string Event_name => "Appear_Task_goal_show";
    public long Dialogue_id;
}

public class DataDotAppearTaskGoalHide : DataDotBase
{
    public override string Event_name => "Appear_Task_goal_hide";
    public long Dialogue_id;
}




public class DataDotClickDialogueTranslateOff : DataDotBase
{
    public override string Event_name => "Click_Dialogue_translate_off";
    public long Dialogue_id;
}



public class DataDotClickDialogueMacStar : DataDotBase
{
    public override string Event_name => "Click_Dialogue_mac_star";
    public long Dialogue_id;
}

public class DataDotClickDialogueSend : DataDotBase
{
    public override string Event_name => "Click_Dialogue_send";
    public long Dialogue_id;
    public long Avatar_id;
    public long Message_id;
    public string Background; //Free_dialogue（自由对话）Task_dialogue（任务对话）
}

public class DataDotClickDialogueCancell : DataDotBase
{
    public override string Event_name => "Click_Dialogue_cancell";
    public long Dialogue_id;
    public long Avatar_id;
    public long Message_id;
    public string Background; //Free_dialogue（自由对话）Task_dialogue（任务对话）
}

public class DataDotClickNext : DataDotBase
{
    public override string Event_name => "Click_Next";
    public long Dialogue_id;
}




public class DataDotClickDialogueStarShow : DataDotBase
{
    public override string Event_name => "Click_Dialogue_star_show";
    public long Dialogue_id;
}



public class DataDotClickDialoguePronounceHide : DataDotBase
{
    public override string Event_name => "Click_Dialogue_pronounce_hide";
    public long Dialogue_id;
}

public class DataDotClickDialogueStarHide : DataDotBase
{
    public override string Event_name => "Click_Dialogue_star_hide";
    public long Dialogue_id;
}


#region 每日强化埋点

    public class DataDotDailyReviewIcon : DataDotBase
    {
        public override string Event_name => "Click_Daily_review_icon";
    }

    public class DataDotDailyReviewStart : DataDotBase
    {
        public override string Event_name => "Click_Daily_review_start";
    }

    public class DataDotDailyReviewResultQuit : DataDotBase
    {
        public override string Event_name => "Click_Daily_review_result_quit";
    }

    public class DataDotDailyReviewResultContinue : DataDotBase
    {
        public override string Event_name => "Click_Daily_review_result_continue";
    }

#endregion









public class DataDotAppearSuggestionShow : DataDotBase
{
    public override string Event_name => "Appear_Suggestion_show";
    public long Dialogue_id;
}

public class DataDotClickSuggestionAdd : DataDotBase
{
    public override string Event_name => "Click_Suggestion_add";
    public long Dialogue_id;
}









public class DataDotClickSuggestionBillingPractice : DataDotBase
{
    public override string Event_name => "Click_Dialogue_billing_practice";
}

public class DataDotClickSuggestionBillingChallenge : DataDotBase
{
    public override string Event_name => "Click_Dialogue_billing_challenge";
}

public class DataDotClickSuggestionBillingConfirm : DataDotBase
{
    public override string Event_name => "Click_Dialogue_billing_confirm";
}

public class DataDotClickDialogueChunk1 : DataDotBase
{
    public override string Event_name => "Click_Dialogue_chunk1";
    public long Dialogue_id;
    public string chunk_text;
}

public class DataDotClickDialogueChunk2 : DataDotBase
{
    public override string Event_name => "Click_Dialogue_chunk2";
    public long Dialogue_id;
    public string chunk_text;
}









public class DataDotClickFlowTaskRecommend : DataDotBase
{
    public override string Event_name => "Appear_Flow_task_recommend";
    public long Avatar_id;
    public long Sub_task_id;  //Avatar推荐文本
    public string Background;
}

public class DataDotClickFlowAvatarRecommend : DataDotBase
{
    public override string Event_name => "Appear_Flow_avatar_recommend";
    public long Avatar_id;
    public string Avatar_recommend_reason;  //Avatar推荐文本
    public string Background;
}

public class DataDotCutApiCase : DataDotBase
{
    public override string Event_name => "Cut_Api_case";
    public string Api_address;
    public long Dialogue_id;
    public long Task_id;
    public long Dialogue_step;
    public int Task_mode;
    public int Cut_mode;
    public string Extra;
}
//Icon_type：Chapter-进入Chapter蒙层；Free_talk：进入Free_talk；Dialogue_id，自由对话回传dialogue_id，chapter回传0

public class DataDotClick_Avatar_icon_map : DataDotBase
{
    
    public override string Event_name => "Click_Avatar_icon_map";
    
}
//Chapter_id：章节ID；

public class DataDotAppear_Chapter_page : DataDotBase
{
    public override string Event_name => "Appear_Chapter_page";
    public long Chapter_id;

}

//Avatar_id：点击的Avatar_id；
//Task_id：任务ID；
//Task_type：Task/Free_talk
//Task_state：任务状态，指任务是否已完成，三个值：Completed-已完成；Available-当前可做；Locked-锁定中；
//Task_stars：已完成任务的星星数，1,2,3，未完成任务为0；

public class DataDotClick_Avatar_icon_chapter : DataDotBase
{
    public override string Event_name => "Click_Avatar_icon_chapter";
    public long Avatar_id;
    public long Task_id;
    public string Task_type;
    public string Task_state;
    public int Task_stars;
}
public class DataDotAppear_Task_card : DataDotBase
{
    public override string Event_name => "Appear_Task_card";
    public long Avatar_id;
    public long Task_id;
    public string Task_type;
    public string Task_state;
    public int Task_stars;
}
public class DataDotClick_Task_card_practice: DataDotBase
{
    public override string Event_name => "Click_Task_card_practice";
    public long Task_id;
    public long Avatar_id;
    public string Task_type;
    public string Task_state;
    public int Task_stars;
}
public class DataDotClick_Task_card_challenge : DataDotBase
{
    public override string Event_name => "Click_Task_card_challenge";
    public long Avatar_id;
    public string Task_type;
    public string Task_state;
    public int Task_stars;
}
public class DataDotClick_Free_talk : DataDotBase
{
    public override string Event_name => "Click_Free_talk";
    public long Avatar_id;
    public string Task_type;
    public string Task_state;
    public int Task_stars;
}
public class DataDotClick_Treasure_chest : DataDotBase
{
    public override string Event_name => "Click_Treasure_chest";
}
public class DataDotSwitch_Chapter_page : DataDotBase
{
    public override string Event_name => "Switch_Chapter_page";
}

public class DataDotClick_Tutor : DataDotBase
{
    public override string Event_name => "Click_Tutor";
    public long Avatar_id;
    public string Task_type;
    public string Task_state;
    public int Task_stars;
}
//Chapter_state：Completed-已完成；on_going-进行中；

public class DataDotClick_Chapter_top_icon : DataDotBase
{
    public override string Event_name => "Click_Chapter_top_icon";
    public string Chapter_state;
}

public class DataDotClick_Goal_top_icon : DataDotBase
{
    public override string Event_name => "Click_Goal_top_icon";
    public string Goal_state;
}

public class DataDotAppear_Goal_select_page : DataDotBase
{
    public override string Event_name => "Appear_Gallery_page";
   
}

public class DataDotCut_Chapter_finished : DataDotBase
{
    public override string Event_name => "Cut_Chapter_finished";
}


public class DataDotAppear_Chapter_select : DataDotBase
{
    public override string Event_name => "Appear_Chapter_select";
    public long Chapter1_id;
    public string Chapter1_name;
    public long Chapter2_id;
    public string Chapter2_name;
    public long Chapter3_id;
    public string Chapter3_name;
}



public class DataDotClick_Chapter_select : DataDotBase
{
    public override string Event_name => "Click_Chapter_select";
    public int Chapter_position;
    public long Chapter_id;
    public string Chapter_name;
}
//Chapter_list_Appear_type：Chapter_list页出现的原因：User_Click-用户点击；New_chapter_choose-新Chapter选择；
//Chapter_unlocked_position：此次出现有新Chapter解锁的话，回传自上而下的位置需要，否则回传0；
//Chapter_unlocked_id：此次出现有新Chapter解锁的话，回传Chapter_id，否则回传0；
//Chapter_unlocked_name：此次出现有新Chapter解锁的话，回传Chapter_name，否则回传null；

public class DataDotAppear_Chapter_list : DataDotBase
{
    public override string Event_name => "Appear_Chapter_list";
    public string Chapter_list_Appear_type;
    public long Chapter_unlocked_id;
    public string Chapter_unlocked_name;
    public int Chapter_unlocked_position;

}

public class DataDotSwitch_Chapter_list : DataDotBase
{
    public override string Event_name => "Switch_Chapter_list";
}
//Chapter_clicked_position：此次点击的Chapter的，自上而下的位置；
//Chapter_clicked_id：此次点击Chapter的Chapter_id；
//Chapter_clicked_name：此次点击Chapter的名称；
//Chapter_clicked_state：此次点击Chapter的状态，Completed-已完成，On_going-进行中（包括刚刚解锁的Chapter）；


public class DataDotClick_Chapter_list : DataDotBase
{
    public override string Event_name => "Click_Chapter_list";
    public int Chapter_clicked_position;
    public long Chapter_clicked_id;
    public string Chapter_clicked_name;
    public string Chapter_clicked_state;
}

// Goal结束时点击宝箱，领取奖励
public class DataDotClick_Goal_chest : DataDotBase
{
    public override string Event_name => "Click_Goal_chest";
    // 这里可以添加其他与点击宝箱相关的属性
}

// Goal列表页滑动
public class DataDotSwitch_Gallery_page_tag : DataDotBase
{
    public override string Event_name => "Gallery_page_tag";
   
}

public class DataDotSwitch_Gallery_page_story : DataDotBase
{
    public override string Event_name => "Gallery_page_story";

}

public class DataDotCut_Gallery_page_type : DataDotBase
{
    public override string Event_name => "Cut_Gallery_page_type";
    public int Story_num; //
    public string Story_type_location; // 滑动方向，Left或Right
}

// 在Goal列表页选择Goal
public class DataDotClick_Gallery_page_story : DataDotBase
{
    public override string Event_name => "Click_Gallery_page_story";
    public long Story_id;
    public CEFRLevel Story_difficulty;
    public string Story_category; //My_Stories；Story_Recommendation；Story_type；
    public int Story_type;
    public int Story_type_location;
    public int Story_location; // 选择的Goal的位置，从左往右数
}

// 点击Goal列表页退出
public class DataDotClick_Goal_select_quite : DataDotBase
{
    public override string Event_name => "Click_Gallery_page_quit";
    // 这里可以添加其他与退出Goal列表页相关的属性
}

// 点击Goal列表页帮助
public class DataDotClick_Gallery_page_help : DataDotBase
{
    public override string Event_name => "Gallery_page_help";
    
}


public class DataDotClickScaffold : DataDotBase
{
    public override string Event_name =>   "Click_scaffold";
}

public class DataDotClickScaffoldPlay : DataDotBase
{
    public override string Event_name =>  "Click_scaffold_play";
}

public class DataDotClickScaffoldTranslate : DataDotBase
{
    public override string Event_name =>  "Click_scaffold_translate";
}

public class DataDotClickScaffoldUp : DataDotBase
{
    public override string Event_name =>  "Click_scaffold_up";
}

public class DataDotClickScaffoldDown : DataDotBase
{
    public override string Event_name =>  "Click_scaffold_down";
}


// Appear_Talkit_login 事件类
public class DataDotAppear_Talkit_login : DataDotBase
{
    public override string Event_name => "Appear_Talkit_login";
}

// Appear_Talkit_entry 事件类
public class DataDotAppear_Talkit_entry : DataDotBase
{
    public override string Event_name => "Appear_Talkit_entry";
}

// Click_Talkit_entry_quickly 事件类
public class DataDotClick_Talkit_entry_quickly : DataDotBase
{
    public override string Event_name => "Click_Talkit_entry_quickly";
}

// Click_Talkit_entry_apple 事件类
public class DataDotClick_Talkit_entry_apple : DataDotBase
{
    public override string Event_name => "Click_Talkit_entry_apple";
}

// Appear_Userinfo_map 事件类
public class DataDotAppear_Userinfo_map : DataDotBase
{
    public override string Event_name => "Appear_Userinfo_map";
}

// Appear_Userinfo_flow_ask 事件类
public class DataDotAppear_Userinfo_flow_ask : DataDotBase
{
    public override string Event_name => "Appear_Userinfo_flow_ask";
}

// Appear_Userinfo_target 事件类
public class DataDotAppear_Userinfo_target : DataDotBase
{
    public override string Event_name => "Appear_Userinfo_target";
}

// Click_Userinfo_target_choice 事件类
public class DataDotClick_Userinfo_target_choice : DataDotBase
{
    public override string Event_name => "Click_Userinfo_target_choice";
    public string Learning_target;
}

// Click_Userinfo_target_next 事件类
public class DataDotClick_Userinfo_target_next : DataDotBase
{
    public override string Event_name => "Click_Userinfo_target_next";
    public string Learning_target;
}

// Appear_Userinfo_target_detail 事件类
public class DataDotAppear_Userinfo_target_detail : DataDotBase
{
    public override string Event_name => "Appear_Userinfo_target_detail";
    public string Learning_target;
}

// Click_Userinfo_target_detail 事件类
public class DataDotClick_Userinfo_target_detail : DataDotBase
{
    public override string Event_name => "Click_Userinfo_target_detail";
    public string Learning_target;
    public List<int> Learning_detail;
}

// Click_Userinfo_target_detail_next 事件类
public class DataDotClick_Userinfo_target_detail_next : DataDotBase
{
    public override string Event_name => "Click_Userinfo_target_detail_next";
    public string Learning_target;
    public List<int> Learning_detail;
}

// Appear_Userinfo_level 事件类
public class DataDotAppear_Userinfo_level : DataDotBase
{
    public override string Event_name => "Appear_Userinfo_level";
}

// Click_Userinfo_level 事件类
public class DataDotClick_Userinfo_level : DataDotBase
{
    public override string Event_name => "Click_Userinfo_level";
    public string User_level;
}

// Click_Userinfo_level_confirm 事件类
public class DataDotClick_Userinfo_level_confirm : DataDotBase
{
    public override string Event_name => "Click_Userinfo_level_confirm";
    public string User_level;
}

//Appear_Userinfo_hobby
public class DataDotAppear_Userinfo_hobby : DataDotBase
{
    public override string Event_name => "Appear_Userinfo_hobby";
}
// Click_Userinfo_hobby
public class DataDotClick_Userinfo_hobby : DataDotBase
{
    public override string Event_name => "Click_Userinfo_hobby";
    public string Hobby;
}

//Click_Userinfo_hobby_next
public class DataDotClick_Userinfo_hobby_next : DataDotBase
{
    public override string Event_name => "Click_Userinfo_hobby_next";
    public string Hobby;
}

//Appear_Userinfo_learning_time
public class DataDotAppear_Userinfo_learning_time : DataDotBase
{
    public override string Event_name => "Appear_Userinfo_learning_time";
}

//Click_Userinfo_learning_time
public class DataDotClick_Userinfo_learning_time : DataDotBase
{
    public override string Event_name => "Click_Userinfo_learning_time";
    public string Learning_time;
}

//Click_Userinfo_learning_time_next
public class DataDotClick_Userinfo_learning_time_next : DataDotBase
{
    public override string Event_name => "Click_Userinfo_learning_time_next";
    public string Learning_time;
}

//Appear_Userinfo_learning_curve
public class DataDotAppear_Userinfo_learning_curve : DataDotBase
{
    public override string Event_name => "Appear_Userinfo_learning_curve";
}

//Click_Userinfo_learning_curve_next
public class DataDotClick_Userinfo_learning_curve_next : DataDotBase
{
    public override string Event_name => "Click_Userinfo_learning_curve_next";
}

//Appear_Userinfo_gender
public class DataDotAppear_Userinfo_gender : DataDotBase
{
    public override string Event_name => "Appear_Userinfo_gender";
}

//Click_Userinfo_gender
public class DataDotClick_Userinfo_gender : DataDotBase
{
    public override string Event_name => "Click_Userinfo_gender";
    public string User_gender;
}

//Click_Userinfo_gender_next
public class DataDotClick_Userinfo_gender_next : DataDotBase
{
    public override string Event_name => "Click_Userinfo_gender_next";
    public string User_gender;
}

//Appear_Userinfo_age
public class DataDotAppear_Userinfo_age : DataDotBase
{
    public override string Event_name => "Appear_Userinfo_age";
}

//Click_Userinfo_age
public class DataDotClick_Userinfo_age : DataDotBase
{
    public override string Event_name => "Click_Userinfo_age";
    public string User_age;
}

//Click_Userinfo_age_next
public class DataDotClick_Userinfo_age_next : DataDotBase
{
    public override string Event_name => "Click_Userinfo_age_next";
    public string User_age;
}

// Appear_Character 事件类
public class DataDotAppear_Character : DataDotBase
{
    public override string Event_name => "Appear_Character";
}

// Appear_Character_select 事件类
public class DataDotAppear_Character_select : DataDotBase
{
    public override string Event_name => "Appear_Character_select";
    public string Character_group;
}

// Click_Character_select 事件类
public class DataDotClick_Character_select : DataDotBase
{
    public override string Event_name => "Click_Character_select";
    public string Character_group;
}

// Click_Character_group_move 事件类
public class DataDotClick_Character_group_move : DataDotBase
{
    public override string Event_name => "Click_Character_group_move";
    public string Direction;
    public string Character_group;
}

// Appear_Character_name 事件类
public class DataDotAppear_Character_name : DataDotBase
{
    public override string Event_name => "Appear_Character_name";
}

// Click_Character_name_comfirm 事件类
public class DataDotClick_Character_name_comfirm : DataDotBase
{
    public override string Event_name => "Click_Character_name_comfirm";
    public string User_name;
}

public class DataDotClick_Character_select_next : DataDotBase
{
    public override string Event_name => "Click_Character_select_next";
    public string Character_group;
    public string Character_location_id;
}

// Appear_Vistor_login 事件类
public class DataDotAppear_Vistor_login : DataDotBase
{
    public override string Event_name => "Appear_Vistor_login";
}

// Click_Vistor_login_comfirm 事件类
public class DataDotClick_Vistor_login_comfirm : DataDotBase
{
    public override string Event_name => "Click_Vistor_login_comfirm";
}



public class DataDotClick_Leaderboard_entrance : DataDotBase
{
    public override string Event_name => "Click_Leaderboard_entrance";
    public string Session_id { get; set; }
  
}

public class DataDotAppear_Leaderboard_locked : DataDotBase
{
    public override string Event_name => "Appear_Leaderboard_locked";
    // Additional properties if needed
}

public class DataDotClick_Leaderboard_locked_task : DataDotBase
{
    public override string Event_name => "Click_Leaderboard_locked_task";
    // Additional properties if needed
}

public class DataDotClick_Leaderboard_locked_quit : DataDotBase
{
    public override string Event_name => "Click_Leaderboard_locked_quit";
    // Additional properties if needed
}

public class DataDotAppear_Leaderboard_unlock : DataDotBase
{
    public override string Event_name => "Appear_Leaderboard_unlock";
    public string CurScene;
    // Additional properties if needed
}

public class DataDotClick_Leaderboard_unlock_go : DataDotBase
{
    public override string Event_name => "Click_Leaderboard_unlock_go";
    public string CurScene;
    // Additional properties if needed
}

public class DataDotClick_Leaderboard_unlock_quit : DataDotBase
{
    public override string Event_name => "Click_Leaderboard_unlock_quit";
    public string CurScene;
    // Additional properties if needed
}

public class DataDotAppear_Leaderboard_congra : DataDotBase
{
    public override string Event_name => "Appear_Leaderboard_congra";
    public string CurScene;
    // Additional properties if needed
}

public class DataDotClick_Leaderboard_congra : DataDotBase
{
    public override string Event_name => "Click_Leaderboard_congra";
    public string CurScene;
    // Additional properties if needed
}


public class DataDotAppear_Leaderboard : DataDotBase
{
    public override string Event_name => "Appear_Leaderboard";
    // Additional properties if needed
}

public class DataDotClick_Leaderboard_quit : DataDotBase
{
    public override string Event_name => "Click_Leaderboard_quit";
    // Additional properties if needed
}

public class DataDotAppear_Leaderboard_levelup : DataDotBase
{
    public override string Event_name => "Appear_Leaderboard_levelup";
    // Additional properties if needed
}

public class DataDotClick_Leaderboard_levelup_confirm : DataDotBase
{
    public override string Event_name => "Click_Leaderboard_levelup_confirm";
    // Additional properties if needed
}

public class DataDotAppear_Leaderboard_reward : DataDotBase
{
    public override string Event_name => "Appear_Leaderboard_reward";
    // Additional properties if needed
}

public class DataDotClick_Leaderboard_reward_get : DataDotBase
{
    public override string Event_name => "Click_Leaderboard_reward_get";
    // Additional properties if needed
}

public class DataDotAppear_Leaderboard_demotion : DataDotBase
{
    public override string Event_name => "Appear_Leaderboard_demotion";
    // Additional properties if needed
}

public class DataDotClick_Leaderboard_demotion_confirm : DataDotBase
{
    public override string Event_name => "Click_Leaderboard_demotion_confirm";
    // Additional properties if needed
}

public class DataDotAppear_Tutor_TitlePage : DataDotBase
{
    public override string Event_name => "Appear_Tutor_TitlePage";
    // Additional properties if needed
}

public class DataDotAppear_Dialogue_bubble : DataDotBase
{
    public override string Event_name => "Appear_Dialogue_bubble";
    public long Session_id;
    public string level_type;
    public string part_title;
    public int part_order;
    public long Dialogue_id;
    public string Bubble_type;
    public int Dialogue_round;
    public long session_record_id;
    public string Bubble_mark;
    public string Dialogue_type;
    public string Help_mode;
    public long question_id;
    public int question_index;
}

public class DataDotClick_Suggestion : DataDotBase
{
    public override string Event_name => "Click_Suggestion";
    public long Dialogue_id;
    public int Dialogue_round;
    public string Help_mode;
}

public class DataDotClick_Help_mode : DataDotBase
{
    public override string Event_name => "Click_Help_mode";
    public long Dialogue_id;
    public int Dialogue_round;
    public string Help_mode;
}

public class DataDotAppear_Suggestion_show : DataDotBase
{
    public override string Event_name => "Appear_Suggestion_show";
    public long Dialogue_id;
    public int Dialogue_round;
}

public class DataDotAppear_Suggestion_hide : DataDotBase
{
    public override string Event_name => "Appear_Suggestion_hide";
    public long Dialogue_id;
    public int Dialogue_round;
}

public class DataDotAppear_Tutor_result : DataDotBase
{
    public override string Event_name => "Appear_Tutor_result";
    public long Dialogue_id;
}

public class DataDotClick_Tutor_result_complete : DataDotBase
{
    public override string Event_name => "Click_Tutor_result_complete";
    public long Dialogue_id;
}

public class DataDotClick_FreeTalk_TitlePage_quit : DataDotBase
{
    public override string Event_name => "Click_FreeTalk_TitlePage_quit";
    public long DialogueID;
    public long AvatarID;
}

public class DataDotAppear_FreeTalk_result : DataDotBase
{
    public override string Event_name => "Appear_FreeTalk_result";
    public long DialogueID;
}

public class DataDotClick_FreeTalk_Result_quit : DataDotBase
{
    public override string Event_name => "Click_FreeTalk_result_quit";
    public long DialogueID;
}

public class DataDotClick_FreeTalk_Result_continue : DataDotBase
{
    public override string Event_name => "Click_FreeTalk_result_continue";
    public long DialogueID;
}

public class DataDotClick_Advice_Forecast : DataDotBase
{
    public override string Event_name => "Click_Advice_Forecast";
    public long Dialogue_id;
    public int Dialogue_round;
}

public class DataDotClick_Example_play : DataDotBase
{
    public override string Event_name => "Click_Example_play";
    public long Dialogue_id;
    public int Dialogue_round;
    public string Help_mode;
}

public class DataDotClick_Example_translate : DataDotBase
{
    public override string Event_name => "Click_Example_translate";
    public long Dialogue_id;
    public int Dialogue_round;
    public string Help_mode;
}

public class DataDotClick_Example_up : DataDotBase
{
    public override string Event_name => "Click_Example_up";
    public long Dialogue_id;
    public int Dialogue_round;
    public string Help_mode;
}

public class DataDotClick_Example_down : DataDotBase
{
    public override string Event_name => "Click_Example_down";
    public long Dialogue_id;
    public int Dialogue_round;
    public string Help_mode;
}

public class DataDotClick_Help : DataDotBase
{
    public override string Event_name => "Click_Help";
    public long Dialogue_id;
    public int Dialogue_round;
    public string Help_mode;
    public int AUA_dialogue_round;
    public long AUA_question_id;
}

public class DataDotClick_Option : DataDotBase
{
    public override string Event_name => "Click_Option";
    public long Dialogue_id;
}

public class DataDotDifficulty_change : DataDotBase
{
    public override string Event_name => "Difficulty_change";
    public long Dialogue_id;
    public int Difficulty_level;
}

public class DataDotClick_Option_hide : DataDotBase
{
    public override string Event_name => "Click_Option_hide";
    public long Dialogue_id;
}




public class DataDotAppear_Dialogue_Result : DataDotBase
{
    public override string Event_name => "Appear_Dialogue_Result";

    public PB_DialogMode Dialogue_type; // 回传对话类型编号，与后端保持一致
    public long Dialogue_id; // 本次对话的对话ID
}

public class DataDotClick_Dialogue_result_quit : DataDotBase
{
    public override string Event_name => "Click_Dialogue_result_quit";
    public PB_DialogMode Dialogue_type;
    public long Dialogue_id;
}

public class DataDotClick_Dialogue_result_continue : DataDotBase
{
    public override string Event_name => "Click_Dialogue_result_continue";
    public PB_DialogMode Dialogue_type;
    public long Dialogue_id;
}

public class DataDotClick_Dialogue_result_reaction : DataDotBase
{
    public override string Event_name => "Click_Dialogue_result_reaction";
    public PB_DialogMode Dialogue_type;
    public long Dialogue_id;
}

public class DataDotClick_Dialogue_result_shadow : DataDotBase
{
    public override string Event_name => "Click_Dialogue_result_shadow";
    public PB_DialogMode Dialogue_type;
    public long Dialogue_id;
}

public class DataDotClick_Dialogue_result_confirm : DataDotBase
{
    public override string Event_name => "Click_Dialogue_result_confirm";
    public PB_DialogMode Dialogue_type;
    public long Dialogue_id;
}

public class DataDotAppear_Dialogue_continue_confirm : DataDotBase
{
    public override string Event_name => "Appear_Dialogue_continue_confirm";
    public PB_DialogMode Dialogue_type;
    public long Dialogue_id;
    public PB_DialogMode Next_dialogue_type;
}

public class DataDotClick_Dialogue_continue_confirm : DataDotBase
{
    public override string Event_name => "Click_Dialogue_continue_confirm";
    public PB_DialogMode Dialogue_type;
    public long Dialogue_id;
    public PB_DialogMode Next_dialogue_type;
}



public class DataDotClick_Dialogue_continue_quit : DataDotBase
{
    public override string Event_name => "Click_Dialogue_continue_quit";
    public PB_DialogMode Dialogue_type;
    public long Dialogue_id;
    public PB_DialogMode Next_dialogue_type;
}

public class DataDotAppear_Dialogue_quit_confirm : DataDotBase
{
    public override string Event_name => "Appear_Dialogue_quit_confirm";
    public PB_DialogMode Dialogue_type;
    public long Dialogue_id;
}

public class DataDotClick_Dialogue_quit_confirm : DataDotBase
{
    public override string Event_name => "Click_Dialogue_quit_confirm";
    public PB_DialogMode Dialogue_type;
    public long Dialogue_id;
}

public class DataDotClick_Dialogue_quit_quit : DataDotBase
{
    public override string Event_name => "Click_Dialogue_quit_quit";
    public PB_DialogMode Dialogue_type;
    public long Dialogue_id;
}

public class DataDotAppear_Dialogue_result_cover : DataDotBase
{
    public override string Event_name => "Appear_Dialogue_result_cover";
}

public class DataDotClick_Dialogue_cover_confirm : DataDotBase
{
    public override string Event_name => "Click_Dialogue_cover_confirm";
}

public class DataDotAppear_Practice_title_page : DataDotBase
{
    public override string Event_name => "Appear_Practice_title_page";
    public long Task_id;
    public string Task_type; // Should be "Task", "Free_talk", or "Warm up"
    public long Dialogue_id;
}

public class DataDotAppear_Practice_learning_point : DataDotBase
{
    public override string Event_name => "Appear_Practice_learning_point";
    public long Task_id;
    public string Task_type; // Should be "Task", "Free_talk", or "Warm up"
    public long Dialogue_id;
    public long Knowledge_ids; // Array to hold multiple knowledge point IDs
}

public class DataDotClick_Practice_learning_point : DataDotBase
{
    public override string Event_name => "Click_Practice_learning_point";
    public long Task_id;
    public string Task_type; // Should be "Task", "Free_talk", or "Warm up"
    public long Dialogue_id;
    public long Knowledge_ids; // Array to hold multiple knowledge point IDs
}
public class DataDotClick_FragmentPractice_entrance : DataDotBase
{
    public override string Event_name => "Click_Quick_entrance";
}

public class DataDotAppear_FragmentPractice_appear : DataDotBase
{
    public override string Event_name => "Appear_Quick_parctice";
    public long List_id;//强化组id
}

public class DataDotAppear_FragmentPractice_question : DataDotBase
{
    public override string Event_name => "Appear_Quick_question";
    public long List_id;//强化组id
    public long Question_id;//题目id
    public int Question_index;//题目索引 二刷清0
    public int Question_num;//题目数 首次 二刷要区分
    public string Exercises_type;// Planned  Corrected
}

public class DataDotClick_FragmentPractice_answer : DataDotBase
{
    public override string Event_name => "Click_Quick_answer";
    public long List_id;//强化组id
    public long Question_id;//题目id
    public int Question_index;//题目索引 二刷清0
    public int Question_num;//题目数 首次 二刷要区分
    public string Exercises_type;// Planned  Corrected
}

public class DataDotClick_FragmentPractice_quit_cancel : DataDotBase
{
    public override string Event_name => "Click_Quick_quit_cancel";
    public long List_id;//强化组id
    public long Question_id;//题目id
    public int Question_index;//题目索引 二刷清0
    public int Question_num;//题目数 首次 二刷要区分
    public string Exercises_type;// Planned  Corrected
}

public class DataDotClick_FragmentPractice_quit_confirm : DataDotBase
{
    public override string Event_name => "Click_Quick_quit_confirm";
    public long List_id;//强化组id
    public long Question_id;//题目id
    public int Question_index;//题目索引 二刷清0
    public int Question_num;//题目数 首次 二刷要区分
    public string Exercises_type;// Planned  Corrected
}

public class DataDotClick_Address_book : DataDotBase
{
    public override string Event_name => "Click_Address_book";
    public bool red_dot;
}

public class DataDotAppear_Address_book_home : DataDotBase
{
    public override string Event_name => "Appear_Address_book_home";
    public string Entry_type;
}

public class DataDotClick_Adress_book_avatar : DataDotBase
{
    public override string Event_name => "Click_Adress_book_avatar";
    public long avatar_id;
}

public class DataDotClick_Address_book_career_explore : DataDotBase
{
    public override string Event_name => "Click_Address_book_career_explore";
    public int Career_type;
    public int Career_tag;
}

public class DataDotAppear_Address_book_friend_list : DataDotBase
{
    public override string Event_name => "Appear_Address_book_friend_list";
}

public class DataDotClick_Address_book_friend_talk : DataDotBase
{
    public override string Event_name => "Click_Address_book_friend_talk";
    public long  avatar_id;
    public string Avatar_type;
}

public class DataDotAppear_MainTitlePage : DataDotBase
{
    public override string Event_name => "Appear_Main_title_page";
    public string Stage_type;
    public long Task_id;
    public string Task_state;
    public long Avatar_id;
    public string Knowledge_ids;
    public string Source;//结算Result 路径Path 小镇Town 通讯录Contact
    public long Last_dialogue_id;//结算传 其他传0
}

public class DataDotClick_MainTitlePage_quit : DataDotBase
{
    public override string Event_name => "Click_Main_title_page_quit";
    public string Stage_type;
    public long Task_id;
    public string Task_state;
    public long Avatar_id;
    public string Knowledge_ids;
    public string Source;//结算Result 路径Path 小镇Town 通讯录Contact
    public long Last_dialogue_id;//结算传 其他传0
}

public class DataDotClick_MainTitlePage_start : DataDotBase
{
    public override string Event_name => "Click_Main_title_page_start";
    public string Stage_type;
    public long Task_id;
    public string Task_state;
    public long Avatar_id;
    public string Knowledge_ids;
    public string Source;//结算Result 路径Path 小镇Town 通讯录Contact
    public long Last_dialogue_id;//结算传 其他传0
}

public class DataDotClick_MainTitlePage_word : DataDotBase
{
    public override string Event_name => "Click_Main_title_page_word";
    public string Stage_type;
    public long Task_id;
    public string Task_state;
    public long Avatar_id;
    public string Knowledge_ids;
    public string Source;//结算Result 路径Path 小镇Town 通讯录Contact
    public long Last_dialogue_id;//结算传 其他传0
}

public class DataDotClick_Main_title_page_continue : DataDotBase
{
    public override string Event_name => "Main_title_page_continue";
    public string Stage_type;
    public long Task_id;
    public string Task_state;
    public long Avatar_id;
    public string Knowledge_ids;
    public string Source;//结算Result 路径Path 小镇Town 通讯录Contact
    public long Last_dialogue_id;//结算传 其他传0
}


public class DataDotAppear_Dialogue_1in3 : DataDotBase
{
    public override string Event_name => "Appear_Dialogue_1in3";
    public PB_DialogMode Dialogue_type;
    public long Dialogue_id;
}

public class DataDotClick_Dialogue_1in3_practice : DataDotBase
{
    public override string Event_name => "Click_Dialogue_1in3_practice";
    public PB_DialogMode Dialogue_type;
    public long Dialogue_id;
}

public class DataDotClick_Dialogue_1in3_shadowing : DataDotBase
{
    public override string Event_name => "Click_Dialogue_1in3_shadowing";
    public PB_DialogMode Dialogue_type;
    public long Dialogue_id;
}

public class DataDotClick_Dialogue_1in3_next : DataDotBase
{
    public override string Event_name => "Click_Dialogue_1in3_next";
    public PB_DialogMode Dialogue_type;
    public long Dialogue_id;
}

public class DataDotClick_Dialogue_1in3_reward : DataDotBase
{
    public override string Event_name => "Click_Dialogue_1in3_reward";
    public PB_DialogMode Dialogue_type;
    public long Dialogue_id;
}

public class DataDotClick_Dialogue_1in3_quit : DataDotBase
{
    public override string Event_name => "Click_Dialogue_1in3_quit";
    public PB_DialogMode Dialogue_type;
    public long Dialogue_id;
}

public class DataDotCut_DrillHub_unlock : DataDotBase
{
    public override string Event_name => "Cut_DrillHub_unlock";
}

//public class DataDotClick_DrillHub : DataDotBase  //使用HomepageUIDataDot的
//{
//    public override string Event_name => "Click_DrillHub";
//}

public class DataDotAppear_DrillHub_page : DataDotBase//没埋 因为是瞬时发生
{
    public override string Event_name => "Appear_DrillHub_page";
    public int Mistake_red_point;//错题红点状态
    public int Learn_point_red_point;//知识点红点状态
}

public class DataDotClick_DrillHub_mistake_review : DataDotBase
{
    public override string Event_name => "Click_Drillhub_mistake_review";
    public int Mistake_red_point;//错题红点状态
    public int Learn_point_red_point;//知识点红点状态
}

public class DataDotClick_Drillhub_learn_point : DataDotBase
{
    public override string Event_name => "Click_Drillhub_learn_point";
    public int Mistake_red_point;//错题红点状态
    public int Learn_point_red_point;//知识点红点状态
}

public class DataDotClick_Drillhub_quit : DataDotBase
{
    public override string Event_name => "Click_DrillHub_quit";
    public int Mistake_red_point;//错题红点状态
    public int Learn_point_red_point;//知识点红点状态
}

public class DataDotAppear_DrillHub_mistake_list : DataDotBase
{
    public override string Event_name => "Appear_DrillHub_mistake_list";
    public int Red_point_num;//红点数量
}

public class DataDotClick_DrillHub_mistake_back : DataDotBase
{
    public override string Event_name => "Click_DrillHub_mistake_back";
    public int Red_point_num;//红点数量
}

public class DataDotClick_DrillHub_mistake : DataDotBase
{
    public override string Event_name => "Click_DrillHub_mistake";
    public int Mistake_red_point;//错题红点状态
    public long Mistake_id;//错题ID
    public long Index_id;//顺序ID
}

public class DataDotAppear_DrillHub_review_list : DataDotBase
{
    public override string Event_name => "Appear_DrillHub_review_list";
    public int Red_point_num;//红点数量
}

public class DataDotClick_DrillHub_review_back : DataDotBase
{
    public override string Event_name => "Click_DrillHub_review_back";
    public int Red_point_num;//红点数量
}

public class DataDotClick_DrillHub_review : DataDotBase
{
    public override string Event_name => "Click_DrillHub_review";
    public int Review_red_point;//知识点红点状态
    public long Review_id;//知识点ID
    public long Index_id;//顺序ID
}

public class DataDotAppear_DrillHub_mistake_title : DataDotBase
{
    public override string Event_name => "Appear_DrillHub_mistake_title";
    public int Mistake_red_point;//错题红点状态
    public long Mistake_id;//错题ID
    public long Index_id;//顺序ID
}

public class DataDotClick_DrillHub_mistake_title_quit : DataDotBase
{
    public override string Event_name => "Click_DrillHub_mistake_title_quit";
    public int Mistake_red_point;//错题红点状态
    public long Mistake_id;//错题ID
    public long Index_id;//顺序ID
}

public class DataDotClick_DrillHub_mistake_title_translate : DataDotBase
{
    public override string Event_name => "Click_DrillHub_mistake_title_translate";
    public string Word;
}

public class DataDotAppear_DrillHub_mistake_title_translate : DataDotBase
{
    public override string Event_name => "Appear_DrillHub_mistake_title_translate";
    public string Word;
    public string Translation;
}

public class DataDotClick_DrillHub_mistake_title_example : DataDotBase
{
    public override string Event_name => "Click_DrillHub_mistake_title_example";
    public string Example;
}

public class DataDotClick_DrillHub_mistake_title_start : DataDotBase
{
    public override string Event_name => "Click_DrillHub_mistake_title_start";
}

public class DataDotAppear_DrillHub_mistake_final : DataDotBase
{
    public override string Event_name => "Appear_DrillHub_mistake_final";
    public string Finnal_type;//Success / Fail
}

public class DataDotClick_DrillHub_mistake_final_quit : DataDotBase
{
    public override string Event_name => "Click_DrillHub_mistake_final_quit";
    public string Finnal_type;//Success / Fail
}

public class DataDotClick_DrillHub_mistake_finnal_continue : DataDotBase
{
    public override string Event_name => "Click_DrillHub_mistake_finnal_continue";
    public string Finnal_type;//Success / Fail
}

public class DataDotAppear_DrillHub_mistake_finish : DataDotBase
{
    public override string Event_name => "Appear_DrillHub_mistake_finish";
}

public class DataDotClick_DrillHub_mistake_final_translate : DataDotBase
{
    public override string Event_name => "Click_DrillHub_mistake_final_translate";
    public string Finnal_type;//Success / Fail
    public string Word;
}

public class DataDotAppear_DrillHub_mistake_final_translate : DataDotBase
{
    public override string Event_name => "Appear_DrillHub_mistake_final_translate";
    public string Finnal_type;//Success / Fail
    public string Word;
    public string Translation;
}

public class DataDotClick_DrillHub_mistake_final_example : DataDotBase
{
    public override string Event_name => "Click_DrillHub_mistake_final_example";
    public string Finnal_type;//Success / Fail
    public string Example;
}

public class DataDotAppear_DrillHub_review_title : DataDotBase
{
    public override string Event_name => "Appear_DrillHub_review_title";
    public int Review_red_point;//红点状态
    public long Language_point_id;//知识点ID
    public long Index_id;//顺序ID
}

public class DataDotClick_DrillHub_review_title_quit : DataDotBase
{
    public override string Event_name => "Click_DrillHub_review_title_quit";
    public int Review_red_point;//红点状态
    public long Language_point_id;//知识点ID
    public long Index_id;//顺序ID
}

public class DataDotClick_DrillHub_review_title_translate : DataDotBase
{
    public override string Event_name => "Click_DrillHub_review_title_translate";
    public string Word;
}

public class DataDotAppear_DrillHub_review_title_translate : DataDotBase
{
    public override string Event_name => "Appear_DrillHub_review_title_translate";
    public string Word;
    public string Translation;
}

public class DataDotClick_DrillHub_review_title_example : DataDotBase
{
    public override string Event_name => "Click_DrillHub_review_title_example";
    public string Example;
}

public class DataDotClick_DrillHub_review_title_start : DataDotBase
{
    public override string Event_name => "Click_DrillHub_review_title_start";
}

public class DataDotAppear_DrillHub_review_final : DataDotBase
{
    public override string Event_name => "Appear_DrillHub_review_final";
    public string Finnal_type;//Success / Fail
}

public class DataDotClick_DrillHub_review_final_translate : DataDotBase
{
    public override string Event_name => "Click_DrillHub_review_final_translate";
    public string Finnal_type;//Success / Fail
    public string Word;
}

public class DataDotAppear_DrillHub_review_final_translate : DataDotBase
{
    public override string Event_name => "Appear_DrillHub_review_final_translate";
    public string Finnal_type;//Success / Fail
    public string Word;
    public string Translation;
}

public class DataDotClick_DrillHub_review_final_example : DataDotBase
{
    public override string Event_name => "Click_DrillHub_review_final_example";
    public string Finnal_type;//Success / Fail
    public string Example;
}

public class DataDotClick_DrillHub_review_final_quit : DataDotBase
{
    public override string Event_name => "Click_DrillHub_review_final_quit";
    public string Finnal_type;//Success / Fail
}

public class DataDotClick_DrillHub_review_finnal_continue : DataDotBase
{
    public override string Event_name => "Click_DrillHub_review_finnal_continue";
    public string Finnal_type;//Success / Fail
}

public class DataDotAppear_DrillHub_review_finish : DataDotBase
{
    public override string Event_name => "Appear_DrillHub_review_finish";
}

public class DataDotSwitch_DrillHub_mistake_back : DataDotBase
{
    public override string Event_name => "Switch_DrillHub_mistake_back";
    public int Red_point_num;
}

public class DataDotSwitch_DrillHub_review_back : DataDotBase
{
    public override string Event_name => "Switch_DrillHub_review_back";
    public int Red_point_num;
}

public class DataDotSwitch_DrillHub_mistake_title_quit : DataDotBase
{
    public override string Event_name => "Switch_DrillHub_mistake_title_quit";
    public int Mistake_red_point;//错题红点状态
    public long Mistake_id;//错题ID
    public long Index_id;//顺序ID
}

public class DataDotSwitch_DrillHub_review_title_quit : DataDotBase
{
    public override string Event_name => "Switch_DrillHub_review_title_quit";
    public int Review_red_point;//红点状态
    public long Language_point_id;//知识点ID
    public long Index_id;//顺序ID
}

public class DataDotAppear_Topic_page : DataDotBase
{
    public override string Event_name => "Appear_Topic_page";
    public string Stage_type;
    public long Task_id;
    public string Task_state;
    public long Avatar_id;
    public string Knowledge_ids;
    public string Source;//结算Result 路径Path 小镇Town 通讯录Contact
    public long Last_dialogue_id;//结算传 其他传0
}

public class DataDotClick_Topic_page_back : DataDotBase
{
    public override string Event_name => "Appear_Topic_page";
    public string Stage_type;
    public long Task_id;
    public string Task_state;
    public long Avatar_id;
    public string Knowledge_ids;
    public string Source;//结算Result 路径Path 小镇Town 通讯录Contact
    public long Last_dialogue_id;//结算传 其他传0
}

public class DataDotClick_Topic_page_topic : DataDotBase
{
    public override string Event_name => "Click_Topic_page_topic";
    public string Stage_type;
    public long Task_id;
    public string Task_state;
    public long Avatar_id;
    public string Knowledge_ids;
    public string Source;//结算Result 路径Path 小镇Town 通讯录Contact
    public long Last_dialogue_id;//结算传 其他传0
    public long Topic_id;
    public string Topic_Title;
}

public class DataDotClick_Topic_page_start : DataDotBase
{
    public override string Event_name => "Click_Topic_page_start";
    public string Stage_type;
    public long Task_id;
    public string Task_state;
    public long Avatar_id;
    public string Knowledge_ids;
    public string Source;//结算Result 路径Path 小镇Town 通讯录Contact
    public long Last_dialogue_id;//结算传 其他传0
    public long Topic_id;
    public string Topic_Title;
}

public class DataDotClick_Topic_page_history : DataDotBase
{
    public override string Event_name => "Click_Topic_page_history";
    public string Stage_type;
    public long Task_id;
    public string Task_state;
    public long Avatar_id;
    public string Knowledge_ids;
    public string Source;//结算Result 路径Path 小镇Town 通讯录Contact
    public long Last_dialogue_id;//结算传 其他传0
}

public class DataDotAppear_Topic_history : DataDotBase
{
    public override string Event_name => "Appear_Topic_history";
    public string Stage_type;
    public long Task_id;
    public string Task_state;
    public long Avatar_id;
    public string Knowledge_ids;
    public string Source;//结算Result 路径Path 小镇Town 通讯录Contact
    public long Last_dialogue_id;//结算传 其他传0
}

public class DataDotClick_Topic_history_translation_on : DataDotBase
{
    public override string Event_name => "Click_Topic_history_translation_on";
    public string Stage_type;
    public long Task_id;
    public string Task_state;
    public long Avatar_id;
    public string Knowledge_ids;
    public string Source;//结算Result 路径Path 小镇Town 通讯录Contact
    public long Last_dialogue_id;//结算传 其他传0
}

public class DataDotClick_Topic_history_translation_off : DataDotBase
{
    public override string Event_name => "Click_Topic_history_translation_off";
    public string Stage_type;
    public long Task_id;
    public string Task_state;
    public long Avatar_id;
    public string Knowledge_ids;
    public string Source;//结算Result 路径Path 小镇Town 通讯录Contact
    public long Last_dialogue_id;//结算传 其他传0
}

public class DataDotClick_Topic_history_back : DataDotBase
{
    public override string Event_name => "Click_Topic_history_back";
    public string Stage_type;
    public long Task_id;
    public string Task_state;
    public long Avatar_id;
    public string Knowledge_ids;
    public string Source;//结算Result 路径Path 小镇Town 通讯录Contact
    public long Last_dialogue_id;//结算传 其他传0
}

public class DataDotAppear_Address_book_suggestion : DataDotBase
{
    public override string Event_name => "Appear_Address_book_suggestion";
}

public class DataDotClick_Address_book_suggestion_claim : DataDotBase
{
    public override string Event_name => "Click_Address_book_suggestion_claim";
}

public class DataDotClick_Address_book_suggestion_ok : DataDotBase
{
    public override string Event_name => "Click_Address_book_suggestion_ok";
}

public class DataDotClick_Address_book_friend_claim : DataDotBase
{
    public override string Event_name => "Click_Address_book_friend_claim";
}

public class DataDotClick_Address_book_friend_reward : DataDotBase
{
    public override string Event_name => "Click_Address_book_friend_reward";
}

public class DataDotClick_Suggestion_round : DataDotBase
{
    public override string Event_name => "Click_Suggestion_round";
    public int AUA_dialogue_round;
    public long AUA_question_id;
}

// 观看插屏广告
public class Show_Interstitial_Ad : DataDotBase
{
    public override string Event_name => "Show_Interstitial_Ad";
}
// 关闭插屏广告
public class Close_Interstitial_Ad : DataDotBase
{
    public override string Event_name => "Close_Interstitial_Ad";
}

// 离开视频播放
public class DataDotLeft_VideoTask : DataDotBase
{
    public override string Event_name => "DataDotLeft_VideoTask";
    public double CurTime;
    public long TaskId;
}

public class DotAppearSessionToExplorePopup : DataDotBase
{
    public override string Event_name => "appear_session_to_explore_popup";
    public int popup_type;//1表示初次引流的弹窗，2表示再次引流的弹窗
    public string traffic_source;//session1_completed/session1_incompleted/leave_in_session/0
}

public class DotClickSessionToExplorePopupButton : DataDotBase
{
    public override string Event_name => "click_session_to_explore_popup_button";
    public int popup_type;//1表示初次引流的弹窗，2表示再次引流的弹窗
    public string traffic_source;//session1_completed/session1_incompleted/leave_in_session/0
    public string button_type;//session/explore
}

public enum RedDotHomeBottomEnum
{
    session_icon,
    explore_icon,
    rank_icon,
    none,
}

public enum ClickIconHomeBottomEnum
{
    home_icon,
    explore_icon,
    rank_icon,
    //exercise_icon,
    voicechat_icon,
    friend_icon,
}

public class DotAppearHomeBottomBar : DataDotBase
{
    public override string Event_name => "appear_home_bottom_bar";
    public string red_dot;
}

public class DotClickHomeBottomBarIcon : DataDotBase
{
    public override string Event_name => "click_home_bottom_bar_icon";
    public string red_dot;
    public string click_icon;
    public string before_page;
    public string after_page;
    public string operation_method;
}


    //add by raybit
    //由于
    public class HomepageUIDataDot : DataDotBase
{
    public HomepageUIDataDot(HPEventType eType,long taskID,long avatarID) {
        this.currEvtType = eType;
        this.Task_id = taskID;
        this.Avatar_id = avatarID;
    }

    // public HomepageUIDataDot(HPEventType eType, LearnPathNextTaskItemInfo nextTaskItemInfo)
    // {
    //     this.currEvtType = eType;
    //     switch (nextTaskItemInfo.nextStatus) {
    //         case LearnPathNextTaskItemStatus.Undefined:
    //             this.Status = HPBtnStatus.InitialState.ToString();
    //             break;
    //         case LearnPathNextTaskItemStatus.Exception:
    //             this.Status = HPBtnStatus.Error.ToString();
    //             break;
    //         case LearnPathNextTaskItemStatus.ToSpeak:
    //             this.Status = nextTaskItemInfo.nextAvatarID == -1 ? HPBtnStatus.Flow.ToString() : HPBtnStatus.Avatar.ToString();
    //             break;
    //         case LearnPathNextTaskItemStatus.ToClaimBox:
    //             this.Status = HPBtnStatus.Chest.ToString();
    //           
    //             break;
    //         case LearnPathNextTaskItemStatus.ToChangeStory:
    //             this.Status = HPBtnStatus.Blank.ToString();
    //             break;
    //     }
    //     this.Avatar_id = nextTaskItemInfo.nextAvatarID;
    //     this.Task_id = nextTaskItemInfo.nextPBTaskItem != null ? nextTaskItemInfo.nextPBTaskItem.task_id : -1;
    // }

    public enum HPEventType
    {
        Click_Home_page_error,
 
        Click_Home_page_practice,
        Click_Home_page_speak,
        Click_Home_page_claim,
        Click_Home_page_bottom_gallery,

        Click_Home_page_profile,
        Click_Home_page_setting,
        Click_Home_page_diamond,
        Click_Home_page_stamina,


        Click_Home_page_streaks,
        Click_Home_page_quest,
        Click_Home_page_survey,

        Click_Home_page_gallery,
        //Click_Home_page_address_book,//通讯录？
        Click_Home_page_contacts,
        
        Click_Home_page_world,//
        Click_Home_page_diy,//
        Click_Home_page_drillhub,

        

        //====//无用
        Click_Home_page_avatar,
        Click_Home_page_flow,// 无用
        Click_Home_page_practice_center,
        Click_Home_page_moments,//不存在
        Click_Home_page_human_conversation,//补充
    }
    public enum HPBtnStatus
    {
        Avatar,
        Flow,//big flow
        Chest,
        Blank,//story/gallery
        Error,
        InitialState //undefined
    }

    private HPEventType currEvtType;
    public override string Event_name => currEvtType.ToString();
    public string Status;//
    public long Task_id =-1;
    public long Avatar_id=-1; 

}




