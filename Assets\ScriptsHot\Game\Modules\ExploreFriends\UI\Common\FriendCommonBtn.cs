using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace UIBind.ExploreFriends
{
    public partial class FriendCommonBtn
    {
        public enum BtnMode
        {
            //黑底白字
            NewHand,
            //紫底白字
            Introduce,
            //白底紫字
            DrawResult,
            //白底紫字
            Bag,
        }
        
        private readonly Color mode2Color = new Color(124f/255,58f/255,237f/255,1f);
        private readonly Color drawResultTxtColor = new Color(124f/255,58f/255,237f/255,1f);
        
        public void SetBtnMode(BtnMode mode)
        {
            switch (mode)
            {
                case BtnMode.NewHand:
                    BtnImg.color = Color.black;
                    OutLineImg.color = Color.black;
                    title0.color = Color.white;
                    break;
                case BtnMode.Introduce:
                    BtnImg.color = mode2Color;
                    OutLineImg.color = mode2Color;
                    title0.color = Color.white;
                    break;
                case BtnMode.DrawResult:
                case BtnMode.Bag:
                    BtnImg.color = Color.white;
                    OutLineImg.color = Color.white;
                    title0.color = drawResultTxtColor;
                    break;
            }          
        }
        
        public void SetDiamondStatus(bool isFree,int diamondCnt = 0)
        {
            IsFree.selectedIndex = isFree ? 0 : 1;
            if (!isFree)
            {
                number.text = diamondCnt.ToString();
            }
        }
        
        public void SetLoadingStatus(bool isLoading)
        {
            IsLoading.selectedIndex = 0;
        }

        public void SetTxt(string btnKey)
        {
            title0.text = I18N.inst.MoStr(btnKey);
        }

        public void SetEnable(bool flag)
        {
            com.enabled = flag;
            OutLineImg.visible = flag;
        }

    }
}
