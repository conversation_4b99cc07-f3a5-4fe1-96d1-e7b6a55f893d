/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class BtnBaseAnswer : GButton
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "BtnBaseAnswer";
        public static string url => "ui://cmoz5osjz7rm2y";

        public Controller grpState;
        public Controller state;
        public GGraph NormalBg;
        public GGraph RightBg;
        public GGraph FalseBg;
        public GGraph SelectBg;
        public GGraph RightNoFillBg;
        public GGraph FalseNoFillBg;
        public GGraph SelectNoFillBg;
        public GTextField tfImageAnswer;
        public GTextField tfAnswer;
        public GTextField tfBoldAnswer;
        public GComponent wave;
        public GGroup grpAnswer;
        public GGraph holder;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(BtnBaseAnswer));
        }

        public override void ConstructFromXML(XML xml)
        {
            grpState = GetControllerAt(0);
            state = GetControllerAt(1);
            NormalBg = GetChildAt(0) as GGraph;
            RightBg = GetChildAt(1) as GGraph;
            FalseBg = GetChildAt(2) as GGraph;
            SelectBg = GetChildAt(3) as GGraph;
            RightNoFillBg = GetChildAt(4) as GGraph;
            FalseNoFillBg = GetChildAt(5) as GGraph;
            SelectNoFillBg = GetChildAt(6) as GGraph;
            tfImageAnswer = GetChildAt(7) as GTextField;
            tfAnswer = GetChildAt(9) as GTextField;
            tfBoldAnswer = GetChildAt(10) as GTextField;
            wave = GetChildAt(11) as GComponent;
            grpAnswer = GetChildAt(12) as GGroup;
            holder = GetChildAt(13) as GGraph;
        }
        public override void Dispose()
        {
            grpState = null;
            state = null;
            NormalBg = null;
            RightBg = null;
            FalseBg = null;
            SelectBg = null;
            RightNoFillBg = null;
            FalseNoFillBg = null;
            SelectNoFillBg = null;
            tfImageAnswer = null;
            tfAnswer = null;
            tfBoldAnswer = null;
            wave = null;
            grpAnswer = null;
            holder = null;

            base.Dispose();
        }
    }
}