/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class JumpTip : ExtendedComponent
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "JumpTip";
        public static string url => "ui://cmoz5osj11dduuvptdk";

        public GImage blurImage;
        public GTextField tfTitle;
        public GTextField tfContent;
        public GButton btnContinue;
        public GTextField tfGiveUp;
        public GGraph btnGiveUp;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(JumpTip));
        }

        public override void ConstructFromXML(XML xml)
        {
            blurImage = GetChildAt(0) as GImage;
            tfTitle = GetChildAt(3) as GTextField;
            tfContent = GetChildAt(4) as GTextField;
            btnContinue = GetChildAt(5) as GButton;
            tfGiveUp = GetChildAt(6) as GTextField;
            btnGiveUp = GetChildAt(7) as GGraph;

            OnConstructed();

            SetMultiLanguageInChildren();
        }
        public override void Dispose()
        {
            OnWillDispose();

            blurImage = null;
            tfTitle = null;
            tfContent = null;
            btnContinue = null;
            tfGiveUp = null;
            btnGiveUp = null;

            base.Dispose();
        }
        public void SetMultiLanguageInChildren()
        {
            this.blurImage.SetKey("BLUR");  // ""
            this.tfTitle.SetKey("jump_tip_title");  // "Take a test to unlock this seciton"
            this.btnContinue.SetKey("ui_main_path_test_start");  // ""
            this.tfGiveUp.SetKey("ui_main_path_no_thanks");  // "No Thanks"
        }
    }
}