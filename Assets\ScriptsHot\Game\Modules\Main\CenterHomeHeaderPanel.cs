// using FairyGUI;
// //using FairyGUtils;
// using System;
// using System.Collections.Generic;
// using UnityEngine;
// using ScriptsHot.Game.Modules.Profile;
// using ScriptsHot.Game.Modules.Shop;
//
// using DG.Tweening;
// using Cysharp.Threading.Tasks;
// using LitJson;
// using Modules.DataDot;
// using Msg.basic;
// using Msg.economic;
// using ScriptsHot.Game.Modules.FragmentPractice;
// using ScriptsHot.Game.Modules.MainPath;
// using ScriptsHot.Game.Modules.Sign;
// using ScriptsHot.Game.Modules.Stamina;
//
// namespace UIBind.Main
// {
//     public partial class CenterHomeHeaderPanel
//     {
//         private MainModel _mainModel => ModelManager.instance.GetModel<MainModel>(ModelConsts.Main);
//         private ProfileModel _profileModel => ModelManager.instance.GetModel<ProfileModel>(ModelConsts.Profile);
//         private CurrencyModel _currencyModel => ModelManager.instance.GetModel<CurrencyModel>(ModelConsts.CurrencyController);
//         // private LearnPathModel _learnPathModel => ModelManager.instance.GetModel<LearnPathModel>(ModelConsts.LearnPath);
//         
//         
//         private ShopModel _shopModel => ModelManager.instance.GetModel<ShopModel>(ModelConsts.Shop);
//         private SignModel _signModel => ModelManager.instance.GetModel<SignModel>(ModelConsts.Sign);
//         private StaminaModel _staminaModel => ModelManager.instance.GetModel<StaminaModel>(ModelConsts.Stamina);
//
//         private long cashedStaminaRecoverTimeStamp = -1;
//         private long lastCachedDate = -1;
//         private int cashedStamina = -1;
//         private bool _showStaminaPurchaseCom;
//         
//         //BI
//         private DotRefillBtnEnum _refillEnum;
//         private DotPracticeBtnEnum _practiceEnum;
//         
//         private const int FreeFragmentPracticeTimes_MAX = 1;
//         
//         public void OnInit()
//         {
//
//             //todo0  看看后面怎么调整 事件赋予的问题
//
//             //缺少BaseUI内层的AddUIEvent事件过滤能力，会导致无法处理 特定状态下禁止ui的点击操作  
//             //缺少BaseUI内层的AddUIEvent 连点类处理能力
//             this.headFrame.GetChild("btnHead").onClick.Add( OnBtnProfileClick);
//             // this.buttonSettings.onClick.Add(OnBtnSettingClick);
//             this.btnBenefits.com.onClick.Add(OnBtnBenefitsClick);
//             this.btnBG.onClick.Add(OnBtnBgClicked);
//             this.ReinitHeaderList();
//             Notifier.instance.RegisterNotification(NotifyConsts.OnIncentiveDataRefreshed, OnIncentiveDataRefreshed);
//             Notifier.instance.RegisterNotification(NotifyConsts.OnShowStaminaPopupEvent, OnShowStaminaPopup);
//             Notifier.instance.RegisterNotification(NotifyConsts.ChangeLanguage, OnChangeLanguage);
//             
//             //stamina
//             this.comStaminaPurchase.btnRefill.onClick.Add(OnBtnRefillClicked);
//             this.comStaminaPurchase.btnPractice.onClick.Add(OnBtnPracticeClicked);
//             this.comStaminaPurchase.btnSubscribe.onClick.Add(OnBtnSubscribeClicked);
//         }
//
//         public void Refresh(bool isRefreshHead=true,bool isRefreshCurrency=true ) {
//
//             if (isRefreshHead)
//             {
//                 TryRefreshHeadFrame();
//                 RefreshGrowthLevel();
//
//             }
//             if (isRefreshCurrency) {
//                 RefreshCurrency();
//             }
//
//             SetBtnBenefits();
//             if (_showStaminaPurchaseCom)
//                 RefreshStamina();
//         }
//
//         public void OnShow()
//         {
//             ResetStaminaPurchaseFlag();
//         }
//
//         #region 01 header frame
//
//         //todo 不应该调用ctrl
//         public void TryRefreshHeadFrame()
//         {
//             //Debug.Log("CHP TryRefresh");
//
//             if (string.IsNullOrEmpty(_mainModel.headerURL))
//                 ControllerManager.instance.GetController<LoginController>(ModelConsts.Login)
//                     .SendGetSelfHeaderURL(_mainModel.userID);
//             else
//                 RefreshHeadFrame();
//
//         }
//
//         private void RefreshHeadFrame() //RefreshHeadURL
//         {
//             if (this.com.visible)
//             {
//                 SetHeadFrameImg();
//                 SetHeaderFrameCtrl();
//             }
//         }
//
//         private void SetHeadFrameImg()
//         {
//             var loader = headFrame.GetChild("headLoader").asCom.GetChild("loader").asLoader;
//             if (_mainModel.avatarHeadType == PB_HeadItemType.HEAD_ITEM_TYPE_MATERIA)
//             {
//                 loader.size = new Vector2(128, 128);
//                 if (Cfg.T.TBItemTable.DataMap.ContainsKey(_mainModel.avatarHeadId))
//                 {
//                     var cfg = Cfg.T.TBItemTable.Get(_mainModel.avatarHeadId);
//                     loader.url = cfg.iconNormalizePath;
//                 }
//             }
//             else if (_mainModel.avatarHeadType == PB_HeadItemType.HEAD_ITEM_TYPE_AVATAR)
//             {
//                 loader.size = new Vector2(160, 160);
//                 if (Cfg.T.TBRoleInfo.DataMap.ContainsKey(_mainModel.avatarHeadId.ToString()))
//                 {
//                     var cfg = Cfg.T.TBRoleInfo.Get(_mainModel.avatarHeadId.ToString());
//                     loader.url = cfg.headUrl;
//                 }
//             }
//             else
//             {
//                 loader.size = new Vector2(160, 160);
//                 VFDebug.Log("【首页头像】 出现未知类型");
//                 loader.url = _mainModel.headerURL;
//             }
//         }
//
//         private void SetHeaderFrameCtrl() {
//             var id = _mainModel.incentiveData?.user_data?.user_dress_up_data?.frame_id;
//             if (string.IsNullOrEmpty(id))
//             {
//                 id = _profileModel.defaultFrameId;
//             }
//
//             var ctrl = headFrame.GetController("effectCtrl");
//             if (!string.IsNullOrEmpty(id) && Cfg.T.TBItemTable.DataMap.ContainsKey(long.Parse(id)))
//             {
//                 var cfg = Cfg.T.TBItemTable.Get(long.Parse(id));
//                 var frameLoader = headFrame.GetChild("Frame").asLoader;
//                 frameLoader.url = cfg.iconNormalizePath;
//                 var dFrame = headFrame.GetChild("dFrame").asImage;
//
//                 if (id == _profileModel.defaultFrameId)
//                 {
//                     frameLoader.visible = false;
//                     dFrame.visible = true;
//                 }
//                 else
//                 {
//                     dFrame.visible = false;
//                     frameLoader.visible = true;
//                 }
//
//                 if (cfg.effectPath.Count > 0)
//                 {
//                     var spineLoader = headFrame.GetChild("effect").asLoader3D;
//                     spineLoader.url = cfg.iconNormalizePath;
//                     spineLoader.animationName = cfg.effectPath[0];
//                     spineLoader.loop = true;
//                 }
//                 ctrl.selectedIndex = cfg.effectPath.Count > 0 ? 1 : 0;
//             }
//             else
//             {
//                 ctrl.selectedIndex = 0;
//             }
//         }
//         #endregion
//
//         #region 02 header frame - growth
//
//         public void RefreshGrowthLevel()
//         {
//             long level = 0;
//             if (_mainModel.incentiveData != null && _mainModel.incentiveData.growth_data != null)
//             {
//                 level = _mainModel.incentiveData.growth_data.growth_level;
//             }
//
//             //level += 1;
//             levelCom.asCom.GetChild("levelText").asTextField.text = level.ToString();
//         }
//
//         #endregion
//
//         #region 03 HeaderList :  currecy+stamina 赋值部分
//         private enum MainHeaderItemType
//         {
//             Diamond = 0,
//             Gold = 1,
//             Stamina100 = 2,
//             Stamina50,
//             Stamina0,
//             EnergyUnlimited,
//             Fire,
//             NoFire,
//         }
//         
//         private enum MainHeaderBenefitsType
//         {
//             Normal = 0,
//             Premium = 1,
//             Premium_Break = 2,
//         }
//
//         public void SetGold()
//         {
//             var postCurNum = _currencyModel.GetEconomicInfo(EconomicType.Gold, GameEventName.GameChatPost).CurNum;
//             SetHeaderbarItemValue(MainHeaderItemType.Gold, postCurNum);
//         }
//
//         public void SetBtnBenefits()
//         {
//             if (_mainModel.incentiveData == null)
//                 return;
//             if (_mainModel.incentiveData.homepage_economic_info == null)
//                 return;
//             if (_mainModel.incentiveData.homepage_economic_info.member_info == null)
//                 return;
//             
//             var type = MainHeaderBenefitsType.Normal;
//             if (_mainModel.incentiveData.homepage_economic_info.member_info.is_member)
//             {
//                 switch (_mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus)
//                 {
//                     case SubscribeStatus.Canceled:
//                         type = MainHeaderBenefitsType.Premium_Break;
//                         break;
//                     case SubscribeStatus.Expired:
//                         type = MainHeaderBenefitsType.Premium;
//                         break;
//                     case SubscribeStatus.Subscribing:
//                         type = MainHeaderBenefitsType.Premium;
//                         break;
//                     case SubscribeStatus.UnSubscribe:
//                         type = MainHeaderBenefitsType.Normal;
//                         break;
//                     case SubscribeStatus.Pending:
//                         type = MainHeaderBenefitsType.Normal;
//                         break;
//                     case SubscribeStatus.Refund:
//                         type = MainHeaderBenefitsType.Premium;
//                         break;
//                     case SubscribeStatus.Retrying:
//                         type = MainHeaderBenefitsType.Normal;
//                         break;
//                     case SubscribeStatus.Suspend:
//                         type = MainHeaderBenefitsType.Premium_Break;
//                         break;
//                     case SubscribeStatus.FreeTrial:
//                         type = MainHeaderBenefitsType.Premium;
//                         break;
//                     case SubscribeStatus.FreeTrialCanceled:
//                         type = MainHeaderBenefitsType.Premium_Break;
//                         break;
//                 } 
//             }
//             // 暂时先不隐藏
//             // SetHeaderBarItemVisible(MainHeaderItemType.Stamina100, type == MainHeaderBenefitsType.Normal);
//
//             if (btnBenefits == null || btnBenefits.sp == null || btnBenefits.state == null)
//             {
//                 return;
//             }
//             btnBenefits.sp.loop = type != MainHeaderBenefitsType.Normal;
//             btnBenefits.state.selectedIndex = (int)type; 
//         }
//
//
//         //UpdateStaminaItem();
//         public void UpdateStaminaItem()
//         {
//             return;
//             if (!this.com.visible) return;
//             var item = GetHeaderItemWithType(MainHeaderItemType.Stamina100);
//             if (_mainModel.incentiveData != null && _mainModel.incentiveData.homepage_economic_info != null &&
//                 _mainModel.incentiveData.homepage_economic_info.member_info.is_member)
//             {
//                 var state = (int)MainHeaderItemType.EnergyUnlimited;
//                 if (item.asCom.GetControllerAt(0).selectedIndex != state)
//                 {
//                     item.asCom.GetControllerAt(0).selectedIndex = state;
//                 }
//                 if (_shopModel.shopUIStyle == ShopModel.ShopUIStyle.Speak)
//                 {
//                     item.GetChild("icon").asLoader.url = "ui://common/item_12009";
//                 }
//             }
//             else if (_mainModel.incentiveData != null && _mainModel.incentiveData.stamina_data.is_infinite_stamina && TimeExt.serverTimestamp < _mainModel.incentiveData.stamina_data.infinite_stamina_end_time)
//             {
//                 if (item != null)
//                 {
//                     var timeSpan = DateTimeOffset.FromUnixTimeSeconds(_mainModel.incentiveData.stamina_data.infinite_stamina_end_time / 1000).DateTime -
//                                    TimeExt.serverTime;
//                     item.asButton.title = timeSpan.ToString().Substring(3);
//                     var state = (int)MainHeaderItemType.EnergyUnlimited;
//                     if (item.asCom.GetControllerAt(0).selectedIndex != state)
//                     {
//                         item.asCom.GetControllerAt(0).selectedIndex = state;
//                     }
//
//                     if (_shopModel.shopUIStyle == ShopModel.ShopUIStyle.Speak)
//                     {
//                         item.GetChild("icon").asLoader.url = "ui://common/item_12009";
//                     }
//                 }
//             }
//             else
//             {
//                 if (item.asCom.GetControllerAt(0).selectedIndex == (int)MainHeaderItemType.EnergyUnlimited || item.asCom.GetControllerAt(0).selectedIndex == (int)MainHeaderItemType.Stamina50)
//                 {
//                     RefreshCurrency();
//                 }
//             }
//         }
//
//         /// <summary>
//         /// 【新首页】根据物品类型获取顶部按钮
//         /// </summary>
//         /// <param name="type"></param>
//         /// <returns></returns>
//         private GComponent GetHeaderItemWithType(MainHeaderItemType type)
//         {
//             for (int i = 0; i < headerList.numItems; i++)
//             {
//                 var item = headerList.GetChildAt(i).asCom;
//                 if (item.data is MainHeaderItemType && (MainHeaderItemType)item.data == type)
//                 {
//                     return item;
//                 }
//             }
//             return null;
//         }
//
//         public void RefreshCurrency()
//         {
//             //Debug.Log("CHP RefreshCurrency");
//             if (_mainModel.incentiveData?.stamina_data != null)
//             {
//                 var item = GetHeaderItemWithType(MainHeaderItemType.Stamina100);
//                 if (item != null)
//                 {
//                     var state = MainHeaderItemType.Stamina100;
//                     if (_mainModel.incentiveData.stamina_data.stamina < _staminaModel.maxStamina)
//                     {
//                         state = MainHeaderItemType.Stamina50;
//                     }
//
//                     //state = MainHeaderItemType.EnergyUnlimited;
//
//                     item.asCom.GetControllerAt(0).selectedIndex = (int)state;
//                 }
//             }
//
//             if (_currencyModel.GetEconomicInfo(EconomicType.Diamond).ValidTime == -1)
//             {
//                 SetHeaderbarItemValue(MainHeaderItemType.Diamond, 0, "-");
//             }
//             else
//             {
//                 SetHeaderbarItemValue(MainHeaderItemType.Diamond, _currencyModel.GetEconomicInfo(EconomicType.Diamond).CurNum);
//             }
//
//             if (_mainModel.incentiveData?.stamina_data != null)
//             {
//                 var maxStamina = (int)(GameEntry.LoginC.GetModel<StaminaModel>(ModelConsts.Stamina).maxStamina / 20);
//                 var stamina = (int)(_mainModel.incentiveData.stamina_data.stamina / 20);
//                 stamina = cashedStamina > 0 ? cashedStamina : stamina;
//                 if (_mainModel.IsUnlimitedStamina())
//                 {
//                     SetStaminaItemIconAndValue(true, stamina , string.Empty);
//                 }
//                 else
//                 {
//                     SetStaminaItemIconAndValue(false, stamina);
//                 }
//                 // else if (stamina >= maxStamina)
//                 // {
//                 //     SetHeaderbarItemValue(MainHeaderItemType.Stamina100, stamina, I18N.inst.MoStr("ui_mainHeader_stamina_full"));
//                 // }
//                 // else
//                 // {
//                 //     var next_recover_timestamp = cashedStaminaRecoverTimeStamp > 0
//                 //         ? cashedStaminaRecoverTimeStamp
//                 //         : _mainModel.incentiveData.stamina_data.next_recover_timestamp;
//                 //     var timeSpan = TimeSpan.FromMilliseconds(next_recover_timestamp - TimeExt.serverTimestamp);
//                 //     if (timeSpan.TotalMilliseconds < maxStamina  && cashedStaminaRecoverTimeStamp < 0 && (TimeExt.serverTimestamp - lastCachedDate) > 2000)
//                 //     {
//                 //         //此功能为了让体力恢复时过渡得更丝滑，加了伪造数据。当服务器返回刷新后清空伪造数据用
//                 //         cashedStaminaRecoverTimeStamp = next_recover_timestamp + _mainModel.incentiveData.stamina_data.recover_interval * 1000 * 20 + (long)timeSpan.TotalMilliseconds;
//                 //         cashedStamina = stamina + 1;
//                 //         lastCachedDate = TimeExt.serverTimestamp;
//                 //         GameEntry.LoginC.GetController<MainController>(ModelConsts.Main).ReqGetIncentiveData();
//                 //     }
//                 //     SetHeaderbarItemValue(MainHeaderItemType.Stamina100, stamina, timeSpan.ToString(@"hh\:mm\:ss"));
//                 // }
//             }
//             else
//             {
//                 SetHeaderbarItemValue(MainHeaderItemType.Stamina100, 0, "-");
//                 // SetHeaderbarItemValue(MainHeaderItemType.Stamina100, 0, "-");
//                 SetStaminaItemIconAndValue(false, 0, "-");
//             }
//         }
//
//         /// <summary>
//         /// 订阅无限体力，图标变
//         /// </summary>
//         /// <param name="isMember"></param> 订阅
//         /// <param name="stamina"></param> 体力值
//         private void SetStaminaItemIconAndValue(bool isMember, int stamina = 0 , string value = "")
//         {
//             if (string.IsNullOrEmpty(value))
//             {
//                 value = stamina.ToString();
//             }
//             if (isMember)
//             {
//                 SetHeaderbarItemValue(MainHeaderItemType.Stamina100, stamina, string.Empty);
//                 var item = GetHeaderItemWithType(MainHeaderItemType.Stamina100);
//                 var state = (int)MainHeaderItemType.EnergyUnlimited;
//                 if (item.asCom.GetControllerAt(0).selectedIndex != state)
//                 {
//                     item.asCom.GetControllerAt(0).selectedIndex = state;
//                 }
//             }
//             else
//             {
//                 SetHeaderbarItemValue(MainHeaderItemType.Stamina100, stamina, value);
//                 var item = GetHeaderItemWithType(MainHeaderItemType.Stamina100);
//                 var state = (int)MainHeaderItemType.Stamina0;
//                 if (item.asCom.GetControllerAt(0).selectedIndex != state)
//                 {
//                     item.asCom.GetControllerAt(0).selectedIndex = state;
//                 }
//             }
//
//         }
//
//         /// <summary>
//         /// 设置Headerbar显示值。若overrideTitle不为空，则使用overrideTitle，否则使用numberValue. 当使用overrideTitle时，useAnimation无效.
//         /// </summary>
//         /// <param name="type">物品类型</param>
//         /// <param name="numberValue">数值</param>
//         /// <param name="overrideTitle">自定义标题，可选</param>
//         /// <param name="useAnimation">是否使用动画效果，默认为true</param>
//         private void SetHeaderbarItemValue(MainHeaderItemType type, long numberValue, string overrideTitle = null, bool useAnimation = true)
//         {
//             var item = GetHeaderItemWithType(type);
//             //Debug.Log("setHeader type="+type.ToString());
//
//             if (item != null)
//             {
//                 if (overrideTitle != null)
//                 {
//                     if (type == MainHeaderItemType.Stamina50 || type == MainHeaderItemType.Stamina100)
//                     {
//                         var cmp = item.GetChild("tfCnt").asTextField;
//                         cmp.text = numberValue.ToString();
//                         item.asButton.title = overrideTitle;
//                     }
//                     else
//                         item.asButton.title = overrideTitle;
//                 }
//                 else
//                 {
//                     long startValue = 0;
//                     var content = item.asButton.title.Trim();
//                     if (content == "999+")
//                     {
//                         startValue = 999L;
//                     }
//                     else if (long.TryParse(content, out startValue))
//                     {
//                     }
//                     else
//                     {
//                         startValue = 0L;
//                     }
//
//                     if (useAnimation)
//                     {
//                         if (type == MainHeaderItemType.Stamina100 && numberValue > 999)
//                         {
//                             if (startValue >= 999)
//                             {
//                                 item.asButton.title = "999+";
//                             }
//                             else
//                             {
//                                 var cmp = item.GetChild("tfCnt").asTextField;
//                                 DOTween.To(() => startValue, (x) =>
//                                 {
//                                     cmp.text = x.ToString();
//                                     startValue = x;
//                                 }, 999, 0.2f).SetEase(Ease.Linear).OnComplete(() =>
//                                 {
//                                     cmp.text = numberValue.ToString();
//                                 });
//                             }
//                         }
//                         else if (type == MainHeaderItemType.Stamina50 || type == MainHeaderItemType.Stamina100)
//                         {
//                             var cmp = item.GetChild("tfCnt").asTextField;
//                             cmp.text = numberValue.ToString();
//                         }
//                         else
//                         {
//                             if (startValue != numberValue)
//                             {
//                                 DOTween.To(() => startValue, (x) =>
//                                 {
//                                     item.asButton.title = x.ToString();
//                                     startValue = x;
//                                 }, numberValue, 0.2f).SetEase(Ease.Linear);
//                             }
//                             else
//                             {
//                                 item.asButton.title = numberValue.ToString();
//                             }
//                         }
//
//                     }
//                     else
//                     {
//                         if (type == MainHeaderItemType.Stamina100 && numberValue > 999)
//                         {
//                             item.asButton.title = "999+";
//                         }
//                         else if (type == MainHeaderItemType.Stamina50 || type == MainHeaderItemType.Stamina100)
//                         {
//                             var cmp = item.GetChild("tfCnt").asTextField;
//                             cmp.text = numberValue.ToString();
//                         }
//                         else
//                         {
//                             item.asButton.title = numberValue.ToString();
//                         }
//                     }
//                 }
//             }
//         }
//         
//         private void SetHeaderBarItemVisible(MainHeaderItemType type, bool visible)
//         {
//             GetHeaderItemWithType(type).visible = visible;
//         }
//
//         #endregion
//         
//         #region 03  点击事件部分
//
//         private GObject itemFire;
//         public void ReinitHeaderList()
//         {
//             //Debug.Log("Reinit header");
//
//             headerList.RemoveChildrenToPool();
//             //fire
//             itemFire = headerList.AddItemFromPool();
//             itemFire.asCom.GetControllerAt(0).selectedIndex = (int)MainHeaderItemType.NoFire;
//             itemFire.asCom.data = MainHeaderItemType.Fire;
//             itemFire.asButton.title = "0";
//             itemFire.asButton.onClick.Add(EnterFire);
//             SetHeaderbarItemValue(MainHeaderItemType.Fire, 0);
//             RefreshFire();
//             
//             //Diamond
//             var itemDiamond = headerList.AddItemFromPool();
//             itemDiamond.asCom.GetControllerAt(0).selectedIndex = (int)MainHeaderItemType.Diamond;
//             itemDiamond.asCom.data = MainHeaderItemType.Diamond;
//             itemDiamond.asButton.title = "0";
//             itemDiamond.asButton.onClick.Add(EnterShop);
//             
//             //Stamina
//             var itemStamina = headerList.AddItemFromPool();
//             itemStamina.asCom.GetControllerAt(0).selectedIndex = (int)MainHeaderItemType.Stamina100;
//             itemStamina.asCom.data = MainHeaderItemType.Stamina100;
//             itemStamina.asButton.title = "0";
//
//             itemStamina.asButton.onClick.Add(OnBtnStaminaClick);
//             //AddUIEvent(itemStamina.asButton.onClick, OnBtnStaminaClick);
//         }
//
//         public async void RefreshFire()
//         {
//             await GameEntry.SignC.TryRefreshSignAndStreak();
//             if (GameEntry.SignC.SignModel.signSummary.finish_checkin)
//             {
//                 itemFire.asCom.GetControllerAt(0).selectedIndex = (int)MainHeaderItemType.Fire;
//             }
//             else
//             {
//                 itemFire.asCom.GetControllerAt(0).selectedIndex = (int)MainHeaderItemType.NoFire;
//             }
//             SetHeaderbarItemValue(MainHeaderItemType.Fire, GameEntry.SignC.SignModel.GetContinueDays());
//         }
//
//         //itemDiamond click
//         private void EnterShop()
//         {
//             VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
//
//             // var dot = new HomepageUIDataDot(HomepageUIDataDot.HPEventType.Click_Home_page_diamond, _learnPathModel.validNextTaskItemInfoCache);
//             // DataDotMgr.Collect(dot);
//             var dot = new DotClickDiamondIcon();
//             DataDotMgr.Collect(dot);
//
//             ControllerManager.instance.GetController<ShopController>(ModelConsts.Shop).EnterShop();
//
//
//         }
//
//         private void EnterFire()
//         {
//             DotClickFireIcon dot = new DotClickFireIcon();
//             dot.fire_count = _signModel.signInfo?.continue_days ?? 0;
//             DataDotMgr.Collect(dot);
//             
//             VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
//             GameEntry.SignC.EnterSign();
//         }
//
//         public void ResetStaminaPurchaseFlag()
//         {
//             StaminaInit.Play();
//             _showStaminaPurchaseCom = false;
//         }
//         
//         private void OnBtnStaminaClick()
//         {
//             VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
//             var dot = new DotClickHeartIcon();
//             
//             if (!_showStaminaPurchaseCom)
//             {
//                 RefreshStamina();
//                 StaminaCutIn.Play(()=>comStaminaPurchase.com.InvalidateBatchingState());
//             }
//             else
//             {
//                 StaminaCutOut.Play(()=>comStaminaPurchase.com.InvalidateBatchingState());
//             }
//             
//             // 这一整段都是为了埋点
//             if (_mainModel.IsMember())
//             {
//                 dot.heart_status = DotHeartStatusEnum.unlimited_heart.ToString("");
//                 DataDotMgr.Collect(dot);
//                 if (!_showStaminaPurchaseCom)
//                 {
//                     var dot2 = new DotAppearUnlimitedHeartPage();
//                     dot2.heart_status = DotHeartStatusEnum.unlimited_heart.ToString("");
//                 }
//             }
//             else if (_mainModel.IsDuringUnlimitedStaminaItem())
//             {
//                 dot.heart_status = DotHeartStatusEnum.unlimited_hearts_tool.ToString("");
//                 DataDotMgr.Collect(dot);
//                 if (!_showStaminaPurchaseCom)
//                 {
//                     var dot2 = new DotAppearUnlimitedHeartToolPage();
//                     var timeSpan = DateTimeOffset.FromUnixTimeSeconds(_mainModel.incentiveData.stamina_data.infinite_stamina_end_time / 1000).DateTime -
//                                    TimeExt.serverTime;
//                     dot2.time_left = timeSpan.TotalMinutes;
//                     DataDotMgr.Collect(dot2);
//                 }
//             }
//             else if (_mainModel.incentiveData.stamina_data.stamina >= 100)
//             {
//                 dot.heart_status = DotHeartStatusEnum.full_hearts.ToString("");
//                 DataDotMgr.Collect(dot);
//                 if (!_showStaminaPurchaseCom)
//                 {
//                     var dot2 = new DotAppearFullHeartsPage();
//                     DataDotMgr.Collect(dot2);
//                 }  
//             }
//             else
//             {
//                 dot.heart_status = DotHeartStatusEnum.next_heart.ToString("");
//                 DataDotMgr.Collect(dot);
//                 if (!_showStaminaPurchaseCom)
//                 {
//                     var _staminaModel = GameEntry.LoginC.GetModel<StaminaModel>(ModelConsts.Stamina);
//                     var isEnough = GameEntry.LoginC.GetController<CurrencyController>(ModelConsts.CurrencyController).IsEnough((int)_staminaModel.staminaData.cost_per_purchase);
//                     _refillEnum = comStaminaPurchase.state.selectedIndex == 0
//                         ? isEnough ? DotRefillBtnEnum.diamond_enough : DotRefillBtnEnum.diamond_not_enough
//                         : DotRefillBtnEnum.button_unavailable;
//                     _practiceEnum = comStaminaPurchase.state.selectedIndex == 0
//                         ? _mainModel.incentiveData.stamina_data.remain_stamina_task_cnt > 0 ? DotPracticeBtnEnum.pracitce_chance_1 : DotPracticeBtnEnum.pracitce_chance_0 : DotPracticeBtnEnum.button_unavailable;
//
//                     var dot2 = new DotAppearNextHeartPage();
//                     dot2.heart_left = (int)(_mainModel.incentiveData.stamina_data.stamina / 20);
//                     var next_recover_timestamp = cashedStaminaRecoverTimeStamp > 0
//                         ? cashedStaminaRecoverTimeStamp
//                         : _mainModel.incentiveData.stamina_data.next_recover_timestamp;
//                     var timeSpan = TimeSpan.FromMilliseconds(next_recover_timestamp - TimeExt.serverTimestamp);
//                     dot2.next_heart_time_left = timeSpan.TotalMinutes;
//                     dot2.refill_hearts_button_status = _refillEnum.ToString();
//                     dot2.practice_to_earn_hearts_button_status = _practiceEnum.ToString();
//                     DataDotMgr.Collect(dot2);
//                 }  
//             }
//
//             _showStaminaPurchaseCom = !_showStaminaPurchaseCom;
//         }
//         
//
//         private void OnBtnBgClicked()
//         {
//             comStaminaPurchase.bg.sortingOrder = 0;
//             StaminaCutOut.Play();
//             _showStaminaPurchaseCom = false;
//         }
//
//
//         private void OnBtnProfileClick()
//         {
//             VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
//
//             // var dot = new HomepageUIDataDot(HomepageUIDataDot.HPEventType.Click_Home_page_profile, _learnPathModel.validNextTaskItemInfoCache);
//             // DataDotMgr.Collect(dot);
//
//             var profileCtrl = ControllerManager.instance.GetController<ProfileController>(ModelConsts.Profile);
//
//             profileCtrl.OnClickSelf();
//             
//     
//         }
//
//         private void OnBtnSettingClick()
//         {
//             VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
//             var userBaseInfo = new UserBaseInfo
//             {
//                 user_id = ModelManager.instance.GetModel<MainModel>(ModelConsts.Main).userID,
//                 learn_purpose = "",
//                 lang_level = "",
//                 favourite_topic = "",
//                 email = ""
//             };
//
//             UIManager.instance.GetUI<ProfileSettingUI>(UIConsts.ProfileSettingUI).Show().onCompleted += () =>
//             {
//                 UIManager.instance.GetUI<ProfileSettingUI>(UIConsts.ProfileSettingUI).RefreshEmail();
//             };
//
//             //这里没有区分setting多个state下不同的语意信息
//             // var dot = new HomepageUIDataDot(HomepageUIDataDot.HPEventType.Click_Home_page_setting, _learnPathModel.validNextTaskItemInfoCache);
//             // DataDotMgr.Collect(dot);
//
//             UniTask.Void(async () =>
//             {
//                 var result = await GHttpManager.instance.PostAsync(AppConst.LoginSrv + "/account/getuserbaseinfo", JsonMapper.ToJson(userBaseInfo));
//                 if (result.code == 200)
//                 {
//                     var msg = JsonMapper.ToObject<UserBaseInfo>(result.data);
//                     ModelManager.instance.GetModel<ProfileModel>(ModelConsts.Profile).email = msg.email;
//                     var settingUI = UIManager.instance.GetUI<ProfileSettingUI>(UIConsts.ProfileSettingUI);
//                     if(settingUI.isShow)
//                         settingUI.RefreshEmail();
//                 }
//             });
//         }
//
//         private void OnBtnBenefitsClick()
//         {
//             VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
//             
//             PayWallDotHelper.LastSourcePage = PaywallEntrySourceType.membership_icon;
//             var dot = new DotClickMembershipIcon();
//             
//             dot.member_status = btnBenefits.state.selectedIndex == 1 ? DotMemberStatusEnum.member.ToString() :
//                 btnBenefits.state.selectedIndex == 2 ? DotMemberStatusEnum.unrenewed_member.ToString() : DotMemberStatusEnum.non_member.ToString();
//             DataDotMgr.Collect(dot);
//             
//             GameEntry.LoginC.GetUI(UIConsts.SpeakPlanPromotionStep1UI).Show();
//         }
//
//         private void OnIncentiveDataRefreshed(string name, object body)
//         {
//             cashedStaminaRecoverTimeStamp = -1;
//             cashedStamina = -1;
//         }
//
//         private void OnChangeLanguage(string name, object body)
//         {
//             RefreshCurrency();
//         }
//
//         private void OnShowStaminaPopup(string name, object body)
//         {
//             OnBtnStaminaClick();
//         }
//         
//         private void OnBtnPracticeClicked()
//         {
//             var dot = new DotClickPracticeToEarnHearts();
//             dot.refill_hearts_button_status = _refillEnum.ToString();
//             DataDotMgr.Collect(dot);
//             
//             ResetStaminaPurchaseFlag();
//             long courseId = ControllerManager.instance.GetController<MainPathController>(ModelConsts.MainPath).Model.CourseId;
//             ControllerManager.instance.GetController<FragmentPracticeController>(ModelConsts.FragmentPractice)
//                 .EnterPractice(courseId, PB_CourseTypeEnum.CTNone, 0, 0, 0, 0, 0, PB_LevelTypeEnum.LTNone, 0, 0, false,
//                     PB_DialogSourceEnum.DialogSourceMenuBarForStamina);
//         }
//
//         private void OnBtnSubscribeClicked()
//         {
//             var _staminaModel = GameEntry.LoginC.GetModel<StaminaModel>(ModelConsts.Stamina);
//             var isEnough = GameEntry.LoginC.GetController<CurrencyController>(ModelConsts.CurrencyController).IsEnough((int)_staminaModel.staminaData.cost_per_purchase);
//
//             var dot = new DotClickUnlimitedHearts();
//             dot.refill_hearts_button_status = _refillEnum.ToString();
//             dot.practice_to_earn_hearts_button_status = _practiceEnum.ToString();
//             DataDotMgr.Collect(dot);
//             
//             PayWallDotHelper.LastSourcePage = PaywallEntrySourceType.heart_icon;
//             
//             ResetStaminaPurchaseFlag();
//             GameEntry.LoginC.GetUI(UIConsts.SpeakPlanPromotionStep1UI).Show();
//         }
//         
//         private void OnBtnRefillClicked()
//         {
//             var dot = new DotClickRefillHearts();
//             dot.practice_to_earn_hearts_button_status = _practiceEnum.ToString();
//             DataDotMgr.Collect(dot);
//             var _staminaModel = GameEntry.LoginC.GetModel<StaminaModel>(ModelConsts.Stamina);
//             if (!GameEntry.LoginC.GetController<CurrencyController>(ModelConsts.CurrencyController).IsEnough((int)_staminaModel.staminaData.cost_per_purchase))
//             {
//                 GameEntry.LoginC.GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N("common_lack_diamond",
//                     GameEntry.LoginC.GetController<ShopController>(ModelConsts.Shop).EnterShop, null, 2, "ui_profile_confirm", "ui_profile_cancel", false, 1);
//                 return;
//             }
//             GameEntry.LoginC.GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N("ui_stamina_confirm_desc",
//                 GameEntry.LoginC.GetController<StaminaController>(ModelConsts.Stamina).PurchaseStamina, null,2,"ui_profile_confirm", "ui_profile_cancel", false, 2, 
//                 contentArgs:new string[]{_staminaModel.staminaData.cost_per_purchase.ToString(), "1"}
//             );
//         }
//
//         #endregion
//
//         #region 04 体力下拉相关
//         public void RefreshStamina()
//         {
//             if (_mainModel.IsUnlimitedStamina())
//             {
//                 SetAsUnlimited();
//             }
//             else
//             {
//                 SetAsNormal();
//                 if (_staminaModel.staminaData != null)
//                 {
//                     var stamina = (int)Math.Floor((decimal)(_mainModel.incentiveData?.stamina_data.stamina / 20));
//
//                     DotAppearHeartPage appearDot = new DotAppearHeartPage();
//                     appearDot.heart_count = stamina;
//                     DataDotMgr.Collect(appearDot);
//                 }
//             }
//
//             comStaminaPurchase.discount.selectedIndex = 0;
//             if (GameEntry.ShopC.ShopModel.IsShowDiscountNode())
//             {
//                 comStaminaPurchase.discount.selectedIndex = 1;
//                 ShopUIUtil.UpdateDiscountNode(comStaminaPurchase.discountNode , GameEntry.ShopC.ShopModel);
//             }
//         }
//
//         private void SetAsUnlimited()
//         {
//             comStaminaPurchase.state.SetSelectedPage("unlimited");
//             if (_mainModel.incentiveData.homepage_economic_info.member_info.is_member)
//             {
//                 comStaminaPurchase.tfDescUnlimited.SetKey("ui_staminaPurchase_unlimited_heart");
//             }
//             else
//             {
//                 var timeSpan = DateTimeOffset.FromUnixTimeMilliseconds (_mainModel.incentiveData.stamina_data.infinite_stamina_end_time).DateTime - TimeExt.serverTime;
//                 comStaminaPurchase.tfDescUnlimited.SetKeyArgs("item_obtained_desc_i0003", timeSpan.Minutes);
//             }
//             if (_shopModel.shopUIStyle == ShopModel.ShopUIStyle.Speak)
//             {
//                 comStaminaPurchase.loaderUnlimitedStaminaIcon.url = "ui://common/item_big_i0005";
//             }
//         }
//
//         private void SetAsNormal()
//         {
//            
//             //ui.tfBtnPractice.SetKeyArgs("ui_stamina_common_btn_practice");
//             comStaminaPurchase.tfBtnPractice.SetKeyArgs("ui_stamina_common_btn_practice", _mainModel.incentiveData.stamina_data.remain_stamina_task_cnt,
//                 _mainModel.incentiveData.stamina_data.max_stamina_task_cnt);
//             comStaminaPurchase.tfBtnRefill.SetKey("ui_stamina_common_btn_refill");
//             comStaminaPurchase.tfBtnSubscribe.SetKey("ui_stamina_purchase_btn_subscribe");
//             comStaminaPurchase.tfGetPremium.SetKey("ui_stamina_purchase_btn_subscribe_desc");
//
//             int complateFreeTimes = FreeFragmentPracticeTimes_MAX -
//                                     (int)_mainModel.incentiveData.stamina_data.remain_stamina_task_cnt;
//             complateFreeTimes = Math.Min(1,complateFreeTimes);
//             complateFreeTimes = Math.Max(0,complateFreeTimes);
//             if (_mainModel.incentiveData.stamina_data.remain_stamina_task_cnt <= 0)
//             {
//                 comStaminaPurchase.state.SetSelectedPage("used_up");
//             }
//             else
//             {
//                 comStaminaPurchase.state.SetSelectedPage("normal");
//             }
//             comStaminaPurchase.tfTimes.text = $"{complateFreeTimes}/{FreeFragmentPracticeTimes_MAX}";
//             
//             if (_staminaModel.staminaData != null)
//             {
//                 var stamina = (int)Math.Floor((decimal)(_mainModel.incentiveData.stamina_data.stamina / 20));
//                 GameEntry.LoginC.GetController<StaminaController>(ModelConsts.Stamina).DrawHearts(comStaminaPurchase.comHeart, stamina);
//                 if (_staminaModel.staminaData.stamina >= _staminaModel.maxStamina)
//                 {
//                     comStaminaPurchase.tfDesc.SetKey("ui_stamina_purchase_unlimited_heart_full_desc");
//                     comStaminaPurchase.state.SetSelectedPage("full");
//                 }
//                 else
//                 {
//                     var timeSpan = DateTimeOffset.FromUnixTimeMilliseconds(_mainModel.incentiveData.stamina_data.next_recover_timestamp).DateTime -
//                                    TimeExt.serverTime;
//                     // 250514 体验优化 分向上取整
//                     int showMin = Math.Max(timeSpan.Minutes + 1, 1);
//                     comStaminaPurchase.tfDesc.SetKeyArgs("ui_stamina_purchase_unlimited_heart_desc", timeSpan.Hours,showMin);
//                 }
//
//                 comStaminaPurchase.tfBtnRefillCost.text = _staminaModel.staminaData.cost_per_purchase.ToString();
//             }
//             // 250327 中国版过审修改
//             if (AppConst.CN_Shop_IsClose)
//             {
//                 comStaminaPurchase.tfGetPremium.visible = false;
//             }
//         }
//         
//         #endregion
//     }
// }