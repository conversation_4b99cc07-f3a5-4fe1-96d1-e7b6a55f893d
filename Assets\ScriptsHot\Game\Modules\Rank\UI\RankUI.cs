﻿using System;
using System.Collections.Generic;
using System.Linq;
using CommonUI;
using FairyGUI;
using Google.Protobuf.Collections;
using Msg.incentive;
using ScriptsHot.Game.Modules.Guide;
using ScriptsHot.Game.Modules.Profile;
using ScriptsHot.Game.Modules.Rank.UI;
using ScriptsHot.Game.Modules.Settlement;
using Spine.Unity;
using UIBind.Rank;
using UnityEngine;
using ColorUtility = UnityEngine.ColorUtility;

[TransitionUI(CutInEffect.LeftToRight)]
public class RankUI : RankUIBase<UIBind.Rank.RankPanel>, IBaseUIUpdate
{
    public override void OnBackBtnClick()
    {
    }
    
    private string timeLimit;
    public RankUI(string name) : base(name) { }

    public override string uiLayer => UILayerConsts.Home; //主UI层
    protected override bool isFullScreen => true;
    private RankModel _rankModel => GetModel<RankModel>(ModelConsts.Rank);
    private RankController _rankController => GetController<RankController>(ModelConsts.Rank);
    private bool _isRewardReceived;
    private bool _isPlayedResultSound; //app生命周期只播放一次上周排行结果音效
        
    private enum RankUIPage
    {
        Rank = 0,
        RankResult,
        RankReward,
        RankUnlock,
    }
    
    protected override void OnInit(GComponent uiCom)
    {
        InitRankUIBase(ui.comRankContent);
        AddUIEvent(this.ui.comRankResult.btnConfirm.onClick, OnRankResultBtnConfirmClicked);
        AddUIEvent(this.ui.comRankReward.btnConfirm.onClick, OnRankRewardBtnConfirmClicked);
        AddUIEvent(this.ui.comRankReward.imgClose.onClick, OnRankRewardBtnImgCloseClicked);
        AddUIEvent(this.ui.comRankUnlock.btnLock.onClick, OnRankUnlockBtnLockClicked);//btnLock是一个btn组件名
    }

    public void Update(int interval)
    {
        if (this.ui!=null && this.isInit && this.isShow) {
            base.Update(interval);
        }
        
    }

    protected override void OnShow()
    {
        base.OnShow();
    }

    public void RefreshUI()
    {
        if (_rankModel.rankingPortalData.ranking_page_show != null && _rankModel.rankingPortalData.ranking_page_show.is_show_weekly_change == PB_RankingPageShow_WeeklyChange.WEEKLY_CHANGE_SHOW)
        {
            SetRankResultPage();
        }
        else if (_rankModel.rankingPortalData.can_draw_reward)
        {
            SetRankRewardPage();
        }
        else if (_rankModel.rankingPortalData.ranking_status == PB_RankingStatus.PB_RANKING_STATUS_LOCKED)
        {
            SetRankUnlockPage();
        }
        else if (_rankModel.rankingPortalData.ranking_status == PB_RankingStatus.PB_RANKING_STATUS_AVAILABLE ||
                 _rankModel.rankingPortalData.ranking_status == PB_RankingStatus.PB_RANKING_STATUS_NOT_AVAILABLE)
        {
            SetRankPage(RankUIState.locked);
        }
        else
        {
            SetRankPage(RankUI.RankUIState.normal);
        }
    }

    #region Rank

    private void SetRankPage(RankUIState state)
    {
        ui.page.SetSelectedIndex((int)RankUIPage.Rank);
        base.SetRankPage(state);
    }
    

    #endregion

    #region RankResult
    private void SetRankResultPage()
    {
        ui.page.SetSelectedIndex((int)RankUIPage.RankResult);
        var data = _rankModel.rankingPortalData;
        ui.comRankResult.btnConfirm.SetKey("ui_rank_notify_btn");
        if (data.change_type == PB_RankingChangeType.PB_RANKING_CHANGE_TYPE_UPGRADE)
        {
            var dot = new DataDotAppear_Leaderboard_levelup();
            DataDotMgr.Collect(dot);
            if (!_isPlayedResultSound)
            {
                SoundManger.instance.PlayUI("rank_up");
                _isPlayedResultSound = true;
            }
            ui.comRankResult.tfDesc.text = string.Format(I18N.inst.MoStr("ui_rank_up_desc"), data.pre_order, I18N.inst.MoStr(Cfg.T.TBRankItems[data.current_level.level_id].languageKey));
        }
        else if (data.change_type == PB_RankingChangeType.PB_RANKING_CHANGE_TYPE_DOWNGRADE)
        {
            var dot = new DataDotAppear_Leaderboard_demotion();
            DataDotMgr.Collect(dot);   
            if (!_isPlayedResultSound)
            {
                SoundManger.instance.PlayUI("rank_down");
                _isPlayedResultSound = true;
            }
            ui.comRankResult.tfDesc.text = string.Format(I18N.inst.MoStr("ui_rank_down_desc"), I18N.inst.MoStr(Cfg.T.TBRankItems[data. pre_level.level_id].languageKey));
        }
        else
        {
            if (!_isPlayedResultSound)
            {
                SoundManger.instance.PlayUI("rank_up");
                _isPlayedResultSound = true;
            }
            ui.comRankResult.tfDesc.text = string.Format(I18N.inst.MoStr("ui_rank_maintain_desc"), data.pre_order,  I18N.inst.MoStr(Cfg.T.TBRankItems[data.current_level.level_id].languageKey));
        }
        ui.comRankResult.imgLoader.url = Cfg.T.TBRankItems[data.current_level.level_id].iconPath;
    }
    

    private void ButtonHelpClick()
    { 
        GetUI(UIConsts.RankHelpUI).Show();
    }

    private async void OnRankResultBtnConfirmClicked()
    {
        var data = _rankModel.rankingPortalData;
        if (data.change_type == PB_RankingChangeType.PB_RANKING_CHANGE_TYPE_UPGRADE)
        {
            var dot = new DataDotClick_Leaderboard_levelup_confirm();
            DataDotMgr.Collect(dot);   
        }
        else if (_rankModel.rankingPortalData.change_type == PB_RankingChangeType.PB_RANKING_CHANGE_TYPE_DOWNGRADE)
        {
            var dot = new DataDotClick_Leaderboard_demotion_confirm();
            DataDotMgr.Collect(dot);
        }
            
        await MsgManager.instance.SendAsyncMsg<SC_SetRankingChangeClickAck>(new CS_SetRankingChangeClickReq(), null, true);
        await _rankController.RefreshRankDataUI();
            
        RefreshUI();
    }
    #endregion

    #region RankReward

    private void SetRankRewardPage()
    {
        ui.page.SetSelectedIndex((int)RankUIPage.RankReward);
        var dot = new DataDotAppear_Leaderboard_reward();
        DataDotMgr.Collect(dot);   
        ui.comRankReward.btnConfirm.SetKey("ui_rank_reward_btn");
        ui.comRankReward.btnConfirm.visible = true;
        ui.comRankReward.tfDiamond.visible = false;
        ui.comRankReward.spGift.spineAnimation.AnimationState.SetAnimation(0, "2", true);
        _isRewardReceived = false;
    }

    private async void OnRankRewardBtnConfirmClicked()
    {
        var dot = new DataDotClick_Leaderboard_reward_get();
        DataDotMgr.Collect(dot);
        var msg = new CS_DrawRewardReq()
        {
            draw_type = PB_DrawRewardType.PB_DrawRewardType_Ranking_Upgrade,
            biz_code = _rankModel.rankingPortalData.current_level.level_id.ToString()
        };
        GetUI(UIConsts.CommBusy).LazyShow();
        var resp = await MsgManager.instance.SendAsyncMsg<SC_DrawRewardAck>(msg, null, true);
        GetUI(UIConsts.CommBusy).Hide();
        if (resp == null)
        {
            GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("webview_notice_timeout");
            return;
        }

        if (resp.code == 0)
        {
            ui.comRankReward.spGift.spineAnimation.AnimationState.ClearListenerNotifications();
            ui.comRankReward.spGift.spineAnimation.AnimationState.SetAnimation(0, "3", false);
            RegisterTimer((t) =>
            {
                ui.comRankReward.tfDiamond.text = resp.data.reward_materia_num.ToString();
                ui.comRankReward.tfDiamond.visible = true;
            }, 1000);
            ui.comRankReward.btnConfirm.visible = false;
        }
        var resp2 = await MsgManager.instance.SendAsyncMsg<SC_GetUserRankingPortalDataAck>(new CS_GetUserRankingPortalDataReq()
        {
            user_id = GetModel<MainModel>(ModelConsts.Main).userID,
        });

        if (resp2 == null)
        {
            return;
        }

        if (resp2.code == 0)
        {
            _rankModel.SetUserRankingPortalData(resp2.data);
            _rankModel.SetUserItem(resp2.data.user_item_list.ToList());
        }

        _isRewardReceived = true;
    }

    private void OnRankRewardBtnImgCloseClicked()
    {
        if (_isRewardReceived)
        {
            RefreshUI();
        }
    }
    #endregion

    #region RankUnlock
    private void SetRankUnlockPage()
    {
        ui.page.SetSelectedIndex((int)RankUIPage.RankUnlock);
        ui.comRankUnlock.tfLockDesc.SetKeyArgs("ui_rank_lock_rank_function_desc", _rankModel.rankingPortalData.last_unlock_count);
        ui.comRankUnlock.btnLock.SetKey("common_got_it");
        var dot = new DataDotAppear_Leaderboard_locked();
        DataDotMgr.Collect(dot);
    }
    
    private void OnRankUnlockBtnLockClicked()
    {
        var dot = new DataDotClick_Leaderboard_locked_task();
        DataDotMgr.Collect(dot);

        // Notifier.instance.SendNotification(LearnPathCallEvent.ShowChapterProgress);
        UIManager.instance.GetUI<MultiTabFrameworkUI>(UIConsts.MultiTabHomepage).SwitchTab(TabIndex.Course, false, true);

    }
    #endregion
    
    

}