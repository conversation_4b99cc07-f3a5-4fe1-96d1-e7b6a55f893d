﻿using System;

namespace ScriptsHot.Game.Modules.Procedure
{
    
  
    public partial class ProcedureManager
    {
        public void AddEvent()
        {
            //story
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_chatstory_look_avatar_talk_to_avatar,OnChatStoryLookAvatarTalkToAvatar);
            //rolePlay
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_roleplay_chat_scene_desc,OnRolePlayBegin);
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_roleplay_avatar,OnRolePlayAvatar);
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_roleplay_audio_play,OnRolePlayAudioPlay);
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_roleplay_player,OnRolePlayPlayer);
            //tutor
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_tutor_avatar,OnTutorAvatar);
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_tutor_player,OnTutorPlayer);
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_tutor_audio_play,OnTutorAudioPlay);
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_tutor_chat_image,OnTutorImage);
            //chat public
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_social,OnChatSocial);
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_do_scaffoid_dram,OnDoScaffoidDram);
            //Explore
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_explore_entity_enter_audio_play,OnExploreEntityEnterAudioPlay);
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_explore_entity_avatar_first_cell_show,OnExploreEntityEnterAvatarCellShow);
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_explore_entity_avatar_translate_show,OnExploreEntityEnterAvatarTranslateShow);
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_explore_entity_avatar_cell_show,OnExploreEntityAvatarShow);
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_explore_entity_player_cell_show,OnExploreEntityPlayerCellShow);
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_explore_entity_avatar_audio_play,OnExploreEntityAvatarAudioPlay);
            //目前去掉了自动出脚手架功能
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_explore_entity_scaffold_show,OnExploreEntityScaffoldShow);
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_explore_entity_recordui_show,OnExploreEntityRecordUIShow);
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_explore_entity_do_recordui_show_out_other,OnExploreEntityRecordUIShowBreakOther);
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_explore_first_avatar_show_out_other,OnExploreEntityEnterAvatarCellShowOutOther);
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_explore_chat_end,OnExploreChatEnd);
            
            //Onboarding
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_onboarding_avatar_audio_play,OnOnboardingAvatarAudioPlay);
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_onboarding_chat_end,OnOnboardingChatEnd);
            
            //Intro
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_explore_entity_intro_audio_play,OnIntroAudioPlay);
            Notifier.instance.RegisterNotification(NotifyConsts.procedure_explore_entity_intro_over,OnIntroOver);
            
            
        }

        public void RemoveEvent()
        {
            //story
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_chatstory_look_avatar_talk_to_avatar,OnChatStoryLookAvatarTalkToAvatar);
            //rolePlay
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_roleplay_chat_scene_desc,OnRolePlayBegin);
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_roleplay_avatar,OnRolePlayAvatar);
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_roleplay_audio_play,OnRolePlayAudioPlay);
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_roleplay_player,OnRolePlayPlayer);
            //tutor
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_tutor_avatar,OnTutorAvatar);
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_tutor_player,OnTutorPlayer);
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_tutor_audio_play,OnTutorAudioPlay);
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_tutor_chat_image,OnTutorImage);
            //chat public
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_social,OnChatSocial);
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_do_scaffoid_dram,OnDoScaffoidDram);
            //Explore
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_explore_entity_enter_audio_play,OnExploreEntityEnterAudioPlay);
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_explore_entity_avatar_first_cell_show,OnExploreEntityEnterAvatarCellShow);
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_explore_entity_avatar_translate_show,OnExploreEntityEnterAvatarTranslateShow);
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_explore_entity_avatar_cell_show,OnExploreEntityAvatarShow);
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_explore_entity_player_cell_show,OnExploreEntityPlayerCellShow);
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_explore_entity_avatar_audio_play,OnExploreEntityAvatarAudioPlay);
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_explore_entity_scaffold_show,OnExploreEntityScaffoldShow);
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_explore_entity_recordui_show,OnExploreEntityRecordUIShow);
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_explore_entity_do_recordui_show_out_other,OnExploreEntityRecordUIShowBreakOther);
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_explore_first_avatar_show_out_other,OnExploreEntityEnterAvatarCellShowOutOther);
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_explore_chat_end,OnExploreChatEnd);
            
            
            //Onboarding
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_onboarding_avatar_audio_play,OnOnboardingAvatarAudioPlay);
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_onboarding_chat_end,OnOnboardingChatEnd);
            
            //Intro
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_explore_entity_intro_audio_play,OnIntroAudioPlay);
            Notifier.instance.UnRegisterNotification(NotifyConsts.procedure_explore_entity_intro_over,OnIntroOver);
        }
        

        private void OnChatStoryLookAvatarTalkToAvatar(string a,object b)
        {
            if(IsRunning) Break(String.Empty, null);

            ProcedureParams p = (ProcedureParams)b;
            
            PackDramaPlayerToPoint(p.param);
            PackDramaLookAvatarTalkToAvatar(p.param);
            this.Type = p.type; 
            Start();
        }

        #region rolePlay
        /// <summary>
        /// roleplay 开始
        /// </summary>
        /// <param name="name"></param>
        /// <param name="body"></param>
        private void OnRolePlayBegin(string name, object body)
        {
            if(IsRunning) Break(String.Empty, null);

            ProcedureParams p = (ProcedureParams)body;
            PackDramaRolePlayChatSceneDesc(p);
            this.Type = p.type; 
            Start();
        }
        
        private void OnRolePlayAvatar(string name, object body)
        {
            ProcedureParams p = (ProcedureParams)body;
            PackDramaRolePlayChatAvatar(p);
            Start();
        }

        private void OnRolePlayAudioPlay(string name, object body)
        {
            ProcedureParams p = (ProcedureParams)body;
            PackDramaRolePlayAudioPlay(p);
            Start();
        }
        
        private void OnRolePlayPlayer(string name, object body)
        {
            ProcedureParams p = (ProcedureParams)body;
            PackDramaRolePlayChatPlayer(p);
            Start();
        }
        #endregion

        #region tutor
        private void OnTutorAvatar(string name, object body)
        {
            ProcedureParams p = (ProcedureParams)body;
            PackDramaTutorChatAvatar(p);
            Start();
        }
        
        private void OnTutorAudioPlay(string name, object body)
        {
            ProcedureParams p = (ProcedureParams)body;
            PackDramaTutorAudioPlay(p);
            Start();
            
        }
        private void OnTutorPlayer(string name, object body)
        {
            ProcedureParams p = (ProcedureParams)body;
            PackDramaTutorChatPlayer(p);
            Start();
        }
        
        private void OnTutorImage(string name, object body)
        {
            ProcedureParams p = (ProcedureParams)body;
            PackDramaTutorChatImage(p);
            Start();
        }
  
        #endregion

        #region chat public
        private void OnChatSocial(string name, object body)
        {
            ProcedureParams p = (ProcedureParams)body;
            PackDramaChatSocial(p);
            Start();
        }
        
        private void OnDoScaffoidDram(string name, object body)
        {
            foreach (var dram in Dramas)
            {
                if ((dram.Params as ProcedureParams).type != EProcedureType.ChatScaffold)
                {
                    dram.Pause();
                }
            }
            //这个要放后面执行
            foreach (var dram in Dramas)
            {
                if ((dram.Params as ProcedureParams).type == EProcedureType.ChatScaffold)
                {
                    dram.Do(false);
                }
            }
        }
        
        #endregion

        #region Explore
        
        private void OnExploreEntityRecordUIShowBreakOther(string name, object body)
        {
            foreach (var dram in Dramas)
            {
                if (dram.Params == null)
                {
                    dram.Pause();
                }
                else
                {
                    if ((dram.Params as ProcedureParams).type != EProcedureType.ExploreShowRecordUI)
                    {
                        dram.Pause();
                    }
                }
            }
            //这个要放后面执行
            foreach (var dram in Dramas)
            {
                if (dram.Params != null && (dram.Params as ProcedureParams).type == EProcedureType.ExploreShowRecordUI)
                {
                    dram.Do(false);
                }
            }
        }
        
        /// <summary>
        /// 显示第一次的 avatar cell  抛弃其他
        /// </summary>
        /// <param name="name"></param>
        /// <param name="body"></param>
        private void OnExploreEntityEnterAvatarCellShowOutOther(string name, object body)
        {
            foreach (var dram in Dramas)
            {
                if (dram.Params == null)
                {
                    dram.Pause();
                }
                else
                {
                    if ((dram.Params as ProcedureParams).type != EProcedureType.ExploreAvatarCallFirst)
                    {
                        dram.Pause();
                    }
                }
            }
            //这个要放后面执行
            foreach (var dram in Dramas)
            {
                if (dram.Params != null && (dram.Params as ProcedureParams).type == EProcedureType.ExploreAvatarCallFirst)
                {
                    dram.Do(false);
                }
            }
        }
        
        
        

        private void OnExploreEntityEnterAudioPlay(string name, object body)
        {
            ProcedureParams p = (ProcedureParams)body;
            PackDramaExploreEntityEnterAudioPlay(p);
            Start();
        }
        private void OnExploreEntityEnterAvatarCellShow(string name, object body)
        {
            ProcedureParams p = (ProcedureParams)body;
            PackDramaExploreEntityEnterAvatarCellShow(body);
            Start();
        }
        
        private void OnExploreEntityEnterAvatarTranslateShow(string name, object body)
        {
            ProcedureParams p = (ProcedureParams)body;
            PackDramaExploreEntityEnterAvatarTranslateShow(p);
            Start();
        }
        
        private void OnExploreEntityAvatarShow(string name, object body)
        {
            ProcedureParams p = (ProcedureParams)body;
            PackDramaExploreEntityAvatarShow(p);
            Start();
        }
        
        private void OnExploreEntityPlayerCellShow(string name, object body)
        {
            ProcedureParams p = (ProcedureParams)body;
            PackDramaExploreEntityEnterPlayerCellShow(p);
            Start();
        }
        
        private void OnExploreEntityAvatarAudioPlay(string name, object body)
        {
            ProcedureParams p = (ProcedureParams)body;
            PackDramaExploreEntityAvatarAudioPlay(p);
            Start();
        }

        private void OnExploreEntityScaffoldShow(string name, object body)
        {
            ProcedureParams p = (ProcedureParams)body;
            PackDramaExploreEntityScaffoldShow(p);
            Start();
        }

        private void OnExploreEntityRecordUIShow(string name, object body)
        {
            ProcedureParams p = (ProcedureParams)body;
            PackDramaExploreEntityRecordUIShow(p);
            Start();
        }
        
        private void OnExploreChatEnd(string name, object body)
        {
            ProcedureParams p = (ProcedureParams)body;
            PackDramaExploreChatEnd(p);
            //被动开始 自己不开始
        }

        #endregion

        #region Onboarding

        private void OnOnboardingAvatarAudioPlay(string name, object body)
        {
            ProcedureParams p = (ProcedureParams)body;
            PackDramaOnBoardingAvatarAudioPlay(p);
            Start();
        }
        private void OnOnboardingChatEnd(string name, object body)
        {
            ProcedureParams p = (ProcedureParams)body;
            PackDramaOnboardingChatEnd(p);
            //被动开始 自己不开始
        }

        #endregion

        #region intro

        private void OnIntroAudioPlay(string name, object body)
        {
            ProcedureParams p = (ProcedureParams)body;
            PackDramaIntroAudioPlay(p);
            Start();
        }
        
        private void OnIntroOver(string name, object body)
        {
            PackDramaIntroOver(null);
            Start();
        }

        #endregion
        
    }
}