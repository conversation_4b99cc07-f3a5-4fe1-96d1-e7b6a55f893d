/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.MainHead
{
    public partial class CompHead : UIBindT
    {
        public override string pkgName => "MainHead";
        public override string comName => "CompHead";

        public HeadInner compInner;
        public CompLevel compLevel;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            compInner = new HeadInner();
            compInner.Construct(com.GetChildAt(1).asCom);
            compLevel = new CompLevel();
            compLevel.Construct(com.GetChildAt(2).asCom);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            compInner.Dispose();
            compInner = null;
            compLevel.Dispose();
            compLevel = null;
        }
    }
}