/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.Review
{
    public partial class SummaryMistakeItem : GComponent
    {
        public static string pkgName => "Review";
        public static string comName => "SummaryMistakeItem";
        public static string url => "ui://l7zo233dd2k92b";

        public Controller ctrlState;
        public GTextField tfMaster;
        public GButton card;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(SummaryMistakeItem));
        }

        public override void ConstructFromXML(XML xml)
        {
            ctrlState = GetControllerAt(0);
            tfMaster = GetChildAt(2) as GTextField;
            card = GetChildAt(4) as GButton;

            SetMultiLanguageInChildren();
        }
        public override void Dispose()
        {
            ctrlState = null;
            tfMaster = null;
            card = null;

            base.Dispose();
        }
        public void SetMultiLanguageInChildren()
        {
            this.tfMaster.SetKey("review_summary_masterd");  // "Mastered!"
        }
    }
}