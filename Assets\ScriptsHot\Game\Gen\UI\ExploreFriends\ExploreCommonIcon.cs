/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreFriends
{
    public partial class ExploreCommonIcon : UIBindT
    {
        public override string pkgName => "ExploreFriends";
        public override string comName => "ExploreCommonIcon";

        public Controller iconType;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            iconType = com.GetControllerAt(0);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            iconType = null;
        }
    }
}