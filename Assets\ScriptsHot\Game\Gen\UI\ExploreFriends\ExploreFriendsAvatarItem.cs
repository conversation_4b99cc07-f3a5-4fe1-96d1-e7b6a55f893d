/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreFriends
{
    public partial class ExploreFriendsAvatarItem : UIBindT
    {
        public override string pkgName => "ExploreFriends";
        public override string comName => "ExploreFriendsAvatarItem";

        public GComponent tfOrigin;
        public GTextField txtTran;
        public GGroup Container;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            tfOrigin = (GComponent)com.GetChildAt(1);
            txtTran = (GTextField)com.GetChildAt(2);
            Container = (GGroup)com.GetChildAt(3);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            tfOrigin = null;
            txtTran = null;
            Container = null;
        }
    }
}