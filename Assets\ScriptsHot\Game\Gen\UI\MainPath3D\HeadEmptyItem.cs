/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.MainPath3D
{
    public partial class HeadEmptyItem : UIBindT
    {
        public override string pkgName => "MainPath3D";
        public override string comName => "HeadEmptyItem";

        public GGraph holder;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            holder = (GGraph)com.GetChildAt(0);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            holder = null;
        }
    }
}