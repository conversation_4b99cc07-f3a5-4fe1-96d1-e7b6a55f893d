﻿/* 
****************************************************
* 作者：LiuXinmiao
* 创建时间：2025/01/09 17:38:06 星期四
* 功能：Nothing
****************************************************
*/


using System;
using System.Collections.Generic;
using System.Linq;
using CommonUI;
using FairyGUI;
using Msg.basic;
using Msg.incentive;
using ScriptsHot.Game.Modules.Profile;
using ScriptsHot.Game.Modules.Settlement;

public class FriendsController: BaseController
{
    private FriendsModel _friendsModel => GetModel<FriendsModel>(ModelConsts.Friends);
    private ProfileController profileController => GetController<ProfileController>(ModelConsts.Profile);
    private MainModel mainMondel => GetModel<MainModel>(ModelConsts.Main); 
    private bool isBack { set; get; }

    public FriendsController() : base(ModelConsts.Friends)
    {
        
    }

    public override void OnUIInit()
    {
        RegisterUI(new FriendsUI(UIConsts.FriendsUI));
        RegisterUI(new  FriendsSearchUI(UIConsts.FriendsSearchUI));
        RegisterUI(new  FriendsGitUI(UIConsts.FriendsGitUI));
    }

    public override void OnInit()
    {
        RegisterModel(new FriendsModel());
    }

    public override void OnUpdate(int interval)
    {
       
    }

    public override void OnEnterGame()
    {
        base.OnEnterGame();
        OnRequestGetUserGiftPortalData();

    }

    public void OpenFriendsUI(bool isFollowers = false)
    {
        CloseFriendsUI();
        GetUI<FriendsUI>(UIConsts.FriendsUI).Show(isFollowers);
    }
    
    public void CloseFriendsUI()
    {
        var ui =  GetUI<FriendsUI>(UIConsts.FriendsUI);
        if (ui.isShow)
        {
            ui.Hide();
        }
    }
    
    public void OpenFriendsSearchUI(Action cb = null)
    {
        GetUI<FriendsSearchUI>(UIConsts.FriendsSearchUI).Show(cb);
    }
    
    public void CloseFriendsSearchUI()
    {
        GetUI<FriendsSearchUI>(UIConsts.FriendsSearchUI).Hide();
    }

    private void RefreshRequset()
    {
        var ui = GetUI<FriendsSearchUI>(UIConsts.FriendsSearchUI);
        if (ui.isShow)
            ui.RefreshRequest();
    }

    public async void RequestAddFriends(long userId,Action<long> callback = null)
    {
        CS_AddFriendReq req = new CS_AddFriendReq();
        req.target_user_id.Add(userId);

        var resp = await MsgManager.instance.SendAsyncMsg<SC_AddFriendAck>(req,(a,b)=>{
            if (callback != null)
                callback(userId);
        });
        if (resp != null && resp.code == PB_Code.Normal)
        {
            _friendsModel.UpdateFollowers(resp.data.friend_list.ToList());
            if (callback != null)
                callback(userId);
        }
        else
        {
             if (callback != null)
                 callback(userId);
            VFDebug.Log("【关注】失败");
        }
    }

    public async void RequestAddFriends(List<long> list,Action<List<PB_UserFriendshipTypeItem>> callback = null)
    {
        CS_AddFriendReq req = new CS_AddFriendReq();
        for (int i = 0; i < list.Count; i++)
        {
            req.target_user_id.Add(list[i]);
        }
        
        var resp = await MsgManager.instance.SendAsyncMsg<SC_AddFriendAck>(req);
        if (resp != null && resp.code == PB_Code.Normal)
        {
            if (callback != null)
                callback(resp.data.friend_list.ToList());
            profileController.AddFollowing(resp.data.friend_list.Count);
            var otherModel = GetModel<ProfileOthersModel>(ModelConsts.ProfileOthers);
            for (int i = 0; i < resp.data.friend_list.Count; i++)
            {
                // var id = resp.data.friend_list[i].user_id;
                // var socialCtrl = GetController<SocialChatController>(ModelConsts.SocialChat);
                // socialCtrl.FollowingCreateSingleChatDialog(id);
                if (otherModel.userId == resp.data.friend_list[i].user_id)
                {
                    profileController.AddFollowersOthers();
                }
            }
        }
        else
        {
            VFDebug.Log("【关注】失败" );
        }
    }
    
    public async void RequestRemoveFriends(List<long> list,Action<List<PB_UserFriendshipTypeItem>> callback = null)
    {
        CS_RemoveFriendReq req = new CS_RemoveFriendReq();
        for (int i = 0; i < list.Count; i++)
        {
            req.target_user_id.Add(list[i]);
        }
        
        var resp = await MsgManager.instance.SendAsyncMsg<SC_RemoveFriendAck>(req);
        if (resp != null && resp.code == PB_Code.Normal)
        {
            if (callback != null)
                callback(resp.data.friend_list.ToList());
            profileController.RemoveFollowing();
            for (int i = 0; i < resp.data.friend_list.Count; i++)
            {
                var otherModel = GetModel<ProfileOthersModel>(ModelConsts.ProfileOthers);
                if (otherModel.userId == resp.data.friend_list[i].user_id)
                {
                    profileController.RemoveFollowersOthers();
                }
            }
        }
        else
        {
            VFDebug.Log("【取关】失败");
        }
    }

    public async void RequestRecommendedList(int size,Action callback)
    {
        CS_GetRecommendedFriendListReq req = new CS_GetRecommendedFriendListReq();
        req.size = size;

        var resp = await MsgManager.instance.SendAsyncMsg<SC_GetRecommendedFriendListAck>(req,(a,b) => FailedGetListData(callback));
        if (resp != null && resp.code == PB_Code.Normal)
        {
            _friendsModel.SetRecommendList(resp.data.friend_list.ToList());
            if (callback != null)
                callback();
        }
        else
        {
            if (callback != null)
                callback();
            VFDebug.Log("【推荐列表】获取失败");
        }
    }
    
    
    private void FailedGetListData(Action callback = null)
    {
        if (callback != null)
            callback();
        GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N("ui_socialchat_network_timeout", null,
            null, 1, null, "common_check");
    }

    public async void RequestSearchUser(string uniqueName,Action callback = null)
    {
        CS_SearchUserReq req = new CS_SearchUserReq();
        req.role_unique_name = uniqueName;
        
        var resp = await MsgManager.instance.SendAsyncMsg<SC_SearchUserAck>(req,(a,b) => FailedGetListData(callback));
        if (resp != null && resp.code == PB_Code.Normal)
        {
            _friendsModel.SetSearchList(resp.data.data.ToList());
            if (callback != null)
                callback();
        }
        else
        {
            if (callback != null)
                callback();
            VFDebug.Log("【搜索列表】获取失败");
        }
    }

    public async void OnRequestUserFriendList(long userid,PB_FriendshipType type,int size ,Action callback = null, string pageToken = null)
    {
        CS_GetUserFriendListReq req = new CS_GetUserFriendListReq();
        req.user_id = userid;
        req.page_token = pageToken == null ? "" : pageToken;
        req.page_size = size;
        req.friendship_type = type;
        
         
        var resp = await MsgManager.instance.SendAsyncMsg<SC_GetUserFriendListAck>(req,(a,b) => FailedGetListData(callback));
        if (resp != null && resp.code == PB_Code.Normal)
        {
            if (type == PB_FriendshipType.FOLLOWING)
            {
                _friendsModel.SetFollowList(resp.data.friend_list.ToList(),resp.data.page_token,string.IsNullOrEmpty(pageToken));
            }
            else if(type == PB_FriendshipType.FOLLOWER)
            {
                _friendsModel.SetFollowerList(resp.data.friend_list.ToList(),resp.data.page_token,string.IsNullOrEmpty(pageToken));
            }

            if (callback != null)
                callback();
        }
        else
        {
            if (callback != null)
                callback();
            VFDebug.Log("【获取关注列表】获取失败");
        }
    }

    public void RequestUserFriendList(long userid, PB_FriendshipType type, int size, Action callback = null, string pageToken = null)
    {
        // if (isBack)
        //     return;
        OnRequestUserFriendList(userid, type, size, callback, pageToken);
    }


    public void SetCurUesr(long userId, string role_name)
    {
        _friendsModel.curUserId = userId;
        _friendsModel.curUserName = role_name;
    }

    public int ProfileBack()
    {
       var data =  _friendsModel.StackPop();
       if (data != null)
       {
           isBack = true;
           _friendsModel.SetFollowList(data.followList,data.followToken,true);
           _friendsModel.SetFollowerList(data.followerList,data.followersToken,true);
           _friendsModel.curUserId = data.userId;
           _friendsModel.curUserName = data.user_name;
           _friendsModel.readIndex = data.readIndex;
           if (data.followType != PB_FriendshipType.FNONE)
           {
               OpenFriendsUI(data.followType == PB_FriendshipType.FOLLOWER);
           }
           else
           {
               RefreshRequset();
           }
       }
       else
       {
           var ui = GetUI<ProfileOthersUI>(ModelConsts.ProfileOthers);
           if(ui != null)
               ui.Hide();
           ReSetIsBack();
       }

       return _friendsModel.StackCount() ;
    }
    
    public void FollowBack()
    {
        if (_friendsModel.curUserId == mainMondel.userID)
        {
            profileController.SetSelfProfileTop();
            var isfollowClost = _friendsModel.StackCount() == 0 ? false : true;
            profileController.OnClickSelf(isfollowClost);
        }
        else
        {
            profileController.SetOtherProfileTop();
            profileController.OnClickOther(_friendsModel.curUserId);
        }
    }

    public void ReSetIsBack()
    {
        isBack = false;
    }

    public async void OnRequestMatchFriendsShipTask(long friendId,Action callback)
    {
        CS_MatchFriendShipTaskReq req = new CS_MatchFriendShipTaskReq();
        req.target_friend_id = friendId;
        GetUI(UIConsts.CommBusy).Show();
        var resp = await MsgManager.instance.SendAsyncMsg<SC_MatchFriendShipTaskAck>(req,(a,b) => FailedGetListData(callback));
        GetUI(UIConsts.CommBusy).Hide();
        if (resp != null && resp.code == PB_Code.Normal)
        {
            if (callback != null)
                callback();
        }
        else 
        {
            if (resp != null)
            {
                var settlementModel = GetModel<SettlementModel>(ModelConsts.Settlement);
                
                
                switch (resp.code)
                {
                    case PB_Code.FriendshipTaskFriendOccupied:
                        GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("ui_friends_quest_friend_occupied"); 
                        settlementModel.SetFriendsQuestTeammateData(resp.recommended_friend_list);
                        GetUI<SettlementSelectTeammateUI>(UIConsts.SettlementSelectTeammate).RefreshUI();
                        break;
                    case PB_Code.FriendshipTaskSelfOccupied:
                        GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("ui_friends_quest_self_occupied");
                        if (callback != null)
                            callback();
                        break;
                    case PB_Code.FriendshipTaskNoFriend:
                        GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("ui_friends_quest_no_friend");
                        if (callback != null)
                            callback();
                        break;
                }
            }
            
            //VFDebug.Log("【好友任务】匹配队友失败");
        }
    }
    
    public void OnRequestMatchFriendsShipTask(long friendId)
    {
        CS_FriendShipNotifyReq req = new CS_FriendShipNotifyReq();
        req.friend_id = friendId;
        req.notify_type = FriendShipNotifyType.FriendShipNotifyType_TaskHiveFive;
        MsgManager.instance.SendMsg(req);
    }

    public async void OnRequestSendGiftForFriends(PB_MerchandiseItemType type,long gitNum,long friendId,long giftId, long count = 1)
    {
        CS_SendGiftForFriendReq req = new CS_SendGiftForFriendReq();
        req.merchandise_type = type;
        req.merchandise_val = gitNum;
        req.friend_id = friendId;
        req.count = count;
        req.rebate_gift_id = giftId;
        var resp = await MsgManager.instance.SendAsyncMsg<SC_SendGiftForFriendAck>(req);
        if (resp != null)
        {
            GetController<CurrencyController>(ModelConsts.CurrencyController).SendGetEconomicInfoReqAsync(GameEventName.GameEnter, () =>
            {
                SendNotification(NotifyConsts.MainHeadRefreshEvent);
            });
        }
    }

    public async void OnRequestGetUserGiftPortalData()
    {
        CS_GetUserGiftPortalDataReq req = new CS_GetUserGiftPortalDataReq();
        var resp = await MsgManager.instance.SendAsyncMsg<SC_GetUserGiftPortalDataAck>(req);
        if (resp != null )
        {
            if (resp.gift_list != null && resp.gift_list.Count > 0)
            {
                _friendsModel.SetGitList(resp.gift_list.ToList());
                GetUI<FriendsGitUI>(UIConsts.FriendsGitUI).PopShow(PopUIManager.Priority.Friend);
            }
        }
        GetController<CurrencyController>(ModelConsts.CurrencyController).SendGetEconomicInfoReqAsync(GameEventName.GameEnter, () =>
        {
            SendNotification(NotifyConsts.MainHeadRefreshEvent);
        });
    }
}
