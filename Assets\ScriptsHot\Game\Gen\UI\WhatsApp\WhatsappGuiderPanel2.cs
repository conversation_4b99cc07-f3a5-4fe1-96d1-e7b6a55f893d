/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.WhatsApp
{
    public partial class WhatsappGuiderPanel2 : UIBindT
    {
        public override string pkgName => "WhatsApp";
        public override string comName => "WhatsappGuiderPanel2";

        public GGraph imgBG;
        public GTextField content1;
        public GTextField content2;
        public GTextField content3;
        public GTextField content4;
        public GImage line;
        public GTextField content5;
        public GGraph closeBtn;
        public GTextField btnTxt;
        public GGraph btn;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            imgBG = (GGraph)com.GetChildAt(0);
            content1 = (GTextField)com.GetChildAt(5);
            content2 = (GTextField)com.GetChildAt(8);
            content3 = (GTextField)com.GetChildAt(10);
            content4 = (GTextField)com.GetChildAt(12);
            line = (GImage)com.GetChildAt(13);
            content5 = (GTextField)com.GetChildAt(16);
            closeBtn = (GGraph)com.GetChildAt(20);
            btnTxt = (GTextField)com.GetChildAt(23);
            btn = (GGraph)com.GetChildAt(24);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            imgBG = null;
            content1 = null;
            content2 = null;
            content3 = null;
            content4 = null;
            line = null;
            content5 = null;
            closeBtn = null;
            btnTxt = null;
            btn = null;
        }
    }
}