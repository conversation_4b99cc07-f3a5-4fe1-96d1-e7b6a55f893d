/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreFriends
{
    public partial class ExploreCommonChosenessItem : UIBindT
    {
        public override string pkgName => "ExploreFriends";
        public override string comName => "ExploreCommonChosenessItem";

        public Controller ctrl;
        public GGraph imgBack;
        public GTextField txtTitle;
        public GTextField txtDesc;
        public ExploreCommonIcon comIcon;
        public GGroup lockGroup;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ctrl = com.GetControllerAt(0);
            imgBack = (GGraph)com.GetChildAt(0);
            txtTitle = (GTextField)com.GetChildAt(1);
            txtDesc = (GTextField)com.GetChildAt(2);
            comIcon = new ExploreCommonIcon();
            comIcon.Construct(com.GetChildAt(3).asCom);
            lockGroup = (GGroup)com.GetChildAt(6);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ctrl = null;
            imgBack = null;
            txtTitle = null;
            txtDesc = null;
            comIcon.Dispose();
            comIcon = null;
            lockGroup = null;
        }
    }
}