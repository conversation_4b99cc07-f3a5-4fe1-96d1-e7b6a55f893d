﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Msg.basic;
using ScriptsHot.Game.Modules.Profile;
using ScriptsHot.Game.Modules.Common;

using ScriptsHot.Game.Modules.Scene.Level;
using ScriptsHot.Game.Modules.Scene.Level.CommonSceneState;
using ScriptsHot.Game.Modules.Scene.Level.Component;

using SRF;

using UnityEngine;

/**
 * 进入对话的中间态
 * lizhuangzhuang
 * 2024年3月2日20:59:11
 */
public class SceneStateChatPre_Homepage : SceneStateBase
{
    public SceneStateChatPre_Homepage() : base(SceneState.PreChat) { }


    public override void OnEnter(params object[] args)
    {
        

        base.OnEnter(args);
        if (!this.AssertParamLength(args, 1))
        {
            this.owner.ChangeState(GameState.SceneIdle);
            Debug.LogError("Internal params error on SceneStateChatPre_Homepage onEnter");
            return;
        }

        SceneStateTaskParam taskParam = (SceneStateTaskParam)args[0];

        // var mainHeaderUI = this._sceneController.GetUI<MainHeaderUI>(UIConsts.MainHeader);
        // if (mainHeaderUI == null)
        // {
        //     return;
        // }
        // if (taskParam.chatMode == PB_DialogMode.Exercise ||taskParam.chatMode == PB_DialogMode.Career ||taskParam.chatMode == PB_DialogMode.RolePlay ||taskParam.chatMode == PB_DialogMode.Tutor)
        // {
        //     //step1:视图调整
        //     mainHeaderUI.SetHeader(MainHeaderMode.ExerciseTalk);
        // }
        // else
        // {
        //     //step1:视图调整
        //     mainHeaderUI.SetHeader(MainHeaderMode.Talk);
        // }
        

        UIManager.instance.HideLayerBeyond(UILayerConsts.Home, UILayerConsts.Top, UILayerConsts.Loading, UILayerConsts.Float,UILayerConsts.Guide);

        //step2:avatar改变-动作&站位&角度  npc口型

        //指定avatar的audioSource播放
        if (_gameScene == null) return;
        var avatarComp = _gameScene.GetComponent<AvatarComponent>();
        if (avatarComp == null) return;
        HomepageChatAvatar avatar = avatarComp.GetHomePageChatAvatar();
        if (avatar != null)
        {
            ObjectUtils.SetLayer(avatar.gameObject, LayerMask.NameToLayer("chat"));

            GAvatarCtrl avatarCtrl = avatar.gameObject.GetComponentOrAdd<GAvatarCtrl>();
            if (avatarCtrl != null)
                GSoundManager.instance.SetCurrAvatarTTS(avatarCtrl.audioSource);
        }
        else {
            Debug.LogError("HomePageChatAvatar failed to get.");
        }

        //首页对话中，不再管理 cullmask的 player层是否可见的问题, GameUtils.SetCameraCullingMask(LayerMask.NameToLayer("player"), false);


        //step3 cam改变

        //GHeadBarManager.instance.SetWork(false);
        _sceneController.GetController<CurrencyController>(ModelConsts.CurrencyController).SendGetEconomicInfoReq(GameEventName.GameChatPre);//更新金币

        //step3 参数流转
        //在homepage的数据流程里 ChatPre_home节点要承担，类似于 SceneStateTask中的拼装参数的任务
        //要让 SceneStateTaskParam =>SceneStateChatParam
    

        SceneStateChatParam chatParam = new SceneStateChatParam();
        chatParam.unitUid = 0;//homepage不关心自己的id unit.uid;
        chatParam.avatarId = taskParam.avatarId;//avatar.avatarID;
        chatParam.isTask = true;
        chatParam.taskId = taskParam.taskId;
        chatParam.entranceType = taskParam.entranceType;
        chatParam.chatMode = taskParam.chatMode;
        chatParam.topicId = taskParam.topicId;

        if (this.owner != null)
        {
            this.owner.ChangeState(SceneState.Chat, chatParam);
            //this.owner.ChangeState(GameState.Chat, taskParam); 
        }
    }

    public override void OnExit()
    {
        base.OnExit();
    }
    
    public override void OnNetReConn()
    {
        base.OnNetReConn();
    }

    public override void OnNetDisConn()
    {
        base.OnNetDisConn();
    }
}