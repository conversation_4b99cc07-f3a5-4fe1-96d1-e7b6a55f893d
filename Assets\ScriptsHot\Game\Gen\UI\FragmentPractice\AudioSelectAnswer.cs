/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class AudioSelectAnswer : AFragAnswer
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "AudioSelectAnswer";
        public static string url => "ui://cmoz5osjz7rm31";

        public GList choiceList;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(AudioSelectAnswer));
        }

        public override void ConstructFromXML(XML xml)
        {
            choiceList = GetChildAt(0) as GList;
        }
        public override void Dispose()
        {
            choiceList = null;

            base.Dispose();
        }
    }
}