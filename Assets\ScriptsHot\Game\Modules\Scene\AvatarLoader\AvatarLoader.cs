﻿using System.Collections.Generic;
using UnityEngine;
using Avatar30;
using System.Threading.Tasks;
using AnimationSystem;
using Newtonsoft.Json;
using YooAsset;
using UnityEngine.Assertions;
using UnityEngine.SceneManagement;



public class AvatarLoader : MonoBehaviour
{
    public class AvatarLoadExData
    {
        public string avatarName = "NPC_Girl_00004";
        public Transform parentNode = null;
        public bool initAnimationManager = false;
        public Purpose purpose;
    }
    public void OnAppPrepared()
    {
    }

    public void OnEditorPrepared()
    {
    }

    public enum Purpose
    {
        //不会额外应用任何逻辑
        Generic = 0,
        
        //会应用当初的探索页配置逻辑，目前有：特殊人物加载后位置调整。
        Explore = 1,
    }

    public async Task<GameObject> LoadNAvatar(AvatarLoadExData exData)
    {
        var style = exData.avatarName;
        var parentNode = exData.parentNode;
        var initAnimationManager = exData.initAnimationManager;
        
        if (style.ToLower().Contains("user"))
        {
            style = "NPC_Girl_00010";
        }

        Assert.IsFalse(string.IsNullOrEmpty(style));

        //假设我们在此可以进行判断，知道我们收到的角色ID是一个配置表fbx
        if (!style.StartsWith("NPC_"))
        {
            var go = new GameObject();
            go.name = style;

            if (parentNode != null)
            {
                ObjectUtils.SetGameObjectParentRoot(go, parentNode);

               // var go1 = GameObject.Find("LAOTOUZHUANYONG");
               // if (go1 == null)
               // {
               //     go1 = new GameObject();
               //     Instantiate(go1);
               //     go1.name = "LAOTOUZHUANYONG";
               //     Scene currentScene = SceneManager.GetActiveScene();
               //     SceneManager.MoveGameObjectToScene(go1, currentScene);
               // }
               //
               // if (go1 != null)
               // {
               //     ObjectUtils.SetGameObjectParentRoot(go, go1.transform);
               // }
            }
            
            go.AddComponent<CartoonAvatarRoot>();
            var monoRoot = go.GetComponent<CartoonAvatarRoot>();
            
            //跳过原来的加载角色步骤。这意味着加载角色步骤只允许进行IO操作以及其它只有普通加载才会做的事情。
            //#在新的角色加载步骤中，需要基于读表加载两个指定预制体。
            monoRoot.LoadAvatar(style,exData);
            
            //#跳过身体的骨骼同步。这意味着单模型加载必须只有二个SMR。身体、头部各一个。但是我们依然需要将头部挂载在正确的骨骼上去。
            monoRoot.SyncBoneForStandalone();
            
            //#我们会将所有的部件都指向该唯二SMR。因此所有部件名获取到的SMR信息都一致。防止报空。这一步目前已经合并到LoadAvatar。
            //#我们暂时不会给该模型添加Animator。如果需要,那么请重新写相关逻辑。以免匹配错误。
            monoRoot.CheckAnimator();
            
            //该模型头部将只有一个SMR,因此无需BS同步
            //#新角色依然需要表情信息以及新版PlayableGraph,因此需要初始化。
            monoRoot.InitDialogAnim(true);

            return go;

        }
        else
        {
            //假设还能通过此方式获取到Json.
            var assetHandle = await GAvatarResManager.instance.LoadText(style);
            var avatarJson = assetHandle?.text;
            if (avatarJson != null)
            {
                CartoonAvatarData avatarData = JsonConvert.DeserializeObject<CartoonAvatarData>(avatarJson);
                if (avatarData == null)
                {
                    Debug.LogError("LoadNAvatar Failed, style=" + style);
                    return null;
                }

                //假设此处Json已经获取成功.开始解析数据.
                GameObject go = new GameObject();
                go.name = avatarData.name;
                if (parentNode != null)
                {
                    ObjectUtils.SetGameObjectParentRoot(go, parentNode);
                }

                //先抄,再改。s
                go.AddComponent<CartoonAvatarRoot>();
                var monoRoot = go.GetComponent<CartoonAvatarRoot>();
                monoRoot.SetAvatarData(avatarData);

                //Debug.LogWarning(avatarJson);
                //Debug.LogWarning(avatarData.body.res);

                monoRoot.LoadAvatar();

                //此时应该已经加载完Go,进行骨骼绑定。
                monoRoot.SyncBone();

                //此时应该已经绑定完骨骼,进行动画播放。
                //根据3.0角色,在Body处应该还有一Animator。然后在运行时Body的Animator的Controller会被替换。
                // an.body.GetComponent<Animator>().runtimeAnimatorController 
                //     = YooAssets.LoadAssetSync<RuntimeAnimatorController>(data.animTypeId==1?"avatarAimBoy": "avatarAimGirl").AssetObject as RuntimeAnimatorController;

                // if (exData != null && (exData.overrideAnimatorControllerGirl != null && !monoRoot.avatarData.isMale))
                // {
                //     monoRoot.SetupAnimator(exData.overrideAnimatorControllerGirl);
                // }
                // else if (exData != null && (exData.overrideAnimatorControllerBoy != null && monoRoot.avatarData.isMale))
                // {
                //     monoRoot.SetupAnimator(exData.overrideAnimatorControllerBoy);
                // }
                //else
                {
                    monoRoot.SetupAnimator(null);
                }


                //Animator不在本环节应用。仅供程序调用。
                //此时应该已经添加了Animator,进行BlendShape注册。
                monoRoot.SetupBlendShapeSynchronization();

                //兜底逻辑
                //  monoRoot.DOUDICHULI();

                //在monoRoot上初始化两个对话表情控制的东西
                if (initAnimationManager)
                {
                    monoRoot.InitDialogAnim();
                }

                return go;
            }
        }

        return null;
    }
    
    /// <summary>
    /// 老接口，以免未来参数增多，建议使用新街口。
    /// </summary>
    /// <param name="style"></param>
    /// <param name="parentNode"></param>
    /// <param name="initAnimationManager"></param>
    /// <returns></returns>
    public async Task<GameObject> LoadNAvatar(
        string style,
        Transform parentNode = null,
        bool initAnimationManager = false
       // AvatarLoadExData exData = null
    )
    {
        AvatarLoadExData exData = new AvatarLoadExData();
        exData.avatarName = style;
        exData.parentNode = parentNode;
        exData.initAnimationManager = initAnimationManager;
        exData.purpose = Purpose.Explore;

        return await LoadNAvatar(exData);
    }

    public async Task<GameObject> LoadNAvatar1(string style, Transform parentNode = null)
    {
        Assert.IsFalse(string.IsNullOrEmpty(style));
        Dictionary<string, GameObject> prefabs = new Dictionary<string, GameObject>();
        HashSet<int> changePersets = new HashSet<int>();
        var avatarJson = (await GAvatarResManager.instance.LoadText(style)).text;
        EditorSyncData data = EditorSyncData.DeserializeFromStr(avatarJson);
        GameObject go = new GameObject();
        if (parentNode != null)
        {
            ObjectUtils.SetGameObjectParentRoot(go, parentNode);
        }

        var an = go.AddComponent<AvatarNode>();
        go.SetActive(false);

        an.body = await GAvatarResManager.instance.LoadAsset<GameObject>(AvatarHelper.GetBodyPathById(data.body_id),
            go.transform);
        an.body.GetComponent<Animator>().runtimeAnimatorController =
            YooAssets.LoadAssetSync<RuntimeAnimatorController>(data.sex == 1 ? "avatarAimBoy" : "avatarAimGirl")
                .AssetObject as RuntimeAnimatorController;
        an.head = await GAvatarResManager.instance.LoadAsset<GameObject>(AvatarHelper.GetHeadPathById(data.head_id),
            go.transform);

        foreach (var choosed in data.persetGroupChooseData)
        {
            var pgc = AvatarHelper.GetPresetGroupCfgById(choosed.Value);

            for (int i = 0; i < pgc.Perset.Count; i++)
            {
                var preset = AvatarHelper.GetPresetById(pgc.Perset[i]);
                var modifier = AvatarHelper.GetModifierById(preset.Modifier);
                var changeValue = preset.ChangeValue;
                changePersets.Add(choosed.Value);
                if (modifier.modifierType == ECfgAvatarModifierType.ChangePrefab)
                {
                    if (string.IsNullOrEmpty(changeValue)) continue;
                    if (prefabs.ContainsKey(changeValue))
                    {
                        an.AddItem(pgc.exclusionGroup.ToString(), prefabs[changeValue]);
                    }
                    else
                    {
                        if (string.IsNullOrEmpty(changeValue))
                        {
                            Debug.LogError("有分组数据是空的");
                        }

                        var item = await GAvatarResManager.instance.LoadAsset<GameObject>(changeValue, go.transform);
                        an.AddItem(pgc.exclusionGroup.ToString(), item);
                        prefabs.Add(changeValue, item);
                    }
                }
            }
        }

        an.FixBones();

        foreach (var choosed in changePersets)
        {
            var pgc = AvatarHelper.GetPresetGroupCfgById(choosed);

            for (int i = 0; i < pgc.Perset.Count; i++)
            {
                var preset = AvatarHelper.GetPresetById(pgc.Perset[i]);
                var modifier = AvatarHelper.GetModifierById(preset.Modifier);
                var changeValue = preset.ChangeValue;
                var nodePath = AvatarHelper.GetNodePathById(modifier.nodeId);

                switch (modifier.modifierType)
                {
                    case ECfgAvatarModifierType.ChangePositionX:
                        an.TranslateBone(nodePath, Vector3.right * float.Parse(changeValue));
                        break;
                    case ECfgAvatarModifierType.ChangePositionY:
                        an.TranslateBone(nodePath, Vector3.up * float.Parse(changeValue));
                        break;
                    case ECfgAvatarModifierType.ChangePositionZ:
                        an.TranslateBone(nodePath, Vector3.forward * float.Parse(changeValue));
                        break;
                    case ECfgAvatarModifierType.ChangeRotationX:
                        an.RotateBone(nodePath, Vector3.right * float.Parse(changeValue));
                        break;
                    case ECfgAvatarModifierType.ChangeRotationY:
                        an.RotateBone(nodePath, Vector3.right * float.Parse(changeValue));
                        break;
                    case ECfgAvatarModifierType.ChangeRotationZ:
                        an.RotateBone(nodePath, Vector3.right * float.Parse(changeValue));
                        break;
                    case ECfgAvatarModifierType.ChangeScaleX:
                        an.RotateBone(nodePath, Vector3.right * float.Parse(changeValue));

                        break;
                    case ECfgAvatarModifierType.ChangeScaleY:
                        an.RotateBone(nodePath, Vector3.right * float.Parse(changeValue));

                        break;
                    case ECfgAvatarModifierType.ChangeScaleZ:
                        an.RotateBone(nodePath, Vector3.right * float.Parse(changeValue));

                        break;
                    case ECfgAvatarModifierType.ChangeMatColor:
                        Color c;
                        ColorUtility.TryParseHtmlString(changeValue, out c);
                        an.ChangeMatColor(nodePath, c);
                        break;
                    case ECfgAvatarModifierType.ChangeMatFloat:
                        an.ChangeMatFloat(nodePath, float.Parse(changeValue));
                        break;
                    case ECfgAvatarModifierType.ChangeMatTex:
                        var tex = await GAvatarResManager.instance.LoadTexture(changeValue);
                        an.ChangeMatTex(nodePath, tex);
                        break;
                    case ECfgAvatarModifierType.ChangeTexCenterX:
                        an.ChangeMatTextureCenterX(nodePath, float.Parse(changeValue));
                        break;
                    case ECfgAvatarModifierType.ChangeTexCenterY:
                        an.ChangeMatTextureCenterY(nodePath, float.Parse(changeValue));
                        break;
                    case ECfgAvatarModifierType.ChangeTexSizeX:
                        an.ChangeMatTextureSizeX(nodePath, float.Parse(changeValue));
                        break;
                    case ECfgAvatarModifierType.ChangeTexSizeY:
                        an.ChangeMatTextureSizeY(nodePath, float.Parse(changeValue));
                        break;
                    case ECfgAvatarModifierType.ChangeMatInt:
                        an.ChangeMatInt(nodePath, int.Parse(changeValue));

                        break;
                    case ECfgAvatarModifierType.ChangeAlpha:
                        break;
                    default:
                        break;
                }
            }
        }


        foreach (var item in data.valueBarDatas)
        {
            var barData = item.Value;

            var barCfg = AvatarHelper.GetValueBarCfg(item.Key);
            if (barCfg.nodeId.Count == 0)
            {
                ProcessSymmetricModifiers(barCfg, barData, an);
            }
            else
            {
                ProcessModifiers(barCfg.nodeId, barData.value1, an);
            }
        }

        foreach (var pgc in data.persetGroupComponentDatas)
        {
            if (!changePersets.Contains(pgc.Key)) continue;
            foreach (var item in pgc.Value.valueBarDatas)
            {
                var barData = item.Value;

                var barCfg = AvatarHelper.GetValueBarCfg(item.Key);

                ProcessModifiers(barCfg.nodeId, barData.value1, an);
            }

            foreach (var item in pgc.Value.colerPickerToggleDatas)
            {
                var cfg = AvatarHelper.GetColorPickerCfg(item.Key);
                an.ChangeMatInt(AvatarHelper.GetNodePathById(cfg.toggleNode), item.Value ? 1 : 0);
            }

            foreach (var item in pgc.Value.colerPickerDatas)
            {
                var cfg = AvatarHelper.GetColorPickerCfg(item.Key);
                Color c;
                ColorUtility.TryParseHtmlString(item.Value, out c);
                foreach (var node in cfg.nodeId)
                {
                    var nodePath = AvatarHelper.GetNodePathById(node);

                    an.ChangeMatColor(nodePath, c);
                }
            }
        }

        return go;
    }

    public void UnLoadNAvatar(GameObject go)
    {
        var monoRoot = go.GetComponent<CartoonAvatarRoot>();
        if (monoRoot)
        {
            monoRoot.OnAvatarDestroy();
        }

        Destroy(go);
    }


    private void ProcessSymmetricModifiers(valueBarComponentCfg barCfg, ValueBarData valueData, AvatarNode an)
    {
        ProcessModifiers(barCfg.leftNodeId, valueData.value1, an);
        ProcessModifiers(barCfg.rightNodeId, valueData.value2, an);
    }

    private void ProcessModifiers(List<int> nodeIds, float value, AvatarNode an)
    {
        foreach (var nodeId in nodeIds)
        {
            var modifierCfg = AvatarHelper.GetModifierById(nodeId);
            var nodePath = AvatarHelper.GetNodePathById(modifierCfg.nodeId);
            var realValue = GetRealNum(modifierCfg.maxValue, modifierCfg.minValue, value);
            switch (modifierCfg.modifierType)
            {
                case ECfgAvatarModifierType.ChangePositionX:
                    an.TranslateBone(nodePath, Vector3.right * realValue);
                    break;
                case ECfgAvatarModifierType.ChangePositionY:
                    an.TranslateBone(nodePath, Vector3.up * realValue);
                    break;
                case ECfgAvatarModifierType.ChangePositionZ:
                    an.TranslateBone(nodePath, Vector3.forward * realValue);
                    break;
                case ECfgAvatarModifierType.ChangeRotationX:
                    an.RotateBone(nodePath, Vector3.right * realValue);
                    break;
                case ECfgAvatarModifierType.ChangeRotationY:
                    an.RotateBone(nodePath, Vector3.right * realValue);
                    break;
                case ECfgAvatarModifierType.ChangeRotationZ:
                    an.RotateBone(nodePath, Vector3.right * realValue);
                    break;
                case ECfgAvatarModifierType.ChangeScaleX:
                    an.RotateBone(nodePath, Vector3.right * realValue);

                    break;
                case ECfgAvatarModifierType.ChangeScaleY:
                    an.RotateBone(nodePath, Vector3.right * realValue);

                    break;
                case ECfgAvatarModifierType.ChangeScaleZ:
                    an.RotateBone(nodePath, Vector3.right * realValue);
                    break;
                case ECfgAvatarModifierType.ChangeTexCenterX:
                    an.ChangeMatTextureCenterX(nodePath, realValue);
                    break;
                case ECfgAvatarModifierType.ChangeTexCenterY:
                    an.ChangeMatTextureCenterY(nodePath, realValue);
                    break;
                case ECfgAvatarModifierType.ChangeTexSizeX:
                    an.ChangeMatTextureSizeX(nodePath, realValue);
                    break;
                case ECfgAvatarModifierType.ChangeTexSizeY:
                    an.ChangeMatTextureSizeY(nodePath, realValue);
                    break;
                case ECfgAvatarModifierType.ChangeMatFloat:
                    an.ChangeMatFloat(nodePath, realValue);
                    break;
                default:
                    break;
            }
        }
    }


    public static float GetRealNum(float max, float min, float value)
    {
        float mappedValue = min + (value + 1000) * (max - min) / 2000f;
        return mappedValue;
    }
}