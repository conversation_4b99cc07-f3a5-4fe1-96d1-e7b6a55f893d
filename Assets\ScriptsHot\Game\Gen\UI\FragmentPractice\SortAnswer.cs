/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class SortAnswer : AFragAnswer
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "SortAnswer";
        public static string url => "ui://cmoz5osjp3vduvptcd";

        public GList listOptions;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(SortAnswer));
        }

        public override void ConstructFromXML(XML xml)
        {
            listOptions = GetChildAt(0) as GList;
        }
        public override void Dispose()
        {
            listOptions = null;

            base.Dispose();
        }
    }
}