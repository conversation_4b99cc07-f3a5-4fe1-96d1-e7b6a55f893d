﻿using FairyGUI;

namespace UIBind.ExploreFriends
{
    public partial class ExploreFriendsNewHandPanelUI : BaseUI<ExploreFriendsNewHandPanel>
    {
        public ExploreFriendsNewHandPanelUI(string name) : base(name)
        {
        }

        public override string uiLayer => UILayerConsts.Top;
        
        protected override bool isFullScreen => true;

        protected override void OnInit(GComponent uiCom)
        {
            base.OnInit(uiCom);

            // 注册按钮点击事件
            AddUIEvent(ui.NextBtn.com.onClick, OnNextBtnClick);
            // AddUIEvent(ui.closeBtn.onClick, OnCloseBtnClick);
            // AddUIEvent(ui.closeBtn2.onClick, OnCloseBtnClick);
            // AddUIEvent(ui.btn1.onClick, OnBtn1Click);

        }

        protected override void OnShow()
        {
            base.OnShow();

            ui.ContentTxt1.text = I18N.inst.MoStr("ui_explore_friends_onboarding_title_text");
            ui.ContentTxt2.text = I18N.inst.MoStr("ui_explore_friends_onboarding_subtitle_text");
            ui.NextBtn.title0.text = I18N.inst.MoStr("ui_explore_friends_meet_new_friend_button_text");
        }

        protected override void OnHide()
        {
            base.OnHide();
            
            // 隐藏时的逻辑
            VFDebug.Log("WhatsappGuiderUI 隐藏");
        }

        private void OnNextBtnClick()
        {
            var introducePanelUI = 
                UIManager.instance.GetUI<ExploreFriendsIntroducePanelUI>(UIConsts.ExploreFriendsIntroducePanel);
            introducePanelUI.OpenPreUI = () =>
            {
                UIManager.instance.GetUI(UIConsts.ExploreNewhandPanelUI).Show();
            };
            introducePanelUI.Show();
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void OnCloseBtnClick()
        {
            Hide();
        }
    }
}