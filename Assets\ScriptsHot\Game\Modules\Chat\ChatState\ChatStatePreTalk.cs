using System.Collections.Generic;
using DG.Tweening;
using Msg;
using Msg.basic;
using Msg.dialog_task;
using ScriptsHot.Game.Modules.Chat.ChatCell;
using ScriptsHot.Game.Modules.Scene.Level.Component;
using ScriptsHot.Game.Modules.Setting;
using ScriptsHot.Game.Modules.Task;
using UnityEngine;

public class ChatStatePreTalk : BaseChatMachineState
{
    private int bubbleId = -1;
    private Dictionary<int, BaseChatCell> _chatCells = new Dictionary<int, BaseChatCell>();
    ChatController _chatController => this.owner.GetController<ChatController>(ModelConsts.Chat);
    private ChatModel _chatModel  => owner.GetModel<ChatModel>(ModelConsts.Chat);
    private bool _isShowChat = false;
    private PB_DialogTaskMsgItem _cachePB_DialogTaskMsgItem = null;
    public ChatStatePreTalk() : base(ChatState.PreTalk)
    {
    }
    
    //创建等待服务器返回数据的动画
    public override void OnEnter(params object[] args)
    {
        base.OnEnter(args);
        _isShowChat = this.owner.GetUI(UIConsts.Chat).isShow;
        if (!this.owner.GetUI(UIConsts.Chat).isShow)
            this.owner.GetUI(ModelConsts.Chat).Show();
        else
        {
            ChatUI chatUI = this.owner.GetUI<ChatUI>(UIConsts.Chat);
            chatUI.ClearData();
            AddLoadingCell();
        }
        // this.owner.GetUI<MainHeaderUI>(UIConsts.MainHeader).SetVisible(true, false);
    }

    private void AddLoadingCell()
    {
        PreTalkCell preTalkCell = new PreTalkCell();
        bubbleId = AddCell(preTalkCell, ChatCellType.PreTalk, null);
        _chatCells[bubbleId] = preTalkCell;
        string name = this.owner.GetController<SceneController>(ModelConsts.Scene).
            scene.GetComponent<ChatComponent>().currAvatarName;
        string job = this.owner.GetController<SceneController>(ModelConsts.Scene).
            scene.GetComponent<ChatComponent>().currAvatarJob;
        //_chatController.ShowAvatarName(name, job);
    }

    public override void Update(float interval)
    {
        base.Update(interval);
        if (!_isShowChat)
        {
            _isShowChat = this.owner.GetUI(UIConsts.Chat).isShow;
            if (_isShowChat)
            {
                AddLoadingCell();
                if(_cachePB_DialogTaskMsgItem !=null)
                    ChangeAvatarState(_cachePB_DialogTaskMsgItem);
            }
        }
    }

    public override void OnExit(string nextState)
    {
        base.OnExit(nextState);
        foreach (var item in _chatCells)
        {
            RemoveCell(item.Key,item.Value.cellType);
        }
        _chatCells.Clear();
        _isShowChat = false;
        _cachePB_DialogTaskMsgItem = null;
    }
    
    public override bool OnHandleMsg(MsgData msg)
    {
        bool isAccept = DealHandleMsgData(msg);
        return isAccept;
    }


    private bool DealHandleMsgData(MsgData msg)
    {
        if (msg.msgId == (short)GameMSGID.SC_CreateDialogTaskAck_ID)
        {
            PB_CreateDialogTaskResp item = (PB_CreateDialogTaskResp)msg.data;
            _chatModel.SetDialogId(item.dialog_id);
            if(_chatModel.chatMode == PB_DialogMode.Tutor)
                _chatModel.SetTutorIsPure(item.is_pure);
            _chatModel.SetTaskRecordId(item.task_record_id);
            _chatModel.SetFirsrTaskState(item.is_notice);
            _chatModel.SetclickWordGuide(item.is_click_word_guide);
            _chatModel.SetUserAssistLevel(item.user_assist_level);
            _chatModel.SetAvatarId(item.avatar_info.avatar_id);
            _chatController.ShowAvatarName(item.avatar_info.avatar_name, item.avatar_info.avatar_job);
            _chatController.ShowTopic(item.topic_info);
            PushInitStarInfo(item.star_info);
            PushTaskStepContent(item.task_step_content_data);
            _chatController.SendDialogTaskCacheMsg();
            Notifier.instance.SendNotification(NotifyConsts.RefreshAutoHelpEvent);//刷新Auto状态
            float deltaTime = TimeExt.currTime - _chatController.TestCreateDialogTime;
            VFDebug.Log(
                $"DialogueSlow: dialogid:{_chatModel.dialogId}, time:{deltaTime}, timestamp:{TimeExt.currTime}, type: 收到服务器创建对话的数据耗时, roundId:{_chatModel.curRoundId}, taskId:{_chatController.curTaskId}");
           
            //chatController.ChangeState(ChatState.Avatar,item);
            _chatController.TestCreateDialogTime = TimeExt.currTime;
            if (_chatModel.chatMode == PB_DialogMode.Career)
            {
                this.owner.GetController<TaskController>(ModelConsts.Task).ShowFreeTalkProgressPanel(item.task_step_content_data.dialog_progress_info);
            }
            this.owner.GetModel<SettingModel>(ModelConsts.Setting).SetFreeTalkDifficultyLevel(item.user_assist_level);
            
            //新埋点：任务开启
            DataDotCutDialogueStart dot = new DataDotCutDialogueStart();
            dot.Dialogue_id = DataDotMgr.GetDialogId();
            DataDotMgr.Collect(dot);
            return true;
        }
        else if(msg.msgId == (short)GameMSGID.SC_DialogTaskMsgHandleAck_ID || msg.msgId == (short)GameMSGID.SC_GetDialogTaskNextRoundMsgAck_ID)
        {
            if (msg.data is PB_DialogTaskMsgItem)
            {
                PB_DialogTaskMsgItem item = (PB_DialogTaskMsgItem)msg.data; 
                if (item.msg_owner == PB_MsgBelong.Avatar)
                {
                    if (_isShowChat)
                        ChangeAvatarState(item);
                    else
                        _cachePB_DialogTaskMsgItem = item;
                    
                    return true;
                }
            }
        }
        return false;
    }

    private void ChangeAvatarState(PB_DialogTaskMsgItem item)
    {
        // _chatModel.SetChatMode(PB_DialogMode.Tutor);
        if (_chatModel.chatMode != PB_DialogMode.Challenge && _chatModel.chatMode != PB_DialogMode.Career)
        {
            this.owner.GetController<TaskController>(ModelConsts.Task).ShowAllTaskUI(_chatModel.chatMode != PB_DialogMode.OnBoarding, _chatModel.chatMode);
        }
        _chatController.ChangeState(ChatState.Avatar,item);
    }

    public override void OnHandleError(params object[] args)
    {
        base.OnHandleError();

    }
}