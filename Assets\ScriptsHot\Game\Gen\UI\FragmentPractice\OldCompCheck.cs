/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class OldCompCheck : ExtendedComponent
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "OldCompCheck";
        public static string url => "ui://cmoz5osjsnlj3s";

        public Controller state;
        public Controller jump;
        public CompCheckContent compCheckContent;
        public BtnJump btnJump;
        public OldBtnBottom btnCheck;
        public GGroup grpCheck;
        public GGroup grp;
        public Transition show;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(OldCompCheck));
        }

        public override void ConstructFromXML(XML xml)
        {
            state = GetControllerAt(0);
            jump = GetControllerAt(1);
            compCheckContent = new CompCheckContent();
            compCheckContent.Construct(GetChildAt(0).asCom);
            btnJump = new BtnJump();
            btnJump.Construct(GetChildAt(1).asCom);
            btnCheck = new OldBtnBottom();
            btnCheck.Construct(GetChildAt(3).asCom);
            grpCheck = GetChildAt(4) as GGroup;
            grp = GetChildAt(5) as GGroup;
            show = GetTransitionAt(0);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();

            state = null;
            jump = null;
            compCheckContent.Dispose();
            compCheckContent = null;
            btnJump.Dispose();
            btnJump = null;
            btnCheck.Dispose();
            btnCheck = null;
            grpCheck = null;
            grp = null;
            show = null;

            base.Dispose();
        }
    }
}