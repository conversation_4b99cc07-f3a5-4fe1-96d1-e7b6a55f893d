/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class CompTag : ExtendedComponent
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "CompTag";
        public static string url => "ui://cmoz5osjds5quvptct";

        public Controller state;
        public GTextField tfText;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(CompTag));
        }

        public override void ConstructFromXML(XML xml)
        {
            state = GetControllerAt(0);
            tfText = GetChildAt(2) as GTextField;

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();

            state = null;
            tfText = null;

            base.Dispose();
        }
    }
}