/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Main
{
    public partial class HomepagePanel : UIBindT
    {
        public override string pkgName => "Main";
        public override string comName => "HomepagePanel";

        public Controller avoidDeviceNotchType;
        public MainPathBackGround imgBG;
        public GComponent mainPath;
        public GButton btnToTop;
        public HomepageTimeBanner homepageTimeBanner;
        public HomepageBanner homepageBanner;
        public GComponent headBar;
        public Transition Out;
        public Transition Reset;
        public Transition BottomButtonsIn;
        public Transition BottomButtonsOut;
        public Transition arrowIn;
        public Transition arrowOut;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            avoidDeviceNotchType = com.GetControllerAt(0);
            imgBG = (MainPathBackGround)com.GetChildAt(0);
            mainPath = (GComponent)com.GetChildAt(1);
            btnToTop = (GButton)com.GetChildAt(2);
            homepageTimeBanner = new HomepageTimeBanner();
            homepageTimeBanner.Construct(com.GetChildAt(3).asCom);
            homepageBanner = new HomepageBanner();
            homepageBanner.Construct(com.GetChildAt(4).asCom);
            headBar = (GComponent)com.GetChildAt(6);
            Out = com.GetTransitionAt(0);
            Reset = com.GetTransitionAt(1);
            BottomButtonsIn = com.GetTransitionAt(2);
            BottomButtonsOut = com.GetTransitionAt(3);
            arrowIn = com.GetTransitionAt(4);
            arrowOut = com.GetTransitionAt(5);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            avoidDeviceNotchType = null;
            imgBG = null;
            mainPath = null;
            btnToTop = null;
            homepageTimeBanner.Dispose();
            homepageTimeBanner = null;
            homepageBanner.Dispose();
            homepageBanner = null;
            headBar = null;
            Out = null;
            Reset = null;
            BottomButtonsIn = null;
            BottomButtonsOut = null;
            arrowIn = null;
            arrowOut = null;
        }
    }
}