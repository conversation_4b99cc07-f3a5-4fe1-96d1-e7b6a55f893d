/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.MainPath3D
{
    public partial class CutItem : UIBindT
    {
        public override string pkgName => "MainPath3D";
        public override string comName => "CutItem";

        public CompPartTitle compTitle;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            compTitle = new CompPartTitle();
            compTitle.Construct(com.GetChildAt(1).asCom);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            compTitle.Dispose();
            compTitle = null;
        }
    }
}