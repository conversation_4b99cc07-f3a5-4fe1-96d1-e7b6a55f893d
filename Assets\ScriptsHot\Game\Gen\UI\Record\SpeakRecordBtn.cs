/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.Record
{
    public partial class SpeakRecordBtn : RecordBtnBase
    {
        public static string pkgName => "Record";
        public static string comName => "SpeakRecordBtn";
        public static string url => "ui://svsori08t63txxx6b";

        public Controller ctlStatus;
        public GLoader3D btnSpine;
        public GButton btnSpeakEndVad;
        public GButton btnSpeakCancel;
        public GTextField tfSpeakcansay;
        public GGroup grpTips2;
        public GGroup speakRecord;
        public GButton btnSpeakReapeatBtn;
        public GTextField tfSpeakrepeat;
        public GGroup grpTips1;
        public GGroup speakRepeat;
        public GButton btnSpeakMicBtn;
        public GTextField tfSpeakmic;
        public GGroup grpTips3;
        public GGroup speakMic;
        public GTextField tfComplete;
        public GGroup grpTips4;
        public GButton btnSmallRepeat;
        public GGraph btnContinue;
        public GGroup speakContinue;
        public Transition Out;
        public Transition Reset;
        public Transition showContinue;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(SpeakRecordBtn));
        }

        public override void ConstructFromXML(XML xml)
        {
            ctlStatus = GetControllerAt(0);
            btnSpine = GetChildAt(0) as GLoader3D;
            btnSpeakEndVad = GetChildAt(1) as GButton;
            btnSpeakCancel = GetChildAt(2) as GButton;
            tfSpeakcansay = GetChildAt(4) as GTextField;
            grpTips2 = GetChildAt(5) as GGroup;
            speakRecord = GetChildAt(6) as GGroup;
            btnSpeakReapeatBtn = GetChildAt(7) as GButton;
            tfSpeakrepeat = GetChildAt(9) as GTextField;
            grpTips1 = GetChildAt(10) as GGroup;
            speakRepeat = GetChildAt(11) as GGroup;
            btnSpeakMicBtn = GetChildAt(12) as GButton;
            tfSpeakmic = GetChildAt(14) as GTextField;
            grpTips3 = GetChildAt(15) as GGroup;
            speakMic = GetChildAt(16) as GGroup;
            tfComplete = GetChildAt(18) as GTextField;
            grpTips4 = GetChildAt(19) as GGroup;
            btnSmallRepeat = GetChildAt(21) as GButton;
            btnContinue = GetChildAt(22) as GGraph;
            speakContinue = GetChildAt(23) as GGroup;
            Out = GetTransitionAt(0);
            Reset = GetTransitionAt(1);
            showContinue = GetTransitionAt(2);

            SetMultiLanguageInChildren();
        }
        public override void Dispose()
        {
            ctlStatus = null;
            btnSpine = null;
            btnSpeakEndVad = null;
            btnSpeakCancel = null;
            tfSpeakcansay = null;
            grpTips2 = null;
            speakRecord = null;
            btnSpeakReapeatBtn = null;
            tfSpeakrepeat = null;
            grpTips1 = null;
            speakRepeat = null;
            btnSpeakMicBtn = null;
            tfSpeakmic = null;
            grpTips3 = null;
            speakMic = null;
            tfComplete = null;
            grpTips4 = null;
            btnSmallRepeat = null;
            btnContinue = null;
            speakContinue = null;
            Out = null;
            Reset = null;
            showContinue = null;

            base.Dispose();
        }
        public void SetMultiLanguageInChildren()
        {
            this.tfSpeakcansay.SetKey("ui_review_cansay");  // "Now you can say..."
            this.tfSpeakrepeat.SetKey("ui_review_tyr_again");  // "Try again!"
            this.tfSpeakmic.SetKey("ui_record_tap_speak");  // "Try again!"
            this.tfComplete.SetKey("ui_record_tap_done");  // "Try again!"
        }
    }
}