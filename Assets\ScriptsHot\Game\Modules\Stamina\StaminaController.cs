﻿
using System;
using System.Collections.Generic;
using System.Linq;
using FairyGUI;
using Msg.ai_recommend;
using Msg.basic;
using Msg.core;
using Msg.dialog_task;
using Msg.economic;
using Msg.incentive;
using Msg.recommend;
using Msg.task_process;
using ScriptsHot.Game.Modules.Managers;
using ScriptsHot.Game.Modules.Rank.UI;
using ScriptsHot.Game.Modules.Stamina;
using UnityEngine;


public class StaminaController : BaseController
{
    public StaminaController() : base(ModelConsts.Stamina) { }

    private StaminaModel _staminaModel => GetModel<StaminaModel>(ModelConsts.Stamina);
    

    public override void OnUIInit()
    {
        RegisterUI(new StaminaBottomPurchaseUI(UIConsts.StaminaBottomPurchaseUI));
        
        // 获取体力信息
        MsgManager.instance.RegisterCallBack<SC_PurchaseStaminaAck>(OnPurchaseStaminaAck);
    }

    public override void OnInit()
    {
        RegisterModel(new StaminaModel());
    }

    public override void OnEnterGame()
    {
        base.OnEnterGame();
        // 25/3/26 hb说暂时先不上
        // AbTestUtil.ReqAbTestValue("max_stamina", msg =>
        // {
        //     if (int.TryParse(msg.data.value, out int maxStamina))
        //     {
        //         _staminaModel.SetMaxStamina(maxStamina);
        //     }
        // });
    }

    public void ShowStaminaPurchaseBottom()
    {
        //没了 430版本
        //GetUI(UIConsts.StaminaBottomPurchaseUI).Show();
    }

    public void PurchaseStamina()
    {
        MsgManager.instance.SendMsg(new CS_PurchaseStaminaReq());
        // GetUI(UIConsts.StaminaPurchaseUI).Hide();
        // GetUI(UIConsts.StaminaBottomPurchaseUI).Hide();
        // GetUI<CommonGetDiamondUI>(UIConsts.CommonGetDiamond).Open(CommonGetDiamondType.More, 50, null, "12006");
    }

    private void OnPurchaseStaminaAck(SC_PurchaseStaminaAck msg)
    {
        if (msg.code == 0)
        {
            GetUI(UIConsts.StaminaBottomPurchaseUI).Hide();
            GetUI<CommonGetDiamondUI>(UIConsts.CommonGetDiamond).Open(CommonGetDiamondType.More, (int)(msg.data.stamina/20), null, "i0004");
            GetModel<CurrencyModel>(ModelConsts.CurrencyController).SetEconomicInfo(EconomicType.Diamond, msg.data.total_diamond);
            _staminaModel.SetStamina(msg.data.total_stamina);

            // var mainHeader = GetUI<MainHeaderUI>(UIConsts.MainHeader);
            //
            // if (mainHeader.isShow)
            //     mainHeader.RefershHeadBar(false,true);

            //todo0 mtHP内的 center没有刷新

            var centerHomeUI = GetUI<CenterHomeUI>(UIConsts.CenterHome);
            if (centerHomeUI.isShow)
                SendNotification(NotifyConsts.MainHeadRefreshEvent);
            //刷新前端体力值
            GetController<HomepageController>(ModelConsts.Homepage).ReqGetIncentiveData();
        }
    }

    public bool IsEnough(int num)
    {
        if (_staminaModel.staminaData != null)
        {
            var curNum = _staminaModel.staminaData.stamina;
            return num <= curNum;
        }
        else
        {
            VFDebug.LogError("_staminaModel.staminaData =null");
            return false;
        }

    }

    public void DrawHearts(GObject cmp, int cnt)
    {
        var maxStamina = GetModel<StaminaModel>(ModelConsts.Stamina).maxStamina;
        var maxHearts = (int)(maxStamina / 20);
        for (int i = 0; i < 5; i++)
        {
            var cmpName = $"comHeart{i + 1}";
            if (i < maxHearts)
            {
                if (i == 0)
                {
                    cmp.asCom.GetChild(cmpName).asCom.GetController("state").selectedPage = cnt > 0 ? "full" : "half";
                }
                else
                {
                    cmp.asCom.GetChild(cmpName).asCom.GetController("state").selectedPage = cnt > i ? "full" : cnt > i - 1 ? "half" : "empty";
                }

                cmp.asCom.GetChild(cmpName).asCom.visible = true;
            }
            else
            {
                cmp.asCom.GetChild(cmpName).asCom.visible = false;
            }
        }
    }
}
