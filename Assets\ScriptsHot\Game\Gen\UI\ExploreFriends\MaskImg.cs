/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreFriends
{
    public partial class MaskImg : UIBindT
    {
        public override string pkgName => "ExploreFriends";
        public override string comName => "MaskImg";

        public GGraph selectedImg;
        public GGraph img;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            selectedImg = (GGraph)com.GetChildAt(0);
            img = (GGraph)com.GetChildAt(1);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            selectedImg = null;
            img = null;
        }
    }
}