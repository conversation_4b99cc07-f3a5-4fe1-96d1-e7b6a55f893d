/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.Login
{
    public partial class InputCom : GComponent
    {
        public static string pkgName => "Login";
        public static string comName => "InputCom";
        public static string url => "ui://gb3tqioj9xrs6n";

        public GTextInput input;
        public GButton btnClear;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(InputCom));
        }

        public override void ConstructFromXML(XML xml)
        {
            input = GetChildAt(1) as GTextInput;
            btnClear = GetChildAt(2) as GButton;
        }
        public override void Dispose()
        {
            input = null;
            btnClear = null;

            base.Dispose();
        }
    }
}