/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreFriends
{
    public partial class IntroduceItem : UIBindT
    {
        public override string pkgName => "ExploreFriends";
        public override string comName => "IntroduceItem";

        public Controller tp;
        public Controller IsRare;
        public GLoader iconLoader;
        public GTextField ContentTxt1;
        public GTextField ContentTxt2;
        public GTextField Boutique;
        public GGroup Node;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            tp = com.GetControllerAt(0);
            IsRare = com.GetControllerAt(1);
            iconLoader = (GLoader)com.GetChildAt(0);
            ContentTxt1 = (GTextField)com.GetChildAt(1);
            ContentTxt2 = (GTextField)com.GetChildAt(2);
            Boutique = (GTextField)com.GetChildAt(4);
            Node = (GGroup)com.GetChildAt(5);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            tp = null;
            IsRare = null;
            iconLoader = null;
            ContentTxt1 = null;
            ContentTxt2 = null;
            Boutique = null;
            Node = null;
        }
    }
}