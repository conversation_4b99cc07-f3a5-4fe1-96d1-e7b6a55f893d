// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/explore/recommend.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.explore {

  /// <summary>Holder for reflection information generated from protobuf/explore/recommend.proto</summary>
  public static partial class RecommendReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/explore/recommend.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static RecommendReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CiBwcm90b2J1Zi9leHBsb3JlL3JlY29tbWVuZC5wcm90bxobcHJvdG9idWYv",
            "ZXhwbG9yZS9iYXNlLnByb3RvGhtwcm90b2J1Zi9iYXNpYy9kaWFsb2cucHJv",
            "dG8aG3Byb3RvYnVmL2Jhc2ljL2NvbW1vbi5wcm90bxodcHJvdG9idWYvZXhw",
            "bG9yZS9kaWFsb2cucHJvdG8aInByb3RvYnVmL2luY2VudGl2ZS9pbmNlbnRp",
            "dmUucHJvdG8aJHByb3RvYnVmL2V4cGxvcmUvbWlzc2lvbl9zdG9yeS5wcm90",
            "byIwChZDU19HZXRSZWNvbW1lbmRMaXN0UmVxEhYKDmlkZW1wb3RlbnRTaWdu",
            "GAEgASgDIn4KF1NDX0dldFJlY29tbWVuZExpc3RSZXNwEiEKBGNvZGUYASAB",
            "KA4yEy5QQl9FeHBsb3JlX0JpekNvZGUSFgoOaWRlbXBvdGVudFNpZ24YAiAB",
            "KAMSKAoPcHJlbG9hZERhdGFMaXN0GAMgAygLMg8uUEJfUHJlbG9hZERhdGEi",
            "oQEKDlBCX1ByZWxvYWREYXRhEhAKCGVudGl0eUlkGAEgASgDEjMKCmVudGl0",
            "eVR5cGUYAiABKA4yHy5QQl9FeHBsb3JlX1JlY29tbWVuZEVudGl0eVR5cGUS",
            "OgoVZGlhbG9nVGFza1ByZWxvYWREYXRhGAMgASgLMhkuUEJfRGlhbG9nVGFz",
            "a1ByZWxvYWREYXRhSABCDAoKZW50aXR5RGF0YSLiAQoYUEJfRGlhbG9nVGFz",
            "a1ByZWxvYWREYXRhEg4KBnRhc2tJZBgBIAEoAxIiCgpkaWFsb2dNb2RlGAIg",
            "ASgOMg4uUEJfRGlhbG9nTW9kZRIfCgZhdmF0YXIYAyABKAsyDy5QQl9UYXNr",
            "X0F2YXRhchIdCgVzY2VuZRgEIAEoCzIOLlBCX1Rhc2tfU2NlbmUSHwoGZGV0",
            "YWlsGAUgASgLMg8uUEJfVGFza19EZXRhaWwSMQoOZmlyc3RSb3VuZERhdGEY",
            "BiABKAsyGS5QQl9UYXNrX0ZpcnN0X1JvdW5kX0RhdGEijgEKDlBCX1Rhc2tf",
            "QXZhdGFyEhAKCGF2YXRhcklkGAEgASgDEhIKCmhlYWRQaWNVcmwYAiABKAkS",
            "LAoLaGVhZEJnQ29sb3IYAyABKA4yFy5QQl9CYWNrZ3JvdW5kQ29sb3JFbnVt",
            "EgwKBG5hbWUYBCABKAkSDAoEcm9sZRgFIAEoCRIMCgRpczNEGAYgASgIIlsK",
            "DVBCX1Rhc2tfU2NlbmUSEAoIYmdQaWNUYWcYASABKAkSEAoIYmdQaWNVcmwY",
            "AiABKAkSEgoKYmdWb2ljZVRhZxgDIAEoCRISCgpiZ1ZvaWNlVXJsGAQgASgJ",
            "IokBCg5QQl9UYXNrX0RldGFpbBIMCgRkZXNjGAEgASgJEioKCGRlc2NUeXBl",
            "GAIgASgOMhguUEJfRXhwbG9yZV9UYXNrRGVzY1R5cGUSMwoOcm9sZVBsYXlE",
            "ZXRhaWwYAyABKAsyGS5QQl9UYXNrX1JvbGVfUGxheV9EZXRhaWxIAEIICgZk",
            "ZXRhaWwiPAoYUEJfVGFza19Sb2xlX1BsYXlfRGV0YWlsEiAKBWdvYWxzGAEg",
            "AygLMhEuUEJfVGFza19Hb2FsSXRlbSJVChBQQl9UYXNrX0dvYWxJdGVtEg4K",
            "BmdvYWxJZBgBIAEoAxIKCgJubxgCIAEoBRIQCghnb2FsRGVzYxgDIAEoCRIT",
            "Cgtpc0NvbXBsZXRlZBgEIAEoCCKAAwoYUEJfVGFza19GaXJzdF9Sb3VuZF9E",
            "YXRhEhEKCXJlcGx5VGV4dBgBIAEoCRIaChJyZXBseVRyYW5zbGF0ZVRleHQY",
            "AiABKAkSNAoKcmVwbHlBdWRpbxgDIAEoCzIgLlBCX0V4cGxvcmVfRGlhbG9n",
            "QXVkaW9Eb3duRnJhbWUSEwoLZXhhbXBsZVRleHQYBCABKAkSHAoUZXhhbXBs",
            "ZVRyYW5zbGF0ZVRleHQYBSABKAkSNgoMZXhhbXBsZUF1ZGlvGAYgASgLMiAu",
            "UEJfRXhwbG9yZV9EaWFsb2dBdWRpb0Rvd25GcmFtZRIVCg1yZXBseUJ1YmJs",
            "ZUlkGAcgASgJEhcKD2V4YW1wbGVCdWJibGVJZBgIIAEoCRI4ChVlbW90aW9u",
            "QW5hbHlzaXNSZXN1bHQYCSABKAsyGS5QQl9FbW90aW9uQW5hbHlzaXNSZXN1",
            "bHQSEgoKYWR2aWNlVGV4dBgKIAEoCRIWCg5hZHZpY2VCdWJibGVJZBgLIAEo",
            "CSIyChhDU19HZXRSZWNvbW1lbmRMaXN0VjJSZXESFgoOaWRlbXBvdGVudFNp",
            "Z24YASABKAMiigEKGVNDX0dldFJlY29tbWVuZExpc3RWMlJlc3ASIQoEY29k",
            "ZRgBIAEoDjITLlBCX0V4cGxvcmVfQml6Q29kZRIWCg5pZGVtcG90ZW50U2ln",
            "bhgCIAEoAxIyChRzdG9yeVByZWxvYWREYXRhTGlzdBgDIAMoCzIULlBCX1N0",
            "b3J5UHJlbG9hZERhdGEiqwIKE1BCX1N0b3J5UHJlbG9hZERhdGESDwoHc3Rv",
            "cnlJZBgBIAEoAxIOCgZ0YXNrSWQYAiABKAMSFAoMdGFza1JlY29yZElkGAMg",
            "ASgDEiIKCmRpYWxvZ01vZGUYBCABKA4yDi5QQl9EaWFsb2dNb2RlEiAKBmF2",
            "YXRhchgFIAEoCzIQLlBCX1N0b3J5X0F2YXRhchIeCgVzY2VuZRgGIAEoCzIP",
            "LlBCX1N0b3J5X1NjZW5lEiAKBmRldGFpbBgHIAEoCzIQLlBCX1N0b3J5X0Rl",
            "dGFpbBIyCg5maXJzdFJvdW5kRGF0YRgIIAEoCzIaLlBCX1N0b3J5X0ZpcnN0",
            "X1JvdW5kX0RhdGESIQoJdGFza0ludHJvGAkgAygLMg4uUEJfVGFza19JbnRy",
            "byIxCg9QQl9TdG9yeV9BdmF0YXISEAoIYXZhdGFySWQYASABKAMSDAoEbmFt",
            "ZRgCIAEoCSJcCg5QQl9TdG9yeV9TY2VuZRIQCghiZ1BpY1RhZxgBIAEoCRIQ",
            "CghiZ1BpY1VybBgCIAEoCRISCgpiZ1ZvaWNlVGFnGAMgASgJEhIKCmJnVm9p",
            "Y2VVcmwYBCABKAkihwEKD1BCX1N0b3J5X0RldGFpbBIRCgltaXNzaW9uSWQY",
            "ASABKAMSEQoJQ2VmckxldmVsGAIgASgJEg8KB3N0b3J5SWQYAyABKAMSEgoK",
            "c3RvcnlUaXRsZRgEIAEoCRIpCg1zdG9yeVByb2dyZXNzGAUgASgLMhIuUEJf",
            "U3RvcnlfUHJvZ3Jlc3MicQoRUEJfU3RvcnlfUHJvZ3Jlc3MSGAoQY3VycmVu",
            "dFRhc2tUaXRsZRgBIAEoCRIXCg9jdXJyZW50U3RlcERlc2MYAiABKAkSFgoO",
            "ZmluaXNoZWRTdGVwTm8YAyABKAUSEQoJc3RlcFRvdGFsGAQgASgFIukCChlQ",
            "Ql9TdG9yeV9GaXJzdF9Sb3VuZF9EYXRhEhEKCXJlcGx5VGV4dBgBIAEoCRIa",
            "ChJyZXBseVRyYW5zbGF0ZVRleHQYAiABKAkSFQoNcmVwbHlCdWJibGVJZBgD",
            "IAEoCRI+CgpyZXBseUF1ZGlvGAQgASgLMiouUEJfRXhwbG9yZV9NaXNzaW9u",
            "U3RvcnlDaGF0QXVkaW9Eb3duRnJhbWUSOAoVZW1vdGlvbkFuYWx5c2lzUmVz",
            "dWx0GAUgASgLMhkuUEJfRW1vdGlvbkFuYWx5c2lzUmVzdWx0EhMKC2V4YW1w",
            "bGVUZXh0GAYgASgJEhwKFGV4YW1wbGVUcmFuc2xhdGVUZXh0GAcgASgJEhcK",
            "D2V4YW1wbGVCdWJibGVJZBgIIAEoCRISCgphZHZpY2VUZXh0GAkgASgJEhYK",
            "DmFkdmljZUJ1YmJsZUlkGAogASgJEhQKDGlzU2V0dGxlbWVudBgLIAEoCCJ0",
            "Cg1QQl9UYXNrX0ludHJvEjkKBWF1ZGlvGAEgASgLMiouUEJfRXhwbG9yZV9N",
            "aXNzaW9uU3RvcnlDaGF0QXVkaW9Eb3duRnJhbWUSEQoJaW50cm9UZXh0GAIg",
            "ASgJEhUKDWZpcnN0TGFuZ1RleHQYAyABKAkiIgogQ1NfR2V0VXNlckhpc3Rv",
            "cnlQcm9ncmVzc0xpc3RSZXEiegohU0NfR2V0VXNlckhpc3RvcnlQcm9ncmVz",
            "c0xpc3RSZXNwEiEKBGNvZGUYASABKA4yEy5QQl9FeHBsb3JlX0JpekNvZGUS",
            "MgoUc3RvcnlQcmVsb2FkRGF0YUxpc3QYAiADKAsyFC5QQl9TdG9yeVByZWxv",
            "YWREYXRhIl4KEVBCX1JlY29tbWVuZFVwTXNnEj0KEHVzZXJTd2l0Y2hFbnRp",
            "dHkYASABKAsyIS5QQl9FeHBsb3JlX1JlY29tbWVuZFN3aXRjaEVudGl0eUgA",
            "QgoKCHVwQml6TXNnImkKIFBCX0V4cGxvcmVfUmVjb21tZW5kU3dpdGNoRW50",
            "aXR5EhAKCGVudGl0eUlkGAEgASgDEjMKCmVudGl0eVR5cGUYAiABKA4yHy5Q",
            "Ql9FeHBsb3JlX1JlY29tbWVuZEVudGl0eVR5cGUiLQoYU0NfUmVjb21tZW5k",
            "U3dpdGNoRW50aXR5EhEKCXRpbWVzdGFtcBgBIAEoAyLLAQouUEJfTWlzc2lv",
            "blN0b3J5Q2hhdE5leHRFbnRpdHlJbmZvRm9yU2V0dGxlbWVudBJCCg5uZXh0",
            "RW50aXR5VHlwZRgBIAEoDjIqLlBCX0V4cGxvcmVfTWlzc2lvblN0b3J5Q2hh",
            "dE5leHRFbnRpdHlUeXBlEhIKCnN0b3J5VGl0bGUYAiABKAkSEQoJdGFza1Rp",
            "dGxlGAMgASgJEi4KEHN0b3J5UHJlbG9hZERhdGEYBCABKAsyFC5QQl9TdG9y",
            "eVByZWxvYWREYXRhIlkKMFBCX01pc3Npb25TdG9yeUNoYXRGaW5pc2hlZFRh",
            "c2tJbmZvRm9yU2V0dGxlbWVudBISCgpzdG9yeVRpdGxlGAEgASgJEhEKCXRh",
            "c2tUaXRsZRgCIAEoCSKiAwonU0NfTWlzc2lvblN0b3J5Q2hhdERvd25Nc2dG",
            "b3JTZXR0bGVtZW50EiEKBGNvZGUYASABKA4yEy5QQl9FeHBsb3JlX0JpekNv",
            "ZGUSQwoKY29tbW9uRGF0YRgCIAEoCzIvLlBCX01pc3Npb25TdG9yeUNoYXRD",
            "b21tb25EYXRhRmllbGRGb3JEYXRhQ2xhc3MSSwoQZmluaXNoZWRUYXNrSW5m",
            "bxgDIAEoCzIxLlBCX01pc3Npb25TdG9yeUNoYXRGaW5pc2hlZFRhc2tJbmZv",
            "Rm9yU2V0dGxlbWVudBJHCg5uZXh0RW50aXR5SW5mbxgEIAEoCzIvLlBCX01p",
            "c3Npb25TdG9yeUNoYXROZXh0RW50aXR5SW5mb0ZvclNldHRsZW1lbnQSLwoM",
            "Y2hlY2tpbl9pdGVtGAUgASgLMhkuUEJfQ2hlY2tpbkl0ZW1Gb3JFeHBsb3Jl",
            "EhYKDnRhc2tTcGVlY2hUaW1lGAYgASgFEhkKEWV4Y2VlZFVzZXJQZXJjZW50",
            "GAcgASgFEhUKDXVzZXJDZWZyTGV2ZWwYCCABKAkqUAoXUEJfRXhwbG9yZV9U",
            "YXNrRGVzY1R5cGUSEgoORU9fVERUX1VOS05PV04QABIQCgxFT19URFRfVE9Q",
            "SUMQARIPCgtFT19URFRfR09BTBACKlsKHlBCX0V4cGxvcmVfUmVjb21tZW5k",
            "RW50aXR5VHlwZRIRCg1FT19SRV9VTktOT1dOEAASFQoRRU9fUkVfRElBTE9H",
            "X1RBU0sQARIPCgtFT19SRV9TVE9SWRACKm4KKVBCX0V4cGxvcmVfTWlzc2lv",
            "blN0b3J5Q2hhdE5leHRFbnRpdHlUeXBlEhYKEkVPX01TQ19ORVRfVU5LTk9X",
            "ThAAEhMKD0VPX01TQ19ORVRfVEFTSxABEhQKEEVPX01TQ19ORVRfU1RPUlkQ",
            "AkIqWhp2Zl9wcm90b2J1Zi9zZXJ2ZXIvZXhwbG9yZaoCC01zZy5leHBsb3Jl",
            "YgZwcm90bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Msg.explore.BaseReflection.Descriptor, global::Msg.basic.DialogReflection.Descriptor, global::Msg.basic.CommonReflection.Descriptor, global::Msg.explore.DialogReflection.Descriptor, global::Msg.incentive.IncentiveReflection.Descriptor, global::Msg.explore.MissionStoryReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Msg.explore.PB_Explore_TaskDescType), typeof(global::Msg.explore.PB_Explore_RecommendEntityType), typeof(global::Msg.explore.PB_Explore_MissionStoryChatNextEntityType), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.CS_GetRecommendListReq), global::Msg.explore.CS_GetRecommendListReq.Parser, new[]{ "idempotentSign" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_GetRecommendListResp), global::Msg.explore.SC_GetRecommendListResp.Parser, new[]{ "code", "idempotentSign", "preloadDataList" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_PreloadData), global::Msg.explore.PB_PreloadData.Parser, new[]{ "entityId", "entityType", "dialogTaskPreloadData" }, new[]{ "entityData" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_DialogTaskPreloadData), global::Msg.explore.PB_DialogTaskPreloadData.Parser, new[]{ "taskId", "dialogMode", "avatar", "scene", "detail", "firstRoundData" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Task_Avatar), global::Msg.explore.PB_Task_Avatar.Parser, new[]{ "avatarId", "headPicUrl", "headBgColor", "name", "role", "is3D" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Task_Scene), global::Msg.explore.PB_Task_Scene.Parser, new[]{ "bgPicTag", "bgPicUrl", "bgVoiceTag", "bgVoiceUrl" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Task_Detail), global::Msg.explore.PB_Task_Detail.Parser, new[]{ "desc", "descType", "rolePlayDetail" }, new[]{ "detail" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Task_Role_Play_Detail), global::Msg.explore.PB_Task_Role_Play_Detail.Parser, new[]{ "goals" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Task_GoalItem), global::Msg.explore.PB_Task_GoalItem.Parser, new[]{ "goalId", "no", "goalDesc", "isCompleted" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Task_First_Round_Data), global::Msg.explore.PB_Task_First_Round_Data.Parser, new[]{ "replyText", "replyTranslateText", "replyAudio", "exampleText", "exampleTranslateText", "exampleAudio", "replyBubbleId", "exampleBubbleId", "emotionAnalysisResult", "adviceText", "adviceBubbleId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.CS_GetRecommendListV2Req), global::Msg.explore.CS_GetRecommendListV2Req.Parser, new[]{ "idempotentSign" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_GetRecommendListV2Resp), global::Msg.explore.SC_GetRecommendListV2Resp.Parser, new[]{ "code", "idempotentSign", "storyPreloadDataList" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_StoryPreloadData), global::Msg.explore.PB_StoryPreloadData.Parser, new[]{ "storyId", "taskId", "taskRecordId", "dialogMode", "avatar", "scene", "detail", "firstRoundData", "taskIntro" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Story_Avatar), global::Msg.explore.PB_Story_Avatar.Parser, new[]{ "avatarId", "name" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Story_Scene), global::Msg.explore.PB_Story_Scene.Parser, new[]{ "bgPicTag", "bgPicUrl", "bgVoiceTag", "bgVoiceUrl" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Story_Detail), global::Msg.explore.PB_Story_Detail.Parser, new[]{ "missionId", "CefrLevel", "storyId", "storyTitle", "storyProgress" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Story_Progress), global::Msg.explore.PB_Story_Progress.Parser, new[]{ "currentTaskTitle", "currentStepDesc", "finishedStepNo", "stepTotal" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Story_First_Round_Data), global::Msg.explore.PB_Story_First_Round_Data.Parser, new[]{ "replyText", "replyTranslateText", "replyBubbleId", "replyAudio", "emotionAnalysisResult", "exampleText", "exampleTranslateText", "exampleBubbleId", "adviceText", "adviceBubbleId", "isSettlement" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Task_Intro), global::Msg.explore.PB_Task_Intro.Parser, new[]{ "audio", "introText", "firstLangText" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.CS_GetUserHistoryProgressListReq), global::Msg.explore.CS_GetUserHistoryProgressListReq.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_GetUserHistoryProgressListResp), global::Msg.explore.SC_GetUserHistoryProgressListResp.Parser, new[]{ "code", "storyPreloadDataList" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_RecommendUpMsg), global::Msg.explore.PB_RecommendUpMsg.Parser, new[]{ "userSwitchEntity" }, new[]{ "upBizMsg" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Explore_RecommendSwitchEntity), global::Msg.explore.PB_Explore_RecommendSwitchEntity.Parser, new[]{ "entityId", "entityType" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_RecommendSwitchEntity), global::Msg.explore.SC_RecommendSwitchEntity.Parser, new[]{ "timestamp" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_MissionStoryChatNextEntityInfoForSettlement), global::Msg.explore.PB_MissionStoryChatNextEntityInfoForSettlement.Parser, new[]{ "nextEntityType", "storyTitle", "taskTitle", "storyPreloadData" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_MissionStoryChatFinishedTaskInfoForSettlement), global::Msg.explore.PB_MissionStoryChatFinishedTaskInfoForSettlement.Parser, new[]{ "storyTitle", "taskTitle" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_MissionStoryChatDownMsgForSettlement), global::Msg.explore.SC_MissionStoryChatDownMsgForSettlement.Parser, new[]{ "code", "commonData", "finishedTaskInfo", "nextEntityInfo", "checkin_item", "taskSpeechTime", "exceedUserPercent", "userCefrLevel" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  /// <summary>
  ///
  /// 任务描述类型
  /// 1. 区分顶部文本描述内容的类型
  /// 2. 目前有两种任务描述类型
  /// </summary>
  public enum PB_Explore_TaskDescType {
    /// <summary>
    /// 未知
    /// </summary>
    [pbr::OriginalName("EO_TDT_UNKNOWN")] EO_TDT_UNKNOWN = 0,
    /// <summary>
    /// 话题
    /// </summary>
    [pbr::OriginalName("EO_TDT_TOPIC")] EO_TDT_TOPIC = 1,
    /// <summary>
    /// 目标
    /// </summary>
    [pbr::OriginalName("EO_TDT_GOAL")] EO_TDT_GOAL = 2,
  }

  /// <summary>
  ///
  /// 推荐实体类型
  /// 1. 推流的每个内容都可以都可能是不同的功能单元
  /// 2. Explore一期本质上做的是任务型对话的功能单元
  /// </summary>
  public enum PB_Explore_RecommendEntityType {
    /// <summary>
    /// 未知
    /// </summary>
    [pbr::OriginalName("EO_RE_UNKNOWN")] EO_RE_UNKNOWN = 0,
    /// <summary>
    /// 任务型对话（FreeTask、DIY、RolePlay、Tutor）
    /// </summary>
    [pbr::OriginalName("EO_RE_DIALOG_TASK")] EO_RE_DIALOG_TASK = 1,
    /// <summary>
    /// Mission-Story
    /// </summary>
    [pbr::OriginalName("EO_RE_STORY")] EO_RE_STORY = 2,
  }

  /// <summary>
  ///*
  /// Mission剧情对话下行 - 下个实体类型枚举
  /// </summary>
  public enum PB_Explore_MissionStoryChatNextEntityType {
    [pbr::OriginalName("EO_MSC_NET_UNKNOWN")] EO_MSC_NET_UNKNOWN = 0,
    [pbr::OriginalName("EO_MSC_NET_TASK")] EO_MSC_NET_TASK = 1,
    [pbr::OriginalName("EO_MSC_NET_STORY")] EO_MSC_NET_STORY = 2,
  }

  #endregion

  #region Messages
  /// <summary>
  ///
  /// 获取推荐列表请求
  /// 1. 客户端本地生成请求序号，保证序号下请求，出现多次重试的幂等性
  /// 2. 幂等标识用于客户端在重试的时候保证接口的幂等性（唯一有序即可）
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_GetRecommendListReq : pb::IMessage<CS_GetRecommendListReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_GetRecommendListReq> _parser = new pb::MessageParser<CS_GetRecommendListReq>(() => new CS_GetRecommendListReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_GetRecommendListReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetRecommendListReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetRecommendListReq(CS_GetRecommendListReq other) : this() {
      idempotentSign_ = other.idempotentSign_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetRecommendListReq Clone() {
      return new CS_GetRecommendListReq(this);
    }

    /// <summary>Field number for the "idempotentSign" field.</summary>
    public const int idempotentSignFieldNumber = 1;
    private long idempotentSign_;
    /// <summary>
    /// 幂等标识（幂等键，必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long idempotentSign {
      get { return idempotentSign_; }
      set {
        idempotentSign_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_GetRecommendListReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_GetRecommendListReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (idempotentSign != other.idempotentSign) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (idempotentSign != 0L) hash ^= idempotentSign.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (idempotentSign != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(idempotentSign);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (idempotentSign != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(idempotentSign);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (idempotentSign != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(idempotentSign);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_GetRecommendListReq other) {
      if (other == null) {
        return;
      }
      if (other.idempotentSign != 0L) {
        idempotentSign = other.idempotentSign;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            idempotentSign = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            idempotentSign = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 获取推荐列表响应
  /// 1. 响应也会把请求序号带回来，方便客户端保证数据的一致性
  /// 2. 使用同一个列表，转载所有推荐实体数据（Explore一期只有任务型对话）
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_GetRecommendListResp : pb::IMessage<SC_GetRecommendListResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_GetRecommendListResp> _parser = new pb::MessageParser<SC_GetRecommendListResp>(() => new SC_GetRecommendListResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_GetRecommendListResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetRecommendListResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetRecommendListResp(SC_GetRecommendListResp other) : this() {
      code_ = other.code_;
      idempotentSign_ = other.idempotentSign_;
      preloadDataList_ = other.preloadDataList_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetRecommendListResp Clone() {
      return new SC_GetRecommendListResp(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "idempotentSign" field.</summary>
    public const int idempotentSignFieldNumber = 2;
    private long idempotentSign_;
    /// <summary>
    /// 幂等标识（幂等键，必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long idempotentSign {
      get { return idempotentSign_; }
      set {
        idempotentSign_ = value;
      }
    }

    /// <summary>Field number for the "preloadDataList" field.</summary>
    public const int preloadDataListFieldNumber = 3;
    private static readonly pb::FieldCodec<global::Msg.explore.PB_PreloadData> _repeated_preloadDataList_codec
        = pb::FieldCodec.ForMessage(26, global::Msg.explore.PB_PreloadData.Parser);
    private readonly pbc::RepeatedField<global::Msg.explore.PB_PreloadData> preloadDataList_ = new pbc::RepeatedField<global::Msg.explore.PB_PreloadData>();
    /// <summary>
    /// 任务预加载数据项列表（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.explore.PB_PreloadData> preloadDataList {
      get { return preloadDataList_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_GetRecommendListResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_GetRecommendListResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (idempotentSign != other.idempotentSign) return false;
      if(!preloadDataList_.Equals(other.preloadDataList_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (idempotentSign != 0L) hash ^= idempotentSign.GetHashCode();
      hash ^= preloadDataList_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (idempotentSign != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(idempotentSign);
      }
      preloadDataList_.WriteTo(output, _repeated_preloadDataList_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (idempotentSign != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(idempotentSign);
      }
      preloadDataList_.WriteTo(ref output, _repeated_preloadDataList_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (idempotentSign != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(idempotentSign);
      }
      size += preloadDataList_.CalculateSize(_repeated_preloadDataList_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_GetRecommendListResp other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.idempotentSign != 0L) {
        idempotentSign = other.idempotentSign;
      }
      preloadDataList_.Add(other.preloadDataList_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 16: {
            idempotentSign = input.ReadInt64();
            break;
          }
          case 26: {
            preloadDataList_.AddEntriesFrom(input, _repeated_preloadDataList_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 16: {
            idempotentSign = input.ReadInt64();
            break;
          }
          case 26: {
            preloadDataList_.AddEntriesFrom(ref input, _repeated_preloadDataList_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 预加载数据
  /// 1. 预加载数据结构，需要支持扩展不同的功能单元
  /// 2. 目前按不同的实体类型做隔离功能单元（Explore一期只有任务型对话）
  /// 3. 不同的预加载数据项，可能装载相同的内容，但实体id不同
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_PreloadData : pb::IMessage<PB_PreloadData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_PreloadData> _parser = new pb::MessageParser<PB_PreloadData>(() => new PB_PreloadData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_PreloadData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_PreloadData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_PreloadData(PB_PreloadData other) : this() {
      entityId_ = other.entityId_;
      entityType_ = other.entityType_;
      switch (other.entityDataCase) {
        case entityDataOneofCase.dialogTaskPreloadData:
          dialogTaskPreloadData = other.dialogTaskPreloadData.Clone();
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_PreloadData Clone() {
      return new PB_PreloadData(this);
    }

    /// <summary>Field number for the "entityId" field.</summary>
    public const int entityIdFieldNumber = 1;
    private long entityId_;
    /// <summary>
    /// 实体id（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long entityId {
      get { return entityId_; }
      set {
        entityId_ = value;
      }
    }

    /// <summary>Field number for the "entityType" field.</summary>
    public const int entityTypeFieldNumber = 2;
    private global::Msg.explore.PB_Explore_RecommendEntityType entityType_ = global::Msg.explore.PB_Explore_RecommendEntityType.EO_RE_UNKNOWN;
    /// <summary>
    /// 实体类型（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_RecommendEntityType entityType {
      get { return entityType_; }
      set {
        entityType_ = value;
      }
    }

    /// <summary>Field number for the "dialogTaskPreloadData" field.</summary>
    public const int dialogTaskPreloadDataFieldNumber = 3;
    /// <summary>
    /// 任务型对话预加载数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_DialogTaskPreloadData dialogTaskPreloadData {
      get { return entityDataCase_ == entityDataOneofCase.dialogTaskPreloadData ? (global::Msg.explore.PB_DialogTaskPreloadData) entityData_ : null; }
      set {
        entityData_ = value;
        entityDataCase_ = value == null ? entityDataOneofCase.None : entityDataOneofCase.dialogTaskPreloadData;
      }
    }

    private object entityData_;
    /// <summary>Enum of possible cases for the "entityData" oneof.</summary>
    public enum entityDataOneofCase {
      None = 0,
      dialogTaskPreloadData = 3,
    }
    private entityDataOneofCase entityDataCase_ = entityDataOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public entityDataOneofCase entityDataCase {
      get { return entityDataCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearentityData() {
      entityDataCase_ = entityDataOneofCase.None;
      entityData_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_PreloadData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_PreloadData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (entityId != other.entityId) return false;
      if (entityType != other.entityType) return false;
      if (!object.Equals(dialogTaskPreloadData, other.dialogTaskPreloadData)) return false;
      if (entityDataCase != other.entityDataCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (entityId != 0L) hash ^= entityId.GetHashCode();
      if (entityType != global::Msg.explore.PB_Explore_RecommendEntityType.EO_RE_UNKNOWN) hash ^= entityType.GetHashCode();
      if (entityDataCase_ == entityDataOneofCase.dialogTaskPreloadData) hash ^= dialogTaskPreloadData.GetHashCode();
      hash ^= (int) entityDataCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (entityId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(entityId);
      }
      if (entityType != global::Msg.explore.PB_Explore_RecommendEntityType.EO_RE_UNKNOWN) {
        output.WriteRawTag(16);
        output.WriteEnum((int) entityType);
      }
      if (entityDataCase_ == entityDataOneofCase.dialogTaskPreloadData) {
        output.WriteRawTag(26);
        output.WriteMessage(dialogTaskPreloadData);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (entityId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(entityId);
      }
      if (entityType != global::Msg.explore.PB_Explore_RecommendEntityType.EO_RE_UNKNOWN) {
        output.WriteRawTag(16);
        output.WriteEnum((int) entityType);
      }
      if (entityDataCase_ == entityDataOneofCase.dialogTaskPreloadData) {
        output.WriteRawTag(26);
        output.WriteMessage(dialogTaskPreloadData);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (entityId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(entityId);
      }
      if (entityType != global::Msg.explore.PB_Explore_RecommendEntityType.EO_RE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) entityType);
      }
      if (entityDataCase_ == entityDataOneofCase.dialogTaskPreloadData) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(dialogTaskPreloadData);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_PreloadData other) {
      if (other == null) {
        return;
      }
      if (other.entityId != 0L) {
        entityId = other.entityId;
      }
      if (other.entityType != global::Msg.explore.PB_Explore_RecommendEntityType.EO_RE_UNKNOWN) {
        entityType = other.entityType;
      }
      switch (other.entityDataCase) {
        case entityDataOneofCase.dialogTaskPreloadData:
          if (dialogTaskPreloadData == null) {
            dialogTaskPreloadData = new global::Msg.explore.PB_DialogTaskPreloadData();
          }
          dialogTaskPreloadData.MergeFrom(other.dialogTaskPreloadData);
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            entityId = input.ReadInt64();
            break;
          }
          case 16: {
            entityType = (global::Msg.explore.PB_Explore_RecommendEntityType) input.ReadEnum();
            break;
          }
          case 26: {
            global::Msg.explore.PB_DialogTaskPreloadData subBuilder = new global::Msg.explore.PB_DialogTaskPreloadData();
            if (entityDataCase_ == entityDataOneofCase.dialogTaskPreloadData) {
              subBuilder.MergeFrom(dialogTaskPreloadData);
            }
            input.ReadMessage(subBuilder);
            dialogTaskPreloadData = subBuilder;
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            entityId = input.ReadInt64();
            break;
          }
          case 16: {
            entityType = (global::Msg.explore.PB_Explore_RecommendEntityType) input.ReadEnum();
            break;
          }
          case 26: {
            global::Msg.explore.PB_DialogTaskPreloadData subBuilder = new global::Msg.explore.PB_DialogTaskPreloadData();
            if (entityDataCase_ == entityDataOneofCase.dialogTaskPreloadData) {
              subBuilder.MergeFrom(dialogTaskPreloadData);
            }
            input.ReadMessage(subBuilder);
            dialogTaskPreloadData = subBuilder;
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 任务型对话预加载数据
  /// 1. 数据块抽象为三个，分别为：Avatar信息、场景信息和任务详情
  /// 2. 每个任务都有明确支持的对话模式，且仅能是一个，如多个则需要拆分为多个任务来表示
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_DialogTaskPreloadData : pb::IMessage<PB_DialogTaskPreloadData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_DialogTaskPreloadData> _parser = new pb::MessageParser<PB_DialogTaskPreloadData>(() => new PB_DialogTaskPreloadData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_DialogTaskPreloadData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_DialogTaskPreloadData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_DialogTaskPreloadData(PB_DialogTaskPreloadData other) : this() {
      taskId_ = other.taskId_;
      dialogMode_ = other.dialogMode_;
      avatar_ = other.avatar_ != null ? other.avatar_.Clone() : null;
      scene_ = other.scene_ != null ? other.scene_.Clone() : null;
      detail_ = other.detail_ != null ? other.detail_.Clone() : null;
      firstRoundData_ = other.firstRoundData_ != null ? other.firstRoundData_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_DialogTaskPreloadData Clone() {
      return new PB_DialogTaskPreloadData(this);
    }

    /// <summary>Field number for the "taskId" field.</summary>
    public const int taskIdFieldNumber = 1;
    private long taskId_;
    /// <summary>
    /// 任务id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long taskId {
      get { return taskId_; }
      set {
        taskId_ = value;
      }
    }

    /// <summary>Field number for the "dialogMode" field.</summary>
    public const int dialogModeFieldNumber = 2;
    private global::Msg.basic.PB_DialogMode dialogMode_ = global::Msg.basic.PB_DialogMode.MNone;
    /// <summary>
    /// 对话模式
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_DialogMode dialogMode {
      get { return dialogMode_; }
      set {
        dialogMode_ = value;
      }
    }

    /// <summary>Field number for the "avatar" field.</summary>
    public const int avatarFieldNumber = 3;
    private global::Msg.explore.PB_Task_Avatar avatar_;
    /// <summary>
    /// Avatar信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Task_Avatar avatar {
      get { return avatar_; }
      set {
        avatar_ = value;
      }
    }

    /// <summary>Field number for the "scene" field.</summary>
    public const int sceneFieldNumber = 4;
    private global::Msg.explore.PB_Task_Scene scene_;
    /// <summary>
    /// 场景信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Task_Scene scene {
      get { return scene_; }
      set {
        scene_ = value;
      }
    }

    /// <summary>Field number for the "detail" field.</summary>
    public const int detailFieldNumber = 5;
    private global::Msg.explore.PB_Task_Detail detail_;
    /// <summary>
    /// 任务详情
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Task_Detail detail {
      get { return detail_; }
      set {
        detail_ = value;
      }
    }

    /// <summary>Field number for the "firstRoundData" field.</summary>
    public const int firstRoundDataFieldNumber = 6;
    private global::Msg.explore.PB_Task_First_Round_Data firstRoundData_;
    /// <summary>
    /// 首轮数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Task_First_Round_Data firstRoundData {
      get { return firstRoundData_; }
      set {
        firstRoundData_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_DialogTaskPreloadData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_DialogTaskPreloadData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (taskId != other.taskId) return false;
      if (dialogMode != other.dialogMode) return false;
      if (!object.Equals(avatar, other.avatar)) return false;
      if (!object.Equals(scene, other.scene)) return false;
      if (!object.Equals(detail, other.detail)) return false;
      if (!object.Equals(firstRoundData, other.firstRoundData)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (taskId != 0L) hash ^= taskId.GetHashCode();
      if (dialogMode != global::Msg.basic.PB_DialogMode.MNone) hash ^= dialogMode.GetHashCode();
      if (avatar_ != null) hash ^= avatar.GetHashCode();
      if (scene_ != null) hash ^= scene.GetHashCode();
      if (detail_ != null) hash ^= detail.GetHashCode();
      if (firstRoundData_ != null) hash ^= firstRoundData.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (taskId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(taskId);
      }
      if (dialogMode != global::Msg.basic.PB_DialogMode.MNone) {
        output.WriteRawTag(16);
        output.WriteEnum((int) dialogMode);
      }
      if (avatar_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(avatar);
      }
      if (scene_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(scene);
      }
      if (detail_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(detail);
      }
      if (firstRoundData_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(firstRoundData);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (taskId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(taskId);
      }
      if (dialogMode != global::Msg.basic.PB_DialogMode.MNone) {
        output.WriteRawTag(16);
        output.WriteEnum((int) dialogMode);
      }
      if (avatar_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(avatar);
      }
      if (scene_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(scene);
      }
      if (detail_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(detail);
      }
      if (firstRoundData_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(firstRoundData);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (taskId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(taskId);
      }
      if (dialogMode != global::Msg.basic.PB_DialogMode.MNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) dialogMode);
      }
      if (avatar_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(avatar);
      }
      if (scene_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(scene);
      }
      if (detail_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(detail);
      }
      if (firstRoundData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(firstRoundData);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_DialogTaskPreloadData other) {
      if (other == null) {
        return;
      }
      if (other.taskId != 0L) {
        taskId = other.taskId;
      }
      if (other.dialogMode != global::Msg.basic.PB_DialogMode.MNone) {
        dialogMode = other.dialogMode;
      }
      if (other.avatar_ != null) {
        if (avatar_ == null) {
          avatar = new global::Msg.explore.PB_Task_Avatar();
        }
        avatar.MergeFrom(other.avatar);
      }
      if (other.scene_ != null) {
        if (scene_ == null) {
          scene = new global::Msg.explore.PB_Task_Scene();
        }
        scene.MergeFrom(other.scene);
      }
      if (other.detail_ != null) {
        if (detail_ == null) {
          detail = new global::Msg.explore.PB_Task_Detail();
        }
        detail.MergeFrom(other.detail);
      }
      if (other.firstRoundData_ != null) {
        if (firstRoundData_ == null) {
          firstRoundData = new global::Msg.explore.PB_Task_First_Round_Data();
        }
        firstRoundData.MergeFrom(other.firstRoundData);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            taskId = input.ReadInt64();
            break;
          }
          case 16: {
            dialogMode = (global::Msg.basic.PB_DialogMode) input.ReadEnum();
            break;
          }
          case 26: {
            if (avatar_ == null) {
              avatar = new global::Msg.explore.PB_Task_Avatar();
            }
            input.ReadMessage(avatar);
            break;
          }
          case 34: {
            if (scene_ == null) {
              scene = new global::Msg.explore.PB_Task_Scene();
            }
            input.ReadMessage(scene);
            break;
          }
          case 42: {
            if (detail_ == null) {
              detail = new global::Msg.explore.PB_Task_Detail();
            }
            input.ReadMessage(detail);
            break;
          }
          case 50: {
            if (firstRoundData_ == null) {
              firstRoundData = new global::Msg.explore.PB_Task_First_Round_Data();
            }
            input.ReadMessage(firstRoundData);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            taskId = input.ReadInt64();
            break;
          }
          case 16: {
            dialogMode = (global::Msg.basic.PB_DialogMode) input.ReadEnum();
            break;
          }
          case 26: {
            if (avatar_ == null) {
              avatar = new global::Msg.explore.PB_Task_Avatar();
            }
            input.ReadMessage(avatar);
            break;
          }
          case 34: {
            if (scene_ == null) {
              scene = new global::Msg.explore.PB_Task_Scene();
            }
            input.ReadMessage(scene);
            break;
          }
          case 42: {
            if (detail_ == null) {
              detail = new global::Msg.explore.PB_Task_Detail();
            }
            input.ReadMessage(detail);
            break;
          }
          case 50: {
            if (firstRoundData_ == null) {
              firstRoundData = new global::Msg.explore.PB_Task_First_Round_Data();
            }
            input.ReadMessage(firstRoundData);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// Avatar信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Task_Avatar : pb::IMessage<PB_Task_Avatar>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Task_Avatar> _parser = new pb::MessageParser<PB_Task_Avatar>(() => new PB_Task_Avatar());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Task_Avatar> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Avatar() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Avatar(PB_Task_Avatar other) : this() {
      avatarId_ = other.avatarId_;
      headPicUrl_ = other.headPicUrl_;
      headBgColor_ = other.headBgColor_;
      name_ = other.name_;
      role_ = other.role_;
      is3D_ = other.is3D_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Avatar Clone() {
      return new PB_Task_Avatar(this);
    }

    /// <summary>Field number for the "avatarId" field.</summary>
    public const int avatarIdFieldNumber = 1;
    private long avatarId_;
    /// <summary>
    /// AvatarId（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long avatarId {
      get { return avatarId_; }
      set {
        avatarId_ = value;
      }
    }

    /// <summary>Field number for the "headPicUrl" field.</summary>
    public const int headPicUrlFieldNumber = 2;
    private string headPicUrl_ = "";
    /// <summary>
    /// 头像url（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string headPicUrl {
      get { return headPicUrl_; }
      set {
        headPicUrl_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "headBgColor" field.</summary>
    public const int headBgColorFieldNumber = 3;
    private global::Msg.basic.PB_BackgroundColorEnum headBgColor_ = global::Msg.basic.PB_BackgroundColorEnum.BGColorNone;
    /// <summary>
    /// 头像背景色（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_BackgroundColorEnum headBgColor {
      get { return headBgColor_; }
      set {
        headBgColor_ = value;
      }
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int nameFieldNumber = 4;
    private string name_ = "";
    /// <summary>
    /// 姓名（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "role" field.</summary>
    public const int roleFieldNumber = 5;
    private string role_ = "";
    /// <summary>
    /// 角色（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string role {
      get { return role_; }
      set {
        role_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "is3D" field.</summary>
    public const int is3DFieldNumber = 6;
    private bool is3D_;
    /// <summary>
    /// 是否3D形象（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is3D {
      get { return is3D_; }
      set {
        is3D_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Task_Avatar);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Task_Avatar other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (avatarId != other.avatarId) return false;
      if (headPicUrl != other.headPicUrl) return false;
      if (headBgColor != other.headBgColor) return false;
      if (name != other.name) return false;
      if (role != other.role) return false;
      if (is3D != other.is3D) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (avatarId != 0L) hash ^= avatarId.GetHashCode();
      if (headPicUrl.Length != 0) hash ^= headPicUrl.GetHashCode();
      if (headBgColor != global::Msg.basic.PB_BackgroundColorEnum.BGColorNone) hash ^= headBgColor.GetHashCode();
      if (name.Length != 0) hash ^= name.GetHashCode();
      if (role.Length != 0) hash ^= role.GetHashCode();
      if (is3D != false) hash ^= is3D.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (avatarId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(avatarId);
      }
      if (headPicUrl.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(headPicUrl);
      }
      if (headBgColor != global::Msg.basic.PB_BackgroundColorEnum.BGColorNone) {
        output.WriteRawTag(24);
        output.WriteEnum((int) headBgColor);
      }
      if (name.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(name);
      }
      if (role.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(role);
      }
      if (is3D != false) {
        output.WriteRawTag(48);
        output.WriteBool(is3D);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (avatarId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(avatarId);
      }
      if (headPicUrl.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(headPicUrl);
      }
      if (headBgColor != global::Msg.basic.PB_BackgroundColorEnum.BGColorNone) {
        output.WriteRawTag(24);
        output.WriteEnum((int) headBgColor);
      }
      if (name.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(name);
      }
      if (role.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(role);
      }
      if (is3D != false) {
        output.WriteRawTag(48);
        output.WriteBool(is3D);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (avatarId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(avatarId);
      }
      if (headPicUrl.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(headPicUrl);
      }
      if (headBgColor != global::Msg.basic.PB_BackgroundColorEnum.BGColorNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) headBgColor);
      }
      if (name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(name);
      }
      if (role.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(role);
      }
      if (is3D != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Task_Avatar other) {
      if (other == null) {
        return;
      }
      if (other.avatarId != 0L) {
        avatarId = other.avatarId;
      }
      if (other.headPicUrl.Length != 0) {
        headPicUrl = other.headPicUrl;
      }
      if (other.headBgColor != global::Msg.basic.PB_BackgroundColorEnum.BGColorNone) {
        headBgColor = other.headBgColor;
      }
      if (other.name.Length != 0) {
        name = other.name;
      }
      if (other.role.Length != 0) {
        role = other.role;
      }
      if (other.is3D != false) {
        is3D = other.is3D;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            avatarId = input.ReadInt64();
            break;
          }
          case 18: {
            headPicUrl = input.ReadString();
            break;
          }
          case 24: {
            headBgColor = (global::Msg.basic.PB_BackgroundColorEnum) input.ReadEnum();
            break;
          }
          case 34: {
            name = input.ReadString();
            break;
          }
          case 42: {
            role = input.ReadString();
            break;
          }
          case 48: {
            is3D = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            avatarId = input.ReadInt64();
            break;
          }
          case 18: {
            headPicUrl = input.ReadString();
            break;
          }
          case 24: {
            headBgColor = (global::Msg.basic.PB_BackgroundColorEnum) input.ReadEnum();
            break;
          }
          case 34: {
            name = input.ReadString();
            break;
          }
          case 42: {
            role = input.ReadString();
            break;
          }
          case 48: {
            is3D = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 场景信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Task_Scene : pb::IMessage<PB_Task_Scene>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Task_Scene> _parser = new pb::MessageParser<PB_Task_Scene>(() => new PB_Task_Scene());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Task_Scene> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Scene() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Scene(PB_Task_Scene other) : this() {
      bgPicTag_ = other.bgPicTag_;
      bgPicUrl_ = other.bgPicUrl_;
      bgVoiceTag_ = other.bgVoiceTag_;
      bgVoiceUrl_ = other.bgVoiceUrl_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Scene Clone() {
      return new PB_Task_Scene(this);
    }

    /// <summary>Field number for the "bgPicTag" field.</summary>
    public const int bgPicTagFieldNumber = 1;
    private string bgPicTag_ = "";
    /// <summary>
    /// 背景图片标签（非必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string bgPicTag {
      get { return bgPicTag_; }
      set {
        bgPicTag_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "bgPicUrl" field.</summary>
    public const int bgPicUrlFieldNumber = 2;
    private string bgPicUrl_ = "";
    /// <summary>
    /// 背景图片地址（非必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string bgPicUrl {
      get { return bgPicUrl_; }
      set {
        bgPicUrl_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "bgVoiceTag" field.</summary>
    public const int bgVoiceTagFieldNumber = 3;
    private string bgVoiceTag_ = "";
    /// <summary>
    /// 背景音标签（非必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string bgVoiceTag {
      get { return bgVoiceTag_; }
      set {
        bgVoiceTag_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "bgVoiceUrl" field.</summary>
    public const int bgVoiceUrlFieldNumber = 4;
    private string bgVoiceUrl_ = "";
    /// <summary>
    /// 背景音标签（非必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string bgVoiceUrl {
      get { return bgVoiceUrl_; }
      set {
        bgVoiceUrl_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Task_Scene);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Task_Scene other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (bgPicTag != other.bgPicTag) return false;
      if (bgPicUrl != other.bgPicUrl) return false;
      if (bgVoiceTag != other.bgVoiceTag) return false;
      if (bgVoiceUrl != other.bgVoiceUrl) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (bgPicTag.Length != 0) hash ^= bgPicTag.GetHashCode();
      if (bgPicUrl.Length != 0) hash ^= bgPicUrl.GetHashCode();
      if (bgVoiceTag.Length != 0) hash ^= bgVoiceTag.GetHashCode();
      if (bgVoiceUrl.Length != 0) hash ^= bgVoiceUrl.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (bgPicTag.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(bgPicTag);
      }
      if (bgPicUrl.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(bgPicUrl);
      }
      if (bgVoiceTag.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(bgVoiceTag);
      }
      if (bgVoiceUrl.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(bgVoiceUrl);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (bgPicTag.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(bgPicTag);
      }
      if (bgPicUrl.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(bgPicUrl);
      }
      if (bgVoiceTag.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(bgVoiceTag);
      }
      if (bgVoiceUrl.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(bgVoiceUrl);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (bgPicTag.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(bgPicTag);
      }
      if (bgPicUrl.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(bgPicUrl);
      }
      if (bgVoiceTag.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(bgVoiceTag);
      }
      if (bgVoiceUrl.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(bgVoiceUrl);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Task_Scene other) {
      if (other == null) {
        return;
      }
      if (other.bgPicTag.Length != 0) {
        bgPicTag = other.bgPicTag;
      }
      if (other.bgPicUrl.Length != 0) {
        bgPicUrl = other.bgPicUrl;
      }
      if (other.bgVoiceTag.Length != 0) {
        bgVoiceTag = other.bgVoiceTag;
      }
      if (other.bgVoiceUrl.Length != 0) {
        bgVoiceUrl = other.bgVoiceUrl;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            bgPicTag = input.ReadString();
            break;
          }
          case 18: {
            bgPicUrl = input.ReadString();
            break;
          }
          case 26: {
            bgVoiceTag = input.ReadString();
            break;
          }
          case 34: {
            bgVoiceUrl = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            bgPicTag = input.ReadString();
            break;
          }
          case 18: {
            bgPicUrl = input.ReadString();
            break;
          }
          case 26: {
            bgVoiceTag = input.ReadString();
            break;
          }
          case 34: {
            bgVoiceUrl = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 任务详情
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Task_Detail : pb::IMessage<PB_Task_Detail>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Task_Detail> _parser = new pb::MessageParser<PB_Task_Detail>(() => new PB_Task_Detail());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Task_Detail> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Detail() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Detail(PB_Task_Detail other) : this() {
      desc_ = other.desc_;
      descType_ = other.descType_;
      switch (other.detailCase) {
        case detailOneofCase.rolePlayDetail:
          rolePlayDetail = other.rolePlayDetail.Clone();
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Detail Clone() {
      return new PB_Task_Detail(this);
    }

    /// <summary>Field number for the "desc" field.</summary>
    public const int descFieldNumber = 1;
    private string desc_ = "";
    /// <summary>
    /// 任务描述（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string desc {
      get { return desc_; }
      set {
        desc_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "descType" field.</summary>
    public const int descTypeFieldNumber = 2;
    private global::Msg.explore.PB_Explore_TaskDescType descType_ = global::Msg.explore.PB_Explore_TaskDescType.EO_TDT_UNKNOWN;
    /// <summary>
    /// 任务描述类型（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_TaskDescType descType {
      get { return descType_; }
      set {
        descType_ = value;
      }
    }

    /// <summary>Field number for the "rolePlayDetail" field.</summary>
    public const int rolePlayDetailFieldNumber = 3;
    /// <summary>
    /// 角色扮演任务详情
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Task_Role_Play_Detail rolePlayDetail {
      get { return detailCase_ == detailOneofCase.rolePlayDetail ? (global::Msg.explore.PB_Task_Role_Play_Detail) detail_ : null; }
      set {
        detail_ = value;
        detailCase_ = value == null ? detailOneofCase.None : detailOneofCase.rolePlayDetail;
      }
    }

    private object detail_;
    /// <summary>Enum of possible cases for the "detail" oneof.</summary>
    public enum detailOneofCase {
      None = 0,
      rolePlayDetail = 3,
    }
    private detailOneofCase detailCase_ = detailOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public detailOneofCase detailCase {
      get { return detailCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void Cleardetail() {
      detailCase_ = detailOneofCase.None;
      detail_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Task_Detail);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Task_Detail other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (desc != other.desc) return false;
      if (descType != other.descType) return false;
      if (!object.Equals(rolePlayDetail, other.rolePlayDetail)) return false;
      if (detailCase != other.detailCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (desc.Length != 0) hash ^= desc.GetHashCode();
      if (descType != global::Msg.explore.PB_Explore_TaskDescType.EO_TDT_UNKNOWN) hash ^= descType.GetHashCode();
      if (detailCase_ == detailOneofCase.rolePlayDetail) hash ^= rolePlayDetail.GetHashCode();
      hash ^= (int) detailCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (desc.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(desc);
      }
      if (descType != global::Msg.explore.PB_Explore_TaskDescType.EO_TDT_UNKNOWN) {
        output.WriteRawTag(16);
        output.WriteEnum((int) descType);
      }
      if (detailCase_ == detailOneofCase.rolePlayDetail) {
        output.WriteRawTag(26);
        output.WriteMessage(rolePlayDetail);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (desc.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(desc);
      }
      if (descType != global::Msg.explore.PB_Explore_TaskDescType.EO_TDT_UNKNOWN) {
        output.WriteRawTag(16);
        output.WriteEnum((int) descType);
      }
      if (detailCase_ == detailOneofCase.rolePlayDetail) {
        output.WriteRawTag(26);
        output.WriteMessage(rolePlayDetail);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (desc.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(desc);
      }
      if (descType != global::Msg.explore.PB_Explore_TaskDescType.EO_TDT_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) descType);
      }
      if (detailCase_ == detailOneofCase.rolePlayDetail) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(rolePlayDetail);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Task_Detail other) {
      if (other == null) {
        return;
      }
      if (other.desc.Length != 0) {
        desc = other.desc;
      }
      if (other.descType != global::Msg.explore.PB_Explore_TaskDescType.EO_TDT_UNKNOWN) {
        descType = other.descType;
      }
      switch (other.detailCase) {
        case detailOneofCase.rolePlayDetail:
          if (rolePlayDetail == null) {
            rolePlayDetail = new global::Msg.explore.PB_Task_Role_Play_Detail();
          }
          rolePlayDetail.MergeFrom(other.rolePlayDetail);
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            desc = input.ReadString();
            break;
          }
          case 16: {
            descType = (global::Msg.explore.PB_Explore_TaskDescType) input.ReadEnum();
            break;
          }
          case 26: {
            global::Msg.explore.PB_Task_Role_Play_Detail subBuilder = new global::Msg.explore.PB_Task_Role_Play_Detail();
            if (detailCase_ == detailOneofCase.rolePlayDetail) {
              subBuilder.MergeFrom(rolePlayDetail);
            }
            input.ReadMessage(subBuilder);
            rolePlayDetail = subBuilder;
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            desc = input.ReadString();
            break;
          }
          case 16: {
            descType = (global::Msg.explore.PB_Explore_TaskDescType) input.ReadEnum();
            break;
          }
          case 26: {
            global::Msg.explore.PB_Task_Role_Play_Detail subBuilder = new global::Msg.explore.PB_Task_Role_Play_Detail();
            if (detailCase_ == detailOneofCase.rolePlayDetail) {
              subBuilder.MergeFrom(rolePlayDetail);
            }
            input.ReadMessage(subBuilder);
            rolePlayDetail = subBuilder;
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 角色扮演任务详情
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Task_Role_Play_Detail : pb::IMessage<PB_Task_Role_Play_Detail>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Task_Role_Play_Detail> _parser = new pb::MessageParser<PB_Task_Role_Play_Detail>(() => new PB_Task_Role_Play_Detail());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Task_Role_Play_Detail> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Role_Play_Detail() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Role_Play_Detail(PB_Task_Role_Play_Detail other) : this() {
      goals_ = other.goals_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Role_Play_Detail Clone() {
      return new PB_Task_Role_Play_Detail(this);
    }

    /// <summary>Field number for the "goals" field.</summary>
    public const int goalsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Msg.explore.PB_Task_GoalItem> _repeated_goals_codec
        = pb::FieldCodec.ForMessage(10, global::Msg.explore.PB_Task_GoalItem.Parser);
    private readonly pbc::RepeatedField<global::Msg.explore.PB_Task_GoalItem> goals_ = new pbc::RepeatedField<global::Msg.explore.PB_Task_GoalItem>();
    /// <summary>
    /// 任务目标
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.explore.PB_Task_GoalItem> goals {
      get { return goals_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Task_Role_Play_Detail);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Task_Role_Play_Detail other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!goals_.Equals(other.goals_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= goals_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      goals_.WriteTo(output, _repeated_goals_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      goals_.WriteTo(ref output, _repeated_goals_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += goals_.CalculateSize(_repeated_goals_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Task_Role_Play_Detail other) {
      if (other == null) {
        return;
      }
      goals_.Add(other.goals_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            goals_.AddEntriesFrom(input, _repeated_goals_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            goals_.AddEntriesFrom(ref input, _repeated_goals_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 任务目标
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Task_GoalItem : pb::IMessage<PB_Task_GoalItem>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Task_GoalItem> _parser = new pb::MessageParser<PB_Task_GoalItem>(() => new PB_Task_GoalItem());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Task_GoalItem> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_GoalItem() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_GoalItem(PB_Task_GoalItem other) : this() {
      goalId_ = other.goalId_;
      no_ = other.no_;
      goalDesc_ = other.goalDesc_;
      isCompleted_ = other.isCompleted_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_GoalItem Clone() {
      return new PB_Task_GoalItem(this);
    }

    /// <summary>Field number for the "goalId" field.</summary>
    public const int goalIdFieldNumber = 1;
    private long goalId_;
    /// <summary>
    /// 目标id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long goalId {
      get { return goalId_; }
      set {
        goalId_ = value;
      }
    }

    /// <summary>Field number for the "no" field.</summary>
    public const int noFieldNumber = 2;
    private int no_;
    /// <summary>
    /// 目标序号
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int no {
      get { return no_; }
      set {
        no_ = value;
      }
    }

    /// <summary>Field number for the "goalDesc" field.</summary>
    public const int goalDescFieldNumber = 3;
    private string goalDesc_ = "";
    /// <summary>
    /// 目标描述
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string goalDesc {
      get { return goalDesc_; }
      set {
        goalDesc_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "isCompleted" field.</summary>
    public const int isCompletedFieldNumber = 4;
    private bool isCompleted_;
    /// <summary>
    /// 是否完成
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool isCompleted {
      get { return isCompleted_; }
      set {
        isCompleted_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Task_GoalItem);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Task_GoalItem other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (goalId != other.goalId) return false;
      if (no != other.no) return false;
      if (goalDesc != other.goalDesc) return false;
      if (isCompleted != other.isCompleted) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (goalId != 0L) hash ^= goalId.GetHashCode();
      if (no != 0) hash ^= no.GetHashCode();
      if (goalDesc.Length != 0) hash ^= goalDesc.GetHashCode();
      if (isCompleted != false) hash ^= isCompleted.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (goalId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(goalId);
      }
      if (no != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(no);
      }
      if (goalDesc.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(goalDesc);
      }
      if (isCompleted != false) {
        output.WriteRawTag(32);
        output.WriteBool(isCompleted);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (goalId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(goalId);
      }
      if (no != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(no);
      }
      if (goalDesc.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(goalDesc);
      }
      if (isCompleted != false) {
        output.WriteRawTag(32);
        output.WriteBool(isCompleted);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (goalId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(goalId);
      }
      if (no != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(no);
      }
      if (goalDesc.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(goalDesc);
      }
      if (isCompleted != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Task_GoalItem other) {
      if (other == null) {
        return;
      }
      if (other.goalId != 0L) {
        goalId = other.goalId;
      }
      if (other.no != 0) {
        no = other.no;
      }
      if (other.goalDesc.Length != 0) {
        goalDesc = other.goalDesc;
      }
      if (other.isCompleted != false) {
        isCompleted = other.isCompleted;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            goalId = input.ReadInt64();
            break;
          }
          case 16: {
            no = input.ReadInt32();
            break;
          }
          case 26: {
            goalDesc = input.ReadString();
            break;
          }
          case 32: {
            isCompleted = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            goalId = input.ReadInt64();
            break;
          }
          case 16: {
            no = input.ReadInt32();
            break;
          }
          case 26: {
            goalDesc = input.ReadString();
            break;
          }
          case 32: {
            isCompleted = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 任务首轮数据
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Task_First_Round_Data : pb::IMessage<PB_Task_First_Round_Data>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Task_First_Round_Data> _parser = new pb::MessageParser<PB_Task_First_Round_Data>(() => new PB_Task_First_Round_Data());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Task_First_Round_Data> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_First_Round_Data() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_First_Round_Data(PB_Task_First_Round_Data other) : this() {
      replyText_ = other.replyText_;
      replyTranslateText_ = other.replyTranslateText_;
      replyAudio_ = other.replyAudio_ != null ? other.replyAudio_.Clone() : null;
      exampleText_ = other.exampleText_;
      exampleTranslateText_ = other.exampleTranslateText_;
      exampleAudio_ = other.exampleAudio_ != null ? other.exampleAudio_.Clone() : null;
      replyBubbleId_ = other.replyBubbleId_;
      exampleBubbleId_ = other.exampleBubbleId_;
      emotionAnalysisResult_ = other.emotionAnalysisResult_ != null ? other.emotionAnalysisResult_.Clone() : null;
      adviceText_ = other.adviceText_;
      adviceBubbleId_ = other.adviceBubbleId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_First_Round_Data Clone() {
      return new PB_Task_First_Round_Data(this);
    }

    /// <summary>Field number for the "replyText" field.</summary>
    public const int replyTextFieldNumber = 1;
    private string replyText_ = "";
    /// <summary>
    /// Avatar回复文本
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string replyText {
      get { return replyText_; }
      set {
        replyText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "replyTranslateText" field.</summary>
    public const int replyTranslateTextFieldNumber = 2;
    private string replyTranslateText_ = "";
    /// <summary>
    /// Avatar回复翻译文本
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string replyTranslateText {
      get { return replyTranslateText_; }
      set {
        replyTranslateText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "replyAudio" field.</summary>
    public const int replyAudioFieldNumber = 3;
    private global::Msg.explore.PB_Explore_DialogAudioDownFrame replyAudio_;
    /// <summary>
    /// Avatar回复TTS音频
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_DialogAudioDownFrame replyAudio {
      get { return replyAudio_; }
      set {
        replyAudio_ = value;
      }
    }

    /// <summary>Field number for the "exampleText" field.</summary>
    public const int exampleTextFieldNumber = 4;
    private string exampleText_ = "";
    /// <summary>
    /// 用户回复示例文本
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string exampleText {
      get { return exampleText_; }
      set {
        exampleText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "exampleTranslateText" field.</summary>
    public const int exampleTranslateTextFieldNumber = 5;
    private string exampleTranslateText_ = "";
    /// <summary>
    /// 用户回复示例翻译文本
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string exampleTranslateText {
      get { return exampleTranslateText_; }
      set {
        exampleTranslateText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "exampleAudio" field.</summary>
    public const int exampleAudioFieldNumber = 6;
    private global::Msg.explore.PB_Explore_DialogAudioDownFrame exampleAudio_;
    /// <summary>
    /// 用户回复示例音频
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_DialogAudioDownFrame exampleAudio {
      get { return exampleAudio_; }
      set {
        exampleAudio_ = value;
      }
    }

    /// <summary>Field number for the "replyBubbleId" field.</summary>
    public const int replyBubbleIdFieldNumber = 7;
    private string replyBubbleId_ = "";
    /// <summary>
    /// Avatar回复气泡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string replyBubbleId {
      get { return replyBubbleId_; }
      set {
        replyBubbleId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "exampleBubbleId" field.</summary>
    public const int exampleBubbleIdFieldNumber = 8;
    private string exampleBubbleId_ = "";
    /// <summary>
    /// 用户回复示例气泡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string exampleBubbleId {
      get { return exampleBubbleId_; }
      set {
        exampleBubbleId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "emotionAnalysisResult" field.</summary>
    public const int emotionAnalysisResultFieldNumber = 9;
    private global::Msg.explore.PB_EmotionAnalysisResult emotionAnalysisResult_;
    /// <summary>
    /// 情感分析结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_EmotionAnalysisResult emotionAnalysisResult {
      get { return emotionAnalysisResult_; }
      set {
        emotionAnalysisResult_ = value;
      }
    }

    /// <summary>Field number for the "adviceText" field.</summary>
    public const int adviceTextFieldNumber = 10;
    private string adviceText_ = "";
    /// <summary>
    /// Advice
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string adviceText {
      get { return adviceText_; }
      set {
        adviceText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "adviceBubbleId" field.</summary>
    public const int adviceBubbleIdFieldNumber = 11;
    private string adviceBubbleId_ = "";
    /// <summary>
    /// Advice气泡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string adviceBubbleId {
      get { return adviceBubbleId_; }
      set {
        adviceBubbleId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Task_First_Round_Data);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Task_First_Round_Data other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (replyText != other.replyText) return false;
      if (replyTranslateText != other.replyTranslateText) return false;
      if (!object.Equals(replyAudio, other.replyAudio)) return false;
      if (exampleText != other.exampleText) return false;
      if (exampleTranslateText != other.exampleTranslateText) return false;
      if (!object.Equals(exampleAudio, other.exampleAudio)) return false;
      if (replyBubbleId != other.replyBubbleId) return false;
      if (exampleBubbleId != other.exampleBubbleId) return false;
      if (!object.Equals(emotionAnalysisResult, other.emotionAnalysisResult)) return false;
      if (adviceText != other.adviceText) return false;
      if (adviceBubbleId != other.adviceBubbleId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (replyText.Length != 0) hash ^= replyText.GetHashCode();
      if (replyTranslateText.Length != 0) hash ^= replyTranslateText.GetHashCode();
      if (replyAudio_ != null) hash ^= replyAudio.GetHashCode();
      if (exampleText.Length != 0) hash ^= exampleText.GetHashCode();
      if (exampleTranslateText.Length != 0) hash ^= exampleTranslateText.GetHashCode();
      if (exampleAudio_ != null) hash ^= exampleAudio.GetHashCode();
      if (replyBubbleId.Length != 0) hash ^= replyBubbleId.GetHashCode();
      if (exampleBubbleId.Length != 0) hash ^= exampleBubbleId.GetHashCode();
      if (emotionAnalysisResult_ != null) hash ^= emotionAnalysisResult.GetHashCode();
      if (adviceText.Length != 0) hash ^= adviceText.GetHashCode();
      if (adviceBubbleId.Length != 0) hash ^= adviceBubbleId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (replyText.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(replyText);
      }
      if (replyTranslateText.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(replyTranslateText);
      }
      if (replyAudio_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(replyAudio);
      }
      if (exampleText.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(exampleText);
      }
      if (exampleTranslateText.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(exampleTranslateText);
      }
      if (exampleAudio_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(exampleAudio);
      }
      if (replyBubbleId.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(replyBubbleId);
      }
      if (exampleBubbleId.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(exampleBubbleId);
      }
      if (emotionAnalysisResult_ != null) {
        output.WriteRawTag(74);
        output.WriteMessage(emotionAnalysisResult);
      }
      if (adviceText.Length != 0) {
        output.WriteRawTag(82);
        output.WriteString(adviceText);
      }
      if (adviceBubbleId.Length != 0) {
        output.WriteRawTag(90);
        output.WriteString(adviceBubbleId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (replyText.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(replyText);
      }
      if (replyTranslateText.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(replyTranslateText);
      }
      if (replyAudio_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(replyAudio);
      }
      if (exampleText.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(exampleText);
      }
      if (exampleTranslateText.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(exampleTranslateText);
      }
      if (exampleAudio_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(exampleAudio);
      }
      if (replyBubbleId.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(replyBubbleId);
      }
      if (exampleBubbleId.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(exampleBubbleId);
      }
      if (emotionAnalysisResult_ != null) {
        output.WriteRawTag(74);
        output.WriteMessage(emotionAnalysisResult);
      }
      if (adviceText.Length != 0) {
        output.WriteRawTag(82);
        output.WriteString(adviceText);
      }
      if (adviceBubbleId.Length != 0) {
        output.WriteRawTag(90);
        output.WriteString(adviceBubbleId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (replyText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(replyText);
      }
      if (replyTranslateText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(replyTranslateText);
      }
      if (replyAudio_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(replyAudio);
      }
      if (exampleText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(exampleText);
      }
      if (exampleTranslateText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(exampleTranslateText);
      }
      if (exampleAudio_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(exampleAudio);
      }
      if (replyBubbleId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(replyBubbleId);
      }
      if (exampleBubbleId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(exampleBubbleId);
      }
      if (emotionAnalysisResult_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(emotionAnalysisResult);
      }
      if (adviceText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(adviceText);
      }
      if (adviceBubbleId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(adviceBubbleId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Task_First_Round_Data other) {
      if (other == null) {
        return;
      }
      if (other.replyText.Length != 0) {
        replyText = other.replyText;
      }
      if (other.replyTranslateText.Length != 0) {
        replyTranslateText = other.replyTranslateText;
      }
      if (other.replyAudio_ != null) {
        if (replyAudio_ == null) {
          replyAudio = new global::Msg.explore.PB_Explore_DialogAudioDownFrame();
        }
        replyAudio.MergeFrom(other.replyAudio);
      }
      if (other.exampleText.Length != 0) {
        exampleText = other.exampleText;
      }
      if (other.exampleTranslateText.Length != 0) {
        exampleTranslateText = other.exampleTranslateText;
      }
      if (other.exampleAudio_ != null) {
        if (exampleAudio_ == null) {
          exampleAudio = new global::Msg.explore.PB_Explore_DialogAudioDownFrame();
        }
        exampleAudio.MergeFrom(other.exampleAudio);
      }
      if (other.replyBubbleId.Length != 0) {
        replyBubbleId = other.replyBubbleId;
      }
      if (other.exampleBubbleId.Length != 0) {
        exampleBubbleId = other.exampleBubbleId;
      }
      if (other.emotionAnalysisResult_ != null) {
        if (emotionAnalysisResult_ == null) {
          emotionAnalysisResult = new global::Msg.explore.PB_EmotionAnalysisResult();
        }
        emotionAnalysisResult.MergeFrom(other.emotionAnalysisResult);
      }
      if (other.adviceText.Length != 0) {
        adviceText = other.adviceText;
      }
      if (other.adviceBubbleId.Length != 0) {
        adviceBubbleId = other.adviceBubbleId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            replyText = input.ReadString();
            break;
          }
          case 18: {
            replyTranslateText = input.ReadString();
            break;
          }
          case 26: {
            if (replyAudio_ == null) {
              replyAudio = new global::Msg.explore.PB_Explore_DialogAudioDownFrame();
            }
            input.ReadMessage(replyAudio);
            break;
          }
          case 34: {
            exampleText = input.ReadString();
            break;
          }
          case 42: {
            exampleTranslateText = input.ReadString();
            break;
          }
          case 50: {
            if (exampleAudio_ == null) {
              exampleAudio = new global::Msg.explore.PB_Explore_DialogAudioDownFrame();
            }
            input.ReadMessage(exampleAudio);
            break;
          }
          case 58: {
            replyBubbleId = input.ReadString();
            break;
          }
          case 66: {
            exampleBubbleId = input.ReadString();
            break;
          }
          case 74: {
            if (emotionAnalysisResult_ == null) {
              emotionAnalysisResult = new global::Msg.explore.PB_EmotionAnalysisResult();
            }
            input.ReadMessage(emotionAnalysisResult);
            break;
          }
          case 82: {
            adviceText = input.ReadString();
            break;
          }
          case 90: {
            adviceBubbleId = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            replyText = input.ReadString();
            break;
          }
          case 18: {
            replyTranslateText = input.ReadString();
            break;
          }
          case 26: {
            if (replyAudio_ == null) {
              replyAudio = new global::Msg.explore.PB_Explore_DialogAudioDownFrame();
            }
            input.ReadMessage(replyAudio);
            break;
          }
          case 34: {
            exampleText = input.ReadString();
            break;
          }
          case 42: {
            exampleTranslateText = input.ReadString();
            break;
          }
          case 50: {
            if (exampleAudio_ == null) {
              exampleAudio = new global::Msg.explore.PB_Explore_DialogAudioDownFrame();
            }
            input.ReadMessage(exampleAudio);
            break;
          }
          case 58: {
            replyBubbleId = input.ReadString();
            break;
          }
          case 66: {
            exampleBubbleId = input.ReadString();
            break;
          }
          case 74: {
            if (emotionAnalysisResult_ == null) {
              emotionAnalysisResult = new global::Msg.explore.PB_EmotionAnalysisResult();
            }
            input.ReadMessage(emotionAnalysisResult);
            break;
          }
          case 82: {
            adviceText = input.ReadString();
            break;
          }
          case 90: {
            adviceBubbleId = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 获取推荐列表V2请求（支持Explore多个顶Tab）
  /// 1. 客户端本地生成请求序号，保证序号下请求，出现多次重试的幂等性
  /// 2. 幂等标识用于客户端在重试的时候保证接口的幂等性（唯一有序即可）
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_GetRecommendListV2Req : pb::IMessage<CS_GetRecommendListV2Req>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_GetRecommendListV2Req> _parser = new pb::MessageParser<CS_GetRecommendListV2Req>(() => new CS_GetRecommendListV2Req());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_GetRecommendListV2Req> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetRecommendListV2Req() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetRecommendListV2Req(CS_GetRecommendListV2Req other) : this() {
      idempotentSign_ = other.idempotentSign_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetRecommendListV2Req Clone() {
      return new CS_GetRecommendListV2Req(this);
    }

    /// <summary>Field number for the "idempotentSign" field.</summary>
    public const int idempotentSignFieldNumber = 1;
    private long idempotentSign_;
    /// <summary>
    /// 幂等标识（幂等键，必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long idempotentSign {
      get { return idempotentSign_; }
      set {
        idempotentSign_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_GetRecommendListV2Req);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_GetRecommendListV2Req other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (idempotentSign != other.idempotentSign) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (idempotentSign != 0L) hash ^= idempotentSign.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (idempotentSign != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(idempotentSign);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (idempotentSign != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(idempotentSign);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (idempotentSign != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(idempotentSign);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_GetRecommendListV2Req other) {
      if (other == null) {
        return;
      }
      if (other.idempotentSign != 0L) {
        idempotentSign = other.idempotentSign;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            idempotentSign = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            idempotentSign = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 获取推荐列表V2响应（支持Explore多个顶Tab）
  /// 1. 响应也会把请求序号带回来，方便客户端保证数据的一致性
  /// 2. 支持多个顶Tab的不同数据，使用统一个协议响应，牺牲一些网络时延，换流畅性
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_GetRecommendListV2Resp : pb::IMessage<SC_GetRecommendListV2Resp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_GetRecommendListV2Resp> _parser = new pb::MessageParser<SC_GetRecommendListV2Resp>(() => new SC_GetRecommendListV2Resp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_GetRecommendListV2Resp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetRecommendListV2Resp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetRecommendListV2Resp(SC_GetRecommendListV2Resp other) : this() {
      code_ = other.code_;
      idempotentSign_ = other.idempotentSign_;
      storyPreloadDataList_ = other.storyPreloadDataList_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetRecommendListV2Resp Clone() {
      return new SC_GetRecommendListV2Resp(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "idempotentSign" field.</summary>
    public const int idempotentSignFieldNumber = 2;
    private long idempotentSign_;
    /// <summary>
    /// 幂等标识（幂等键，必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long idempotentSign {
      get { return idempotentSign_; }
      set {
        idempotentSign_ = value;
      }
    }

    /// <summary>Field number for the "storyPreloadDataList" field.</summary>
    public const int storyPreloadDataListFieldNumber = 3;
    private static readonly pb::FieldCodec<global::Msg.explore.PB_StoryPreloadData> _repeated_storyPreloadDataList_codec
        = pb::FieldCodec.ForMessage(26, global::Msg.explore.PB_StoryPreloadData.Parser);
    private readonly pbc::RepeatedField<global::Msg.explore.PB_StoryPreloadData> storyPreloadDataList_ = new pbc::RepeatedField<global::Msg.explore.PB_StoryPreloadData>();
    /// <summary>
    /// Story预加载数据项列表（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.explore.PB_StoryPreloadData> storyPreloadDataList {
      get { return storyPreloadDataList_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_GetRecommendListV2Resp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_GetRecommendListV2Resp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (idempotentSign != other.idempotentSign) return false;
      if(!storyPreloadDataList_.Equals(other.storyPreloadDataList_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (idempotentSign != 0L) hash ^= idempotentSign.GetHashCode();
      hash ^= storyPreloadDataList_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (idempotentSign != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(idempotentSign);
      }
      storyPreloadDataList_.WriteTo(output, _repeated_storyPreloadDataList_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (idempotentSign != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(idempotentSign);
      }
      storyPreloadDataList_.WriteTo(ref output, _repeated_storyPreloadDataList_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (idempotentSign != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(idempotentSign);
      }
      size += storyPreloadDataList_.CalculateSize(_repeated_storyPreloadDataList_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_GetRecommendListV2Resp other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.idempotentSign != 0L) {
        idempotentSign = other.idempotentSign;
      }
      storyPreloadDataList_.Add(other.storyPreloadDataList_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 16: {
            idempotentSign = input.ReadInt64();
            break;
          }
          case 26: {
            storyPreloadDataList_.AddEntriesFrom(input, _repeated_storyPreloadDataList_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 16: {
            idempotentSign = input.ReadInt64();
            break;
          }
          case 26: {
            storyPreloadDataList_.AddEntriesFrom(ref input, _repeated_storyPreloadDataList_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Story预加载数据
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_StoryPreloadData : pb::IMessage<PB_StoryPreloadData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_StoryPreloadData> _parser = new pb::MessageParser<PB_StoryPreloadData>(() => new PB_StoryPreloadData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_StoryPreloadData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_StoryPreloadData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_StoryPreloadData(PB_StoryPreloadData other) : this() {
      storyId_ = other.storyId_;
      taskId_ = other.taskId_;
      taskRecordId_ = other.taskRecordId_;
      dialogMode_ = other.dialogMode_;
      avatar_ = other.avatar_ != null ? other.avatar_.Clone() : null;
      scene_ = other.scene_ != null ? other.scene_.Clone() : null;
      detail_ = other.detail_ != null ? other.detail_.Clone() : null;
      firstRoundData_ = other.firstRoundData_ != null ? other.firstRoundData_.Clone() : null;
      taskIntro_ = other.taskIntro_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_StoryPreloadData Clone() {
      return new PB_StoryPreloadData(this);
    }

    /// <summary>Field number for the "storyId" field.</summary>
    public const int storyIdFieldNumber = 1;
    private long storyId_;
    /// <summary>
    /// story_id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long storyId {
      get { return storyId_; }
      set {
        storyId_ = value;
      }
    }

    /// <summary>Field number for the "taskId" field.</summary>
    public const int taskIdFieldNumber = 2;
    private long taskId_;
    /// <summary>
    /// task_id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long taskId {
      get { return taskId_; }
      set {
        taskId_ = value;
      }
    }

    /// <summary>Field number for the "taskRecordId" field.</summary>
    public const int taskRecordIdFieldNumber = 3;
    private long taskRecordId_;
    /// <summary>
    /// task_record_id（可能为0）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long taskRecordId {
      get { return taskRecordId_; }
      set {
        taskRecordId_ = value;
      }
    }

    /// <summary>Field number for the "dialogMode" field.</summary>
    public const int dialogModeFieldNumber = 4;
    private global::Msg.basic.PB_DialogMode dialogMode_ = global::Msg.basic.PB_DialogMode.MNone;
    /// <summary>
    /// 对话模式
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_DialogMode dialogMode {
      get { return dialogMode_; }
      set {
        dialogMode_ = value;
      }
    }

    /// <summary>Field number for the "avatar" field.</summary>
    public const int avatarFieldNumber = 5;
    private global::Msg.explore.PB_Story_Avatar avatar_;
    /// <summary>
    /// Avatar信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Story_Avatar avatar {
      get { return avatar_; }
      set {
        avatar_ = value;
      }
    }

    /// <summary>Field number for the "scene" field.</summary>
    public const int sceneFieldNumber = 6;
    private global::Msg.explore.PB_Story_Scene scene_;
    /// <summary>
    /// 场景信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Story_Scene scene {
      get { return scene_; }
      set {
        scene_ = value;
      }
    }

    /// <summary>Field number for the "detail" field.</summary>
    public const int detailFieldNumber = 7;
    private global::Msg.explore.PB_Story_Detail detail_;
    /// <summary>
    /// story详情
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Story_Detail detail {
      get { return detail_; }
      set {
        detail_ = value;
      }
    }

    /// <summary>Field number for the "firstRoundData" field.</summary>
    public const int firstRoundDataFieldNumber = 8;
    private global::Msg.explore.PB_Story_First_Round_Data firstRoundData_;
    /// <summary>
    /// 首轮数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Story_First_Round_Data firstRoundData {
      get { return firstRoundData_; }
      set {
        firstRoundData_ = value;
      }
    }

    /// <summary>Field number for the "taskIntro" field.</summary>
    public const int taskIntroFieldNumber = 9;
    private static readonly pb::FieldCodec<global::Msg.explore.PB_Task_Intro> _repeated_taskIntro_codec
        = pb::FieldCodec.ForMessage(74, global::Msg.explore.PB_Task_Intro.Parser);
    private readonly pbc::RepeatedField<global::Msg.explore.PB_Task_Intro> taskIntro_ = new pbc::RepeatedField<global::Msg.explore.PB_Task_Intro>();
    /// <summary>
    /// task intro信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.explore.PB_Task_Intro> taskIntro {
      get { return taskIntro_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_StoryPreloadData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_StoryPreloadData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (storyId != other.storyId) return false;
      if (taskId != other.taskId) return false;
      if (taskRecordId != other.taskRecordId) return false;
      if (dialogMode != other.dialogMode) return false;
      if (!object.Equals(avatar, other.avatar)) return false;
      if (!object.Equals(scene, other.scene)) return false;
      if (!object.Equals(detail, other.detail)) return false;
      if (!object.Equals(firstRoundData, other.firstRoundData)) return false;
      if(!taskIntro_.Equals(other.taskIntro_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (storyId != 0L) hash ^= storyId.GetHashCode();
      if (taskId != 0L) hash ^= taskId.GetHashCode();
      if (taskRecordId != 0L) hash ^= taskRecordId.GetHashCode();
      if (dialogMode != global::Msg.basic.PB_DialogMode.MNone) hash ^= dialogMode.GetHashCode();
      if (avatar_ != null) hash ^= avatar.GetHashCode();
      if (scene_ != null) hash ^= scene.GetHashCode();
      if (detail_ != null) hash ^= detail.GetHashCode();
      if (firstRoundData_ != null) hash ^= firstRoundData.GetHashCode();
      hash ^= taskIntro_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (storyId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(storyId);
      }
      if (taskId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(taskId);
      }
      if (taskRecordId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(taskRecordId);
      }
      if (dialogMode != global::Msg.basic.PB_DialogMode.MNone) {
        output.WriteRawTag(32);
        output.WriteEnum((int) dialogMode);
      }
      if (avatar_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(avatar);
      }
      if (scene_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(scene);
      }
      if (detail_ != null) {
        output.WriteRawTag(58);
        output.WriteMessage(detail);
      }
      if (firstRoundData_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(firstRoundData);
      }
      taskIntro_.WriteTo(output, _repeated_taskIntro_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (storyId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(storyId);
      }
      if (taskId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(taskId);
      }
      if (taskRecordId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(taskRecordId);
      }
      if (dialogMode != global::Msg.basic.PB_DialogMode.MNone) {
        output.WriteRawTag(32);
        output.WriteEnum((int) dialogMode);
      }
      if (avatar_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(avatar);
      }
      if (scene_ != null) {
        output.WriteRawTag(50);
        output.WriteMessage(scene);
      }
      if (detail_ != null) {
        output.WriteRawTag(58);
        output.WriteMessage(detail);
      }
      if (firstRoundData_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(firstRoundData);
      }
      taskIntro_.WriteTo(ref output, _repeated_taskIntro_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (storyId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(storyId);
      }
      if (taskId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(taskId);
      }
      if (taskRecordId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(taskRecordId);
      }
      if (dialogMode != global::Msg.basic.PB_DialogMode.MNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) dialogMode);
      }
      if (avatar_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(avatar);
      }
      if (scene_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(scene);
      }
      if (detail_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(detail);
      }
      if (firstRoundData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(firstRoundData);
      }
      size += taskIntro_.CalculateSize(_repeated_taskIntro_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_StoryPreloadData other) {
      if (other == null) {
        return;
      }
      if (other.storyId != 0L) {
        storyId = other.storyId;
      }
      if (other.taskId != 0L) {
        taskId = other.taskId;
      }
      if (other.taskRecordId != 0L) {
        taskRecordId = other.taskRecordId;
      }
      if (other.dialogMode != global::Msg.basic.PB_DialogMode.MNone) {
        dialogMode = other.dialogMode;
      }
      if (other.avatar_ != null) {
        if (avatar_ == null) {
          avatar = new global::Msg.explore.PB_Story_Avatar();
        }
        avatar.MergeFrom(other.avatar);
      }
      if (other.scene_ != null) {
        if (scene_ == null) {
          scene = new global::Msg.explore.PB_Story_Scene();
        }
        scene.MergeFrom(other.scene);
      }
      if (other.detail_ != null) {
        if (detail_ == null) {
          detail = new global::Msg.explore.PB_Story_Detail();
        }
        detail.MergeFrom(other.detail);
      }
      if (other.firstRoundData_ != null) {
        if (firstRoundData_ == null) {
          firstRoundData = new global::Msg.explore.PB_Story_First_Round_Data();
        }
        firstRoundData.MergeFrom(other.firstRoundData);
      }
      taskIntro_.Add(other.taskIntro_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            storyId = input.ReadInt64();
            break;
          }
          case 16: {
            taskId = input.ReadInt64();
            break;
          }
          case 24: {
            taskRecordId = input.ReadInt64();
            break;
          }
          case 32: {
            dialogMode = (global::Msg.basic.PB_DialogMode) input.ReadEnum();
            break;
          }
          case 42: {
            if (avatar_ == null) {
              avatar = new global::Msg.explore.PB_Story_Avatar();
            }
            input.ReadMessage(avatar);
            break;
          }
          case 50: {
            if (scene_ == null) {
              scene = new global::Msg.explore.PB_Story_Scene();
            }
            input.ReadMessage(scene);
            break;
          }
          case 58: {
            if (detail_ == null) {
              detail = new global::Msg.explore.PB_Story_Detail();
            }
            input.ReadMessage(detail);
            break;
          }
          case 66: {
            if (firstRoundData_ == null) {
              firstRoundData = new global::Msg.explore.PB_Story_First_Round_Data();
            }
            input.ReadMessage(firstRoundData);
            break;
          }
          case 74: {
            taskIntro_.AddEntriesFrom(input, _repeated_taskIntro_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            storyId = input.ReadInt64();
            break;
          }
          case 16: {
            taskId = input.ReadInt64();
            break;
          }
          case 24: {
            taskRecordId = input.ReadInt64();
            break;
          }
          case 32: {
            dialogMode = (global::Msg.basic.PB_DialogMode) input.ReadEnum();
            break;
          }
          case 42: {
            if (avatar_ == null) {
              avatar = new global::Msg.explore.PB_Story_Avatar();
            }
            input.ReadMessage(avatar);
            break;
          }
          case 50: {
            if (scene_ == null) {
              scene = new global::Msg.explore.PB_Story_Scene();
            }
            input.ReadMessage(scene);
            break;
          }
          case 58: {
            if (detail_ == null) {
              detail = new global::Msg.explore.PB_Story_Detail();
            }
            input.ReadMessage(detail);
            break;
          }
          case 66: {
            if (firstRoundData_ == null) {
              firstRoundData = new global::Msg.explore.PB_Story_First_Round_Data();
            }
            input.ReadMessage(firstRoundData);
            break;
          }
          case 74: {
            taskIntro_.AddEntriesFrom(ref input, _repeated_taskIntro_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// Avatar信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Story_Avatar : pb::IMessage<PB_Story_Avatar>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Story_Avatar> _parser = new pb::MessageParser<PB_Story_Avatar>(() => new PB_Story_Avatar());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Story_Avatar> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[13]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Story_Avatar() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Story_Avatar(PB_Story_Avatar other) : this() {
      avatarId_ = other.avatarId_;
      name_ = other.name_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Story_Avatar Clone() {
      return new PB_Story_Avatar(this);
    }

    /// <summary>Field number for the "avatarId" field.</summary>
    public const int avatarIdFieldNumber = 1;
    private long avatarId_;
    /// <summary>
    /// AvatarId（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long avatarId {
      get { return avatarId_; }
      set {
        avatarId_ = value;
      }
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int nameFieldNumber = 2;
    private string name_ = "";
    /// <summary>
    /// 姓名（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Story_Avatar);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Story_Avatar other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (avatarId != other.avatarId) return false;
      if (name != other.name) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (avatarId != 0L) hash ^= avatarId.GetHashCode();
      if (name.Length != 0) hash ^= name.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (avatarId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(avatarId);
      }
      if (name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(name);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (avatarId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(avatarId);
      }
      if (name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(name);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (avatarId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(avatarId);
      }
      if (name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(name);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Story_Avatar other) {
      if (other == null) {
        return;
      }
      if (other.avatarId != 0L) {
        avatarId = other.avatarId;
      }
      if (other.name.Length != 0) {
        name = other.name;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            avatarId = input.ReadInt64();
            break;
          }
          case 18: {
            name = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            avatarId = input.ReadInt64();
            break;
          }
          case 18: {
            name = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 场景信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Story_Scene : pb::IMessage<PB_Story_Scene>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Story_Scene> _parser = new pb::MessageParser<PB_Story_Scene>(() => new PB_Story_Scene());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Story_Scene> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[14]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Story_Scene() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Story_Scene(PB_Story_Scene other) : this() {
      bgPicTag_ = other.bgPicTag_;
      bgPicUrl_ = other.bgPicUrl_;
      bgVoiceTag_ = other.bgVoiceTag_;
      bgVoiceUrl_ = other.bgVoiceUrl_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Story_Scene Clone() {
      return new PB_Story_Scene(this);
    }

    /// <summary>Field number for the "bgPicTag" field.</summary>
    public const int bgPicTagFieldNumber = 1;
    private string bgPicTag_ = "";
    /// <summary>
    /// 背景图片标签（非必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string bgPicTag {
      get { return bgPicTag_; }
      set {
        bgPicTag_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "bgPicUrl" field.</summary>
    public const int bgPicUrlFieldNumber = 2;
    private string bgPicUrl_ = "";
    /// <summary>
    /// 背景图片地址（非必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string bgPicUrl {
      get { return bgPicUrl_; }
      set {
        bgPicUrl_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "bgVoiceTag" field.</summary>
    public const int bgVoiceTagFieldNumber = 3;
    private string bgVoiceTag_ = "";
    /// <summary>
    /// 背景音标签（非必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string bgVoiceTag {
      get { return bgVoiceTag_; }
      set {
        bgVoiceTag_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "bgVoiceUrl" field.</summary>
    public const int bgVoiceUrlFieldNumber = 4;
    private string bgVoiceUrl_ = "";
    /// <summary>
    /// 背景音标签（非必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string bgVoiceUrl {
      get { return bgVoiceUrl_; }
      set {
        bgVoiceUrl_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Story_Scene);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Story_Scene other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (bgPicTag != other.bgPicTag) return false;
      if (bgPicUrl != other.bgPicUrl) return false;
      if (bgVoiceTag != other.bgVoiceTag) return false;
      if (bgVoiceUrl != other.bgVoiceUrl) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (bgPicTag.Length != 0) hash ^= bgPicTag.GetHashCode();
      if (bgPicUrl.Length != 0) hash ^= bgPicUrl.GetHashCode();
      if (bgVoiceTag.Length != 0) hash ^= bgVoiceTag.GetHashCode();
      if (bgVoiceUrl.Length != 0) hash ^= bgVoiceUrl.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (bgPicTag.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(bgPicTag);
      }
      if (bgPicUrl.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(bgPicUrl);
      }
      if (bgVoiceTag.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(bgVoiceTag);
      }
      if (bgVoiceUrl.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(bgVoiceUrl);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (bgPicTag.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(bgPicTag);
      }
      if (bgPicUrl.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(bgPicUrl);
      }
      if (bgVoiceTag.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(bgVoiceTag);
      }
      if (bgVoiceUrl.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(bgVoiceUrl);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (bgPicTag.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(bgPicTag);
      }
      if (bgPicUrl.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(bgPicUrl);
      }
      if (bgVoiceTag.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(bgVoiceTag);
      }
      if (bgVoiceUrl.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(bgVoiceUrl);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Story_Scene other) {
      if (other == null) {
        return;
      }
      if (other.bgPicTag.Length != 0) {
        bgPicTag = other.bgPicTag;
      }
      if (other.bgPicUrl.Length != 0) {
        bgPicUrl = other.bgPicUrl;
      }
      if (other.bgVoiceTag.Length != 0) {
        bgVoiceTag = other.bgVoiceTag;
      }
      if (other.bgVoiceUrl.Length != 0) {
        bgVoiceUrl = other.bgVoiceUrl;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            bgPicTag = input.ReadString();
            break;
          }
          case 18: {
            bgPicUrl = input.ReadString();
            break;
          }
          case 26: {
            bgVoiceTag = input.ReadString();
            break;
          }
          case 34: {
            bgVoiceUrl = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            bgPicTag = input.ReadString();
            break;
          }
          case 18: {
            bgPicUrl = input.ReadString();
            break;
          }
          case 26: {
            bgVoiceTag = input.ReadString();
            break;
          }
          case 34: {
            bgVoiceUrl = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// Story详情
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Story_Detail : pb::IMessage<PB_Story_Detail>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Story_Detail> _parser = new pb::MessageParser<PB_Story_Detail>(() => new PB_Story_Detail());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Story_Detail> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[15]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Story_Detail() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Story_Detail(PB_Story_Detail other) : this() {
      missionId_ = other.missionId_;
      CefrLevel_ = other.CefrLevel_;
      storyId_ = other.storyId_;
      storyTitle_ = other.storyTitle_;
      storyProgress_ = other.storyProgress_ != null ? other.storyProgress_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Story_Detail Clone() {
      return new PB_Story_Detail(this);
    }

    /// <summary>Field number for the "missionId" field.</summary>
    public const int missionIdFieldNumber = 1;
    private long missionId_;
    /// <summary>
    /// missionId（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long missionId {
      get { return missionId_; }
      set {
        missionId_ = value;
      }
    }

    /// <summary>Field number for the "CefrLevel" field.</summary>
    public const int CefrLevelFieldNumber = 2;
    private string CefrLevel_ = "";
    /// <summary>
    /// 难度等级（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string CefrLevel {
      get { return CefrLevel_; }
      set {
        CefrLevel_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "storyId" field.</summary>
    public const int storyIdFieldNumber = 3;
    private long storyId_;
    /// <summary>
    /// storyId（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long storyId {
      get { return storyId_; }
      set {
        storyId_ = value;
      }
    }

    /// <summary>Field number for the "storyTitle" field.</summary>
    public const int storyTitleFieldNumber = 4;
    private string storyTitle_ = "";
    /// <summary>
    /// story标题（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string storyTitle {
      get { return storyTitle_; }
      set {
        storyTitle_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "storyProgress" field.</summary>
    public const int storyProgressFieldNumber = 5;
    private global::Msg.explore.PB_Story_Progress storyProgress_;
    /// <summary>
    /// story进度（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Story_Progress storyProgress {
      get { return storyProgress_; }
      set {
        storyProgress_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Story_Detail);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Story_Detail other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (missionId != other.missionId) return false;
      if (CefrLevel != other.CefrLevel) return false;
      if (storyId != other.storyId) return false;
      if (storyTitle != other.storyTitle) return false;
      if (!object.Equals(storyProgress, other.storyProgress)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (missionId != 0L) hash ^= missionId.GetHashCode();
      if (CefrLevel.Length != 0) hash ^= CefrLevel.GetHashCode();
      if (storyId != 0L) hash ^= storyId.GetHashCode();
      if (storyTitle.Length != 0) hash ^= storyTitle.GetHashCode();
      if (storyProgress_ != null) hash ^= storyProgress.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (missionId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(missionId);
      }
      if (CefrLevel.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(CefrLevel);
      }
      if (storyId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(storyId);
      }
      if (storyTitle.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(storyTitle);
      }
      if (storyProgress_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(storyProgress);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (missionId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(missionId);
      }
      if (CefrLevel.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(CefrLevel);
      }
      if (storyId != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(storyId);
      }
      if (storyTitle.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(storyTitle);
      }
      if (storyProgress_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(storyProgress);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (missionId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(missionId);
      }
      if (CefrLevel.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(CefrLevel);
      }
      if (storyId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(storyId);
      }
      if (storyTitle.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(storyTitle);
      }
      if (storyProgress_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(storyProgress);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Story_Detail other) {
      if (other == null) {
        return;
      }
      if (other.missionId != 0L) {
        missionId = other.missionId;
      }
      if (other.CefrLevel.Length != 0) {
        CefrLevel = other.CefrLevel;
      }
      if (other.storyId != 0L) {
        storyId = other.storyId;
      }
      if (other.storyTitle.Length != 0) {
        storyTitle = other.storyTitle;
      }
      if (other.storyProgress_ != null) {
        if (storyProgress_ == null) {
          storyProgress = new global::Msg.explore.PB_Story_Progress();
        }
        storyProgress.MergeFrom(other.storyProgress);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            missionId = input.ReadInt64();
            break;
          }
          case 18: {
            CefrLevel = input.ReadString();
            break;
          }
          case 24: {
            storyId = input.ReadInt64();
            break;
          }
          case 34: {
            storyTitle = input.ReadString();
            break;
          }
          case 42: {
            if (storyProgress_ == null) {
              storyProgress = new global::Msg.explore.PB_Story_Progress();
            }
            input.ReadMessage(storyProgress);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            missionId = input.ReadInt64();
            break;
          }
          case 18: {
            CefrLevel = input.ReadString();
            break;
          }
          case 24: {
            storyId = input.ReadInt64();
            break;
          }
          case 34: {
            storyTitle = input.ReadString();
            break;
          }
          case 42: {
            if (storyProgress_ == null) {
              storyProgress = new global::Msg.explore.PB_Story_Progress();
            }
            input.ReadMessage(storyProgress);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// Story进度
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Story_Progress : pb::IMessage<PB_Story_Progress>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Story_Progress> _parser = new pb::MessageParser<PB_Story_Progress>(() => new PB_Story_Progress());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Story_Progress> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[16]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Story_Progress() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Story_Progress(PB_Story_Progress other) : this() {
      currentTaskTitle_ = other.currentTaskTitle_;
      currentStepDesc_ = other.currentStepDesc_;
      finishedStepNo_ = other.finishedStepNo_;
      stepTotal_ = other.stepTotal_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Story_Progress Clone() {
      return new PB_Story_Progress(this);
    }

    /// <summary>Field number for the "currentTaskTitle" field.</summary>
    public const int currentTaskTitleFieldNumber = 1;
    private string currentTaskTitle_ = "";
    /// <summary>
    /// 当前task标题（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string currentTaskTitle {
      get { return currentTaskTitle_; }
      set {
        currentTaskTitle_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "currentStepDesc" field.</summary>
    public const int currentStepDescFieldNumber = 2;
    private string currentStepDesc_ = "";
    /// <summary>
    /// 当前step描述（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string currentStepDesc {
      get { return currentStepDesc_; }
      set {
        currentStepDesc_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "finishedStepNo" field.</summary>
    public const int finishedStepNoFieldNumber = 3;
    private int finishedStepNo_;
    /// <summary>
    /// 已完成step数量（必选，从0开始）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int finishedStepNo {
      get { return finishedStepNo_; }
      set {
        finishedStepNo_ = value;
      }
    }

    /// <summary>Field number for the "stepTotal" field.</summary>
    public const int stepTotalFieldNumber = 4;
    private int stepTotal_;
    /// <summary>
    /// step总数量（必选，task下有多少个step）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int stepTotal {
      get { return stepTotal_; }
      set {
        stepTotal_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Story_Progress);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Story_Progress other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (currentTaskTitle != other.currentTaskTitle) return false;
      if (currentStepDesc != other.currentStepDesc) return false;
      if (finishedStepNo != other.finishedStepNo) return false;
      if (stepTotal != other.stepTotal) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (currentTaskTitle.Length != 0) hash ^= currentTaskTitle.GetHashCode();
      if (currentStepDesc.Length != 0) hash ^= currentStepDesc.GetHashCode();
      if (finishedStepNo != 0) hash ^= finishedStepNo.GetHashCode();
      if (stepTotal != 0) hash ^= stepTotal.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (currentTaskTitle.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(currentTaskTitle);
      }
      if (currentStepDesc.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(currentStepDesc);
      }
      if (finishedStepNo != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(finishedStepNo);
      }
      if (stepTotal != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(stepTotal);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (currentTaskTitle.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(currentTaskTitle);
      }
      if (currentStepDesc.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(currentStepDesc);
      }
      if (finishedStepNo != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(finishedStepNo);
      }
      if (stepTotal != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(stepTotal);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (currentTaskTitle.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(currentTaskTitle);
      }
      if (currentStepDesc.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(currentStepDesc);
      }
      if (finishedStepNo != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(finishedStepNo);
      }
      if (stepTotal != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(stepTotal);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Story_Progress other) {
      if (other == null) {
        return;
      }
      if (other.currentTaskTitle.Length != 0) {
        currentTaskTitle = other.currentTaskTitle;
      }
      if (other.currentStepDesc.Length != 0) {
        currentStepDesc = other.currentStepDesc;
      }
      if (other.finishedStepNo != 0) {
        finishedStepNo = other.finishedStepNo;
      }
      if (other.stepTotal != 0) {
        stepTotal = other.stepTotal;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            currentTaskTitle = input.ReadString();
            break;
          }
          case 18: {
            currentStepDesc = input.ReadString();
            break;
          }
          case 24: {
            finishedStepNo = input.ReadInt32();
            break;
          }
          case 32: {
            stepTotal = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            currentTaskTitle = input.ReadString();
            break;
          }
          case 18: {
            currentStepDesc = input.ReadString();
            break;
          }
          case 24: {
            finishedStepNo = input.ReadInt32();
            break;
          }
          case 32: {
            stepTotal = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// Story首轮数据
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Story_First_Round_Data : pb::IMessage<PB_Story_First_Round_Data>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Story_First_Round_Data> _parser = new pb::MessageParser<PB_Story_First_Round_Data>(() => new PB_Story_First_Round_Data());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Story_First_Round_Data> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[17]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Story_First_Round_Data() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Story_First_Round_Data(PB_Story_First_Round_Data other) : this() {
      replyText_ = other.replyText_;
      replyTranslateText_ = other.replyTranslateText_;
      replyBubbleId_ = other.replyBubbleId_;
      replyAudio_ = other.replyAudio_ != null ? other.replyAudio_.Clone() : null;
      emotionAnalysisResult_ = other.emotionAnalysisResult_ != null ? other.emotionAnalysisResult_.Clone() : null;
      exampleText_ = other.exampleText_;
      exampleTranslateText_ = other.exampleTranslateText_;
      exampleBubbleId_ = other.exampleBubbleId_;
      adviceText_ = other.adviceText_;
      adviceBubbleId_ = other.adviceBubbleId_;
      isSettlement_ = other.isSettlement_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Story_First_Round_Data Clone() {
      return new PB_Story_First_Round_Data(this);
    }

    /// <summary>Field number for the "replyText" field.</summary>
    public const int replyTextFieldNumber = 1;
    private string replyText_ = "";
    /// <summary>
    /// avatar回复相关
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string replyText {
      get { return replyText_; }
      set {
        replyText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "replyTranslateText" field.</summary>
    public const int replyTranslateTextFieldNumber = 2;
    private string replyTranslateText_ = "";
    /// <summary>
    /// Avatar回复翻译文本
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string replyTranslateText {
      get { return replyTranslateText_; }
      set {
        replyTranslateText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "replyBubbleId" field.</summary>
    public const int replyBubbleIdFieldNumber = 3;
    private string replyBubbleId_ = "";
    /// <summary>
    /// Avatar回复气泡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string replyBubbleId {
      get { return replyBubbleId_; }
      set {
        replyBubbleId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "replyAudio" field.</summary>
    public const int replyAudioFieldNumber = 4;
    private global::Msg.explore.PB_Explore_MissionStoryChatAudioDownFrame replyAudio_;
    /// <summary>
    /// Avatar回复TTS音频
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_MissionStoryChatAudioDownFrame replyAudio {
      get { return replyAudio_; }
      set {
        replyAudio_ = value;
      }
    }

    /// <summary>Field number for the "emotionAnalysisResult" field.</summary>
    public const int emotionAnalysisResultFieldNumber = 5;
    private global::Msg.explore.PB_EmotionAnalysisResult emotionAnalysisResult_;
    /// <summary>
    /// 情感分析结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_EmotionAnalysisResult emotionAnalysisResult {
      get { return emotionAnalysisResult_; }
      set {
        emotionAnalysisResult_ = value;
      }
    }

    /// <summary>Field number for the "exampleText" field.</summary>
    public const int exampleTextFieldNumber = 6;
    private string exampleText_ = "";
    /// <summary>
    /// 脚手架相关
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string exampleText {
      get { return exampleText_; }
      set {
        exampleText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "exampleTranslateText" field.</summary>
    public const int exampleTranslateTextFieldNumber = 7;
    private string exampleTranslateText_ = "";
    /// <summary>
    /// 用户回复示例翻译文本
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string exampleTranslateText {
      get { return exampleTranslateText_; }
      set {
        exampleTranslateText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "exampleBubbleId" field.</summary>
    public const int exampleBubbleIdFieldNumber = 8;
    private string exampleBubbleId_ = "";
    /// <summary>
    /// 用户回复示例气泡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string exampleBubbleId {
      get { return exampleBubbleId_; }
      set {
        exampleBubbleId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "adviceText" field.</summary>
    public const int adviceTextFieldNumber = 9;
    private string adviceText_ = "";
    /// <summary>
    /// Advice
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string adviceText {
      get { return adviceText_; }
      set {
        adviceText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "adviceBubbleId" field.</summary>
    public const int adviceBubbleIdFieldNumber = 10;
    private string adviceBubbleId_ = "";
    /// <summary>
    /// Advice气泡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string adviceBubbleId {
      get { return adviceBubbleId_; }
      set {
        adviceBubbleId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "isSettlement" field.</summary>
    public const int isSettlementFieldNumber = 11;
    private bool isSettlement_;
    /// <summary>
    /// 是否是结算环节下发
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool isSettlement {
      get { return isSettlement_; }
      set {
        isSettlement_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Story_First_Round_Data);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Story_First_Round_Data other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (replyText != other.replyText) return false;
      if (replyTranslateText != other.replyTranslateText) return false;
      if (replyBubbleId != other.replyBubbleId) return false;
      if (!object.Equals(replyAudio, other.replyAudio)) return false;
      if (!object.Equals(emotionAnalysisResult, other.emotionAnalysisResult)) return false;
      if (exampleText != other.exampleText) return false;
      if (exampleTranslateText != other.exampleTranslateText) return false;
      if (exampleBubbleId != other.exampleBubbleId) return false;
      if (adviceText != other.adviceText) return false;
      if (adviceBubbleId != other.adviceBubbleId) return false;
      if (isSettlement != other.isSettlement) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (replyText.Length != 0) hash ^= replyText.GetHashCode();
      if (replyTranslateText.Length != 0) hash ^= replyTranslateText.GetHashCode();
      if (replyBubbleId.Length != 0) hash ^= replyBubbleId.GetHashCode();
      if (replyAudio_ != null) hash ^= replyAudio.GetHashCode();
      if (emotionAnalysisResult_ != null) hash ^= emotionAnalysisResult.GetHashCode();
      if (exampleText.Length != 0) hash ^= exampleText.GetHashCode();
      if (exampleTranslateText.Length != 0) hash ^= exampleTranslateText.GetHashCode();
      if (exampleBubbleId.Length != 0) hash ^= exampleBubbleId.GetHashCode();
      if (adviceText.Length != 0) hash ^= adviceText.GetHashCode();
      if (adviceBubbleId.Length != 0) hash ^= adviceBubbleId.GetHashCode();
      if (isSettlement != false) hash ^= isSettlement.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (replyText.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(replyText);
      }
      if (replyTranslateText.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(replyTranslateText);
      }
      if (replyBubbleId.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(replyBubbleId);
      }
      if (replyAudio_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(replyAudio);
      }
      if (emotionAnalysisResult_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(emotionAnalysisResult);
      }
      if (exampleText.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(exampleText);
      }
      if (exampleTranslateText.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(exampleTranslateText);
      }
      if (exampleBubbleId.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(exampleBubbleId);
      }
      if (adviceText.Length != 0) {
        output.WriteRawTag(74);
        output.WriteString(adviceText);
      }
      if (adviceBubbleId.Length != 0) {
        output.WriteRawTag(82);
        output.WriteString(adviceBubbleId);
      }
      if (isSettlement != false) {
        output.WriteRawTag(88);
        output.WriteBool(isSettlement);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (replyText.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(replyText);
      }
      if (replyTranslateText.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(replyTranslateText);
      }
      if (replyBubbleId.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(replyBubbleId);
      }
      if (replyAudio_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(replyAudio);
      }
      if (emotionAnalysisResult_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(emotionAnalysisResult);
      }
      if (exampleText.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(exampleText);
      }
      if (exampleTranslateText.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(exampleTranslateText);
      }
      if (exampleBubbleId.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(exampleBubbleId);
      }
      if (adviceText.Length != 0) {
        output.WriteRawTag(74);
        output.WriteString(adviceText);
      }
      if (adviceBubbleId.Length != 0) {
        output.WriteRawTag(82);
        output.WriteString(adviceBubbleId);
      }
      if (isSettlement != false) {
        output.WriteRawTag(88);
        output.WriteBool(isSettlement);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (replyText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(replyText);
      }
      if (replyTranslateText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(replyTranslateText);
      }
      if (replyBubbleId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(replyBubbleId);
      }
      if (replyAudio_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(replyAudio);
      }
      if (emotionAnalysisResult_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(emotionAnalysisResult);
      }
      if (exampleText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(exampleText);
      }
      if (exampleTranslateText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(exampleTranslateText);
      }
      if (exampleBubbleId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(exampleBubbleId);
      }
      if (adviceText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(adviceText);
      }
      if (adviceBubbleId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(adviceBubbleId);
      }
      if (isSettlement != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Story_First_Round_Data other) {
      if (other == null) {
        return;
      }
      if (other.replyText.Length != 0) {
        replyText = other.replyText;
      }
      if (other.replyTranslateText.Length != 0) {
        replyTranslateText = other.replyTranslateText;
      }
      if (other.replyBubbleId.Length != 0) {
        replyBubbleId = other.replyBubbleId;
      }
      if (other.replyAudio_ != null) {
        if (replyAudio_ == null) {
          replyAudio = new global::Msg.explore.PB_Explore_MissionStoryChatAudioDownFrame();
        }
        replyAudio.MergeFrom(other.replyAudio);
      }
      if (other.emotionAnalysisResult_ != null) {
        if (emotionAnalysisResult_ == null) {
          emotionAnalysisResult = new global::Msg.explore.PB_EmotionAnalysisResult();
        }
        emotionAnalysisResult.MergeFrom(other.emotionAnalysisResult);
      }
      if (other.exampleText.Length != 0) {
        exampleText = other.exampleText;
      }
      if (other.exampleTranslateText.Length != 0) {
        exampleTranslateText = other.exampleTranslateText;
      }
      if (other.exampleBubbleId.Length != 0) {
        exampleBubbleId = other.exampleBubbleId;
      }
      if (other.adviceText.Length != 0) {
        adviceText = other.adviceText;
      }
      if (other.adviceBubbleId.Length != 0) {
        adviceBubbleId = other.adviceBubbleId;
      }
      if (other.isSettlement != false) {
        isSettlement = other.isSettlement;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            replyText = input.ReadString();
            break;
          }
          case 18: {
            replyTranslateText = input.ReadString();
            break;
          }
          case 26: {
            replyBubbleId = input.ReadString();
            break;
          }
          case 34: {
            if (replyAudio_ == null) {
              replyAudio = new global::Msg.explore.PB_Explore_MissionStoryChatAudioDownFrame();
            }
            input.ReadMessage(replyAudio);
            break;
          }
          case 42: {
            if (emotionAnalysisResult_ == null) {
              emotionAnalysisResult = new global::Msg.explore.PB_EmotionAnalysisResult();
            }
            input.ReadMessage(emotionAnalysisResult);
            break;
          }
          case 50: {
            exampleText = input.ReadString();
            break;
          }
          case 58: {
            exampleTranslateText = input.ReadString();
            break;
          }
          case 66: {
            exampleBubbleId = input.ReadString();
            break;
          }
          case 74: {
            adviceText = input.ReadString();
            break;
          }
          case 82: {
            adviceBubbleId = input.ReadString();
            break;
          }
          case 88: {
            isSettlement = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            replyText = input.ReadString();
            break;
          }
          case 18: {
            replyTranslateText = input.ReadString();
            break;
          }
          case 26: {
            replyBubbleId = input.ReadString();
            break;
          }
          case 34: {
            if (replyAudio_ == null) {
              replyAudio = new global::Msg.explore.PB_Explore_MissionStoryChatAudioDownFrame();
            }
            input.ReadMessage(replyAudio);
            break;
          }
          case 42: {
            if (emotionAnalysisResult_ == null) {
              emotionAnalysisResult = new global::Msg.explore.PB_EmotionAnalysisResult();
            }
            input.ReadMessage(emotionAnalysisResult);
            break;
          }
          case 50: {
            exampleText = input.ReadString();
            break;
          }
          case 58: {
            exampleTranslateText = input.ReadString();
            break;
          }
          case 66: {
            exampleBubbleId = input.ReadString();
            break;
          }
          case 74: {
            adviceText = input.ReadString();
            break;
          }
          case 82: {
            adviceBubbleId = input.ReadString();
            break;
          }
          case 88: {
            isSettlement = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Task_Intro : pb::IMessage<PB_Task_Intro>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Task_Intro> _parser = new pb::MessageParser<PB_Task_Intro>(() => new PB_Task_Intro());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Task_Intro> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[18]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Intro() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Intro(PB_Task_Intro other) : this() {
      audio_ = other.audio_ != null ? other.audio_.Clone() : null;
      introText_ = other.introText_;
      firstLangText_ = other.firstLangText_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Task_Intro Clone() {
      return new PB_Task_Intro(this);
    }

    /// <summary>Field number for the "audio" field.</summary>
    public const int audioFieldNumber = 1;
    private global::Msg.explore.PB_Explore_MissionStoryChatAudioDownFrame audio_;
    /// <summary>
    /// tts 内容字节流
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_MissionStoryChatAudioDownFrame audio {
      get { return audio_; }
      set {
        audio_ = value;
      }
    }

    /// <summary>Field number for the "introText" field.</summary>
    public const int introTextFieldNumber = 2;
    private string introText_ = "";
    /// <summary>
    /// intro文案
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string introText {
      get { return introText_; }
      set {
        introText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "firstLangText" field.</summary>
    public const int firstLangTextFieldNumber = 3;
    private string firstLangText_ = "";
    /// <summary>
    /// 母语文案
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string firstLangText {
      get { return firstLangText_; }
      set {
        firstLangText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Task_Intro);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Task_Intro other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(audio, other.audio)) return false;
      if (introText != other.introText) return false;
      if (firstLangText != other.firstLangText) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (audio_ != null) hash ^= audio.GetHashCode();
      if (introText.Length != 0) hash ^= introText.GetHashCode();
      if (firstLangText.Length != 0) hash ^= firstLangText.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (audio_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(audio);
      }
      if (introText.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(introText);
      }
      if (firstLangText.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(firstLangText);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (audio_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(audio);
      }
      if (introText.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(introText);
      }
      if (firstLangText.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(firstLangText);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (audio_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(audio);
      }
      if (introText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(introText);
      }
      if (firstLangText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(firstLangText);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Task_Intro other) {
      if (other == null) {
        return;
      }
      if (other.audio_ != null) {
        if (audio_ == null) {
          audio = new global::Msg.explore.PB_Explore_MissionStoryChatAudioDownFrame();
        }
        audio.MergeFrom(other.audio);
      }
      if (other.introText.Length != 0) {
        introText = other.introText;
      }
      if (other.firstLangText.Length != 0) {
        firstLangText = other.firstLangText;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (audio_ == null) {
              audio = new global::Msg.explore.PB_Explore_MissionStoryChatAudioDownFrame();
            }
            input.ReadMessage(audio);
            break;
          }
          case 18: {
            introText = input.ReadString();
            break;
          }
          case 26: {
            firstLangText = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (audio_ == null) {
              audio = new global::Msg.explore.PB_Explore_MissionStoryChatAudioDownFrame();
            }
            input.ReadMessage(audio);
            break;
          }
          case 18: {
            introText = input.ReadString();
            break;
          }
          case 26: {
            firstLangText = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///*
  /// 获取用户历史进度列表请求
  /// 1. 只返回3条数据
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_GetUserHistoryProgressListReq : pb::IMessage<CS_GetUserHistoryProgressListReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_GetUserHistoryProgressListReq> _parser = new pb::MessageParser<CS_GetUserHistoryProgressListReq>(() => new CS_GetUserHistoryProgressListReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_GetUserHistoryProgressListReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[19]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetUserHistoryProgressListReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetUserHistoryProgressListReq(CS_GetUserHistoryProgressListReq other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_GetUserHistoryProgressListReq Clone() {
      return new CS_GetUserHistoryProgressListReq(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_GetUserHistoryProgressListReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_GetUserHistoryProgressListReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_GetUserHistoryProgressListReq other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

  }

  /// <summary>
  ///*
  /// 获取用户历史进度列表响应
  /// 1. 可以理解为，也是拉取了一个列表，获得的数据与预加载列表没有区别
  /// 2. 区别在于，用户需要先看到一个弹窗选择，选哪个把哪个story插进去
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_GetUserHistoryProgressListResp : pb::IMessage<SC_GetUserHistoryProgressListResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_GetUserHistoryProgressListResp> _parser = new pb::MessageParser<SC_GetUserHistoryProgressListResp>(() => new SC_GetUserHistoryProgressListResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_GetUserHistoryProgressListResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[20]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetUserHistoryProgressListResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetUserHistoryProgressListResp(SC_GetUserHistoryProgressListResp other) : this() {
      code_ = other.code_;
      storyPreloadDataList_ = other.storyPreloadDataList_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_GetUserHistoryProgressListResp Clone() {
      return new SC_GetUserHistoryProgressListResp(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "storyPreloadDataList" field.</summary>
    public const int storyPreloadDataListFieldNumber = 2;
    private static readonly pb::FieldCodec<global::Msg.explore.PB_StoryPreloadData> _repeated_storyPreloadDataList_codec
        = pb::FieldCodec.ForMessage(18, global::Msg.explore.PB_StoryPreloadData.Parser);
    private readonly pbc::RepeatedField<global::Msg.explore.PB_StoryPreloadData> storyPreloadDataList_ = new pbc::RepeatedField<global::Msg.explore.PB_StoryPreloadData>();
    /// <summary>
    /// Story预加载数据项列表（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.explore.PB_StoryPreloadData> storyPreloadDataList {
      get { return storyPreloadDataList_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_GetUserHistoryProgressListResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_GetUserHistoryProgressListResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if(!storyPreloadDataList_.Equals(other.storyPreloadDataList_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      hash ^= storyPreloadDataList_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      storyPreloadDataList_.WriteTo(output, _repeated_storyPreloadDataList_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      storyPreloadDataList_.WriteTo(ref output, _repeated_storyPreloadDataList_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      size += storyPreloadDataList_.CalculateSize(_repeated_storyPreloadDataList_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_GetUserHistoryProgressListResp other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      storyPreloadDataList_.Add(other.storyPreloadDataList_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            storyPreloadDataList_.AddEntriesFrom(input, _repeated_storyPreloadDataList_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            storyPreloadDataList_.AddEntriesFrom(ref input, _repeated_storyPreloadDataList_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 推荐功能上行消息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_RecommendUpMsg : pb::IMessage<PB_RecommendUpMsg>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_RecommendUpMsg> _parser = new pb::MessageParser<PB_RecommendUpMsg>(() => new PB_RecommendUpMsg());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_RecommendUpMsg> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[21]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RecommendUpMsg() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RecommendUpMsg(PB_RecommendUpMsg other) : this() {
      switch (other.upBizMsgCase) {
        case upBizMsgOneofCase.userSwitchEntity:
          userSwitchEntity = other.userSwitchEntity.Clone();
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_RecommendUpMsg Clone() {
      return new PB_RecommendUpMsg(this);
    }

    /// <summary>Field number for the "userSwitchEntity" field.</summary>
    public const int userSwitchEntityFieldNumber = 1;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_RecommendSwitchEntity userSwitchEntity {
      get { return upBizMsgCase_ == upBizMsgOneofCase.userSwitchEntity ? (global::Msg.explore.PB_Explore_RecommendSwitchEntity) upBizMsg_ : null; }
      set {
        upBizMsg_ = value;
        upBizMsgCase_ = value == null ? upBizMsgOneofCase.None : upBizMsgOneofCase.userSwitchEntity;
      }
    }

    private object upBizMsg_;
    /// <summary>Enum of possible cases for the "upBizMsg" oneof.</summary>
    public enum upBizMsgOneofCase {
      None = 0,
      userSwitchEntity = 1,
    }
    private upBizMsgOneofCase upBizMsgCase_ = upBizMsgOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public upBizMsgOneofCase upBizMsgCase {
      get { return upBizMsgCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearupBizMsg() {
      upBizMsgCase_ = upBizMsgOneofCase.None;
      upBizMsg_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_RecommendUpMsg);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_RecommendUpMsg other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(userSwitchEntity, other.userSwitchEntity)) return false;
      if (upBizMsgCase != other.upBizMsgCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (upBizMsgCase_ == upBizMsgOneofCase.userSwitchEntity) hash ^= userSwitchEntity.GetHashCode();
      hash ^= (int) upBizMsgCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (upBizMsgCase_ == upBizMsgOneofCase.userSwitchEntity) {
        output.WriteRawTag(10);
        output.WriteMessage(userSwitchEntity);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (upBizMsgCase_ == upBizMsgOneofCase.userSwitchEntity) {
        output.WriteRawTag(10);
        output.WriteMessage(userSwitchEntity);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (upBizMsgCase_ == upBizMsgOneofCase.userSwitchEntity) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(userSwitchEntity);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_RecommendUpMsg other) {
      if (other == null) {
        return;
      }
      switch (other.upBizMsgCase) {
        case upBizMsgOneofCase.userSwitchEntity:
          if (userSwitchEntity == null) {
            userSwitchEntity = new global::Msg.explore.PB_Explore_RecommendSwitchEntity();
          }
          userSwitchEntity.MergeFrom(other.userSwitchEntity);
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            global::Msg.explore.PB_Explore_RecommendSwitchEntity subBuilder = new global::Msg.explore.PB_Explore_RecommendSwitchEntity();
            if (upBizMsgCase_ == upBizMsgOneofCase.userSwitchEntity) {
              subBuilder.MergeFrom(userSwitchEntity);
            }
            input.ReadMessage(subBuilder);
            userSwitchEntity = subBuilder;
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            global::Msg.explore.PB_Explore_RecommendSwitchEntity subBuilder = new global::Msg.explore.PB_Explore_RecommendSwitchEntity();
            if (upBizMsgCase_ == upBizMsgOneofCase.userSwitchEntity) {
              subBuilder.MergeFrom(userSwitchEntity);
            }
            input.ReadMessage(subBuilder);
            userSwitchEntity = subBuilder;
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 推荐上行 - 切换推荐实体
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Explore_RecommendSwitchEntity : pb::IMessage<PB_Explore_RecommendSwitchEntity>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Explore_RecommendSwitchEntity> _parser = new pb::MessageParser<PB_Explore_RecommendSwitchEntity>(() => new PB_Explore_RecommendSwitchEntity());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Explore_RecommendSwitchEntity> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[22]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_RecommendSwitchEntity() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_RecommendSwitchEntity(PB_Explore_RecommendSwitchEntity other) : this() {
      entityId_ = other.entityId_;
      entityType_ = other.entityType_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_RecommendSwitchEntity Clone() {
      return new PB_Explore_RecommendSwitchEntity(this);
    }

    /// <summary>Field number for the "entityId" field.</summary>
    public const int entityIdFieldNumber = 1;
    private long entityId_;
    /// <summary>
    /// 实体id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long entityId {
      get { return entityId_; }
      set {
        entityId_ = value;
      }
    }

    /// <summary>Field number for the "entityType" field.</summary>
    public const int entityTypeFieldNumber = 2;
    private global::Msg.explore.PB_Explore_RecommendEntityType entityType_ = global::Msg.explore.PB_Explore_RecommendEntityType.EO_RE_UNKNOWN;
    /// <summary>
    /// 实体类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_RecommendEntityType entityType {
      get { return entityType_; }
      set {
        entityType_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Explore_RecommendSwitchEntity);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Explore_RecommendSwitchEntity other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (entityId != other.entityId) return false;
      if (entityType != other.entityType) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (entityId != 0L) hash ^= entityId.GetHashCode();
      if (entityType != global::Msg.explore.PB_Explore_RecommendEntityType.EO_RE_UNKNOWN) hash ^= entityType.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (entityId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(entityId);
      }
      if (entityType != global::Msg.explore.PB_Explore_RecommendEntityType.EO_RE_UNKNOWN) {
        output.WriteRawTag(16);
        output.WriteEnum((int) entityType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (entityId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(entityId);
      }
      if (entityType != global::Msg.explore.PB_Explore_RecommendEntityType.EO_RE_UNKNOWN) {
        output.WriteRawTag(16);
        output.WriteEnum((int) entityType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (entityId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(entityId);
      }
      if (entityType != global::Msg.explore.PB_Explore_RecommendEntityType.EO_RE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) entityType);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Explore_RecommendSwitchEntity other) {
      if (other == null) {
        return;
      }
      if (other.entityId != 0L) {
        entityId = other.entityId;
      }
      if (other.entityType != global::Msg.explore.PB_Explore_RecommendEntityType.EO_RE_UNKNOWN) {
        entityType = other.entityType;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            entityId = input.ReadInt64();
            break;
          }
          case 16: {
            entityType = (global::Msg.explore.PB_Explore_RecommendEntityType) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            entityId = input.ReadInt64();
            break;
          }
          case 16: {
            entityType = (global::Msg.explore.PB_Explore_RecommendEntityType) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// 推荐下行 - 切换推荐实体
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_RecommendSwitchEntity : pb::IMessage<SC_RecommendSwitchEntity>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_RecommendSwitchEntity> _parser = new pb::MessageParser<SC_RecommendSwitchEntity>(() => new SC_RecommendSwitchEntity());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_RecommendSwitchEntity> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[23]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_RecommendSwitchEntity() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_RecommendSwitchEntity(SC_RecommendSwitchEntity other) : this() {
      timestamp_ = other.timestamp_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_RecommendSwitchEntity Clone() {
      return new SC_RecommendSwitchEntity(this);
    }

    /// <summary>Field number for the "timestamp" field.</summary>
    public const int timestampFieldNumber = 1;
    private long timestamp_;
    /// <summary>
    /// 切换成功时间戳（单位：秒）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long timestamp {
      get { return timestamp_; }
      set {
        timestamp_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_RecommendSwitchEntity);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_RecommendSwitchEntity other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (timestamp != other.timestamp) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (timestamp != 0L) hash ^= timestamp.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (timestamp != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(timestamp);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (timestamp != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(timestamp);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (timestamp != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(timestamp);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_RecommendSwitchEntity other) {
      if (other == null) {
        return;
      }
      if (other.timestamp != 0L) {
        timestamp = other.timestamp;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            timestamp = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            timestamp = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///*
  /// Mission剧情对话下行 - 下个实体信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_MissionStoryChatNextEntityInfoForSettlement : pb::IMessage<PB_MissionStoryChatNextEntityInfoForSettlement>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_MissionStoryChatNextEntityInfoForSettlement> _parser = new pb::MessageParser<PB_MissionStoryChatNextEntityInfoForSettlement>(() => new PB_MissionStoryChatNextEntityInfoForSettlement());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_MissionStoryChatNextEntityInfoForSettlement> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[24]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MissionStoryChatNextEntityInfoForSettlement() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MissionStoryChatNextEntityInfoForSettlement(PB_MissionStoryChatNextEntityInfoForSettlement other) : this() {
      nextEntityType_ = other.nextEntityType_;
      storyTitle_ = other.storyTitle_;
      taskTitle_ = other.taskTitle_;
      storyPreloadData_ = other.storyPreloadData_ != null ? other.storyPreloadData_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MissionStoryChatNextEntityInfoForSettlement Clone() {
      return new PB_MissionStoryChatNextEntityInfoForSettlement(this);
    }

    /// <summary>Field number for the "nextEntityType" field.</summary>
    public const int nextEntityTypeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_MissionStoryChatNextEntityType nextEntityType_ = global::Msg.explore.PB_Explore_MissionStoryChatNextEntityType.EO_MSC_NET_UNKNOWN;
    /// <summary>
    /// 下个实体类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_MissionStoryChatNextEntityType nextEntityType {
      get { return nextEntityType_; }
      set {
        nextEntityType_ = value;
      }
    }

    /// <summary>Field number for the "storyTitle" field.</summary>
    public const int storyTitleFieldNumber = 2;
    private string storyTitle_ = "";
    /// <summary>
    /// story标题
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string storyTitle {
      get { return storyTitle_; }
      set {
        storyTitle_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "taskTitle" field.</summary>
    public const int taskTitleFieldNumber = 3;
    private string taskTitle_ = "";
    /// <summary>
    /// task标题
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string taskTitle {
      get { return taskTitle_; }
      set {
        taskTitle_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "storyPreloadData" field.</summary>
    public const int storyPreloadDataFieldNumber = 4;
    private global::Msg.explore.PB_StoryPreloadData storyPreloadData_;
    /// <summary>
    /// story首轮信息（注意：只有nextEntityType为任务时才会赋值）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_StoryPreloadData storyPreloadData {
      get { return storyPreloadData_; }
      set {
        storyPreloadData_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_MissionStoryChatNextEntityInfoForSettlement);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_MissionStoryChatNextEntityInfoForSettlement other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (nextEntityType != other.nextEntityType) return false;
      if (storyTitle != other.storyTitle) return false;
      if (taskTitle != other.taskTitle) return false;
      if (!object.Equals(storyPreloadData, other.storyPreloadData)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (nextEntityType != global::Msg.explore.PB_Explore_MissionStoryChatNextEntityType.EO_MSC_NET_UNKNOWN) hash ^= nextEntityType.GetHashCode();
      if (storyTitle.Length != 0) hash ^= storyTitle.GetHashCode();
      if (taskTitle.Length != 0) hash ^= taskTitle.GetHashCode();
      if (storyPreloadData_ != null) hash ^= storyPreloadData.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (nextEntityType != global::Msg.explore.PB_Explore_MissionStoryChatNextEntityType.EO_MSC_NET_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) nextEntityType);
      }
      if (storyTitle.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(storyTitle);
      }
      if (taskTitle.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(taskTitle);
      }
      if (storyPreloadData_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(storyPreloadData);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (nextEntityType != global::Msg.explore.PB_Explore_MissionStoryChatNextEntityType.EO_MSC_NET_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) nextEntityType);
      }
      if (storyTitle.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(storyTitle);
      }
      if (taskTitle.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(taskTitle);
      }
      if (storyPreloadData_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(storyPreloadData);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (nextEntityType != global::Msg.explore.PB_Explore_MissionStoryChatNextEntityType.EO_MSC_NET_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) nextEntityType);
      }
      if (storyTitle.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(storyTitle);
      }
      if (taskTitle.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(taskTitle);
      }
      if (storyPreloadData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(storyPreloadData);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_MissionStoryChatNextEntityInfoForSettlement other) {
      if (other == null) {
        return;
      }
      if (other.nextEntityType != global::Msg.explore.PB_Explore_MissionStoryChatNextEntityType.EO_MSC_NET_UNKNOWN) {
        nextEntityType = other.nextEntityType;
      }
      if (other.storyTitle.Length != 0) {
        storyTitle = other.storyTitle;
      }
      if (other.taskTitle.Length != 0) {
        taskTitle = other.taskTitle;
      }
      if (other.storyPreloadData_ != null) {
        if (storyPreloadData_ == null) {
          storyPreloadData = new global::Msg.explore.PB_StoryPreloadData();
        }
        storyPreloadData.MergeFrom(other.storyPreloadData);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            nextEntityType = (global::Msg.explore.PB_Explore_MissionStoryChatNextEntityType) input.ReadEnum();
            break;
          }
          case 18: {
            storyTitle = input.ReadString();
            break;
          }
          case 26: {
            taskTitle = input.ReadString();
            break;
          }
          case 34: {
            if (storyPreloadData_ == null) {
              storyPreloadData = new global::Msg.explore.PB_StoryPreloadData();
            }
            input.ReadMessage(storyPreloadData);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            nextEntityType = (global::Msg.explore.PB_Explore_MissionStoryChatNextEntityType) input.ReadEnum();
            break;
          }
          case 18: {
            storyTitle = input.ReadString();
            break;
          }
          case 26: {
            taskTitle = input.ReadString();
            break;
          }
          case 34: {
            if (storyPreloadData_ == null) {
              storyPreloadData = new global::Msg.explore.PB_StoryPreloadData();
            }
            input.ReadMessage(storyPreloadData);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///*
  /// Mission剧情对话下行 - 已完成信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_MissionStoryChatFinishedTaskInfoForSettlement : pb::IMessage<PB_MissionStoryChatFinishedTaskInfoForSettlement>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_MissionStoryChatFinishedTaskInfoForSettlement> _parser = new pb::MessageParser<PB_MissionStoryChatFinishedTaskInfoForSettlement>(() => new PB_MissionStoryChatFinishedTaskInfoForSettlement());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_MissionStoryChatFinishedTaskInfoForSettlement> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[25]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MissionStoryChatFinishedTaskInfoForSettlement() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MissionStoryChatFinishedTaskInfoForSettlement(PB_MissionStoryChatFinishedTaskInfoForSettlement other) : this() {
      storyTitle_ = other.storyTitle_;
      taskTitle_ = other.taskTitle_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_MissionStoryChatFinishedTaskInfoForSettlement Clone() {
      return new PB_MissionStoryChatFinishedTaskInfoForSettlement(this);
    }

    /// <summary>Field number for the "storyTitle" field.</summary>
    public const int storyTitleFieldNumber = 1;
    private string storyTitle_ = "";
    /// <summary>
    /// story标题
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string storyTitle {
      get { return storyTitle_; }
      set {
        storyTitle_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "taskTitle" field.</summary>
    public const int taskTitleFieldNumber = 2;
    private string taskTitle_ = "";
    /// <summary>
    /// task标题
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string taskTitle {
      get { return taskTitle_; }
      set {
        taskTitle_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_MissionStoryChatFinishedTaskInfoForSettlement);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_MissionStoryChatFinishedTaskInfoForSettlement other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (storyTitle != other.storyTitle) return false;
      if (taskTitle != other.taskTitle) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (storyTitle.Length != 0) hash ^= storyTitle.GetHashCode();
      if (taskTitle.Length != 0) hash ^= taskTitle.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (storyTitle.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(storyTitle);
      }
      if (taskTitle.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(taskTitle);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (storyTitle.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(storyTitle);
      }
      if (taskTitle.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(taskTitle);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (storyTitle.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(storyTitle);
      }
      if (taskTitle.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(taskTitle);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_MissionStoryChatFinishedTaskInfoForSettlement other) {
      if (other == null) {
        return;
      }
      if (other.storyTitle.Length != 0) {
        storyTitle = other.storyTitle;
      }
      if (other.taskTitle.Length != 0) {
        taskTitle = other.taskTitle;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            storyTitle = input.ReadString();
            break;
          }
          case 18: {
            taskTitle = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            storyTitle = input.ReadString();
            break;
          }
          case 18: {
            taskTitle = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Mission剧情对话下行 - 对话结算
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_MissionStoryChatDownMsgForSettlement : pb::IMessage<SC_MissionStoryChatDownMsgForSettlement>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_MissionStoryChatDownMsgForSettlement> _parser = new pb::MessageParser<SC_MissionStoryChatDownMsgForSettlement>(() => new SC_MissionStoryChatDownMsgForSettlement());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_MissionStoryChatDownMsgForSettlement> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.RecommendReflection.Descriptor.MessageTypes[26]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForSettlement() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForSettlement(SC_MissionStoryChatDownMsgForSettlement other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      finishedTaskInfo_ = other.finishedTaskInfo_ != null ? other.finishedTaskInfo_.Clone() : null;
      nextEntityInfo_ = other.nextEntityInfo_ != null ? other.nextEntityInfo_.Clone() : null;
      checkin_item_ = other.checkin_item_ != null ? other.checkin_item_.Clone() : null;
      taskSpeechTime_ = other.taskSpeechTime_;
      exceedUserPercent_ = other.exceedUserPercent_;
      userCefrLevel_ = other.userCefrLevel_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_MissionStoryChatDownMsgForSettlement Clone() {
      return new SC_MissionStoryChatDownMsgForSettlement(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "finishedTaskInfo" field.</summary>
    public const int finishedTaskInfoFieldNumber = 3;
    private global::Msg.explore.PB_MissionStoryChatFinishedTaskInfoForSettlement finishedTaskInfo_;
    /// <summary>
    /// 已完成任务信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_MissionStoryChatFinishedTaskInfoForSettlement finishedTaskInfo {
      get { return finishedTaskInfo_; }
      set {
        finishedTaskInfo_ = value;
      }
    }

    /// <summary>Field number for the "nextEntityInfo" field.</summary>
    public const int nextEntityInfoFieldNumber = 4;
    private global::Msg.explore.PB_MissionStoryChatNextEntityInfoForSettlement nextEntityInfo_;
    /// <summary>
    /// 下个实体信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_MissionStoryChatNextEntityInfoForSettlement nextEntityInfo {
      get { return nextEntityInfo_; }
      set {
        nextEntityInfo_ = value;
      }
    }

    /// <summary>Field number for the "checkin_item" field.</summary>
    public const int checkin_itemFieldNumber = 5;
    private global::Msg.incentive.PB_CheckinItemForExplore checkin_item_;
    /// <summary>
    /// 签到日历
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.incentive.PB_CheckinItemForExplore checkin_item {
      get { return checkin_item_; }
      set {
        checkin_item_ = value;
      }
    }

    /// <summary>Field number for the "taskSpeechTime" field.</summary>
    public const int taskSpeechTimeFieldNumber = 6;
    private int taskSpeechTime_;
    /// <summary>
    /// 任务发言时长（单位秒）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int taskSpeechTime {
      get { return taskSpeechTime_; }
      set {
        taskSpeechTime_ = value;
      }
    }

    /// <summary>Field number for the "exceedUserPercent" field.</summary>
    public const int exceedUserPercentFieldNumber = 7;
    private int exceedUserPercent_;
    /// <summary>
    /// 超过了百分之多少的用户
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int exceedUserPercent {
      get { return exceedUserPercent_; }
      set {
        exceedUserPercent_ = value;
      }
    }

    /// <summary>Field number for the "userCefrLevel" field.</summary>
    public const int userCefrLevelFieldNumber = 8;
    private string userCefrLevel_ = "";
    /// <summary>
    /// 用户CEFR等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string userCefrLevel {
      get { return userCefrLevel_; }
      set {
        userCefrLevel_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_MissionStoryChatDownMsgForSettlement);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_MissionStoryChatDownMsgForSettlement other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (!object.Equals(finishedTaskInfo, other.finishedTaskInfo)) return false;
      if (!object.Equals(nextEntityInfo, other.nextEntityInfo)) return false;
      if (!object.Equals(checkin_item, other.checkin_item)) return false;
      if (taskSpeechTime != other.taskSpeechTime) return false;
      if (exceedUserPercent != other.exceedUserPercent) return false;
      if (userCefrLevel != other.userCefrLevel) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (finishedTaskInfo_ != null) hash ^= finishedTaskInfo.GetHashCode();
      if (nextEntityInfo_ != null) hash ^= nextEntityInfo.GetHashCode();
      if (checkin_item_ != null) hash ^= checkin_item.GetHashCode();
      if (taskSpeechTime != 0) hash ^= taskSpeechTime.GetHashCode();
      if (exceedUserPercent != 0) hash ^= exceedUserPercent.GetHashCode();
      if (userCefrLevel.Length != 0) hash ^= userCefrLevel.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (finishedTaskInfo_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(finishedTaskInfo);
      }
      if (nextEntityInfo_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(nextEntityInfo);
      }
      if (checkin_item_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(checkin_item);
      }
      if (taskSpeechTime != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(taskSpeechTime);
      }
      if (exceedUserPercent != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(exceedUserPercent);
      }
      if (userCefrLevel.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(userCefrLevel);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (finishedTaskInfo_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(finishedTaskInfo);
      }
      if (nextEntityInfo_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(nextEntityInfo);
      }
      if (checkin_item_ != null) {
        output.WriteRawTag(42);
        output.WriteMessage(checkin_item);
      }
      if (taskSpeechTime != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(taskSpeechTime);
      }
      if (exceedUserPercent != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(exceedUserPercent);
      }
      if (userCefrLevel.Length != 0) {
        output.WriteRawTag(66);
        output.WriteString(userCefrLevel);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (finishedTaskInfo_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(finishedTaskInfo);
      }
      if (nextEntityInfo_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(nextEntityInfo);
      }
      if (checkin_item_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(checkin_item);
      }
      if (taskSpeechTime != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(taskSpeechTime);
      }
      if (exceedUserPercent != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(exceedUserPercent);
      }
      if (userCefrLevel.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(userCefrLevel);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_MissionStoryChatDownMsgForSettlement other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.finishedTaskInfo_ != null) {
        if (finishedTaskInfo_ == null) {
          finishedTaskInfo = new global::Msg.explore.PB_MissionStoryChatFinishedTaskInfoForSettlement();
        }
        finishedTaskInfo.MergeFrom(other.finishedTaskInfo);
      }
      if (other.nextEntityInfo_ != null) {
        if (nextEntityInfo_ == null) {
          nextEntityInfo = new global::Msg.explore.PB_MissionStoryChatNextEntityInfoForSettlement();
        }
        nextEntityInfo.MergeFrom(other.nextEntityInfo);
      }
      if (other.checkin_item_ != null) {
        if (checkin_item_ == null) {
          checkin_item = new global::Msg.incentive.PB_CheckinItemForExplore();
        }
        checkin_item.MergeFrom(other.checkin_item);
      }
      if (other.taskSpeechTime != 0) {
        taskSpeechTime = other.taskSpeechTime;
      }
      if (other.exceedUserPercent != 0) {
        exceedUserPercent = other.exceedUserPercent;
      }
      if (other.userCefrLevel.Length != 0) {
        userCefrLevel = other.userCefrLevel;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            if (finishedTaskInfo_ == null) {
              finishedTaskInfo = new global::Msg.explore.PB_MissionStoryChatFinishedTaskInfoForSettlement();
            }
            input.ReadMessage(finishedTaskInfo);
            break;
          }
          case 34: {
            if (nextEntityInfo_ == null) {
              nextEntityInfo = new global::Msg.explore.PB_MissionStoryChatNextEntityInfoForSettlement();
            }
            input.ReadMessage(nextEntityInfo);
            break;
          }
          case 42: {
            if (checkin_item_ == null) {
              checkin_item = new global::Msg.incentive.PB_CheckinItemForExplore();
            }
            input.ReadMessage(checkin_item);
            break;
          }
          case 48: {
            taskSpeechTime = input.ReadInt32();
            break;
          }
          case 56: {
            exceedUserPercent = input.ReadInt32();
            break;
          }
          case 66: {
            userCefrLevel = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_MissionStoryChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            if (finishedTaskInfo_ == null) {
              finishedTaskInfo = new global::Msg.explore.PB_MissionStoryChatFinishedTaskInfoForSettlement();
            }
            input.ReadMessage(finishedTaskInfo);
            break;
          }
          case 34: {
            if (nextEntityInfo_ == null) {
              nextEntityInfo = new global::Msg.explore.PB_MissionStoryChatNextEntityInfoForSettlement();
            }
            input.ReadMessage(nextEntityInfo);
            break;
          }
          case 42: {
            if (checkin_item_ == null) {
              checkin_item = new global::Msg.incentive.PB_CheckinItemForExplore();
            }
            input.ReadMessage(checkin_item);
            break;
          }
          case 48: {
            taskSpeechTime = input.ReadInt32();
            break;
          }
          case 56: {
            exceedUserPercent = input.ReadInt32();
            break;
          }
          case 66: {
            userCefrLevel = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
