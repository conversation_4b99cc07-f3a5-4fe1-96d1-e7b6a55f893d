using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AnimationSystem;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using ZTemp;
using FilterMode = UnityEngine.FilterMode;
using Object = UnityEngine.Object;

/// <summary>
/// 场景背景根节点管理器，支持加载预制体场景并自动识别挂点
/// 提供完整的角色管理和API功能
/// </summary>
public class BackgroundSceneRoot 
{
    #region 私有字段
    
    // 算场景位置
    private static Vector3 scenePos = new Vector3(0, 1000F, 1000F);
    private static int sceneCount = 0;
    
    // 场景挂点
    private GameObject _sceneRoot;
    private GameObject _character1MountPoint;
    private Camera _mainCamera;
    private GameObject _lightsMountPoint;
    private GameObject _globalVolumeMountPoint;
    private GameObject _locationMountPoint;
    private GameObject _environmentMountPoint;
    private readonly Dictionary<string, GameObject> _locationDict = new Dictionary<string, GameObject>();   
    
    // 角色管理器
    private AvatarRoot _avatarRoot;
    
    // 场景状态
    private bool _isInitialized = false;
    private string _loadedSceneName;
    
    // RenderTexture相关
    private RenderTexture _renderTexture;
    
    // 场景数据存储
    private readonly Dictionary<string, object> _sceneData = new Dictionary<string, object>();
    
    #endregion
    
    #region 公共属性
    
    /// <summary>
    /// Character1 挂点
    /// </summary>
    public GameObject Character1 => _character1MountPoint;
    
    /// <summary>
    /// Main Camera
    /// </summary>
    public Camera MainCamera => _mainCamera;
    
    /// <summary>
    /// Lights 挂点
    /// </summary>
    public GameObject Lights => _lightsMountPoint;
    
    /// <summary>
    /// Global Volume 挂点
    /// </summary>
    public GameObject GlobalVolume => _globalVolumeMountPoint;
    
    /// <summary>
    /// Location 挂点
    /// </summary>
    public GameObject Location => _locationMountPoint;
    
    /// <summary>
    /// 当前角色
    /// </summary>
    public GameObject CurrentAvatar => _avatarRoot?.CurrentAvatar;
    
    /// <summary>
    /// 动画管理器
    /// </summary>
    public AnimationAvatarManager Manager => _avatarRoot?.Manager;
    
    /// <summary>
    /// 是否已初始化
    /// </summary>
    public bool IsInitialized => _isInitialized;
    
    /// <summary>
    /// 当前加载的场景名称
    /// </summary>
    public string LoadedSceneName => _loadedSceneName;
    
    /// <summary>
    /// 渲染纹理
    /// </summary>
    public RenderTexture RenderTexture => _renderTexture;
    
    public GameObject Environment => _environmentMountPoint;
    
    #endregion
    
    #region 场景加载管理方法
    
    /// <summary>
    /// 生成RenderTexture - 加载场景并配置渲染输出
    /// </summary>
    /// <param name="sceneName">场景预制体名称</param>
    /// <param name="avatarId">角色ID，-1表示不加载角色</param>
    /// <returns>配置好的RenderTexture</returns>
    public async Task<RenderTexture> GenerateRT(string sceneName = "WWAAA22233344", long avatarId = -1,int width=750,int height=1624)
    {
        try
        {
            bool sceneLoaded = await LoadScene(sceneName);
            if (!sceneLoaded)
            {
                VFDebug.LogError($"BackgroundSceneRoot: 场景加载失败 {sceneName}");
                return null;
            }
            
            ConfigureCamera(width,height);
            
            if (avatarId > 0)
            {
                bool avatarLoaded = await LoadAvatar(avatarId);
                if (!avatarLoaded)
                {
                    VFDebug.LogWarning($"BackgroundSceneRoot: 角色加载失败 {avatarId}，但场景已加载");
                }
            }
            
            return _renderTexture;
        }
        catch (System.Exception ex)
        {
            VFDebug.LogError($"BackgroundSceneRoot: GenerateRT异常 {sceneName}, error={ex.Message}");
            return null;
        }
    }
    
    /// <summary>
    /// 加载场景预制体
    /// </summary>
    /// <param name="sceneName">场景预制体名称</param>
    /// <returns>加载成功返回true</returns>
    private async Task<bool> LoadScene(string sceneName)
    {
        // 如果已经加载了相同场景，直接返回成功
        if (_isInitialized && _loadedSceneName == sceneName)
        {
            return true;
        }
        
        // 清理之前的场景
        if (_isInitialized)
        {
            ClearScene();
        }
        
        try
        {
            if (GAvatarResManager.instance.CheckPath(sceneName))
            {
                var sceneGameObject = await GAvatarResManager.instance.LoadAsset<GameObject>(sceneName);
                
                if (sceneGameObject)
                {
                    _sceneRoot = sceneGameObject;
                    _sceneRoot.name = $"Scene_{sceneName}";
                    GameObject.DontDestroyOnLoad(_sceneRoot);
                    _sceneRoot.transform.position = new Vector3(scenePos.x + (sceneCount++) * 50, scenePos.y , scenePos.z);
                    
                    AutoDetectMountPoints();
                    ValidateMountPoints();
                    
                    _avatarRoot = new AvatarRoot();
                    
                    _isInitialized = true;
                    _loadedSceneName = sceneName;
                    
                    return true;
                }
                else
                {
                    VFDebug.LogError($"BackgroundSceneRoot: 场景资源加载失败 {sceneName}");
                    return false;
                }
            }
            else
            {
                VFDebug.LogError($"BackgroundSceneRoot: 场景资源不存在 {sceneName}");
                return false;
            }
        }
        catch (System.Exception ex)
        {
            VFDebug.LogError($"BackgroundSceneRoot: 加载场景异常 {sceneName}, error={ex.Message}");
            return false;
        }
    }
    
    /// <summary>
    /// 清理RenderTexture（保留场景）
    /// </summary>
    public void ClearRT()
    {
        if (_mainCamera != null)
        {
            _mainCamera.enabled = false;
            _mainCamera.targetTexture = null;
        }
        
        if (_renderTexture != null)
        {
            _renderTexture.Release();
            _renderTexture = null;
        }

        this.ReleaseAvatar();
        
        Debug.Log("BackgroundSceneRoot: RenderTexture已清理");
    }
    
    /// <summary>
    /// 清理当前场景
    /// </summary>
    public void ClearScene()
    {
        // 先清理RenderTexture
        ClearRT();
        
        if (_sceneRoot)
        {
            Object.Destroy(_sceneRoot);
            _sceneRoot = null;
        }
        
        _avatarRoot?.Dispose();
        _avatarRoot = null;
        _character1MountPoint = null;
        _mainCamera = null;
        _lightsMountPoint = null;
        _globalVolumeMountPoint = null;
        _environmentMountPoint = null;
        _locationMountPoint = null;
        _locationDict.Clear();
        ClearSceneData();
        
        _isInitialized = false;
        _loadedSceneName = null;
        
    }
    
    public void CloseRendering()
    {
        if (this._mainCamera)
        {
            this._mainCamera.enabled = false;
        }

        if (this._lightsMountPoint != null)
        {
            this._lightsMountPoint.SetActive(false);
        }
    }
    
    public void EnableRendering()
    {
        if (this._mainCamera)
        {
            this._mainCamera.enabled = true;
        }

        if (this._lightsMountPoint != null)
        {
            this._lightsMountPoint.SetActive(true);
        }
    }
    
    #endregion
    
    #region 角色管理方法
    
    /// <summary>
    /// 加载角色到 Character1 挂点
    /// </summary>
    /// <param name="avatarId">角色ID</param>
    /// <returns>加载成功返回true</returns>
    public async Task<bool> LoadAvatar(long avatarId)
    {
        if (!_isInitialized)
        {
            Debug.LogError("BackgroundSceneRoot: 场景未初始化，无法加载角色");
            return false;
        }
        
        if (_character1MountPoint == null)
        {
            Debug.LogError("BackgroundSceneRoot: Character1 挂点不存在，无法加载角色");
            return false;
        }
        
        return await _avatarRoot.LoadAvatarAsync(avatarId, _character1MountPoint.transform);
    }
    
    /// <summary>
    /// 释放当前角色
    /// </summary>
    public void ReleaseAvatar()
    {
        _avatarRoot?.ReleaseCurrentAvatar();
    }
    
    #endregion
    
    #region 动画API
    
    /// <summary>
    /// 基于配表播放动画
    /// </summary>
    public void PlayAnimationByExcel(string excelAnimation, Action<StarX5PlayAnimationState.AnimCallBack> cb = null, float targetDuration = 0f)
    {
        _avatarRoot?.PlayAnimationByExcel(excelAnimation, cb, targetDuration);
    }
    
    /// <summary>
    /// 基于名称播放动画
    /// </summary>
    public void PlayAnimationByName(string nameAnimation, Action<StarX5PlayAnimationState.AnimCallBack> cb = null, float targetDuration = 0f)
    {
        _avatarRoot?.PlayAnimationByName(nameAnimation, cb, targetDuration);
    }
    
    #endregion
    
    #region 音频API
    
    /// <summary>
    /// 播放角色TTS音频
    /// </summary>
    public void PlayAvatarTTS(AudioClip audioClip, float rate = 1f, float volume = 1f)
    {
        _avatarRoot?.PlayAvatarTTS(audioClip, rate, volume);
    }
    
    /// <summary>
    /// 播放角色TTS音频（通过URL）
    /// </summary>
    public void PlayAvatarTTS(string url, float volume = 1f)
    {
        _avatarRoot?.PlayAvatarTTS(url, volume);
    }
    
    /// <summary>
    /// 停止角色TTS音频
    /// </summary>
    public void StopAvatarTTS()
    {
        _avatarRoot?.StopAvatarTTS();
    }
    
    #endregion
    
    #region 挂点控制方法
    
    /// <summary>
    /// 设置角色挂点变换
    /// </summary>
    public void SetCharacterTransform(Vector3? position = null, Quaternion? rotation = null, Vector3? scale = null)
    {
        if (_character1MountPoint != null)
        {
            var transform = _character1MountPoint.transform;
            if (position.HasValue) transform.localPosition = position.Value;
            if (rotation.HasValue) transform.localRotation = rotation.Value;
            if (scale.HasValue) transform.localScale = scale.Value;
        }
    }
    
    /// <summary>
    /// 设置摄像机变换
    /// </summary>
    public void SetCameraTransform(Vector3? position = null, Quaternion? rotation = null)
    {
        if (_mainCamera != null)
        {
            var transform = _mainCamera.transform;
            if (position.HasValue) transform.localPosition = position.Value;
            if (rotation.HasValue) transform.localRotation = rotation.Value;
        }
    }
    
    /// <summary>
    /// 设置灯光挂点变换
    /// </summary>
    public void SetLightsTransform(Vector3? position = null, Quaternion? rotation = null, Vector3? scale = null)
    {
        if (_lightsMountPoint != null)
        {
            var transform = _lightsMountPoint.transform;
            if (position.HasValue) transform.localPosition = position.Value;
            if (rotation.HasValue) transform.localRotation = rotation.Value;
            if (scale.HasValue) transform.localScale = scale.Value;
        }
    }
    
    /// <summary>
    /// 设置后效挂点变换
    /// </summary>
    public void SetGlobalVolumeTransform(Vector3? position = null, Quaternion? rotation = null, Vector3? scale = null)
    {
        if (_globalVolumeMountPoint != null)
        {
            var transform = _globalVolumeMountPoint.transform;
            if (position.HasValue) transform.localPosition = position.Value;
            if (rotation.HasValue) transform.localRotation = rotation.Value;
            if (scale.HasValue) transform.localScale = scale.Value;
        }
    }
    
    #endregion
    
    #region Location挂点管理方法
    
    /// <summary>
    /// 获取Location下指定名称的挂点
    /// </summary>
    /// <param name="mountPointName">挂点名称</param>
    /// <returns>找到返回GameObject，否则返回null</returns>
    public GameObject GetLocationMountPoint(string mountPointName)
    {
        if (string.IsNullOrEmpty(mountPointName))
        {
            VFDebug.LogError("BackgroundSceneRoot: 挂点名称不能为空");
            return null;
        }
        
        if (_locationDict.TryGetValue(mountPointName, out GameObject cachedMountPoint))
        {
            if (cachedMountPoint != null)
            {
                return cachedMountPoint;
            }
            else
            {
                _locationDict.Remove(mountPointName);
            }
        }
        
        GameObject foundMountPoint = FindChildByName(_locationMountPoint.transform, mountPointName);
        
        if (foundMountPoint != null)
        {
            _locationDict[mountPointName] = foundMountPoint;
        }
        else
        {
            VFDebug.LogWarning($"BackgroundSceneRoot: 未找到Location挂点 {mountPointName}");
        }
        
        return foundMountPoint;
    }
    
    #endregion
    
    #region 相机管理办法
    
    public void SetCameraRenderPipeline(int pipeline)
    {
        if (this._mainCamera)
        {
            this._mainCamera.GetUniversalAdditionalCameraData()?.SetRenderer(pipeline);
        }
    }
    
    #endregion
    
    #region 场景数据管理方法
    
    /// <summary>
    /// 设置场景关联数据
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="key">数据键</param>
    /// <param name="value">数据值</param>
    public void SetSceneData<T>(string key, T value)
    {
        if (string.IsNullOrEmpty(key)) 
        {
            Debug.LogWarning("BackgroundSceneRoot: 场景数据键不能为空");
            return;
        }
        
        _sceneData[key] = value;
        Debug.Log($"BackgroundSceneRoot: 设置场景数据 {key} = {value}");
    }
    
    /// <summary>
    /// 获取场景关联数据
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="key">数据键</param>
    /// <returns>数据值，不存在则返回默认值</returns>
    public T GetSceneData<T>(string key)
    {
        if (string.IsNullOrEmpty(key))
        {
            VFDebug.LogWarning("BackgroundSceneRoot: 场景数据键不能为空");
            return default(T);
        }
        
        if (!_sceneData.TryGetValue(key, out object value))
        {
            VFDebug.LogWarning($"BackgroundSceneRoot: 场景数据不存在 {key}");
            return default(T);
        }
        
        if (value is T typedValue)
        {
            return typedValue;
        }
        
        VFDebug.LogWarning($"BackgroundSceneRoot: 场景数据类型不匹配 {key}, 期望类型: {typeof(T)}, 实际类型: {value?.GetType()}");
        return default(T);
    }
    
    /// <summary>
    /// 获取场景关联数据，带默认值
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="key">数据键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>数据值，不存在或类型不匹配则返回默认值</returns>
    public T GetSceneData<T>(string key, T defaultValue)
    {
        if (string.IsNullOrEmpty(key))
        {
            return defaultValue;
        }
        
        if (!_sceneData.TryGetValue(key, out object value))
        {
            return defaultValue;
        }
        
        return value is T typedValue ? typedValue : defaultValue;
    }
    
    /// <summary>
    /// 检查是否存在指定场景数据
    /// </summary>
    /// <param name="key">数据键</param>
    /// <returns>存在返回true</returns>
    public bool HasSceneData(string key)
    {
        return !string.IsNullOrEmpty(key) && _sceneData.ContainsKey(key);
    }
    
    /// <summary>
    /// 移除场景数据
    /// </summary>
    /// <param name="key">数据键</param>
    /// <returns>成功移除返回true</returns>
    public bool RemoveSceneData(string key)
    {
        if (string.IsNullOrEmpty(key))
        {
            return false;
        }
        
        bool removed = _sceneData.Remove(key);
        if (removed)
        {
            Debug.Log($"BackgroundSceneRoot: 移除场景数据 {key}");
        }
        
        return removed;
    }
    
    /// <summary>
    /// 清空所有场景数据
    /// </summary>
    public void ClearSceneData()
    {
        int count = _sceneData.Count;
        _sceneData.Clear();
        
        if (count > 0)
        {
            Debug.Log($"BackgroundSceneRoot: 清空场景数据，共清理 {count} 个数据项");
        }
    }
    
    /// <summary>
    /// 获取所有场景数据键
    /// </summary>
    /// <returns>数据键集合</returns>
    public IEnumerable<string> GetSceneDataKeys()
    {
        return _sceneData.Keys;
    }
    
    /// <summary>
    /// 获取场景数据数量
    /// </summary>
    /// <returns>数据项数量</returns>
    public int GetSceneDataCount()
    {
        return _sceneData.Count;
    }
    
    #endregion
    
    #region 私有工具方法
    
    /// <summary>
    /// 配置摄像机和RenderTexture
    /// </summary>
    private void ConfigureCamera(int RT_WIDTH,int RT_HEIGHT)
    {
        if (_mainCamera == null)
        {
            VFDebug.LogError("BackgroundSceneRoot: MainCamera不存在，无法配置渲染");
            return;
        }
        
        if (_renderTexture != null)
        {
            _renderTexture.Release();
        }
        
        _renderTexture = new RenderTexture(RT_WIDTH, RT_HEIGHT, 24, RenderTextureFormat.ARGB32);
        _renderTexture.autoGenerateMips = false;
        _renderTexture.antiAliasing = 1;
        _renderTexture.filterMode = FilterMode.Bilinear;
        _renderTexture.name = $"BackgroundSceneRT-{this.LoadedSceneName}";
        
        _mainCamera.targetTexture = _renderTexture;
        _mainCamera.enabled = true;
        
        VFDebug.Log($"BackgroundSceneRoot: 摄像机配置完成，RenderTexture尺寸：{RT_WIDTH}x{RT_HEIGHT}");
    }
    
    /// <summary>
    /// 自动识别场景挂点
    /// </summary>
    private void AutoDetectMountPoints()
    {
        if (_sceneRoot == null) return;
        
        // 在场景根节点下查找指定名称的挂点
        _character1MountPoint = FindChildByName(_sceneRoot.transform, "Character1");
        
        var mainCameraGo = FindChildByName(_sceneRoot.transform, "SceneCamera");
        _mainCamera = mainCameraGo?.GetComponent<Camera>();
        
        _lightsMountPoint = FindChildByName(_sceneRoot.transform, "Lights");
        _globalVolumeMountPoint = FindChildByName(_sceneRoot.transform, "Global Volume");
        _locationMountPoint = FindChildByName(_sceneRoot.transform, "Location");
        _environmentMountPoint = FindChildByName(_sceneRoot.transform, "Environment");
        
        // 初始化Location挂点字典
        InitializeLocationDict();
        
        Debug.Log($"BackgroundSceneRoot: 挂点识别完成 " +
                  $"Character1={_character1MountPoint != null}, " +
                  $"MainCamera={_mainCamera != null}, " +
                  $"Lights={_lightsMountPoint != null}, " +
                  $"GlobalVolume={_globalVolumeMountPoint != null}, " +
                  $"Location={_locationMountPoint != null}");
    }
    
    /// <summary>
    /// 递归查找子对象
    /// </summary>
    private GameObject FindChildByName(Transform parent, string name)
    {
        // 直接子对象查找
        for (int i = 0; i < parent.childCount; i++)
        {
            var child = parent.GetChild(i);
            if (child.name == name)
            {
                return child.gameObject;
            }
        }
        
        // 递归查找
        for (int i = 0; i < parent.childCount; i++)
        {
            var result = FindChildByName(parent.GetChild(i), name);
            if (result != null)
            {
                return result;
            }
        }
        
        return null;
    }
    
    /// <summary>
    /// 初始化Location挂点字典
    /// </summary>
    private void InitializeLocationDict()
    {
        _locationDict.Clear();
        
        if (_locationMountPoint == null)
        {
            return;
        }
        
        // 预加载Location下的所有直接子挂点到缓存
        for (int i = 0; i < _locationMountPoint.transform.childCount; i++)
        {
            var child = _locationMountPoint.transform.GetChild(i);
            _locationDict[child.name] = child.gameObject;
        }
        
        Debug.Log($"BackgroundSceneRoot: Location挂点字典初始化完成，共{_locationDict.Count}个挂点");
    }
    
    /// <summary>
    /// 验证挂点有效性
    /// </summary>
    private void ValidateMountPoints()
    {
        if (_character1MountPoint == null)
        {
            Debug.LogWarning("BackgroundSceneRoot: 未找到 Character1 挂点");
        }
        
        if (_mainCamera == null || _mainCamera.GetComponent<Camera>() == null)
        {
            Debug.LogWarning("BackgroundSceneRoot: 未找到 Main Camera");
        }
        
        if (_lightsMountPoint == null)
        {
            Debug.LogWarning("BackgroundSceneRoot: 未找到 Lights 挂点");
        }
        
        if (_globalVolumeMountPoint == null)
        {
            Debug.LogWarning("BackgroundSceneRoot: 未找到 Global Volume 挂点");
        }
        
        if (_locationMountPoint == null)
        {
            Debug.LogWarning("BackgroundSceneRoot: 未找到 Location 挂点");
        }
    }
    
    #endregion
}