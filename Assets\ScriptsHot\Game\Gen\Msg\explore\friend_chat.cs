// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/explore/friend_chat.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.explore {

  /// <summary>Holder for reflection information generated from protobuf/explore/friend_chat.proto</summary>
  public static partial class FriendChatReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/explore/friend_chat.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static FriendChatReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CiJwcm90b2J1Zi9leHBsb3JlL2ZyaWVuZF9jaGF0LnByb3RvGhtwcm90b2J1",
            "Zi9leHBsb3JlL2Jhc2UucHJvdG8aG3Byb3RvYnVmL2Jhc2ljL2RpYWxvZy5w",
            "cm90byKoAQoSUEJfRnJpZW5kQ2hhdFVwTXNnEhAKCGF2YXRhcklkGAEgASgD",
            "EjwKDnVzZXJJbnB1dEF1ZGlvGAQgASgLMiIuUEJfRXhwbG9yZV9GcmllbmRD",
            "aGF0QXVkaW9VcEZyYW1lSAASNgoKdXBCaXpFdmVudBgFIAEoDjIgLlBCX0V4",
            "cGxvcmVfRnJpZW5kQ2hhdFVwQml6RXZlbnRIAEIKCgh1cEJpek1zZyJdCiFQ",
            "Ql9FeHBsb3JlX0ZyaWVuZENoYXRBdWRpb1VwRnJhbWUSDQoFYXVkaW8YASAB",
            "KAwSEwoLc2FtcGxlX3JhdGUYAiABKA0SFAoMbnVtX2NoYW5uZWxzGAMgASgN",
            "IpQBCiNQQl9FeHBsb3JlX0ZyaWVuZENoYXRBdWRpb0Rvd25GcmFtZRIKCgJp",
            "ZBgBIAEoBBINCgVhdWRpbxgCIAEoDBITCgtzYW1wbGVfcmF0ZRgDIAEoDRIU",
            "CgxudW1fY2hhbm5lbHMYBCABKA0SEQoJYnViYmxlX2lkGAUgASgJEhQKDGlz",
            "X2xhc3RfY2xpcBgGIAEoCCKfAQooUEJfRnJpZW5kQ2hhdENvbW1vbkRhdGFG",
            "aWVsZEZvckRhdGFDbGFzcxIQCghkaWFsb2dJZBgBIAEoAxINCgVtc2dJZBgC",
            "IAEoAxIQCghidWJibGVJZBgDIAEoCRINCgVyb3VuZBgEIAEoBRIfCghtc2dP",
            "d25lchgFIAEoDjINLlBCX01zZ0JlbG9uZxIQCghhdmF0YXJJZBgGIAEoAyLm",
            "AQoiU0NfRnJpZW5kQ2hhdERvd25Nc2dGb3JBdmF0YXJSZXBseRIhCgRjb2Rl",
            "GAEgASgOMhMuUEJfRXhwbG9yZV9CaXpDb2RlEj0KCmNvbW1vbkRhdGEYAiAB",
            "KAsyKS5QQl9GcmllbmRDaGF0Q29tbW9uRGF0YUZpZWxkRm9yRGF0YUNsYXNz",
            "EhEKCXJlcGx5VGV4dBgDIAEoCRIaChJJc1VwZ3JhZGVDbG9zZW5lc3MYBCAB",
            "KAgSFQoNSXNDaGFuZ2VUb3BpYxgFIAEoCBIYChBjaGFuZ2VUb3BpY1RpdGxl",
            "GAYgASgJIqsBCitTQ19GcmllbmRDaGF0RG93bk1zZ0ZvckF2YXRhclJlcGx5",
            "VHJhbnNsYXRlEiEKBGNvZGUYASABKA4yEy5QQl9FeHBsb3JlX0JpekNvZGUS",
            "PQoKY29tbW9uRGF0YRgCIAEoCzIpLlBCX0ZyaWVuZENoYXRDb21tb25EYXRh",
            "RmllbGRGb3JEYXRhQ2xhc3MSGgoScmVwbHlUcmFuc2xhdGVUZXh0GAMgASgJ",
            "IvgBCiVTQ19GcmllbmRDaGF0RG93bk1zZ0ZvckF2YXRhclJlcGx5VFRTEiEK",
            "BGNvZGUYASABKA4yEy5QQl9FeHBsb3JlX0JpekNvZGUSPQoKY29tbW9uRGF0",
            "YRgCIAEoCzIpLlBCX0ZyaWVuZENoYXRDb21tb25EYXRhRmllbGRGb3JEYXRh",
            "Q2xhc3MSMwoFYXVkaW8YAyABKAsyJC5QQl9FeHBsb3JlX0ZyaWVuZENoYXRB",
            "dWRpb0Rvd25GcmFtZRI4ChVlbW90aW9uQW5hbHlzaXNSZXN1bHQYBCABKAsy",
            "GS5QQl9FbW90aW9uQW5hbHlzaXNSZXN1bHQijAEKGlNDX0ZyaWVuZENoYXRE",
            "b3duTXNnRm9yQVNSEiEKBGNvZGUYASABKA4yEy5QQl9FeHBsb3JlX0JpekNv",
            "ZGUSPQoKY29tbW9uRGF0YRgCIAEoCzIpLlBCX0ZyaWVuZENoYXRDb21tb25E",
            "YXRhRmllbGRGb3JEYXRhQ2xhc3MSDAoEdGV4dBgDIAEoCSK3AQofU0NfRnJp",
            "ZW5kQ2hhdERvd25Nc2dGb3JCaXpFdmVudBIhCgRjb2RlGAEgASgOMhMuUEJf",
            "RXhwbG9yZV9CaXpDb2RlEj0KCmNvbW1vbkRhdGEYAiABKAsyKS5QQl9Gcmll",
            "bmRDaGF0Q29tbW9uRGF0YUZpZWxkRm9yRGF0YUNsYXNzEjIKCGJpekV2ZW50",
            "GAMgASgOMiAuUEJfRXhwbG9yZV9GcmllbmRDaGF0VXBCaXpFdmVudCLTAQou",
            "U0NfRnJpZW5kQ2hhdERvd25Nc2dGb3JDbG9zZW5lc3NQcm9ncmVzc0NoYW5n",
            "ZRIhCgRjb2RlGAEgASgOMhMuUEJfRXhwbG9yZV9CaXpDb2RlEj0KCmNvbW1v",
            "bkRhdGEYAiABKAsyKS5QQl9GcmllbmRDaGF0Q29tbW9uRGF0YUZpZWxkRm9y",
            "RGF0YUNsYXNzEj8KFWNsb3NlbmVzc1Byb2dyZXNzSW5mbxgDIAEoCzIgLlBC",
            "X0ZyaWVuZF9DbG9zZW5lc3NQcm9ncmVzc0luZm8ioAEKH1BCX0ZyaWVuZF9D",
            "bG9zZW5lc3NQcm9ncmVzc0luZm8SEwoLYmVmb3JlVmFsdWUYASABKAUSEgoK",
            "YWZ0ZXJWYWx1ZRgCIAEoBRIRCglpc1VwZ3JhZGUYBiABKAgSQQoWY2xvc2Vu",
            "ZXNzVXBncmFkZURldGFpbBgHIAEoCzIhLlBCX0ZyaWVuZF9DbG9zZW5lc3NV",
            "cGdyYWRlRGV0YWlsIlMKIFBCX0ZyaWVuZF9DbG9zZW5lc3NVcGdyYWRlRGV0",
            "YWlsEhwKFG5leHRDbG9zZW5lc3NMZXZlbElkGAEgASgDEhEKCWluaXRWYWx1",
            "ZRgCIAEoBSrWAQofUEJfRXhwbG9yZV9GcmllbmRDaGF0VXBCaXpFdmVudBIg",
            "ChxFT19GQ19VTktOT1dOX0JJWl9FVkVOVF9UWVBFEAASGwoXRU9fRkNfVVNF",
            "Ul9NQU5VQUxfU1RBUlQQARIcChhFT19GQ19VU0VSX01BTlVBTF9TVUJNSVQQ",
            "AhIcChhFT19GQ19VU0VSX1NXSVRDSF9NQU5VQUwQAxIaChZFT19GQ19VU0VS",
            "X1NXSVRDSF9BVVRPEAQSHAoYRU9fRkNfVVNFUl9DQU5DRUxfVVBMT0FEEAUq",
            "6AMKJFBCX0V4cGxvcmVfRnJpZW5kQ2hhdERvd25CaXpEYXRhVHlwZRIfChtF",
            "T19GQ19VTktOT1dOX0JJWl9EQVRBX1RZUEUQABITCg9FT19GQ19CSVpfRVZF",
            "TlQQARIkCiBFT19GQ19TUEVFQ0hfVE9fVEVYVF9SRUNPR05JWklORxACEiMK",
            "H0VPX0ZDX1NQRUVDSF9UT19URVhUX1JFQ09HTklaRUQQAxIYChRFT19GQ19U",
            "RVhUX1RPX1NQRUVDSBAEEhYKEkVPX0ZDX0FWQVRBUl9SRVBMWRAFEiAKHEVP",
            "X0ZDX0FWQVRBUl9SRVBMWV9UUkFOU0xBVEUQBhIcChhFT19GQ19VU0VSX1JF",
            "UExZX0VYQU1QTEUQBxImCiJFT19GQ19VU0VSX1JFUExZX0VYQU1QTEVfVFJB",
            "TlNMQVRFEAgSEgoORU9fRkNfTExNX1RURkIQCRIXChNFT19GQ19DVVJSRU5U",
            "X1JPVU5EEAoSGgoWRU9fRkNfSVNfRklOSVNIX0RJQUxPRxALEhAKDEVPX0ZD",
            "X0FEVklDRRAMEhoKFkVPX0ZDX0FEVklDRV9UUkFOU0xBVEUQDRIXChNFT19G",
            "Q19UQVNLX1BST0dSRVNTEA4SFQoRRU9fRkNfVEFTS19SRVNVTFQQD0IqWhp2",
            "Zl9wcm90b2J1Zi9zZXJ2ZXIvZXhwbG9yZaoCC01zZy5leHBsb3JlYgZwcm90",
            "bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Msg.explore.BaseReflection.Descriptor, global::Msg.basic.DialogReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Msg.explore.PB_Explore_FriendChatUpBizEvent), typeof(global::Msg.explore.PB_Explore_FriendChatDownBizDataType), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_FriendChatUpMsg), global::Msg.explore.PB_FriendChatUpMsg.Parser, new[]{ "avatarId", "userInputAudio", "upBizEvent" }, new[]{ "upBizMsg" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Explore_FriendChatAudioUpFrame), global::Msg.explore.PB_Explore_FriendChatAudioUpFrame.Parser, new[]{ "audio", "sample_rate", "num_channels" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Explore_FriendChatAudioDownFrame), global::Msg.explore.PB_Explore_FriendChatAudioDownFrame.Parser, new[]{ "id", "audio", "sample_rate", "num_channels", "bubble_id", "is_last_clip" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass), global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass.Parser, new[]{ "dialogId", "msgId", "bubbleId", "round", "msgOwner", "avatarId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_FriendChatDownMsgForAvatarReply), global::Msg.explore.SC_FriendChatDownMsgForAvatarReply.Parser, new[]{ "code", "commonData", "replyText", "IsUpgradeCloseness", "IsChangeTopic", "changeTopicTitle" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_FriendChatDownMsgForAvatarReplyTranslate), global::Msg.explore.SC_FriendChatDownMsgForAvatarReplyTranslate.Parser, new[]{ "code", "commonData", "replyTranslateText" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_FriendChatDownMsgForAvatarReplyTTS), global::Msg.explore.SC_FriendChatDownMsgForAvatarReplyTTS.Parser, new[]{ "code", "commonData", "audio", "emotionAnalysisResult" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_FriendChatDownMsgForASR), global::Msg.explore.SC_FriendChatDownMsgForASR.Parser, new[]{ "code", "commonData", "text" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_FriendChatDownMsgForBizEvent), global::Msg.explore.SC_FriendChatDownMsgForBizEvent.Parser, new[]{ "code", "commonData", "bizEvent" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_FriendChatDownMsgForClosenessProgressChange), global::Msg.explore.SC_FriendChatDownMsgForClosenessProgressChange.Parser, new[]{ "code", "commonData", "closenessProgressInfo" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Friend_ClosenessProgressInfo), global::Msg.explore.PB_Friend_ClosenessProgressInfo.Parser, new[]{ "beforeValue", "afterValue", "isUpgrade", "closenessUpgradeDetail" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_Friend_ClosenessUpgradeDetail), global::Msg.explore.PB_Friend_ClosenessUpgradeDetail.Parser, new[]{ "nextClosenessLevelId", "initValue" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  /// <summary>
  ///
  /// Friend对话上行 - 业务事件枚举
  /// </summary>
  public enum PB_Explore_FriendChatUpBizEvent {
    /// <summary>
    /// 无意义
    /// </summary>
    [pbr::OriginalName("EO_FC_UNKNOWN_BIZ_EVENT_TYPE")] EO_FC_UNKNOWN_BIZ_EVENT_TYPE = 0,
    /// <summary>
    /// 用户手动开始
    /// </summary>
    [pbr::OriginalName("EO_FC_USER_MANUAL_START")] EO_FC_USER_MANUAL_START = 1,
    /// <summary>
    /// 用户手动提交
    /// </summary>
    [pbr::OriginalName("EO_FC_USER_MANUAL_SUBMIT")] EO_FC_USER_MANUAL_SUBMIT = 2,
    /// <summary>
    /// 切换手动模式
    /// </summary>
    [pbr::OriginalName("EO_FC_USER_SWITCH_MANUAL")] EO_FC_USER_SWITCH_MANUAL = 3,
    /// <summary>
    /// 切换自动模式
    /// </summary>
    [pbr::OriginalName("EO_FC_USER_SWITCH_AUTO")] EO_FC_USER_SWITCH_AUTO = 4,
    /// <summary>
    /// 用户取消收音
    /// </summary>
    [pbr::OriginalName("EO_FC_USER_CANCEL_UPLOAD")] EO_FC_USER_CANCEL_UPLOAD = 5,
  }

  /// <summary>
  ///
  /// Friend对话下行 - 业务数据类型枚举
  /// </summary>
  public enum PB_Explore_FriendChatDownBizDataType {
    /// <summary>
    /// 无意义
    /// </summary>
    [pbr::OriginalName("EO_FC_UNKNOWN_BIZ_DATA_TYPE")] EO_FC_UNKNOWN_BIZ_DATA_TYPE = 0,
    /// <summary>
    /// 业务事件
    /// </summary>
    [pbr::OriginalName("EO_FC_BIZ_EVENT")] EO_FC_BIZ_EVENT = 1,
    /// <summary>
    /// 语音识别过程结果
    /// </summary>
    [pbr::OriginalName("EO_FC_SPEECH_TO_TEXT_RECOGNIZING")] EO_FC_SPEECH_TO_TEXT_RECOGNIZING = 2,
    /// <summary>
    /// 语音识别最终结果
    /// </summary>
    [pbr::OriginalName("EO_FC_SPEECH_TO_TEXT_RECOGNIZED")] EO_FC_SPEECH_TO_TEXT_RECOGNIZED = 3,
    /// <summary>
    /// 语音合成结果
    /// </summary>
    [pbr::OriginalName("EO_FC_TEXT_TO_SPEECH")] EO_FC_TEXT_TO_SPEECH = 4,
    /// <summary>
    /// Avatar回复文本
    /// </summary>
    [pbr::OriginalName("EO_FC_AVATAR_REPLY")] EO_FC_AVATAR_REPLY = 5,
    /// <summary>
    /// Avatar回复文本翻译
    /// </summary>
    [pbr::OriginalName("EO_FC_AVATAR_REPLY_TRANSLATE")] EO_FC_AVATAR_REPLY_TRANSLATE = 6,
    /// <summary>
    /// 用户回复示例
    /// </summary>
    [pbr::OriginalName("EO_FC_USER_REPLY_EXAMPLE")] EO_FC_USER_REPLY_EXAMPLE = 7,
    /// <summary>
    /// 用户回复示例翻译
    /// </summary>
    [pbr::OriginalName("EO_FC_USER_REPLY_EXAMPLE_TRANSLATE")] EO_FC_USER_REPLY_EXAMPLE_TRANSLATE = 8,
    /// <summary>
    /// LLM首包时延（TTFB 代表 "Time to First Byte"（首字节时间））
    /// </summary>
    [pbr::OriginalName("EO_FC_LLM_TTFB")] EO_FC_LLM_TTFB = 9,
    /// <summary>
    /// 当前轮次
    /// </summary>
    [pbr::OriginalName("EO_FC_CURRENT_ROUND")] EO_FC_CURRENT_ROUND = 10,
    /// <summary>
    /// 是否结束对话
    /// </summary>
    [pbr::OriginalName("EO_FC_IS_FINISH_DIALOG")] EO_FC_IS_FINISH_DIALOG = 11,
    /// <summary>
    /// 用户建议
    /// </summary>
    [pbr::OriginalName("EO_FC_ADVICE")] EO_FC_ADVICE = 12,
    /// <summary>
    /// 用户建议翻译
    /// </summary>
    [pbr::OriginalName("EO_FC_ADVICE_TRANSLATE")] EO_FC_ADVICE_TRANSLATE = 13,
    /// <summary>
    /// 任务进度
    /// </summary>
    [pbr::OriginalName("EO_FC_TASK_PROGRESS")] EO_FC_TASK_PROGRESS = 14,
    /// <summary>
    /// 任务完成结果
    /// </summary>
    [pbr::OriginalName("EO_FC_TASK_RESULT")] EO_FC_TASK_RESULT = 15,
  }

  #endregion

  #region Messages
  /// <summary>
  ///
  /// Friend对话功能上行消息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_FriendChatUpMsg : pb::IMessage<PB_FriendChatUpMsg>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_FriendChatUpMsg> _parser = new pb::MessageParser<PB_FriendChatUpMsg>(() => new PB_FriendChatUpMsg());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_FriendChatUpMsg> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.FriendChatReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_FriendChatUpMsg() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_FriendChatUpMsg(PB_FriendChatUpMsg other) : this() {
      avatarId_ = other.avatarId_;
      switch (other.upBizMsgCase) {
        case upBizMsgOneofCase.userInputAudio:
          userInputAudio = other.userInputAudio.Clone();
          break;
        case upBizMsgOneofCase.upBizEvent:
          upBizEvent = other.upBizEvent;
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_FriendChatUpMsg Clone() {
      return new PB_FriendChatUpMsg(this);
    }

    /// <summary>Field number for the "avatarId" field.</summary>
    public const int avatarIdFieldNumber = 1;
    private long avatarId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long avatarId {
      get { return avatarId_; }
      set {
        avatarId_ = value;
      }
    }

    /// <summary>Field number for the "userInputAudio" field.</summary>
    public const int userInputAudioFieldNumber = 4;
    /// <summary>
    /// 用户输入音频
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_FriendChatAudioUpFrame userInputAudio {
      get { return upBizMsgCase_ == upBizMsgOneofCase.userInputAudio ? (global::Msg.explore.PB_Explore_FriendChatAudioUpFrame) upBizMsg_ : null; }
      set {
        upBizMsg_ = value;
        upBizMsgCase_ = value == null ? upBizMsgOneofCase.None : upBizMsgOneofCase.userInputAudio;
      }
    }

    /// <summary>Field number for the "upBizEvent" field.</summary>
    public const int upBizEventFieldNumber = 5;
    /// <summary>
    /// 上行业务事件
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_FriendChatUpBizEvent upBizEvent {
      get { return HasupBizEvent ? (global::Msg.explore.PB_Explore_FriendChatUpBizEvent) upBizMsg_ : global::Msg.explore.PB_Explore_FriendChatUpBizEvent.EO_FC_UNKNOWN_BIZ_EVENT_TYPE; }
      set {
        upBizMsg_ = value;
        upBizMsgCase_ = upBizMsgOneofCase.upBizEvent;
      }
    }
    /// <summary>Gets whether the "upBizEvent" field is set</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool HasupBizEvent {
      get { return upBizMsgCase_ == upBizMsgOneofCase.upBizEvent; }
    }
    /// <summary> Clears the value of the oneof if it's currently set to "upBizEvent" </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearupBizEvent() {
      if (HasupBizEvent) {
        ClearupBizMsg();
      }
    }

    private object upBizMsg_;
    /// <summary>Enum of possible cases for the "upBizMsg" oneof.</summary>
    public enum upBizMsgOneofCase {
      None = 0,
      userInputAudio = 4,
      upBizEvent = 5,
    }
    private upBizMsgOneofCase upBizMsgCase_ = upBizMsgOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public upBizMsgOneofCase upBizMsgCase {
      get { return upBizMsgCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearupBizMsg() {
      upBizMsgCase_ = upBizMsgOneofCase.None;
      upBizMsg_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_FriendChatUpMsg);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_FriendChatUpMsg other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (avatarId != other.avatarId) return false;
      if (!object.Equals(userInputAudio, other.userInputAudio)) return false;
      if (upBizEvent != other.upBizEvent) return false;
      if (upBizMsgCase != other.upBizMsgCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (avatarId != 0L) hash ^= avatarId.GetHashCode();
      if (upBizMsgCase_ == upBizMsgOneofCase.userInputAudio) hash ^= userInputAudio.GetHashCode();
      if (HasupBizEvent) hash ^= upBizEvent.GetHashCode();
      hash ^= (int) upBizMsgCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (avatarId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(avatarId);
      }
      if (upBizMsgCase_ == upBizMsgOneofCase.userInputAudio) {
        output.WriteRawTag(34);
        output.WriteMessage(userInputAudio);
      }
      if (HasupBizEvent) {
        output.WriteRawTag(40);
        output.WriteEnum((int) upBizEvent);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (avatarId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(avatarId);
      }
      if (upBizMsgCase_ == upBizMsgOneofCase.userInputAudio) {
        output.WriteRawTag(34);
        output.WriteMessage(userInputAudio);
      }
      if (HasupBizEvent) {
        output.WriteRawTag(40);
        output.WriteEnum((int) upBizEvent);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (avatarId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(avatarId);
      }
      if (upBizMsgCase_ == upBizMsgOneofCase.userInputAudio) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(userInputAudio);
      }
      if (HasupBizEvent) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) upBizEvent);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_FriendChatUpMsg other) {
      if (other == null) {
        return;
      }
      if (other.avatarId != 0L) {
        avatarId = other.avatarId;
      }
      switch (other.upBizMsgCase) {
        case upBizMsgOneofCase.userInputAudio:
          if (userInputAudio == null) {
            userInputAudio = new global::Msg.explore.PB_Explore_FriendChatAudioUpFrame();
          }
          userInputAudio.MergeFrom(other.userInputAudio);
          break;
        case upBizMsgOneofCase.upBizEvent:
          upBizEvent = other.upBizEvent;
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            avatarId = input.ReadInt64();
            break;
          }
          case 34: {
            global::Msg.explore.PB_Explore_FriendChatAudioUpFrame subBuilder = new global::Msg.explore.PB_Explore_FriendChatAudioUpFrame();
            if (upBizMsgCase_ == upBizMsgOneofCase.userInputAudio) {
              subBuilder.MergeFrom(userInputAudio);
            }
            input.ReadMessage(subBuilder);
            userInputAudio = subBuilder;
            break;
          }
          case 40: {
            upBizMsg_ = input.ReadEnum();
            upBizMsgCase_ = upBizMsgOneofCase.upBizEvent;
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            avatarId = input.ReadInt64();
            break;
          }
          case 34: {
            global::Msg.explore.PB_Explore_FriendChatAudioUpFrame subBuilder = new global::Msg.explore.PB_Explore_FriendChatAudioUpFrame();
            if (upBizMsgCase_ == upBizMsgOneofCase.userInputAudio) {
              subBuilder.MergeFrom(userInputAudio);
            }
            input.ReadMessage(subBuilder);
            userInputAudio = subBuilder;
            break;
          }
          case 40: {
            upBizMsg_ = input.ReadEnum();
            upBizMsgCase_ = upBizMsgOneofCase.upBizEvent;
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Friend对话上行 - 音频框架（用户录音）
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Explore_FriendChatAudioUpFrame : pb::IMessage<PB_Explore_FriendChatAudioUpFrame>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Explore_FriendChatAudioUpFrame> _parser = new pb::MessageParser<PB_Explore_FriendChatAudioUpFrame>(() => new PB_Explore_FriendChatAudioUpFrame());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Explore_FriendChatAudioUpFrame> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.FriendChatReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_FriendChatAudioUpFrame() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_FriendChatAudioUpFrame(PB_Explore_FriendChatAudioUpFrame other) : this() {
      audio_ = other.audio_;
      sample_rate_ = other.sample_rate_;
      num_channels_ = other.num_channels_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_FriendChatAudioUpFrame Clone() {
      return new PB_Explore_FriendChatAudioUpFrame(this);
    }

    /// <summary>Field number for the "audio" field.</summary>
    public const int audioFieldNumber = 1;
    private pb::ByteString audio_ = pb::ByteString.Empty;
    /// <summary>
    /// 音频数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString audio {
      get { return audio_; }
      set {
        audio_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "sample_rate" field.</summary>
    public const int sample_rateFieldNumber = 2;
    private uint sample_rate_;
    /// <summary>
    /// 采样率
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint sample_rate {
      get { return sample_rate_; }
      set {
        sample_rate_ = value;
      }
    }

    /// <summary>Field number for the "num_channels" field.</summary>
    public const int num_channelsFieldNumber = 3;
    private uint num_channels_;
    /// <summary>
    /// 声道数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint num_channels {
      get { return num_channels_; }
      set {
        num_channels_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Explore_FriendChatAudioUpFrame);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Explore_FriendChatAudioUpFrame other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (audio != other.audio) return false;
      if (sample_rate != other.sample_rate) return false;
      if (num_channels != other.num_channels) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (audio.Length != 0) hash ^= audio.GetHashCode();
      if (sample_rate != 0) hash ^= sample_rate.GetHashCode();
      if (num_channels != 0) hash ^= num_channels.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (audio.Length != 0) {
        output.WriteRawTag(10);
        output.WriteBytes(audio);
      }
      if (sample_rate != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(sample_rate);
      }
      if (num_channels != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(num_channels);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (audio.Length != 0) {
        output.WriteRawTag(10);
        output.WriteBytes(audio);
      }
      if (sample_rate != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(sample_rate);
      }
      if (num_channels != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(num_channels);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (audio.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(audio);
      }
      if (sample_rate != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(sample_rate);
      }
      if (num_channels != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(num_channels);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Explore_FriendChatAudioUpFrame other) {
      if (other == null) {
        return;
      }
      if (other.audio.Length != 0) {
        audio = other.audio;
      }
      if (other.sample_rate != 0) {
        sample_rate = other.sample_rate;
      }
      if (other.num_channels != 0) {
        num_channels = other.num_channels;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            audio = input.ReadBytes();
            break;
          }
          case 16: {
            sample_rate = input.ReadUInt32();
            break;
          }
          case 24: {
            num_channels = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            audio = input.ReadBytes();
            break;
          }
          case 16: {
            sample_rate = input.ReadUInt32();
            break;
          }
          case 24: {
            num_channels = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Friend对话音频下行框架（TTS）
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Explore_FriendChatAudioDownFrame : pb::IMessage<PB_Explore_FriendChatAudioDownFrame>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Explore_FriendChatAudioDownFrame> _parser = new pb::MessageParser<PB_Explore_FriendChatAudioDownFrame>(() => new PB_Explore_FriendChatAudioDownFrame());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Explore_FriendChatAudioDownFrame> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.FriendChatReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_FriendChatAudioDownFrame() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_FriendChatAudioDownFrame(PB_Explore_FriendChatAudioDownFrame other) : this() {
      id_ = other.id_;
      audio_ = other.audio_;
      sample_rate_ = other.sample_rate_;
      num_channels_ = other.num_channels_;
      bubble_id_ = other.bubble_id_;
      is_last_clip_ = other.is_last_clip_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Explore_FriendChatAudioDownFrame Clone() {
      return new PB_Explore_FriendChatAudioDownFrame(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int idFieldNumber = 1;
    private ulong id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "audio" field.</summary>
    public const int audioFieldNumber = 2;
    private pb::ByteString audio_ = pb::ByteString.Empty;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString audio {
      get { return audio_; }
      set {
        audio_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "sample_rate" field.</summary>
    public const int sample_rateFieldNumber = 3;
    private uint sample_rate_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint sample_rate {
      get { return sample_rate_; }
      set {
        sample_rate_ = value;
      }
    }

    /// <summary>Field number for the "num_channels" field.</summary>
    public const int num_channelsFieldNumber = 4;
    private uint num_channels_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint num_channels {
      get { return num_channels_; }
      set {
        num_channels_ = value;
      }
    }

    /// <summary>Field number for the "bubble_id" field.</summary>
    public const int bubble_idFieldNumber = 5;
    private string bubble_id_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string bubble_id {
      get { return bubble_id_; }
      set {
        bubble_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "is_last_clip" field.</summary>
    public const int is_last_clipFieldNumber = 6;
    private bool is_last_clip_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is_last_clip {
      get { return is_last_clip_; }
      set {
        is_last_clip_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Explore_FriendChatAudioDownFrame);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Explore_FriendChatAudioDownFrame other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (id != other.id) return false;
      if (audio != other.audio) return false;
      if (sample_rate != other.sample_rate) return false;
      if (num_channels != other.num_channels) return false;
      if (bubble_id != other.bubble_id) return false;
      if (is_last_clip != other.is_last_clip) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (id != 0UL) hash ^= id.GetHashCode();
      if (audio.Length != 0) hash ^= audio.GetHashCode();
      if (sample_rate != 0) hash ^= sample_rate.GetHashCode();
      if (num_channels != 0) hash ^= num_channels.GetHashCode();
      if (bubble_id.Length != 0) hash ^= bubble_id.GetHashCode();
      if (is_last_clip != false) hash ^= is_last_clip.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(id);
      }
      if (audio.Length != 0) {
        output.WriteRawTag(18);
        output.WriteBytes(audio);
      }
      if (sample_rate != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(sample_rate);
      }
      if (num_channels != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(num_channels);
      }
      if (bubble_id.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(bubble_id);
      }
      if (is_last_clip != false) {
        output.WriteRawTag(48);
        output.WriteBool(is_last_clip);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(id);
      }
      if (audio.Length != 0) {
        output.WriteRawTag(18);
        output.WriteBytes(audio);
      }
      if (sample_rate != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(sample_rate);
      }
      if (num_channels != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(num_channels);
      }
      if (bubble_id.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(bubble_id);
      }
      if (is_last_clip != false) {
        output.WriteRawTag(48);
        output.WriteBool(is_last_clip);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (id != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(id);
      }
      if (audio.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(audio);
      }
      if (sample_rate != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(sample_rate);
      }
      if (num_channels != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(num_channels);
      }
      if (bubble_id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(bubble_id);
      }
      if (is_last_clip != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Explore_FriendChatAudioDownFrame other) {
      if (other == null) {
        return;
      }
      if (other.id != 0UL) {
        id = other.id;
      }
      if (other.audio.Length != 0) {
        audio = other.audio;
      }
      if (other.sample_rate != 0) {
        sample_rate = other.sample_rate;
      }
      if (other.num_channels != 0) {
        num_channels = other.num_channels;
      }
      if (other.bubble_id.Length != 0) {
        bubble_id = other.bubble_id;
      }
      if (other.is_last_clip != false) {
        is_last_clip = other.is_last_clip;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            id = input.ReadUInt64();
            break;
          }
          case 18: {
            audio = input.ReadBytes();
            break;
          }
          case 24: {
            sample_rate = input.ReadUInt32();
            break;
          }
          case 32: {
            num_channels = input.ReadUInt32();
            break;
          }
          case 42: {
            bubble_id = input.ReadString();
            break;
          }
          case 48: {
            is_last_clip = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            id = input.ReadUInt64();
            break;
          }
          case 18: {
            audio = input.ReadBytes();
            break;
          }
          case 24: {
            sample_rate = input.ReadUInt32();
            break;
          }
          case 32: {
            num_channels = input.ReadUInt32();
            break;
          }
          case 42: {
            bubble_id = input.ReadString();
            break;
          }
          case 48: {
            is_last_clip = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Friend对话下行 - 通用数据字段（数据类）
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_FriendChatCommonDataFieldForDataClass : pb::IMessage<PB_FriendChatCommonDataFieldForDataClass>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_FriendChatCommonDataFieldForDataClass> _parser = new pb::MessageParser<PB_FriendChatCommonDataFieldForDataClass>(() => new PB_FriendChatCommonDataFieldForDataClass());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_FriendChatCommonDataFieldForDataClass> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.FriendChatReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_FriendChatCommonDataFieldForDataClass() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_FriendChatCommonDataFieldForDataClass(PB_FriendChatCommonDataFieldForDataClass other) : this() {
      dialogId_ = other.dialogId_;
      msgId_ = other.msgId_;
      bubbleId_ = other.bubbleId_;
      round_ = other.round_;
      msgOwner_ = other.msgOwner_;
      avatarId_ = other.avatarId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_FriendChatCommonDataFieldForDataClass Clone() {
      return new PB_FriendChatCommonDataFieldForDataClass(this);
    }

    /// <summary>Field number for the "dialogId" field.</summary>
    public const int dialogIdFieldNumber = 1;
    private long dialogId_;
    /// <summary>
    /// 对话id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long dialogId {
      get { return dialogId_; }
      set {
        dialogId_ = value;
      }
    }

    /// <summary>Field number for the "msgId" field.</summary>
    public const int msgIdFieldNumber = 2;
    private long msgId_;
    /// <summary>
    /// 消息id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long msgId {
      get { return msgId_; }
      set {
        msgId_ = value;
      }
    }

    /// <summary>Field number for the "bubbleId" field.</summary>
    public const int bubbleIdFieldNumber = 3;
    private string bubbleId_ = "";
    /// <summary>
    /// 气泡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string bubbleId {
      get { return bubbleId_; }
      set {
        bubbleId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "round" field.</summary>
    public const int roundFieldNumber = 4;
    private int round_;
    /// <summary>
    /// 对话轮次
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int round {
      get { return round_; }
      set {
        round_ = value;
      }
    }

    /// <summary>Field number for the "msgOwner" field.</summary>
    public const int msgOwnerFieldNumber = 5;
    private global::Msg.basic.PB_MsgBelong msgOwner_ = global::Msg.basic.PB_MsgBelong.BNone;
    /// <summary>
    /// 消息归属方
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_MsgBelong msgOwner {
      get { return msgOwner_; }
      set {
        msgOwner_ = value;
      }
    }

    /// <summary>Field number for the "avatarId" field.</summary>
    public const int avatarIdFieldNumber = 6;
    private long avatarId_;
    /// <summary>
    /// avatar_id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long avatarId {
      get { return avatarId_; }
      set {
        avatarId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_FriendChatCommonDataFieldForDataClass);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_FriendChatCommonDataFieldForDataClass other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (dialogId != other.dialogId) return false;
      if (msgId != other.msgId) return false;
      if (bubbleId != other.bubbleId) return false;
      if (round != other.round) return false;
      if (msgOwner != other.msgOwner) return false;
      if (avatarId != other.avatarId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (dialogId != 0L) hash ^= dialogId.GetHashCode();
      if (msgId != 0L) hash ^= msgId.GetHashCode();
      if (bubbleId.Length != 0) hash ^= bubbleId.GetHashCode();
      if (round != 0) hash ^= round.GetHashCode();
      if (msgOwner != global::Msg.basic.PB_MsgBelong.BNone) hash ^= msgOwner.GetHashCode();
      if (avatarId != 0L) hash ^= avatarId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (dialogId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(dialogId);
      }
      if (msgId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(msgId);
      }
      if (bubbleId.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(bubbleId);
      }
      if (round != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(round);
      }
      if (msgOwner != global::Msg.basic.PB_MsgBelong.BNone) {
        output.WriteRawTag(40);
        output.WriteEnum((int) msgOwner);
      }
      if (avatarId != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(avatarId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (dialogId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(dialogId);
      }
      if (msgId != 0L) {
        output.WriteRawTag(16);
        output.WriteInt64(msgId);
      }
      if (bubbleId.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(bubbleId);
      }
      if (round != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(round);
      }
      if (msgOwner != global::Msg.basic.PB_MsgBelong.BNone) {
        output.WriteRawTag(40);
        output.WriteEnum((int) msgOwner);
      }
      if (avatarId != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(avatarId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (dialogId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(dialogId);
      }
      if (msgId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(msgId);
      }
      if (bubbleId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(bubbleId);
      }
      if (round != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(round);
      }
      if (msgOwner != global::Msg.basic.PB_MsgBelong.BNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) msgOwner);
      }
      if (avatarId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(avatarId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_FriendChatCommonDataFieldForDataClass other) {
      if (other == null) {
        return;
      }
      if (other.dialogId != 0L) {
        dialogId = other.dialogId;
      }
      if (other.msgId != 0L) {
        msgId = other.msgId;
      }
      if (other.bubbleId.Length != 0) {
        bubbleId = other.bubbleId;
      }
      if (other.round != 0) {
        round = other.round;
      }
      if (other.msgOwner != global::Msg.basic.PB_MsgBelong.BNone) {
        msgOwner = other.msgOwner;
      }
      if (other.avatarId != 0L) {
        avatarId = other.avatarId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            dialogId = input.ReadInt64();
            break;
          }
          case 16: {
            msgId = input.ReadInt64();
            break;
          }
          case 26: {
            bubbleId = input.ReadString();
            break;
          }
          case 32: {
            round = input.ReadInt32();
            break;
          }
          case 40: {
            msgOwner = (global::Msg.basic.PB_MsgBelong) input.ReadEnum();
            break;
          }
          case 48: {
            avatarId = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            dialogId = input.ReadInt64();
            break;
          }
          case 16: {
            msgId = input.ReadInt64();
            break;
          }
          case 26: {
            bubbleId = input.ReadString();
            break;
          }
          case 32: {
            round = input.ReadInt32();
            break;
          }
          case 40: {
            msgOwner = (global::Msg.basic.PB_MsgBelong) input.ReadEnum();
            break;
          }
          case 48: {
            avatarId = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Friend对话下行 - Avatar回复
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_FriendChatDownMsgForAvatarReply : pb::IMessage<SC_FriendChatDownMsgForAvatarReply>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_FriendChatDownMsgForAvatarReply> _parser = new pb::MessageParser<SC_FriendChatDownMsgForAvatarReply>(() => new SC_FriendChatDownMsgForAvatarReply());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_FriendChatDownMsgForAvatarReply> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.FriendChatReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_FriendChatDownMsgForAvatarReply() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_FriendChatDownMsgForAvatarReply(SC_FriendChatDownMsgForAvatarReply other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      replyText_ = other.replyText_;
      IsUpgradeCloseness_ = other.IsUpgradeCloseness_;
      IsChangeTopic_ = other.IsChangeTopic_;
      changeTopicTitle_ = other.changeTopicTitle_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_FriendChatDownMsgForAvatarReply Clone() {
      return new SC_FriendChatDownMsgForAvatarReply(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "replyText" field.</summary>
    public const int replyTextFieldNumber = 3;
    private string replyText_ = "";
    /// <summary>
    /// Avatar回复文本
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string replyText {
      get { return replyText_; }
      set {
        replyText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "IsUpgradeCloseness" field.</summary>
    public const int IsUpgradeClosenessFieldNumber = 4;
    private bool IsUpgradeCloseness_;
    /// <summary>
    /// 是否亲密度升级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsUpgradeCloseness {
      get { return IsUpgradeCloseness_; }
      set {
        IsUpgradeCloseness_ = value;
      }
    }

    /// <summary>Field number for the "IsChangeTopic" field.</summary>
    public const int IsChangeTopicFieldNumber = 5;
    private bool IsChangeTopic_;
    /// <summary>
    /// 是否变更话题
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsChangeTopic {
      get { return IsChangeTopic_; }
      set {
        IsChangeTopic_ = value;
      }
    }

    /// <summary>Field number for the "changeTopicTitle" field.</summary>
    public const int changeTopicTitleFieldNumber = 6;
    private string changeTopicTitle_ = "";
    /// <summary>
    /// 变更话题标题
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string changeTopicTitle {
      get { return changeTopicTitle_; }
      set {
        changeTopicTitle_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_FriendChatDownMsgForAvatarReply);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_FriendChatDownMsgForAvatarReply other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (replyText != other.replyText) return false;
      if (IsUpgradeCloseness != other.IsUpgradeCloseness) return false;
      if (IsChangeTopic != other.IsChangeTopic) return false;
      if (changeTopicTitle != other.changeTopicTitle) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (replyText.Length != 0) hash ^= replyText.GetHashCode();
      if (IsUpgradeCloseness != false) hash ^= IsUpgradeCloseness.GetHashCode();
      if (IsChangeTopic != false) hash ^= IsChangeTopic.GetHashCode();
      if (changeTopicTitle.Length != 0) hash ^= changeTopicTitle.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (replyText.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(replyText);
      }
      if (IsUpgradeCloseness != false) {
        output.WriteRawTag(32);
        output.WriteBool(IsUpgradeCloseness);
      }
      if (IsChangeTopic != false) {
        output.WriteRawTag(40);
        output.WriteBool(IsChangeTopic);
      }
      if (changeTopicTitle.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(changeTopicTitle);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (replyText.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(replyText);
      }
      if (IsUpgradeCloseness != false) {
        output.WriteRawTag(32);
        output.WriteBool(IsUpgradeCloseness);
      }
      if (IsChangeTopic != false) {
        output.WriteRawTag(40);
        output.WriteBool(IsChangeTopic);
      }
      if (changeTopicTitle.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(changeTopicTitle);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (replyText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(replyText);
      }
      if (IsUpgradeCloseness != false) {
        size += 1 + 1;
      }
      if (IsChangeTopic != false) {
        size += 1 + 1;
      }
      if (changeTopicTitle.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(changeTopicTitle);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_FriendChatDownMsgForAvatarReply other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.replyText.Length != 0) {
        replyText = other.replyText;
      }
      if (other.IsUpgradeCloseness != false) {
        IsUpgradeCloseness = other.IsUpgradeCloseness;
      }
      if (other.IsChangeTopic != false) {
        IsChangeTopic = other.IsChangeTopic;
      }
      if (other.changeTopicTitle.Length != 0) {
        changeTopicTitle = other.changeTopicTitle;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            replyText = input.ReadString();
            break;
          }
          case 32: {
            IsUpgradeCloseness = input.ReadBool();
            break;
          }
          case 40: {
            IsChangeTopic = input.ReadBool();
            break;
          }
          case 50: {
            changeTopicTitle = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            replyText = input.ReadString();
            break;
          }
          case 32: {
            IsUpgradeCloseness = input.ReadBool();
            break;
          }
          case 40: {
            IsChangeTopic = input.ReadBool();
            break;
          }
          case 50: {
            changeTopicTitle = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Friend对话下行 - Avatar回复翻译
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_FriendChatDownMsgForAvatarReplyTranslate : pb::IMessage<SC_FriendChatDownMsgForAvatarReplyTranslate>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_FriendChatDownMsgForAvatarReplyTranslate> _parser = new pb::MessageParser<SC_FriendChatDownMsgForAvatarReplyTranslate>(() => new SC_FriendChatDownMsgForAvatarReplyTranslate());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_FriendChatDownMsgForAvatarReplyTranslate> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.FriendChatReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_FriendChatDownMsgForAvatarReplyTranslate() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_FriendChatDownMsgForAvatarReplyTranslate(SC_FriendChatDownMsgForAvatarReplyTranslate other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      replyTranslateText_ = other.replyTranslateText_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_FriendChatDownMsgForAvatarReplyTranslate Clone() {
      return new SC_FriendChatDownMsgForAvatarReplyTranslate(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "replyTranslateText" field.</summary>
    public const int replyTranslateTextFieldNumber = 3;
    private string replyTranslateText_ = "";
    /// <summary>
    /// Avatar回复文本翻译
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string replyTranslateText {
      get { return replyTranslateText_; }
      set {
        replyTranslateText_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_FriendChatDownMsgForAvatarReplyTranslate);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_FriendChatDownMsgForAvatarReplyTranslate other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (replyTranslateText != other.replyTranslateText) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (replyTranslateText.Length != 0) hash ^= replyTranslateText.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (replyTranslateText.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(replyTranslateText);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (replyTranslateText.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(replyTranslateText);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (replyTranslateText.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(replyTranslateText);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_FriendChatDownMsgForAvatarReplyTranslate other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.replyTranslateText.Length != 0) {
        replyTranslateText = other.replyTranslateText;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            replyTranslateText = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            replyTranslateText = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Friend对话下行 - Avatar回复TTS
  /// 1. 流式下发：一个音频会出现多个结果
  /// 2. 非流式下发：一个音频只有一个结果
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_FriendChatDownMsgForAvatarReplyTTS : pb::IMessage<SC_FriendChatDownMsgForAvatarReplyTTS>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_FriendChatDownMsgForAvatarReplyTTS> _parser = new pb::MessageParser<SC_FriendChatDownMsgForAvatarReplyTTS>(() => new SC_FriendChatDownMsgForAvatarReplyTTS());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_FriendChatDownMsgForAvatarReplyTTS> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.FriendChatReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_FriendChatDownMsgForAvatarReplyTTS() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_FriendChatDownMsgForAvatarReplyTTS(SC_FriendChatDownMsgForAvatarReplyTTS other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      audio_ = other.audio_ != null ? other.audio_.Clone() : null;
      emotionAnalysisResult_ = other.emotionAnalysisResult_ != null ? other.emotionAnalysisResult_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_FriendChatDownMsgForAvatarReplyTTS Clone() {
      return new SC_FriendChatDownMsgForAvatarReplyTTS(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "audio" field.</summary>
    public const int audioFieldNumber = 3;
    private global::Msg.explore.PB_Explore_FriendChatAudioDownFrame audio_;
    /// <summary>
    /// 语音合成结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_FriendChatAudioDownFrame audio {
      get { return audio_; }
      set {
        audio_ = value;
      }
    }

    /// <summary>Field number for the "emotionAnalysisResult" field.</summary>
    public const int emotionAnalysisResultFieldNumber = 4;
    private global::Msg.explore.PB_EmotionAnalysisResult emotionAnalysisResult_;
    /// <summary>
    /// 情感分析结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_EmotionAnalysisResult emotionAnalysisResult {
      get { return emotionAnalysisResult_; }
      set {
        emotionAnalysisResult_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_FriendChatDownMsgForAvatarReplyTTS);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_FriendChatDownMsgForAvatarReplyTTS other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (!object.Equals(audio, other.audio)) return false;
      if (!object.Equals(emotionAnalysisResult, other.emotionAnalysisResult)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (audio_ != null) hash ^= audio.GetHashCode();
      if (emotionAnalysisResult_ != null) hash ^= emotionAnalysisResult.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (audio_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(audio);
      }
      if (emotionAnalysisResult_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(emotionAnalysisResult);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (audio_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(audio);
      }
      if (emotionAnalysisResult_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(emotionAnalysisResult);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (audio_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(audio);
      }
      if (emotionAnalysisResult_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(emotionAnalysisResult);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_FriendChatDownMsgForAvatarReplyTTS other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.audio_ != null) {
        if (audio_ == null) {
          audio = new global::Msg.explore.PB_Explore_FriendChatAudioDownFrame();
        }
        audio.MergeFrom(other.audio);
      }
      if (other.emotionAnalysisResult_ != null) {
        if (emotionAnalysisResult_ == null) {
          emotionAnalysisResult = new global::Msg.explore.PB_EmotionAnalysisResult();
        }
        emotionAnalysisResult.MergeFrom(other.emotionAnalysisResult);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            if (audio_ == null) {
              audio = new global::Msg.explore.PB_Explore_FriendChatAudioDownFrame();
            }
            input.ReadMessage(audio);
            break;
          }
          case 34: {
            if (emotionAnalysisResult_ == null) {
              emotionAnalysisResult = new global::Msg.explore.PB_EmotionAnalysisResult();
            }
            input.ReadMessage(emotionAnalysisResult);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            if (audio_ == null) {
              audio = new global::Msg.explore.PB_Explore_FriendChatAudioDownFrame();
            }
            input.ReadMessage(audio);
            break;
          }
          case 34: {
            if (emotionAnalysisResult_ == null) {
              emotionAnalysisResult = new global::Msg.explore.PB_EmotionAnalysisResult();
            }
            input.ReadMessage(emotionAnalysisResult);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Friend对话下行 - 用户语音识别最终结果
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_FriendChatDownMsgForASR : pb::IMessage<SC_FriendChatDownMsgForASR>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_FriendChatDownMsgForASR> _parser = new pb::MessageParser<SC_FriendChatDownMsgForASR>(() => new SC_FriendChatDownMsgForASR());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_FriendChatDownMsgForASR> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.FriendChatReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_FriendChatDownMsgForASR() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_FriendChatDownMsgForASR(SC_FriendChatDownMsgForASR other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      text_ = other.text_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_FriendChatDownMsgForASR Clone() {
      return new SC_FriendChatDownMsgForASR(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "text" field.</summary>
    public const int textFieldNumber = 3;
    private string text_ = "";
    /// <summary>
    /// 语音识别结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string text {
      get { return text_; }
      set {
        text_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_FriendChatDownMsgForASR);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_FriendChatDownMsgForASR other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (text != other.text) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (text.Length != 0) hash ^= text.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (text.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(text);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (text.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(text);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (text.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(text);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_FriendChatDownMsgForASR other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.text.Length != 0) {
        text = other.text;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            text = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            text = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Friend对话下行 - 业务事件
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_FriendChatDownMsgForBizEvent : pb::IMessage<SC_FriendChatDownMsgForBizEvent>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_FriendChatDownMsgForBizEvent> _parser = new pb::MessageParser<SC_FriendChatDownMsgForBizEvent>(() => new SC_FriendChatDownMsgForBizEvent());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_FriendChatDownMsgForBizEvent> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.FriendChatReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_FriendChatDownMsgForBizEvent() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_FriendChatDownMsgForBizEvent(SC_FriendChatDownMsgForBizEvent other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      bizEvent_ = other.bizEvent_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_FriendChatDownMsgForBizEvent Clone() {
      return new SC_FriendChatDownMsgForBizEvent(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "bizEvent" field.</summary>
    public const int bizEventFieldNumber = 3;
    private global::Msg.explore.PB_Explore_FriendChatUpBizEvent bizEvent_ = global::Msg.explore.PB_Explore_FriendChatUpBizEvent.EO_FC_UNKNOWN_BIZ_EVENT_TYPE;
    /// <summary>
    /// 业务事件
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_FriendChatUpBizEvent bizEvent {
      get { return bizEvent_; }
      set {
        bizEvent_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_FriendChatDownMsgForBizEvent);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_FriendChatDownMsgForBizEvent other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (bizEvent != other.bizEvent) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (bizEvent != global::Msg.explore.PB_Explore_FriendChatUpBizEvent.EO_FC_UNKNOWN_BIZ_EVENT_TYPE) hash ^= bizEvent.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (bizEvent != global::Msg.explore.PB_Explore_FriendChatUpBizEvent.EO_FC_UNKNOWN_BIZ_EVENT_TYPE) {
        output.WriteRawTag(24);
        output.WriteEnum((int) bizEvent);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (bizEvent != global::Msg.explore.PB_Explore_FriendChatUpBizEvent.EO_FC_UNKNOWN_BIZ_EVENT_TYPE) {
        output.WriteRawTag(24);
        output.WriteEnum((int) bizEvent);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (bizEvent != global::Msg.explore.PB_Explore_FriendChatUpBizEvent.EO_FC_UNKNOWN_BIZ_EVENT_TYPE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) bizEvent);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_FriendChatDownMsgForBizEvent other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.bizEvent != global::Msg.explore.PB_Explore_FriendChatUpBizEvent.EO_FC_UNKNOWN_BIZ_EVENT_TYPE) {
        bizEvent = other.bizEvent;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 24: {
            bizEvent = (global::Msg.explore.PB_Explore_FriendChatUpBizEvent) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 24: {
            bizEvent = (global::Msg.explore.PB_Explore_FriendChatUpBizEvent) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Friend对话下行 - 亲密度等级进度变更
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_FriendChatDownMsgForClosenessProgressChange : pb::IMessage<SC_FriendChatDownMsgForClosenessProgressChange>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_FriendChatDownMsgForClosenessProgressChange> _parser = new pb::MessageParser<SC_FriendChatDownMsgForClosenessProgressChange>(() => new SC_FriendChatDownMsgForClosenessProgressChange());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_FriendChatDownMsgForClosenessProgressChange> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.FriendChatReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_FriendChatDownMsgForClosenessProgressChange() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_FriendChatDownMsgForClosenessProgressChange(SC_FriendChatDownMsgForClosenessProgressChange other) : this() {
      code_ = other.code_;
      commonData_ = other.commonData_ != null ? other.commonData_.Clone() : null;
      closenessProgressInfo_ = other.closenessProgressInfo_ != null ? other.closenessProgressInfo_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_FriendChatDownMsgForClosenessProgressChange Clone() {
      return new SC_FriendChatDownMsgForClosenessProgressChange(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "commonData" field.</summary>
    public const int commonDataFieldNumber = 2;
    private global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass commonData_;
    /// <summary>
    /// 通用数据字段
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass commonData {
      get { return commonData_; }
      set {
        commonData_ = value;
      }
    }

    /// <summary>Field number for the "closenessProgressInfo" field.</summary>
    public const int closenessProgressInfoFieldNumber = 3;
    private global::Msg.explore.PB_Friend_ClosenessProgressInfo closenessProgressInfo_;
    /// <summary>
    /// 亲密度进度信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Friend_ClosenessProgressInfo closenessProgressInfo {
      get { return closenessProgressInfo_; }
      set {
        closenessProgressInfo_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_FriendChatDownMsgForClosenessProgressChange);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_FriendChatDownMsgForClosenessProgressChange other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (!object.Equals(commonData, other.commonData)) return false;
      if (!object.Equals(closenessProgressInfo, other.closenessProgressInfo)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (commonData_ != null) hash ^= commonData.GetHashCode();
      if (closenessProgressInfo_ != null) hash ^= closenessProgressInfo.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (closenessProgressInfo_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(closenessProgressInfo);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (commonData_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(commonData);
      }
      if (closenessProgressInfo_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(closenessProgressInfo);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (commonData_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(commonData);
      }
      if (closenessProgressInfo_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(closenessProgressInfo);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_FriendChatDownMsgForClosenessProgressChange other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.commonData_ != null) {
        if (commonData_ == null) {
          commonData = new global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass();
        }
        commonData.MergeFrom(other.commonData);
      }
      if (other.closenessProgressInfo_ != null) {
        if (closenessProgressInfo_ == null) {
          closenessProgressInfo = new global::Msg.explore.PB_Friend_ClosenessProgressInfo();
        }
        closenessProgressInfo.MergeFrom(other.closenessProgressInfo);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            if (closenessProgressInfo_ == null) {
              closenessProgressInfo = new global::Msg.explore.PB_Friend_ClosenessProgressInfo();
            }
            input.ReadMessage(closenessProgressInfo);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 18: {
            if (commonData_ == null) {
              commonData = new global::Msg.explore.PB_FriendChatCommonDataFieldForDataClass();
            }
            input.ReadMessage(commonData);
            break;
          }
          case 26: {
            if (closenessProgressInfo_ == null) {
              closenessProgressInfo = new global::Msg.explore.PB_Friend_ClosenessProgressInfo();
            }
            input.ReadMessage(closenessProgressInfo);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Friend对话下行 - 亲密度升级详情信息（附带亲密度升级弹窗数据）
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Friend_ClosenessProgressInfo : pb::IMessage<PB_Friend_ClosenessProgressInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Friend_ClosenessProgressInfo> _parser = new pb::MessageParser<PB_Friend_ClosenessProgressInfo>(() => new PB_Friend_ClosenessProgressInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Friend_ClosenessProgressInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.FriendChatReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Friend_ClosenessProgressInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Friend_ClosenessProgressInfo(PB_Friend_ClosenessProgressInfo other) : this() {
      beforeValue_ = other.beforeValue_;
      afterValue_ = other.afterValue_;
      isUpgrade_ = other.isUpgrade_;
      closenessUpgradeDetail_ = other.closenessUpgradeDetail_ != null ? other.closenessUpgradeDetail_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Friend_ClosenessProgressInfo Clone() {
      return new PB_Friend_ClosenessProgressInfo(this);
    }

    /// <summary>Field number for the "beforeValue" field.</summary>
    public const int beforeValueFieldNumber = 1;
    private int beforeValue_;
    /// <summary>
    /// 变更前亲密度数值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int beforeValue {
      get { return beforeValue_; }
      set {
        beforeValue_ = value;
      }
    }

    /// <summary>Field number for the "afterValue" field.</summary>
    public const int afterValueFieldNumber = 2;
    private int afterValue_;
    /// <summary>
    /// 变更后亲密度数值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int afterValue {
      get { return afterValue_; }
      set {
        afterValue_ = value;
      }
    }

    /// <summary>Field number for the "isUpgrade" field.</summary>
    public const int isUpgradeFieldNumber = 6;
    private bool isUpgrade_;
    /// <summary>
    /// 亲密度是否升级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool isUpgrade {
      get { return isUpgrade_; }
      set {
        isUpgrade_ = value;
      }
    }

    /// <summary>Field number for the "closenessUpgradeDetail" field.</summary>
    public const int closenessUpgradeDetailFieldNumber = 7;
    private global::Msg.explore.PB_Friend_ClosenessUpgradeDetail closenessUpgradeDetail_;
    /// <summary>
    /// 亲密度升级详情
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Friend_ClosenessUpgradeDetail closenessUpgradeDetail {
      get { return closenessUpgradeDetail_; }
      set {
        closenessUpgradeDetail_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Friend_ClosenessProgressInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Friend_ClosenessProgressInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (beforeValue != other.beforeValue) return false;
      if (afterValue != other.afterValue) return false;
      if (isUpgrade != other.isUpgrade) return false;
      if (!object.Equals(closenessUpgradeDetail, other.closenessUpgradeDetail)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (beforeValue != 0) hash ^= beforeValue.GetHashCode();
      if (afterValue != 0) hash ^= afterValue.GetHashCode();
      if (isUpgrade != false) hash ^= isUpgrade.GetHashCode();
      if (closenessUpgradeDetail_ != null) hash ^= closenessUpgradeDetail.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (beforeValue != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(beforeValue);
      }
      if (afterValue != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(afterValue);
      }
      if (isUpgrade != false) {
        output.WriteRawTag(48);
        output.WriteBool(isUpgrade);
      }
      if (closenessUpgradeDetail_ != null) {
        output.WriteRawTag(58);
        output.WriteMessage(closenessUpgradeDetail);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (beforeValue != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(beforeValue);
      }
      if (afterValue != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(afterValue);
      }
      if (isUpgrade != false) {
        output.WriteRawTag(48);
        output.WriteBool(isUpgrade);
      }
      if (closenessUpgradeDetail_ != null) {
        output.WriteRawTag(58);
        output.WriteMessage(closenessUpgradeDetail);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (beforeValue != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(beforeValue);
      }
      if (afterValue != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(afterValue);
      }
      if (isUpgrade != false) {
        size += 1 + 1;
      }
      if (closenessUpgradeDetail_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(closenessUpgradeDetail);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Friend_ClosenessProgressInfo other) {
      if (other == null) {
        return;
      }
      if (other.beforeValue != 0) {
        beforeValue = other.beforeValue;
      }
      if (other.afterValue != 0) {
        afterValue = other.afterValue;
      }
      if (other.isUpgrade != false) {
        isUpgrade = other.isUpgrade;
      }
      if (other.closenessUpgradeDetail_ != null) {
        if (closenessUpgradeDetail_ == null) {
          closenessUpgradeDetail = new global::Msg.explore.PB_Friend_ClosenessUpgradeDetail();
        }
        closenessUpgradeDetail.MergeFrom(other.closenessUpgradeDetail);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            beforeValue = input.ReadInt32();
            break;
          }
          case 16: {
            afterValue = input.ReadInt32();
            break;
          }
          case 48: {
            isUpgrade = input.ReadBool();
            break;
          }
          case 58: {
            if (closenessUpgradeDetail_ == null) {
              closenessUpgradeDetail = new global::Msg.explore.PB_Friend_ClosenessUpgradeDetail();
            }
            input.ReadMessage(closenessUpgradeDetail);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            beforeValue = input.ReadInt32();
            break;
          }
          case 16: {
            afterValue = input.ReadInt32();
            break;
          }
          case 48: {
            isUpgrade = input.ReadBool();
            break;
          }
          case 58: {
            if (closenessUpgradeDetail_ == null) {
              closenessUpgradeDetail = new global::Msg.explore.PB_Friend_ClosenessUpgradeDetail();
            }
            input.ReadMessage(closenessUpgradeDetail);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Friend对话下行 - 亲密度升级详情信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_Friend_ClosenessUpgradeDetail : pb::IMessage<PB_Friend_ClosenessUpgradeDetail>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_Friend_ClosenessUpgradeDetail> _parser = new pb::MessageParser<PB_Friend_ClosenessUpgradeDetail>(() => new PB_Friend_ClosenessUpgradeDetail());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_Friend_ClosenessUpgradeDetail> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.FriendChatReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Friend_ClosenessUpgradeDetail() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Friend_ClosenessUpgradeDetail(PB_Friend_ClosenessUpgradeDetail other) : this() {
      nextClosenessLevelId_ = other.nextClosenessLevelId_;
      initValue_ = other.initValue_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_Friend_ClosenessUpgradeDetail Clone() {
      return new PB_Friend_ClosenessUpgradeDetail(this);
    }

    /// <summary>Field number for the "nextClosenessLevelId" field.</summary>
    public const int nextClosenessLevelIdFieldNumber = 1;
    private long nextClosenessLevelId_;
    /// <summary>
    /// 下个亲密度等级id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long nextClosenessLevelId {
      get { return nextClosenessLevelId_; }
      set {
        nextClosenessLevelId_ = value;
      }
    }

    /// <summary>Field number for the "initValue" field.</summary>
    public const int initValueFieldNumber = 2;
    private int initValue_;
    /// <summary>
    /// 下个亲密度等级初始数值
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int initValue {
      get { return initValue_; }
      set {
        initValue_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_Friend_ClosenessUpgradeDetail);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_Friend_ClosenessUpgradeDetail other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (nextClosenessLevelId != other.nextClosenessLevelId) return false;
      if (initValue != other.initValue) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (nextClosenessLevelId != 0L) hash ^= nextClosenessLevelId.GetHashCode();
      if (initValue != 0) hash ^= initValue.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (nextClosenessLevelId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(nextClosenessLevelId);
      }
      if (initValue != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(initValue);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (nextClosenessLevelId != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(nextClosenessLevelId);
      }
      if (initValue != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(initValue);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (nextClosenessLevelId != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(nextClosenessLevelId);
      }
      if (initValue != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(initValue);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_Friend_ClosenessUpgradeDetail other) {
      if (other == null) {
        return;
      }
      if (other.nextClosenessLevelId != 0L) {
        nextClosenessLevelId = other.nextClosenessLevelId;
      }
      if (other.initValue != 0) {
        initValue = other.initValue;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            nextClosenessLevelId = input.ReadInt64();
            break;
          }
          case 16: {
            initValue = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            nextClosenessLevelId = input.ReadInt64();
            break;
          }
          case 16: {
            initValue = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
