/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class FastSlowListenQuestion : AFragQuestion
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "FastSlowListenQuestion";
        public static string url => "ui://cmoz5osjz7rm2p";

        public BtnNormalPlay btnFast;
        public BtnSlowPlay btnSlow;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(FastSlowListenQuestion));
        }

        public override void ConstructFromXML(XML xml)
        {
            btnFast = GetChildAt(0) as BtnNormalPlay;
            btnSlow = GetChildAt(1) as BtnSlowPlay;
        }
        public override void Dispose()
        {
            btnFast = null;
            btnSlow = null;

            base.Dispose();
        }
    }
}