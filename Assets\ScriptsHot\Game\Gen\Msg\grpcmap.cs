using Grpc.Core;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using pb = global::Google.Protobuf;
using Msg.GM;

using Msg.abtest;
using Msg.achievement;
using Msg.aitown;
using Msg.ai_recommend;
using Msg.ai_tutor;
using Msg.center;
using Msg.course;
using Msg.dialog_base;
using Msg.dialog_flow;
using Msg.dialog_task;
using Msg.economic;
using Msg.explore;
using Msg.flash;
using Msg.free_talk;
using Msg.incentive;
using Msg.learn_assist;
using Msg.learn_path;
using Msg.login;
using Msg.notification;
using Msg.question;
using Msg.question_process;
using Msg.recommend;
using Msg.scaffold;
using Msg.social;
using Msg.speech;
using Msg.talkitpush;
using Msg.task;
using Msg.task_process;
using Msg.translate;
using Msg.tts;
using Msg.video;
using Msg.world;


namespace Msg
{

	public class GrpcMap
	{
		private Dictionary<Type, ClientBase> _grpcClientMap = new Dictionary<Type, ClientBase>();

		public async ValueTask<pb::IMessage> GRpcCall(short msgId, pb::IMessage message, CallInvoker invoker) 
		{
			switch ((GameMSGID)msgId)
			{
				case GameMSGID.CS_GetAbTestResultReq_ID:
					return await this.GetGrpcClient<AbtestService.AbtestServiceClient>(msgId, invoker).GetAbTestResultAsync((CS_GetAbTestResultReq)message);
				case GameMSGID.CS_TreeListForHomepageReq_ID:
					return await this.GetGrpcClient<AchievementService.AchievementServiceClient>(msgId, invoker).TreeListForHomepageAsync((CS_TreeListForHomepageReq)message);
				case GameMSGID.CS_TreeDetailForHomepageReq_ID:
					return await this.GetGrpcClient<AchievementService.AchievementServiceClient>(msgId, invoker).TreeDetailForHomepageAsync((CS_TreeDetailForHomepageReq)message);
				case GameMSGID.CS_SandTableListReq_ID:
					return await this.GetGrpcClient<AchievementService.AchievementServiceClient>(msgId, invoker).SandTableListAsync((CS_SandTableListReq)message);
				case GameMSGID.CS_SandTableHomepageReq_ID:
					return await this.GetGrpcClient<AchievementService.AchievementServiceClient>(msgId, invoker).SandTableHomepageAsync((CS_SandTableHomepageReq)message);
				case GameMSGID.CS_DataBatchReq_ID:
					return await this.GetGrpcClient<CenterService.CenterServiceClient>(msgId, invoker).DataBatchAsync((CS_DataBatchReq)message);
				case GameMSGID.CS_GetUserCourseReq_ID:
					return await this.GetGrpcClient<CourseService.CourseServiceClient>(msgId, invoker).GetUserCourseAsync((CS_GetUserCourseReq)message);
				case GameMSGID.CS_SkipCourseReq_ID:
					return await this.GetGrpcClient<CourseService.CourseServiceClient>(msgId, invoker).SkipCourseAsync((CS_SkipCourseReq)message);
				case GameMSGID.CS_RewardBoxReq_ID:
					return await this.GetGrpcClient<CourseService.CourseServiceClient>(msgId, invoker).RewardBoxAsync((CS_RewardBoxReq)message);
				case GameMSGID.CS_GetBookDataReq_ID:
					return await this.GetGrpcClient<CourseService.CourseServiceClient>(msgId, invoker).GetBookDataAsync((CS_GetBookDataReq)message);
				case GameMSGID.CS_GetRadioDataReq_ID:
					return await this.GetGrpcClient<CourseService.CourseServiceClient>(msgId, invoker).GetRadioDataAsync((CS_GetRadioDataReq)message);
				case GameMSGID.CS_GetCourseSettlementReq_ID:
					return await this.GetGrpcClient<CourseService.CourseServiceClient>(msgId, invoker).GetCourseSettlementAsync((CS_GetCourseSettlementReq)message);
				case GameMSGID.CS_BuyCourseTicketReq_ID:
					return await this.GetGrpcClient<CourseService.CourseServiceClient>(msgId, invoker).BuyCourseTicketAsync((CS_BuyCourseTicketReq)message);
				case GameMSGID.CS_GetUserShelfReq_ID:
					return await this.GetGrpcClient<CourseService.CourseServiceClient>(msgId, invoker).GetUserShelfAsync((CS_GetUserShelfReq)message);
				case GameMSGID.CS_GetFailedMsgReq_ID:
					return await this.GetGrpcClient<DialogBaseService.DialogBaseServiceClient>(msgId, invoker).GetFailedMsgAsync((CS_GetFailedMsgReq)message);
				case GameMSGID.CS_DialogHelpReq_ID:
					return await this.GetGrpcClient<DialogBaseService.DialogBaseServiceClient>(msgId, invoker).DialogHelpAsync((CS_DialogHelpReq)message);
				case GameMSGID.CS_QueryDialogRecordReq_ID:
					return await this.GetGrpcClient<DialogBaseService.DialogBaseServiceClient>(msgId, invoker).QueryDialogRecordAsync((CS_QueryDialogRecordReq)message);
				case GameMSGID.CS_DialogFlowCreateReq_ID:
					return await this.GetGrpcClient<DialogFlowService.DialogFlowServiceClient>(msgId, invoker).DialogFlowCreateAsync((CS_DialogFlowCreateReq)message);
				case GameMSGID.CS_DialogFlowMsgHandleReq_ID:
					return await this.GetGrpcClient<DialogFlowService.DialogFlowServiceClient>(msgId, invoker).DialogFlowMsgSendAsync((CS_DialogFlowMsgHandleReq)message);
				case GameMSGID.CS_DialogFlowStopReq_ID:
					return await this.GetGrpcClient<DialogFlowService.DialogFlowServiceClient>(msgId, invoker).FlowStopDialogAsync((CS_DialogFlowStopReq)message);
				case GameMSGID.CS_FlowHelpReq_ID:
					return await this.GetGrpcClient<DialogFlowService.DialogFlowServiceClient>(msgId, invoker).FlowHelpAsync((CS_FlowHelpReq)message);
				case GameMSGID.CS_CreateDialogTaskCacheReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).DialogTaskCreateCacheAsync((CS_CreateDialogTaskCacheReq)message);
				case GameMSGID.CS_GetDialogTaskModeListReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).GetDialogTaskModeListAsync((CS_GetDialogTaskModeListReq)message);
				case GameMSGID.CS_GetModeInfoReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).GetModeInfoAsync((CS_GetModeInfoReq)message);
				case GameMSGID.CS_GetModeInfoForCareerReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).GetModeInfoForCareerAsync((CS_GetModeInfoForCareerReq)message);
				case GameMSGID.CS_CreateDialogTaskReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).CreateDialogTaskAsync((CS_CreateDialogTaskReq)message);
				case GameMSGID.CS_DialogTaskMsgHandleReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).DialogTaskMsgHandleAsync((CS_DialogTaskMsgHandleReq)message);
				case GameMSGID.CS_GetDialogTaskNextRoundMsgReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).GetDialogTaskNextRoundMsgAsync((CS_GetDialogTaskNextRoundMsgReq)message);
				case GameMSGID.CS_StrengthenCreateDialogTaskReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).CreateStrengthenDialogTaskAsync((CS_StrengthenCreateDialogTaskReq)message);
				case GameMSGID.CS_StrengthenDialogTaskMsgHandleReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).StrengthenDialogTaskMsgHandleAsync((CS_StrengthenDialogTaskMsgHandleReq)message);
				case GameMSGID.CS_GetStrengthenDialogTaskNextRoundMsgReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).GetStrengthenDialogTaskNextRoundMsgAsync((CS_GetStrengthenDialogTaskNextRoundMsgReq)message);
				case GameMSGID.CS_WarmupPracticeCreateDialogTaskReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).CreateWarmupPracticeDialogTaskAsync((CS_WarmupPracticeCreateDialogTaskReq)message);
				case GameMSGID.CS_WarmupPracticeDialogTaskMsgHandleReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).WarmupPracticeDialogTaskMsgHandleAsync((CS_WarmupPracticeDialogTaskMsgHandleReq)message);
				case GameMSGID.CS_GetWarmupPracticeDialogTaskNextRoundMsgReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).GetWarmupPracticeDialogTaskNextRoundMsgAsync((CS_GetWarmupPracticeDialogTaskNextRoundMsgReq)message);
				case GameMSGID.CS_ExitDialogTaskReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).ExitDialogTaskAsync((CS_ExitDialogTaskReq)message);
				case GameMSGID.CS_ClickWordReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).ChatWordClickAsync((CS_ClickWordReq)message);
				case GameMSGID.CS_FeedbackReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).FeedbackHandleAsync((CS_FeedbackReq)message);
				case GameMSGID.CS_GetAudioForFlowReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).GetAudioForFlowAsync((CS_GetAudioForFlowReq)message);
				case GameMSGID.CS_BuyDialogTicketReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).BuyDialogTicketAsync((CS_BuyDialogTicketReq)message);
				case GameMSGID.CS_GetInProgressDialogReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).GetInProgressDialogAsync((CS_GetInProgressDialogReq)message);
				case GameMSGID.CS_GetPopListInfoReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).GetPopListInfoAsync((CS_GetPopListInfoReq)message);
				case GameMSGID.CS_GetDialogScaffoldReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).GetDialogScaffoldAsync((CS_GetDialogScaffoldReq)message);
				case GameMSGID.CS_GetAvatarListByDialogModeReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).GetAvatarListByDialogModeAsync((CS_GetAvatarListByDialogModeReq)message);
				case GameMSGID.CS_DialogSuggestionReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).DialogSuggestionAsync((CS_DialogSuggestionReq)message);
				case GameMSGID.CS_DialogTranslateReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).DialogTranslateAsync((CS_DialogTranslateReq)message);
				case GameMSGID.CS_GetAvatarTaskInfoReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).GetAvatarTaskInfoAsync((CS_GetAvatarTaskInfoReq)message);
				case GameMSGID.CS_GetStartPageInfoReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).GetStartPageInfoAsync((CS_GetStartPageInfoReq)message);
				case GameMSGID.CS_GetUserAssistLevelListReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).GetUserAssistLevelListAsync((CS_GetUserAssistLevelListReq)message);
				case GameMSGID.CS_SetUserAssistLevelReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).SetUserAssistLevelAsync((CS_SetUserAssistLevelReq)message);
				case GameMSGID.CS_GetUserContactsReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).GetUserContactsAsync((CS_GetUserContactsReq)message);
				case GameMSGID.CS_GetRedPodReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).GetRedPodAsync((CS_GetRedPodReq)message);
				case GameMSGID.CS_ClickRedPodReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).ClickRedPodAsync((CS_ClickRedPodReq)message);
				case GameMSGID.CS_AvatarFavoriteSettingReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).AvatarFavoriteSettingAsync((CS_AvatarFavoriteSettingReq)message);
				case GameMSGID.CS_GetUserQuestionExtraListReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).GetUserQuestionExtraListAsync((CS_GetUserQuestionExtraListReq)message);
				case GameMSGID.CS_GetKnowledgePointListReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).GetKnowledgePointListAsync((CS_GetKnowledgePointListReq)message);
				case GameMSGID.CS_GetKnowledgePointFrontPageReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).GetKnowledgePointFrontPageAsync((CS_GetKnowledgePointFrontPageReq)message);
				case GameMSGID.CS_GetUserQuestionExtraFrontPageReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).GetUserQuestionExtraFrontPageAsync((CS_GetUserQuestionExtraFrontPageReq)message);
				case GameMSGID.CS_BatchGetUserQuestionExtraFrontPageReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).BatchGetUserQuestionExtraFrontPageAsync((CS_BatchGetUserQuestionExtraFrontPageReq)message);
				case GameMSGID.CS_ReviewUserQuestionExtraReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).ReviewUserQuestionExtraAsync((CS_ReviewUserQuestionExtraReq)message);
				case GameMSGID.CS_GetDialogSettlementReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).GetDialogResultAsync((CS_GetDialogSettlementReq)message);
				case GameMSGID.CS_StartBatchQuestionDialogReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).StartBatchQuestionDialogAsync((CS_StartBatchQuestionDialogReq)message);
				case GameMSGID.CS_SubmitBatchQuestionDialogReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).SubmitBatchQuestionDialogAsync((CS_SubmitBatchQuestionDialogReq)message);
				case GameMSGID.CS_GetUserDialogHistoryReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).GetUserDialogHistoryAsync((CS_GetUserDialogHistoryReq)message);
				case GameMSGID.CS_GetBoxRewardReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).GetBoxRewardAsync((CS_GetBoxRewardReq)message);
				case GameMSGID.CS_GetUserTopicDialogHistoryReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).GetUserTopicDialogHistoryAsync((CS_GetUserTopicDialogHistoryReq)message);
				case GameMSGID.CS_CreateQuestionDialogReq_ID:
					return await this.GetGrpcClient<DialogTaskService.DialogTaskServiceClient>(msgId, invoker).CreateQuestionDialogAsync((CS_CreateQuestionDialogReq)message);
				case GameMSGID.CS_GetUserInfoReq_ID:
					return await this.GetGrpcClient<DialogUserService.DialogUserServiceClient>(msgId, invoker).GetUserInfoAsync((CS_GetUserInfoReq)message);
				case GameMSGID.CS_ChangeMarkInfoReq_ID:
					return await this.GetGrpcClient<DialogUserService.DialogUserServiceClient>(msgId, invoker).ChangeMarkInfoAsync((CS_ChangeMarkInfoReq)message);
				case GameMSGID.CS_GetLearnPathPageTypeReq_ID:
					return await this.GetGrpcClient<UserLearnPathService.UserLearnPathServiceClient>(msgId, invoker).GetLearnPathPageTypeAsync((CS_GetLearnPathPageTypeReq)message);
				case GameMSGID.CS_GetUserChapterInfoReq_ID:
					return await this.GetGrpcClient<UserLearnPathService.UserLearnPathServiceClient>(msgId, invoker).GetUserChapterInfoAsync((CS_GetUserChapterInfoReq)message);
				case GameMSGID.CS_GetUserGoalNodeReq_ID:
					return await this.GetGrpcClient<UserLearnPathService.UserLearnPathServiceClient>(msgId, invoker).GetUserGoalNodeAsync((CS_GetUserGoalNodeReq)message);
				case GameMSGID.CS_GetGoalInfoReq_ID:
					return await this.GetGrpcClient<UserLearnPathService.UserLearnPathServiceClient>(msgId, invoker).GetUserGoalInfoAsync((CS_GetGoalInfoReq)message);
				case GameMSGID.CS_GetChapterRecommendListReq_ID:
					return await this.GetGrpcClient<UserLearnPathService.UserLearnPathServiceClient>(msgId, invoker).GetChapterRecommendListAsync((CS_GetChapterRecommendListReq)message);
				case GameMSGID.CS_SelectChapterReq_ID:
					return await this.GetGrpcClient<UserLearnPathService.UserLearnPathServiceClient>(msgId, invoker).SelectChapterAsync((CS_SelectChapterReq)message);
				case GameMSGID.CS_GetChapterProgressInfoReq_ID:
					return await this.GetGrpcClient<UserLearnPathService.UserLearnPathServiceClient>(msgId, invoker).GetChapterProgressInfoAsync((CS_GetChapterProgressInfoReq)message);
				case GameMSGID.CS_GetChapterRewardReq_ID:
					return await this.GetGrpcClient<UserLearnPathService.UserLearnPathServiceClient>(msgId, invoker).GetChapterRewardAsync((CS_GetChapterRewardReq)message);
				case GameMSGID.CS_NotifyAnimationStateReq_ID:
					return await this.GetGrpcClient<UserLearnPathService.UserLearnPathServiceClient>(msgId, invoker).NotifyAnimationStateAsync((CS_NotifyAnimationStateReq)message);
				case GameMSGID.CS_GetUserGoalDetailReq_ID:
					return await this.GetGrpcClient<UserLearnPathService.UserLearnPathServiceClient>(msgId, invoker).GetUserGoalDetailAsync((CS_GetUserGoalDetailReq)message);
				case GameMSGID.CS_LearnPathRewardReq_ID:
					return await this.GetGrpcClient<UserLearnPathService.UserLearnPathServiceClient>(msgId, invoker).LearnPathRewardAsync((CS_LearnPathRewardReq)message);
				case GameMSGID.CS_DelLearnPathDataReq_ID:
					return await this.GetGrpcClient<UserLearnPathService.UserLearnPathServiceClient>(msgId, invoker).DelLearnPathDataAsync((CS_DelLearnPathDataReq)message);
				case GameMSGID.CS_OpenRewardBoxReq_ID:
					return await this.GetGrpcClient<UserLearnPathService.UserLearnPathServiceClient>(msgId, invoker).OpenRewardBoxAsync((CS_OpenRewardBoxReq)message);
				case GameMSGID.CS_OnboardingNewStoryReq_ID:
					return await this.GetGrpcClient<UserLearnPathService.UserLearnPathServiceClient>(msgId, invoker).OnboardingNewStoryAsync((CS_OnboardingNewStoryReq)message);
				case GameMSGID.CS_QueryMemberInfoReq_ID:
					return await this.GetGrpcClient<BenefitService.BenefitServiceClient>(msgId, invoker).QueryMemberInfoAsync((CS_QueryMemberInfoReq)message);
				case GameMSGID.CS_PurchaseStaminaReq_ID:
					return await this.GetGrpcClient<BenefitService.BenefitServiceClient>(msgId, invoker).PurchaseStaminaAsync((CS_PurchaseStaminaReq)message);
				case GameMSGID.CS_EconomicCoinGetBalanceReq_ID:
					return await this.GetGrpcClient<EconomicService.EconomicServiceClient>(msgId, invoker).GetCoinBalanceAsync((CS_EconomicCoinGetBalanceReq)message);
				case GameMSGID.CS_GetEconomicInfoReq_ID:
					return await this.GetGrpcClient<EconomicService.EconomicServiceClient>(msgId, invoker).GetEconomicInfoAsync((CS_GetEconomicInfoReq)message);
				case GameMSGID.CS_GetShopInfoReq_ID:
					return await this.GetGrpcClient<MerchandiseService.MerchandiseServiceClient>(msgId, invoker).GetShopInfoAsync((CS_GetShopInfoReq)message);
				case GameMSGID.CS_PayMerchandiseReq_ID:
					return await this.GetGrpcClient<MerchandiseService.MerchandiseServiceClient>(msgId, invoker).PayMerchandiseAsync((CS_PayMerchandiseReq)message);
				case GameMSGID.CS_GetAllShopMerchandiseReq_ID:
					return await this.GetGrpcClient<MerchandiseService.MerchandiseServiceClient>(msgId, invoker).GetAllShopMerchandiseAsync((CS_GetAllShopMerchandiseReq)message);
				case GameMSGID.CS_CreateOrderReq_ID:
					return await this.GetGrpcClient<OrderService.OrderServiceClient>(msgId, invoker).CreateOrderAsync((CS_CreateOrderReq)message);
				case GameMSGID.CS_CancelOrderReq_ID:
					return await this.GetGrpcClient<OrderService.OrderServiceClient>(msgId, invoker).CancelOrderAsync((CS_CancelOrderReq)message);
				case GameMSGID.CS_GetUnpaidOrderReq_ID:
					return await this.GetGrpcClient<OrderService.OrderServiceClient>(msgId, invoker).GetUnpaidOrderByProductIdAsync((CS_GetUnpaidOrderReq)message);
				case GameMSGID.CS_VerifyReceiptDataReq_ID:
					return await this.GetGrpcClient<PayService.PayServiceClient>(msgId, invoker).VerifyReceiptAsync((CS_VerifyReceiptDataReq)message);
				case GameMSGID.CS_GetOnboardingChatPreloadDataReq_ID:
					return await this.GetGrpcClient<ExploreOnboardingService.ExploreOnboardingServiceClient>(msgId, invoker).GetOnboardingChatPreloadDataAsync((CS_GetOnboardingChatPreloadDataReq)message);
				case GameMSGID.CS_GetRecommendListReq_ID:
					return await this.GetGrpcClient<ExploreRecommendService.ExploreRecommendServiceClient>(msgId, invoker).GetRecommendListAsync((CS_GetRecommendListReq)message);
				case GameMSGID.CS_GetRecommendListV2Req_ID:
					return await this.GetGrpcClient<ExploreRecommendService.ExploreRecommendServiceClient>(msgId, invoker).GetRecommendListV2Async((CS_GetRecommendListV2Req)message);
				case GameMSGID.CS_GetUserHistoryProgressListReq_ID:
					return await this.GetGrpcClient<ExploreRecommendService.ExploreRecommendServiceClient>(msgId, invoker).GetUserHistoryProgressListAsync((CS_GetUserHistoryProgressListReq)message);
				case GameMSGID.CS_GetUserCheckinDataReq_ID:
					return await this.GetGrpcClient<CheckinService.CheckinServiceClient>(msgId, invoker).GetUserCheckinDataAsync((CS_GetUserCheckinDataReq)message);
				case GameMSGID.CS_DrawCheckinRewardReq_ID:
					return await this.GetGrpcClient<CheckinService.CheckinServiceClient>(msgId, invoker).DrawCheckinRewardAsync((CS_DrawCheckinRewardReq)message);
				case GameMSGID.CS_SetCheckinMilestoneReq_ID:
					return await this.GetGrpcClient<CheckinService.CheckinServiceClient>(msgId, invoker).SetCheckinMilestoneAsync((CS_SetCheckinMilestoneReq)message);
				case GameMSGID.CS_GetCheckinSummaryReq_ID:
					return await this.GetGrpcClient<CheckinService.CheckinServiceClient>(msgId, invoker).GetCheckinSummaryAsync((CS_GetCheckinSummaryReq)message);
				case GameMSGID.CS_RecheckinReq_ID:
					return await this.GetGrpcClient<CheckinService.CheckinServiceClient>(msgId, invoker).RecheckinAsync((CS_RecheckinReq)message);
				case GameMSGID.CS_GetUserCheckinPortalDataReq_ID:
					return await this.GetGrpcClient<CheckinService.CheckinServiceClient>(msgId, invoker).GetUserCheckinPortalDataAsync((CS_GetUserCheckinPortalDataReq)message);
				case GameMSGID.CS_DrawRewardReq_ID:
					return await this.GetGrpcClient<CheckinService.CheckinServiceClient>(msgId, invoker).DrawRewardAsync((CS_DrawRewardReq)message);
				case GameMSGID.CS_GetUserCheckinDataForTaskFinishReq_ID:
					return await this.GetGrpcClient<CheckinService.CheckinServiceClient>(msgId, invoker).GetUserCheckinDataForTaskFinishAsync((CS_GetUserCheckinDataForTaskFinishReq)message);
				case GameMSGID.CS_GetUserCheckinCalendarReq_ID:
					return await this.GetGrpcClient<CheckinService.CheckinServiceClient>(msgId, invoker).GetUserCheckinCalendarAsync((CS_GetUserCheckinCalendarReq)message);
				case GameMSGID.CS_RefuseRecheckinReq_ID:
					return await this.GetGrpcClient<CheckinService.CheckinServiceClient>(msgId, invoker).RefuseRecheckinAsync((CS_RefuseRecheckinReq)message);
				case GameMSGID.CS_GetFriendStreakPortalReq_ID:
					return await this.GetGrpcClient<CheckinService.CheckinServiceClient>(msgId, invoker).GetFriendStreakPortalAsync((CS_GetFriendStreakPortalReq)message);
				case GameMSGID.CS_UpdateFriendStreakRelationReq_ID:
					return await this.GetGrpcClient<CheckinService.CheckinServiceClient>(msgId, invoker).UpdateFriendStreakRelationAsync((CS_UpdateFriendStreakRelationReq)message);
				case GameMSGID.CS_GetFriendStreakRecommendListReq_ID:
					return await this.GetGrpcClient<CheckinService.CheckinServiceClient>(msgId, invoker).GetFriendStreakRecommendListAsync((CS_GetFriendStreakRecommendListReq)message);
				case GameMSGID.CS_RedeemCouponReq_ID:
					return await this.GetGrpcClient<CouponService.CouponServiceClient>(msgId, invoker).RedeemCouponAsync((CS_RedeemCouponReq)message);
				case GameMSGID.CS_GetUserFriendListReq_ID:
					return await this.GetGrpcClient<FriendshipService.FriendshipServiceClient>(msgId, invoker).GetUserFriendListAsync((CS_GetUserFriendListReq)message);
				case GameMSGID.CS_AddFriendReq_ID:
					return await this.GetGrpcClient<FriendshipService.FriendshipServiceClient>(msgId, invoker).AddFriendAsync((CS_AddFriendReq)message);
				case GameMSGID.CS_RemoveFriendReq_ID:
					return await this.GetGrpcClient<FriendshipService.FriendshipServiceClient>(msgId, invoker).RemoveFriendAsync((CS_RemoveFriendReq)message);
				case GameMSGID.CS_GetRecommendedFriendListReq_ID:
					return await this.GetGrpcClient<FriendshipService.FriendshipServiceClient>(msgId, invoker).GetRecommendedFriendListAsync((CS_GetRecommendedFriendListReq)message);
				case GameMSGID.CS_SearchUserReq_ID:
					return await this.GetGrpcClient<FriendshipService.FriendshipServiceClient>(msgId, invoker).SearchUserAsync((CS_SearchUserReq)message);
				case GameMSGID.CS_GetUserGiftPortalDataReq_ID:
					return await this.GetGrpcClient<FriendshipService.FriendshipServiceClient>(msgId, invoker).GetUserGiftPortalDataAsync((CS_GetUserGiftPortalDataReq)message);
				case GameMSGID.CS_MatchFriendShipTaskReq_ID:
					return await this.GetGrpcClient<FriendshipService.FriendshipServiceClient>(msgId, invoker).MatchFriendShipTaskAsync((CS_MatchFriendShipTaskReq)message);
				case GameMSGID.CS_FriendShipNotifyReq_ID:
					return await this.GetGrpcClient<FriendshipService.FriendshipServiceClient>(msgId, invoker).FriendShipNotifyAsync((CS_FriendShipNotifyReq)message);
				case GameMSGID.CS_GetGrowthWeeklyGiftReq_ID:
					return await this.GetGrpcClient<GrowthService.GrowthServiceClient>(msgId, invoker).GetGrowthWeeklyGiftAsync((CS_GetGrowthWeeklyGiftReq)message);
				case GameMSGID.CS_GetUserProfileReq_ID:
					return await this.GetGrpcClient<IncentiveService.IncentiveServiceClient>(msgId, invoker).GetUserProfileAsync((CS_GetUserProfileReq)message);
				case GameMSGID.CS_GetMyProfileReq_ID:
					return await this.GetGrpcClient<IncentiveService.IncentiveServiceClient>(msgId, invoker).GetMyProfileAsync((CS_GetMyProfileReq)message);
				case GameMSGID.CS_SetUserProfileAuthorityReq_ID:
					return await this.GetGrpcClient<IncentiveService.IncentiveServiceClient>(msgId, invoker).SetUserProfileAuthorityAsync((CS_SetUserProfileAuthorityReq)message);
				case GameMSGID.CS_GetUserProfileAuthorityReq_ID:
					return await this.GetGrpcClient<IncentiveService.IncentiveServiceClient>(msgId, invoker).GetUserProfileAuthorityAsync((CS_GetUserProfileAuthorityReq)message);
				case GameMSGID.CS_SendFeedbackReq_ID:
					return await this.GetGrpcClient<IncentiveService.IncentiveServiceClient>(msgId, invoker).SendFeedbackAsync((CS_SendFeedbackReq)message);
				case GameMSGID.CS_SetUserVoiceInformationReq_ID:
					return await this.GetGrpcClient<IncentiveService.IncentiveServiceClient>(msgId, invoker).SetUserVoiceInformationAsync((CS_SetUserVoiceInformationReq)message);
				case GameMSGID.CS_SetUserPortraitsReq_ID:
					return await this.GetGrpcClient<IncentiveService.IncentiveServiceClient>(msgId, invoker).SetUserPortraitsAsync((CS_SetUserPortraitsReq)message);
				case GameMSGID.CS_SetUserLanguageLevelReq_ID:
					return await this.GetGrpcClient<IncentiveService.IncentiveServiceClient>(msgId, invoker).SetUserLanguageLevelAsync((CS_SetUserLanguageLevelReq)message);
				case GameMSGID.CS_GetUserPortraitsReq_ID:
					return await this.GetGrpcClient<IncentiveService.IncentiveServiceClient>(msgId, invoker).GetUserPortraitsAsync((CS_GetUserPortraitsReq)message);
				case GameMSGID.CS_GetIncentiveDataForPortalReq_ID:
					return await this.GetGrpcClient<IncentiveService.IncentiveServiceClient>(msgId, invoker).GetIncentiveDataForPortalAsync((CS_GetIncentiveDataForPortalReq)message);
				case GameMSGID.CS_SetUserDressUpReq_ID:
					return await this.GetGrpcClient<IncentiveService.IncentiveServiceClient>(msgId, invoker).SetUserDressUpAsync((CS_SetUserDressUpReq)message);
				case GameMSGID.CS_GetUserDressUpMerchandiseDataReq_ID:
					return await this.GetGrpcClient<IncentiveService.IncentiveServiceClient>(msgId, invoker).GetUserDressUpMerchandiseDataAsync((CS_GetUserDressUpMerchandiseDataReq)message);
				case GameMSGID.CS_SetUserHeadItemReq_ID:
					return await this.GetGrpcClient<IncentiveService.IncentiveServiceClient>(msgId, invoker).SetUserHeadItemAsync((CS_SetUserHeadItemReq)message);
				case GameMSGID.CS_SendGiftForFriendReq_ID:
					return await this.GetGrpcClient<IncentiveService.IncentiveServiceClient>(msgId, invoker).SendGiftForFriendAsync((CS_SendGiftForFriendReq)message);
				case GameMSGID.CS_SetShowStateReq_ID:
					return await this.GetGrpcClient<IncentiveService.IncentiveServiceClient>(msgId, invoker).SetShowStateAsync((CS_SetShowStateReq)message);
				case GameMSGID.CS_GetIncentiveDataForExploreReq_ID:
					return await this.GetGrpcClient<IncentiveService.IncentiveServiceClient>(msgId, invoker).GetIncentiveDataForExploreAsync((CS_GetIncentiveDataForExploreReq)message);
				case GameMSGID.CS_StartConsumeReq_ID:
					return await this.GetGrpcClient<IncentiveService.IncentiveServiceClient>(msgId, invoker).StartConsumeAsync((CS_StartConsumeReq)message);
				case GameMSGID.CS_GetUserVoiceCloneAudioReq_ID:
					return await this.GetGrpcClient<IncentiveService.IncentiveServiceClient>(msgId, invoker).GetUserVoiceCloneAudioAsync((CS_GetUserVoiceCloneAudioReq)message);
				case GameMSGID.CS_ReportInvalidUserAudioReq_ID:
					return await this.GetGrpcClient<IncentiveService.IncentiveServiceClient>(msgId, invoker).ReportInvalidUserAudioAsync((CS_ReportInvalidUserAudioReq)message);
				case GameMSGID.CS_GetUserRankingPortalDataReq_ID:
					return await this.GetGrpcClient<RankingService.RankingServiceClient>(msgId, invoker).GetUserRankingPortalDataAsync((CS_GetUserRankingPortalDataReq)message);
				case GameMSGID.CS_JoinRankingReq_ID:
					return await this.GetGrpcClient<RankingService.RankingServiceClient>(msgId, invoker).JoinRankingAsync((CS_JoinRankingReq)message);
				case GameMSGID.CS_SetRankingChangeClickReq_ID:
					return await this.GetGrpcClient<RankingService.RankingServiceClient>(msgId, invoker).SetRankingChangeClickAsync((CS_SetRankingChangeClickReq)message);
				case GameMSGID.CS_ScaffoldFeedbackReq_ID:
					return await this.GetGrpcClient<LearnAssistService.LearnAssistServiceClient>(msgId, invoker).ScaffoldFeedbackAsync((CS_ScaffoldFeedbackReq)message);
				case GameMSGID.CS_TaskFeedbackReq_ID:
					return await this.GetGrpcClient<LearnAssistService.LearnAssistServiceClient>(msgId, invoker).TaskFeedbackAsync((CS_TaskFeedbackReq)message);
				case GameMSGID.CS_QuestionFeedbackReq_ID:
					return await this.GetGrpcClient<LearnAssistService.LearnAssistServiceClient>(msgId, invoker).QuestionFeedbackAsync((CS_QuestionFeedbackReq)message);
				case GameMSGID.CS_FreeTalkFeedbackReq_ID:
					return await this.GetGrpcClient<LearnAssistService.LearnAssistServiceClient>(msgId, invoker).FreeTalkFeedbackAsync((CS_FreeTalkFeedbackReq)message);
				case GameMSGID.CS_Content2AudioReq_ID:
					return await this.GetGrpcClient<LearnAssistService.LearnAssistServiceClient>(msgId, invoker).GetAudioAsync((CS_Content2AudioReq)message);
				case GameMSGID.CS_WordTransReq_ID:
					return await this.GetGrpcClient<LearnAssistService.LearnAssistServiceClient>(msgId, invoker).WordTransAsync((CS_WordTransReq)message);
				case GameMSGID.CS_TranslateReq_ID:
					return await this.GetGrpcClient<LearnAssistService.LearnAssistServiceClient>(msgId, invoker).TranslateAsync((CS_TranslateReq)message);
				case GameMSGID.CS_SendVerificationCodeReq_ID:
					return await this.GetGrpcClient<LoginService.LoginServiceClient>(msgId, invoker).SendVerificationCodeAsync((CS_SendVerificationCodeReq)message);
				case GameMSGID.CS_UpdateAppsflyerCallbackDataReq_ID:
					return await this.GetGrpcClient<UserService.UserServiceClient>(msgId, invoker).UpdateAppsflyerCallbackDataAsync((CS_UpdateAppsflyerCallbackDataReq)message);
				case GameMSGID.CS_GetHomepageGuideItemReq_ID:
					return await this.GetGrpcClient<UserService.UserServiceClient>(msgId, invoker).GetHomepageGuideItemAsync((CS_GetHomepageGuideItemReq)message);
				case GameMSGID.CS_RegisterUserDeviceReq_ID:
					return await this.GetGrpcClient<NotificationService.NotificationServiceClient>(msgId, invoker).RegisterUserDeviceAsync((CS_RegisterUserDeviceReq)message);
				case GameMSGID.CS_GetSystemNoticeReq_ID:
					return await this.GetGrpcClient<NotificationService.NotificationServiceClient>(msgId, invoker).GetSystemNoticeAsync((CS_GetSystemNoticeReq)message);
				case GameMSGID.CS_SubmitQuestionFeedbackReq_ID:
					return await this.GetGrpcClient<QuestionProcessService.QuestionProcessServiceClient>(msgId, invoker).SubmitQuestionFeedbackAsync((CS_SubmitQuestionFeedbackReq)message);
				case GameMSGID.CS_GetQuickPracticeListReq_ID:
					return await this.GetGrpcClient<QuestionProcessService.QuestionProcessServiceClient>(msgId, invoker).GetQuickPracticeListAsync((CS_GetQuickPracticeListReq)message);
				case GameMSGID.CS_GetDynamicQpListReq_ID:
					return await this.GetGrpcClient<QuestionProcessService.QuestionProcessServiceClient>(msgId, invoker).GetDynamicQpListAsync((CS_GetDynamicQpListReq)message);
				case GameMSGID.CS_SubmitQuickPracticeReq_ID:
					return await this.GetGrpcClient<QuestionProcessService.QuestionProcessServiceClient>(msgId, invoker).SubmitQuickPracticeAsync((CS_SubmitQuickPracticeReq)message);
				case GameMSGID.CS_SubmitQuickPracticeNewReq_ID:
					return await this.GetGrpcClient<QuestionProcessService.QuestionProcessServiceClient>(msgId, invoker).SubmitQuickPracticeNewAsync((CS_SubmitQuickPracticeNewReq)message);
				case GameMSGID.CS_ExitQuickPracticeReq_ID:
					return await this.GetGrpcClient<QuestionProcessService.QuestionProcessServiceClient>(msgId, invoker).ExitQuickPracticeAsync((CS_ExitQuickPracticeReq)message);
				case GameMSGID.CS_SubmitSpeakUserAnswerReq_ID:
					return await this.GetGrpcClient<QuestionProcessService.QuestionProcessServiceClient>(msgId, invoker).SubmitSpeakUserAnswerAsync((CS_SubmitSpeakUserAnswerReq)message);
				case GameMSGID.CS_RecommendTaskReq_ID:
					return await this.GetGrpcClient<RecommendService.RecommendServiceClient>(msgId, invoker).RecommendTaskAsync((CS_RecommendTaskReq)message);
				case GameMSGID.CS_UploadUserTaskActionReq_ID:
					return await this.GetGrpcClient<RecommendService.RecommendServiceClient>(msgId, invoker).UploadUserTaskActionAsync((CS_UploadUserTaskActionReq)message);
				case GameMSGID.CS_GetSingleChatDialogReq_ID:
					return await this.GetGrpcClient<ChatService.ChatServiceClient>(msgId, invoker).GetSingleChatDialogAsync((CS_GetSingleChatDialogReq)message);
				case GameMSGID.CS_QueryChatListReq_ID:
					return await this.GetGrpcClient<ChatService.ChatServiceClient>(msgId, invoker).QueryChatListAsync((CS_QueryChatListReq)message);
				case GameMSGID.CS_QueryChatMessageListReq_ID:
					return await this.GetGrpcClient<ChatService.ChatServiceClient>(msgId, invoker).QueryChatMessageListAsync((CS_QueryChatMessageListReq)message);
				case GameMSGID.CS_SendChatMessageReq_ID:
					return await this.GetGrpcClient<ChatService.ChatServiceClient>(msgId, invoker).SendChatMessageAsync((CS_SendChatMessageReq)message);
				case GameMSGID.CS_UpdateChatIndexReq_ID:
					return await this.GetGrpcClient<ChatService.ChatServiceClient>(msgId, invoker).UpdateChatIndexAsync((CS_UpdateChatIndexReq)message);
				case GameMSGID.CS_QueryChatUserListReq_ID:
					return await this.GetGrpcClient<ChatService.ChatServiceClient>(msgId, invoker).QueryChatUserListAsync((CS_QueryChatUserListReq)message);
				case GameMSGID.CS_ChatScaffoldReq_ID:
					return await this.GetGrpcClient<ChatService.ChatServiceClient>(msgId, invoker).ChatScaffoldAsync((CS_ChatScaffoldReq)message);
				case GameMSGID.CS_GenTestSingleChatReq_ID:
					return await this.GetGrpcClient<ChatService.ChatServiceClient>(msgId, invoker).GenTestSingleChatAsync((CS_GenTestSingleChatReq)message);
				case GameMSGID.CS_QueryUnReadMessageNumReq_ID:
					return await this.GetGrpcClient<ChatService.ChatServiceClient>(msgId, invoker).QueryUnReadMessageNumAsync((CS_QueryUnReadMessageNumReq)message);
				case GameMSGID.CS_ChatRecordStatusSyncReq_ID:
					return await this.GetGrpcClient<ChatService.ChatServiceClient>(msgId, invoker).ChatRecordStatusSyncAsync((CS_ChatRecordStatusSyncReq)message);
				case GameMSGID.CS_GetBuildingTopicListReq_ID:
					return await this.GetGrpcClient<WorldDialogService.WorldDialogServiceClient>(msgId, invoker).GetBuildingTopicListAsync((CS_GetBuildingTopicListReq)message);
				case GameMSGID.CS_GetAreaAchievementListReq_ID:
					return await this.GetGrpcClient<WorldDialogService.WorldDialogServiceClient>(msgId, invoker).GetAreaAchievementListAsync((CS_GetAreaAchievementListReq)message);
				case GameMSGID.CS_CreateWorldStoryTaskReq_ID:
					return await this.GetGrpcClient<WorldDialogService.WorldDialogServiceClient>(msgId, invoker).CreateWorldStoryTaskAsync((CS_CreateWorldStoryTaskReq)message);
				case GameMSGID.CS_FinishWorldStoryTaskReq_ID:
					return await this.GetGrpcClient<WorldDialogService.WorldDialogServiceClient>(msgId, invoker).FinishWorldStoryTaskAsync((CS_FinishWorldStoryTaskReq)message);
				case GameMSGID.CS_CancelAssessReq_ID:
					return await this.GetGrpcClient<SpeechService.SpeechServiceClient>(msgId, invoker).CancelAssessAsync((CS_CancelAssessReq)message);
				case GameMSGID.CS_CancelASRReq_ID:
					return await this.GetGrpcClient<SpeechService.SpeechServiceClient>(msgId, invoker).CancelASRAsync((CS_CancelASRReq)message);
				case GameMSGID.CS_GetASRAudioReq_ID:
					return await this.GetGrpcClient<SpeechService.SpeechServiceClient>(msgId, invoker).GetASRAudioAsync((CS_GetASRAudioReq)message);
				case GameMSGID.CS_UploadAsrAudioReq_ID:
					return await this.GetGrpcClient<SpeechService.SpeechServiceClient>(msgId, invoker).UploadAsrAudioAsync((CS_UploadAsrAudioReq)message);
				case GameMSGID.CS_GetTalkitPushReq_ID:
					return await this.GetGrpcClient<TalkitPush.TalkitPushClient>(msgId, invoker).GetTalkitPushReqAsync((CS_GetTalkitPushReq)message);
				case GameMSGID.CS_SubmitTaskFeedbackReq_ID:
					return await this.GetGrpcClient<TaskProcessService.TaskProcessServiceClient>(msgId, invoker).SubmitTaskFeedbackAsync((CS_SubmitTaskFeedbackReq)message);
				case GameMSGID.CS_SwitchTaskDifficultyReq_ID:
					return await this.GetGrpcClient<TaskProcessService.TaskProcessServiceClient>(msgId, invoker).SwitchTaskDifficultyAsync((CS_SwitchTaskDifficultyReq)message);
				case GameMSGID.CS_GetTaskModeBatchReq_ID:
					return await this.GetGrpcClient<TaskProcessService.TaskProcessServiceClient>(msgId, invoker).GetTaskModeBatchAsync((CS_GetTaskModeBatchReq)message);
				case GameMSGID.CS_GetTaskInfoReq_ID:
					return await this.GetGrpcClient<TaskProcessService.TaskProcessServiceClient>(msgId, invoker).GetTaskInfoAsync((CS_GetTaskInfoReq)message);
				case GameMSGID.CS_GetDailyTaskInfoReq_ID:
					return await this.GetGrpcClient<TaskProcessService.TaskProcessServiceClient>(msgId, invoker).GetDailyTaskInfoAsync((CS_GetDailyTaskInfoReq)message);
				case GameMSGID.CS_GetTTSAudioTranscriptReq_ID:
					return await this.GetGrpcClient<TtsService.TtsServiceClient>(msgId, invoker).GetTTSAudioTranscriptAsync((CS_GetTTSAudioTranscriptReq)message);
				case GameMSGID.CS_GetWorldRoleInfoReq_ID:
					return await this.GetGrpcClient<WorldService.WorldServiceClient>(msgId, invoker).GetWorldRoleInfoAsync((CS_GetWorldRoleInfoReq)message);
				case GameMSGID.CS_CreateWorldRoleReq_ID:
					return await this.GetGrpcClient<WorldService.WorldServiceClient>(msgId, invoker).CreateWorldRoleAsync((CS_CreateWorldRoleReq)message);
			}
			return null;
		}

		public ClientBase CreateGrpcClient(short msgId, CallInvoker callInvoker)
		{
			switch ((GameMSGID)msgId)
			{
				case GameMSGID.CS_GetAbTestResultReq_ID:
					return new AbtestService.AbtestServiceClient(callInvoker);
				case GameMSGID.CS_TreeListForHomepageReq_ID:
					return new AchievementService.AchievementServiceClient(callInvoker);
				case GameMSGID.CS_TreeDetailForHomepageReq_ID:
					return new AchievementService.AchievementServiceClient(callInvoker);
				case GameMSGID.CS_SandTableListReq_ID:
					return new AchievementService.AchievementServiceClient(callInvoker);
				case GameMSGID.CS_SandTableHomepageReq_ID:
					return new AchievementService.AchievementServiceClient(callInvoker);
				case GameMSGID.CS_DataBatchReq_ID:
					return new CenterService.CenterServiceClient(callInvoker);
				case GameMSGID.CS_GetUserCourseReq_ID:
					return new CourseService.CourseServiceClient(callInvoker);
				case GameMSGID.CS_SkipCourseReq_ID:
					return new CourseService.CourseServiceClient(callInvoker);
				case GameMSGID.CS_RewardBoxReq_ID:
					return new CourseService.CourseServiceClient(callInvoker);
				case GameMSGID.CS_GetBookDataReq_ID:
					return new CourseService.CourseServiceClient(callInvoker);
				case GameMSGID.CS_GetRadioDataReq_ID:
					return new CourseService.CourseServiceClient(callInvoker);
				case GameMSGID.CS_GetCourseSettlementReq_ID:
					return new CourseService.CourseServiceClient(callInvoker);
				case GameMSGID.CS_BuyCourseTicketReq_ID:
					return new CourseService.CourseServiceClient(callInvoker);
				case GameMSGID.CS_GetUserShelfReq_ID:
					return new CourseService.CourseServiceClient(callInvoker);
				case GameMSGID.CS_GetFailedMsgReq_ID:
					return new DialogBaseService.DialogBaseServiceClient(callInvoker);
				case GameMSGID.CS_DialogHelpReq_ID:
					return new DialogBaseService.DialogBaseServiceClient(callInvoker);
				case GameMSGID.CS_QueryDialogRecordReq_ID:
					return new DialogBaseService.DialogBaseServiceClient(callInvoker);
				case GameMSGID.CS_DialogFlowCreateReq_ID:
					return new DialogFlowService.DialogFlowServiceClient(callInvoker);
				case GameMSGID.CS_DialogFlowMsgHandleReq_ID:
					return new DialogFlowService.DialogFlowServiceClient(callInvoker);
				case GameMSGID.CS_DialogFlowStopReq_ID:
					return new DialogFlowService.DialogFlowServiceClient(callInvoker);
				case GameMSGID.CS_FlowHelpReq_ID:
					return new DialogFlowService.DialogFlowServiceClient(callInvoker);
				case GameMSGID.CS_CreateDialogTaskCacheReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetDialogTaskModeListReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetModeInfoReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetModeInfoForCareerReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_CreateDialogTaskReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_DialogTaskMsgHandleReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetDialogTaskNextRoundMsgReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_StrengthenCreateDialogTaskReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_StrengthenDialogTaskMsgHandleReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetStrengthenDialogTaskNextRoundMsgReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_WarmupPracticeCreateDialogTaskReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_WarmupPracticeDialogTaskMsgHandleReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetWarmupPracticeDialogTaskNextRoundMsgReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_ExitDialogTaskReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_ClickWordReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_FeedbackReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetAudioForFlowReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_BuyDialogTicketReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetInProgressDialogReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetPopListInfoReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetDialogScaffoldReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetAvatarListByDialogModeReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_DialogSuggestionReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_DialogTranslateReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetAvatarTaskInfoReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetStartPageInfoReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetUserAssistLevelListReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_SetUserAssistLevelReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetUserContactsReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetRedPodReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_ClickRedPodReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_AvatarFavoriteSettingReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetUserQuestionExtraListReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetKnowledgePointListReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetKnowledgePointFrontPageReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetUserQuestionExtraFrontPageReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_BatchGetUserQuestionExtraFrontPageReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_ReviewUserQuestionExtraReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetDialogSettlementReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_StartBatchQuestionDialogReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_SubmitBatchQuestionDialogReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetUserDialogHistoryReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetBoxRewardReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetUserTopicDialogHistoryReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_CreateQuestionDialogReq_ID:
					return new DialogTaskService.DialogTaskServiceClient(callInvoker);
				case GameMSGID.CS_GetUserInfoReq_ID:
					return new DialogUserService.DialogUserServiceClient(callInvoker);
				case GameMSGID.CS_ChangeMarkInfoReq_ID:
					return new DialogUserService.DialogUserServiceClient(callInvoker);
				case GameMSGID.CS_GetLearnPathPageTypeReq_ID:
					return new UserLearnPathService.UserLearnPathServiceClient(callInvoker);
				case GameMSGID.CS_GetUserChapterInfoReq_ID:
					return new UserLearnPathService.UserLearnPathServiceClient(callInvoker);
				case GameMSGID.CS_GetUserGoalNodeReq_ID:
					return new UserLearnPathService.UserLearnPathServiceClient(callInvoker);
				case GameMSGID.CS_GetGoalInfoReq_ID:
					return new UserLearnPathService.UserLearnPathServiceClient(callInvoker);
				case GameMSGID.CS_GetChapterRecommendListReq_ID:
					return new UserLearnPathService.UserLearnPathServiceClient(callInvoker);
				case GameMSGID.CS_SelectChapterReq_ID:
					return new UserLearnPathService.UserLearnPathServiceClient(callInvoker);
				case GameMSGID.CS_GetChapterProgressInfoReq_ID:
					return new UserLearnPathService.UserLearnPathServiceClient(callInvoker);
				case GameMSGID.CS_GetChapterRewardReq_ID:
					return new UserLearnPathService.UserLearnPathServiceClient(callInvoker);
				case GameMSGID.CS_NotifyAnimationStateReq_ID:
					return new UserLearnPathService.UserLearnPathServiceClient(callInvoker);
				case GameMSGID.CS_GetUserGoalDetailReq_ID:
					return new UserLearnPathService.UserLearnPathServiceClient(callInvoker);
				case GameMSGID.CS_LearnPathRewardReq_ID:
					return new UserLearnPathService.UserLearnPathServiceClient(callInvoker);
				case GameMSGID.CS_DelLearnPathDataReq_ID:
					return new UserLearnPathService.UserLearnPathServiceClient(callInvoker);
				case GameMSGID.CS_OpenRewardBoxReq_ID:
					return new UserLearnPathService.UserLearnPathServiceClient(callInvoker);
				case GameMSGID.CS_OnboardingNewStoryReq_ID:
					return new UserLearnPathService.UserLearnPathServiceClient(callInvoker);
				case GameMSGID.CS_QueryMemberInfoReq_ID:
					return new BenefitService.BenefitServiceClient(callInvoker);
				case GameMSGID.CS_PurchaseStaminaReq_ID:
					return new BenefitService.BenefitServiceClient(callInvoker);
				case GameMSGID.CS_EconomicCoinGetBalanceReq_ID:
					return new EconomicService.EconomicServiceClient(callInvoker);
				case GameMSGID.CS_GetEconomicInfoReq_ID:
					return new EconomicService.EconomicServiceClient(callInvoker);
				case GameMSGID.CS_GetShopInfoReq_ID:
					return new MerchandiseService.MerchandiseServiceClient(callInvoker);
				case GameMSGID.CS_PayMerchandiseReq_ID:
					return new MerchandiseService.MerchandiseServiceClient(callInvoker);
				case GameMSGID.CS_GetAllShopMerchandiseReq_ID:
					return new MerchandiseService.MerchandiseServiceClient(callInvoker);
				case GameMSGID.CS_CreateOrderReq_ID:
					return new OrderService.OrderServiceClient(callInvoker);
				case GameMSGID.CS_CancelOrderReq_ID:
					return new OrderService.OrderServiceClient(callInvoker);
				case GameMSGID.CS_GetUnpaidOrderReq_ID:
					return new OrderService.OrderServiceClient(callInvoker);
				case GameMSGID.CS_VerifyReceiptDataReq_ID:
					return new PayService.PayServiceClient(callInvoker);
				case GameMSGID.CS_ExploreUpMsg_ID:
					return new ExploreBizService.ExploreBizServiceClient(callInvoker);
				case GameMSGID.CS_GetOnboardingChatPreloadDataReq_ID:
					return new ExploreOnboardingService.ExploreOnboardingServiceClient(callInvoker);
				case GameMSGID.CS_GetRecommendListReq_ID:
					return new ExploreRecommendService.ExploreRecommendServiceClient(callInvoker);
				case GameMSGID.CS_GetRecommendListV2Req_ID:
					return new ExploreRecommendService.ExploreRecommendServiceClient(callInvoker);
				case GameMSGID.CS_GetUserHistoryProgressListReq_ID:
					return new ExploreRecommendService.ExploreRecommendServiceClient(callInvoker);
				case GameMSGID.CS_GetUserCheckinDataReq_ID:
					return new CheckinService.CheckinServiceClient(callInvoker);
				case GameMSGID.CS_DrawCheckinRewardReq_ID:
					return new CheckinService.CheckinServiceClient(callInvoker);
				case GameMSGID.CS_SetCheckinMilestoneReq_ID:
					return new CheckinService.CheckinServiceClient(callInvoker);
				case GameMSGID.CS_GetCheckinSummaryReq_ID:
					return new CheckinService.CheckinServiceClient(callInvoker);
				case GameMSGID.CS_RecheckinReq_ID:
					return new CheckinService.CheckinServiceClient(callInvoker);
				case GameMSGID.CS_GetUserCheckinPortalDataReq_ID:
					return new CheckinService.CheckinServiceClient(callInvoker);
				case GameMSGID.CS_DrawRewardReq_ID:
					return new CheckinService.CheckinServiceClient(callInvoker);
				case GameMSGID.CS_GetUserCheckinDataForTaskFinishReq_ID:
					return new CheckinService.CheckinServiceClient(callInvoker);
				case GameMSGID.CS_GetUserCheckinCalendarReq_ID:
					return new CheckinService.CheckinServiceClient(callInvoker);
				case GameMSGID.CS_RefuseRecheckinReq_ID:
					return new CheckinService.CheckinServiceClient(callInvoker);
				case GameMSGID.CS_GetFriendStreakPortalReq_ID:
					return new CheckinService.CheckinServiceClient(callInvoker);
				case GameMSGID.CS_UpdateFriendStreakRelationReq_ID:
					return new CheckinService.CheckinServiceClient(callInvoker);
				case GameMSGID.CS_GetFriendStreakRecommendListReq_ID:
					return new CheckinService.CheckinServiceClient(callInvoker);
				case GameMSGID.CS_RedeemCouponReq_ID:
					return new CouponService.CouponServiceClient(callInvoker);
				case GameMSGID.CS_GetUserFriendListReq_ID:
					return new FriendshipService.FriendshipServiceClient(callInvoker);
				case GameMSGID.CS_AddFriendReq_ID:
					return new FriendshipService.FriendshipServiceClient(callInvoker);
				case GameMSGID.CS_RemoveFriendReq_ID:
					return new FriendshipService.FriendshipServiceClient(callInvoker);
				case GameMSGID.CS_GetRecommendedFriendListReq_ID:
					return new FriendshipService.FriendshipServiceClient(callInvoker);
				case GameMSGID.CS_SearchUserReq_ID:
					return new FriendshipService.FriendshipServiceClient(callInvoker);
				case GameMSGID.CS_GetUserGiftPortalDataReq_ID:
					return new FriendshipService.FriendshipServiceClient(callInvoker);
				case GameMSGID.CS_MatchFriendShipTaskReq_ID:
					return new FriendshipService.FriendshipServiceClient(callInvoker);
				case GameMSGID.CS_FriendShipNotifyReq_ID:
					return new FriendshipService.FriendshipServiceClient(callInvoker);
				case GameMSGID.CS_GetGrowthWeeklyGiftReq_ID:
					return new GrowthService.GrowthServiceClient(callInvoker);
				case GameMSGID.CS_GetUserProfileReq_ID:
					return new IncentiveService.IncentiveServiceClient(callInvoker);
				case GameMSGID.CS_GetMyProfileReq_ID:
					return new IncentiveService.IncentiveServiceClient(callInvoker);
				case GameMSGID.CS_SetUserProfileAuthorityReq_ID:
					return new IncentiveService.IncentiveServiceClient(callInvoker);
				case GameMSGID.CS_GetUserProfileAuthorityReq_ID:
					return new IncentiveService.IncentiveServiceClient(callInvoker);
				case GameMSGID.CS_SendFeedbackReq_ID:
					return new IncentiveService.IncentiveServiceClient(callInvoker);
				case GameMSGID.CS_SetUserVoiceInformationReq_ID:
					return new IncentiveService.IncentiveServiceClient(callInvoker);
				case GameMSGID.CS_SetUserPortraitsReq_ID:
					return new IncentiveService.IncentiveServiceClient(callInvoker);
				case GameMSGID.CS_SetUserLanguageLevelReq_ID:
					return new IncentiveService.IncentiveServiceClient(callInvoker);
				case GameMSGID.CS_GetUserPortraitsReq_ID:
					return new IncentiveService.IncentiveServiceClient(callInvoker);
				case GameMSGID.CS_GetIncentiveDataForPortalReq_ID:
					return new IncentiveService.IncentiveServiceClient(callInvoker);
				case GameMSGID.CS_SetUserDressUpReq_ID:
					return new IncentiveService.IncentiveServiceClient(callInvoker);
				case GameMSGID.CS_GetUserDressUpMerchandiseDataReq_ID:
					return new IncentiveService.IncentiveServiceClient(callInvoker);
				case GameMSGID.CS_SetUserHeadItemReq_ID:
					return new IncentiveService.IncentiveServiceClient(callInvoker);
				case GameMSGID.CS_SendGiftForFriendReq_ID:
					return new IncentiveService.IncentiveServiceClient(callInvoker);
				case GameMSGID.CS_SetShowStateReq_ID:
					return new IncentiveService.IncentiveServiceClient(callInvoker);
				case GameMSGID.CS_GetIncentiveDataForExploreReq_ID:
					return new IncentiveService.IncentiveServiceClient(callInvoker);
				case GameMSGID.CS_StartConsumeReq_ID:
					return new IncentiveService.IncentiveServiceClient(callInvoker);
				case GameMSGID.CS_GetUserVoiceCloneAudioReq_ID:
					return new IncentiveService.IncentiveServiceClient(callInvoker);
				case GameMSGID.CS_ReportInvalidUserAudioReq_ID:
					return new IncentiveService.IncentiveServiceClient(callInvoker);
				case GameMSGID.CS_GetUserRankingPortalDataReq_ID:
					return new RankingService.RankingServiceClient(callInvoker);
				case GameMSGID.CS_JoinRankingReq_ID:
					return new RankingService.RankingServiceClient(callInvoker);
				case GameMSGID.CS_SetRankingChangeClickReq_ID:
					return new RankingService.RankingServiceClient(callInvoker);
				case GameMSGID.CS_ScaffoldFeedbackReq_ID:
					return new LearnAssistService.LearnAssistServiceClient(callInvoker);
				case GameMSGID.CS_TaskFeedbackReq_ID:
					return new LearnAssistService.LearnAssistServiceClient(callInvoker);
				case GameMSGID.CS_QuestionFeedbackReq_ID:
					return new LearnAssistService.LearnAssistServiceClient(callInvoker);
				case GameMSGID.CS_FreeTalkFeedbackReq_ID:
					return new LearnAssistService.LearnAssistServiceClient(callInvoker);
				case GameMSGID.CS_Content2AudioReq_ID:
					return new LearnAssistService.LearnAssistServiceClient(callInvoker);
				case GameMSGID.CS_WordTransReq_ID:
					return new LearnAssistService.LearnAssistServiceClient(callInvoker);
				case GameMSGID.CS_TranslateReq_ID:
					return new LearnAssistService.LearnAssistServiceClient(callInvoker);
				case GameMSGID.CS_SendVerificationCodeReq_ID:
					return new LoginService.LoginServiceClient(callInvoker);
				case GameMSGID.CS_UpdateAppsflyerCallbackDataReq_ID:
					return new UserService.UserServiceClient(callInvoker);
				case GameMSGID.CS_GetHomepageGuideItemReq_ID:
					return new UserService.UserServiceClient(callInvoker);
				case GameMSGID.CS_RegisterUserDeviceReq_ID:
					return new NotificationService.NotificationServiceClient(callInvoker);
				case GameMSGID.CS_GetSystemNoticeReq_ID:
					return new NotificationService.NotificationServiceClient(callInvoker);
				case GameMSGID.CS_SubmitQuestionFeedbackReq_ID:
					return new QuestionProcessService.QuestionProcessServiceClient(callInvoker);
				case GameMSGID.CS_GetQuickPracticeListReq_ID:
					return new QuestionProcessService.QuestionProcessServiceClient(callInvoker);
				case GameMSGID.CS_GetDynamicQpListReq_ID:
					return new QuestionProcessService.QuestionProcessServiceClient(callInvoker);
				case GameMSGID.CS_SubmitQuickPracticeReq_ID:
					return new QuestionProcessService.QuestionProcessServiceClient(callInvoker);
				case GameMSGID.CS_SubmitQuickPracticeNewReq_ID:
					return new QuestionProcessService.QuestionProcessServiceClient(callInvoker);
				case GameMSGID.CS_ExitQuickPracticeReq_ID:
					return new QuestionProcessService.QuestionProcessServiceClient(callInvoker);
				case GameMSGID.CS_SubmitSpeakUserAnswerReq_ID:
					return new QuestionProcessService.QuestionProcessServiceClient(callInvoker);
				case GameMSGID.CS_RecommendTaskReq_ID:
					return new RecommendService.RecommendServiceClient(callInvoker);
				case GameMSGID.CS_UploadUserTaskActionReq_ID:
					return new RecommendService.RecommendServiceClient(callInvoker);
				case GameMSGID.CS_GetSingleChatDialogReq_ID:
					return new ChatService.ChatServiceClient(callInvoker);
				case GameMSGID.CS_QueryChatListReq_ID:
					return new ChatService.ChatServiceClient(callInvoker);
				case GameMSGID.CS_QueryChatMessageListReq_ID:
					return new ChatService.ChatServiceClient(callInvoker);
				case GameMSGID.CS_SendChatMessageReq_ID:
					return new ChatService.ChatServiceClient(callInvoker);
				case GameMSGID.CS_UpdateChatIndexReq_ID:
					return new ChatService.ChatServiceClient(callInvoker);
				case GameMSGID.CS_QueryChatUserListReq_ID:
					return new ChatService.ChatServiceClient(callInvoker);
				case GameMSGID.CS_ChatScaffoldReq_ID:
					return new ChatService.ChatServiceClient(callInvoker);
				case GameMSGID.CS_GenTestSingleChatReq_ID:
					return new ChatService.ChatServiceClient(callInvoker);
				case GameMSGID.CS_QueryUnReadMessageNumReq_ID:
					return new ChatService.ChatServiceClient(callInvoker);
				case GameMSGID.CS_ChatRecordStatusSyncReq_ID:
					return new ChatService.ChatServiceClient(callInvoker);
				case GameMSGID.CS_GetBuildingTopicListReq_ID:
					return new WorldDialogService.WorldDialogServiceClient(callInvoker);
				case GameMSGID.CS_GetAreaAchievementListReq_ID:
					return new WorldDialogService.WorldDialogServiceClient(callInvoker);
				case GameMSGID.CS_CreateWorldStoryTaskReq_ID:
					return new WorldDialogService.WorldDialogServiceClient(callInvoker);
				case GameMSGID.CS_StartWorldDialogReq_ID:
					return new WorldDialogService.WorldDialogServiceClient(callInvoker);
				case GameMSGID.CS_FinishWorldStoryTaskReq_ID:
					return new WorldDialogService.WorldDialogServiceClient(callInvoker);
				case GameMSGID.CS_AssessWithAudioStreamReq_ID:
					return new SpeechService.SpeechServiceClient(callInvoker);
				case GameMSGID.CS_CancelAssessReq_ID:
					return new SpeechService.SpeechServiceClient(callInvoker);
				case GameMSGID.CS_StartASRStreamReq_ID:
					return new SpeechService.SpeechServiceClient(callInvoker);
				case GameMSGID.CS_CancelASRReq_ID:
					return new SpeechService.SpeechServiceClient(callInvoker);
				case GameMSGID.CS_GetSpeechAudioReq_ID:
					return new SpeechService.SpeechServiceClient(callInvoker);
				case GameMSGID.CS_GetASRAudioReq_ID:
					return new SpeechService.SpeechServiceClient(callInvoker);
				case GameMSGID.CS_UploadAsrAudioReq_ID:
					return new SpeechService.SpeechServiceClient(callInvoker);
				case GameMSGID.CS_GetTalkitPushReq_ID:
					return new TalkitPush.TalkitPushClient(callInvoker);
				case GameMSGID.CS_SubmitTaskFeedbackReq_ID:
					return new TaskProcessService.TaskProcessServiceClient(callInvoker);
				case GameMSGID.CS_SwitchTaskDifficultyReq_ID:
					return new TaskProcessService.TaskProcessServiceClient(callInvoker);
				case GameMSGID.CS_GetTaskModeBatchReq_ID:
					return new TaskProcessService.TaskProcessServiceClient(callInvoker);
				case GameMSGID.CS_GetTaskInfoReq_ID:
					return new TaskProcessService.TaskProcessServiceClient(callInvoker);
				case GameMSGID.CS_GetDailyTaskInfoReq_ID:
					return new TaskProcessService.TaskProcessServiceClient(callInvoker);
				case GameMSGID.CS_GetTTSAudioReq_ID:
					return new TtsService.TtsServiceClient(callInvoker);
				case GameMSGID.CS_GetTTSAudioTranscriptReq_ID:
					return new TtsService.TtsServiceClient(callInvoker);
				case GameMSGID.CS_GetWorldRoleInfoReq_ID:
					return new WorldService.WorldServiceClient(callInvoker);
				case GameMSGID.CS_CreateWorldRoleReq_ID:
					return new WorldService.WorldServiceClient(callInvoker);
			}
			return null;
		}

		public T GetGrpcClient<T>(short msgId, CallInvoker callInvoker) where T : ClientBase<T>
		{
			Type t = typeof(T);
			if (this._grpcClientMap.ContainsKey(t))
				return (T)this._grpcClientMap[t];
			var client = this.CreateGrpcClient(msgId, callInvoker);
			this._grpcClientMap[t] = client;
			return (T)client;
		}
	}
}