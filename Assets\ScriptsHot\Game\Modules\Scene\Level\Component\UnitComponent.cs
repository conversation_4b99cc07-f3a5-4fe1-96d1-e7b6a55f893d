﻿using Msg.core;
using System;
using System.Collections.Generic;
using System.Linq;
using ScriptsHot.Game.Modules.Scene.Level;
using ScriptsHot.Game.Modules.Scene.Level.Component;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Scene.Level.Component
{
    public class UnitComponent : ComponentBase
    {
        class UnitLockCacheVO
        {
            public int time;
            public Action successCallback;
            public Action failCallback;
        }

        private Unit _mainUnit;
        private Dictionary<long, UnitLockCacheVO> _unitLockCbMap = new();

        private Level _scene;

        public UnitComponent(IComponetOwner owner) : base(owner)
        {
            this._scene = owner as Level;
        }

        public Level scene
        {
            get { return _scene; }
        }

        public long mainUnitID
        {
            get
            {
                if (this._mainUnit != null)
                    return this._mainUnit.uid;
                return 0;
            }
        }

        public Unit mainUnit
        {
            get { return this._mainUnit; }
        }
        

        public Unit GetUnitByAvatarTid(long avatarTid)
        {
            var list = this._scene.GetAllCharacterUids();
            foreach (var uid in list)
            {
                Unit unit = this._scene.GetCharacter(uid) as Unit;
                if (unit.avatarTid == avatarTid)
                    return unit;
            }

            return null;
        }

        public Unit GetUnitByObjInsID(long objInsID)
        {
            var list = this._scene.GetAllCharacterUids();
            foreach (var uid in list)
            {
                Unit unit = this._scene.GetCharacter(uid) as Unit;
                if (unit.objInsId == objInsID)
                    return unit;
            }

            return null;
        }

        //自身角色
        public void CreateMainUnit(PB_RoleBaseInfo baseInfo, string headUrl = null, string styleName = null,
            long userId = -1)
        {
            Debug.Log("Main========" + baseInfo.style);

            if (this._mainUnit != null)
            {
                Debug.LogError("Canot create main unit.Current:" + this._mainUnit.uid);
                return;
            }

            // TODO 
            Unit unit = new Unit(this.scene, baseInfo.objInsID, baseInfo.avatarID, baseInfo.playerName,
                ESceneAvatar.Role, baseInfo.gender, styles[int.Parse(baseInfo.style)], String.Empty,
                baseInfo.roleUniqueName, baseInfo.member_type, headUrl, baseInfo.style);
            unit.EnterScene(baseInfo.pos.x, baseInfo.pos.z, baseInfo.dir);
            this._mainUnit = unit;
            if (userId != -1)
                this._mainUnit.SetAccountId(userId);
            if (headUrl != null)
                SetHeadUrlData(headUrl, userId, baseInfo.style);
        }

        //其他玩家角色
        public void CreateRoleUnit(PB_RoleBaseInfo baseInfo, long accountID, int state, string headUrl = null)
        {
            if (string.IsNullOrEmpty(baseInfo.style))
            {
                Debug.LogError(baseInfo.playerName + "styleId 是空的");
                return;
            }

            if ((this.GetOwner() as Level).GetCharacter(baseInfo.objInsID) != null)
            {
                return;
            }
            
            SetHeadUrlData(headUrl, accountID, baseInfo.style);
            Debug.Log("Role========" + baseInfo.style);
            Unit unit = new Unit(this.scene, baseInfo.objInsID, baseInfo.avatarID, baseInfo.playerName,
                ESceneAvatar.Role, baseInfo.gender, styles[int.Parse(baseInfo.style)], string.Empty,
                baseInfo.roleUniqueName, baseInfo.member_type, headUrl, baseInfo.style, state);
            unit.SetAccountId(accountID);
            unit.EnterScene(baseInfo.pos.x, baseInfo.pos.z, baseInfo.dir);
        }

        //npc角色
        public void CreateAvatarUnit(PB_RoleBaseInfo baseInfo, PB_ActionInfo actionInfo, PB_BubblingInfo bullingInfo,
            string jobTitle, string headUrl, string styleName)
        {
            Debug.Log("Avatar=========" + styles[int.Parse(styleName)]);

            if ((this.GetOwner() as Level).GetCharacter(baseInfo.objInsID) != null)
            {
                return;
            }
            //SetHeadUrlData(headUrl, baseInfo.avatarID,styleName);
            Unit unit = new Unit(this.scene, baseInfo.objInsID, baseInfo.avatarID, baseInfo.playerName,
                ESceneAvatar.Avatar, baseInfo.gender, styles[int.Parse(styleName)], jobTitle, baseInfo.roleUniqueName,
                baseInfo.member_type, headUrl, styleName);
            unit.EnterScene(baseInfo.pos.x, baseInfo.pos.z, baseInfo.dir);


            unit.modAction.SetAction(EAvatarMoveAction.Idle);

            //TODO Action info
        }

        public void LockUnit(Unit unit, Action successCallback, Action failCallback = null)
        {
            if (unit.GetBit(EUnitBit.Lock))
            {
                Debug.Log("LockUnit is return");
                if (successCallback != null) successCallback();
                return;
            }

            if (this._unitLockCbMap.ContainsKey(unit.uid))
            {
                if (failCallback != null) failCallback();
                return;
            }

            this._unitLockCbMap[unit.uid] = new UnitLockCacheVO
                {time = TimeExt.currTime, successCallback = successCallback, failCallback = failCallback};
            unit.SetBit(EUnitBit.Lock, true);

            int talkType = 1;
            if (scene.GetComponent<ChatComponent>().nextAvatarTid == unit.avatarTid)
                talkType = scene.GetComponent<ChatComponent>().nextTalkMode;
            else
            {
                Debug.LogError(
                    $"LockUnit avatarId is error avatarTid {unit.avatarTid} nextAvatarTid {scene.GetComponent<ChatComponent>().nextAvatarTid} ");
            }
            VFDebug.Log("锁定！！！！！" + unit.uid);
            MsgManager.instance.SendMsg(new CS_TalkToAvatarReq
                {objInsID = unit.uid, talkType = talkType, talkFlag = 1});
        }

        public void OnUnitLockSrvMsg(Unit unit, bool locked)
        {
            if (this._unitLockCbMap.ContainsKey(unit.uid))
            {
                var successCallback = this._unitLockCbMap[unit.uid].successCallback;
                var failCallback = this._unitLockCbMap[unit.uid].failCallback;
                this._unitLockCbMap.Remove(unit.uid);
                if (locked)
                    if (successCallback != null) successCallback();
                    else if (failCallback != null) failCallback();
            }
        }

        public void UnLockAllUnit()
        {
           
            foreach (var vo in this._unitLockCbMap.Values)
            {
                if (vo.failCallback != null)
                    vo.failCallback();
            }
            VFDebug.Log("取消锁定！！！！开始！" );
            this._unitLockCbMap.Clear();
            List<long> uidList = this._scene.GetAllCharacterUids();
            foreach (var uid in uidList)
            {
                Unit unit = this.scene.GetCharacter(uid) as Unit;
                int talkType = 1;
                if (scene.GetComponent<ChatComponent>().nextAvatarTid == unit.avatarTid)
                    talkType = scene.GetComponent<ChatComponent>().nextTalkMode;
                if (unit == null) continue;
                if (!unit.GetBit(EUnitBit.Lock)) continue;
                unit.SetBit(EUnitBit.Lock, false);
                VFDebug.Log("取消锁定！！！！！" + unit.uid);
                MsgManager.instance.SendMsg(new CS_TalkToAvatarReq
                    {objInsID = unit.uid, talkFlag = 0, talkType = talkType});
            }
        }

        public override void OnInit()
        {
        }

        public override void OnDispose()
        {
        }

        public override void Refresh()
        {
        }

        public override void Update(int interval)
        { 
            //清理超时的lock请求
            var currTime = TimeExt.currTime;
            var cacheKeys = this._unitLockCbMap.Keys.ToArray();
            foreach (var avatarId in cacheKeys)
            {
                var vo = this._unitLockCbMap[avatarId];
                if (currTime - vo.time > 2000)
                {
                    if (vo.failCallback != null)
                        vo.failCallback();
                    this._unitLockCbMap.Remove(avatarId);
                }
            }
        }

        public override void EnterScene()
        {
        }

        public override void ExitScene()
        {
            Clear();
        }
        
        public override void Clear()
        {
            this.UnLockAllUnit();
            _scene.ClearAllChars();
            _mainUnit = null;
        }

        private void SetHeadUrlData(string headUrl, long avatarId, string styleId = null)
        {
            if (string.IsNullOrEmpty(headUrl))
                return;
            var ctl = ControllerManager.instance.GetController(ModelConsts.HeadUrl) as HeadUrlController;
            if (string.IsNullOrEmpty((styleId)))
                ctl.GetModel<HeadUrlModel>(ModelConsts.HeadUrl).AddUrlData(headUrl, avatarId);
            else
                ctl.GetModel<HeadUrlModel>(ModelConsts.HeadUrl).AddUrlData(styleId, headUrl, avatarId);
        }

        public string GetStyleNameByAvatarId(long avatarID)
        {
            AvatarsCfg cfg = Cfg.T.TBAvatars.GetOrDefault(avatarID.ToString());
            if (cfg == null)
            {
                Debug.LogError("传入的角色id加载失败,因此未能加载角色!  avatarID:" + avatarID.ToString());
                return this.styles.FirstOrDefault().Value;
            }

            bool isValid = this.styles.TryGetValue(cfg.styleId, out var returnVal);
            if (!isValid)
            {
                Debug.LogError("传入的角色id加载失败,因此未能加载角色!");
                return this.styles.FirstOrDefault().Value;
            }
            else
            {
                return returnVal;
            }

            return this.styles[cfg.styleId];
        }
        public Dictionary<int, string> Styles => styles;

        //10007~10020是为了兼容老用户做的额外映射
        private readonly Dictionary<int, string> styles = new Dictionary<int, string>
        {
             { 10001, "User_Boy_00001" },
             { 10002, "User_Girl_00001" },
             { 10003, "User_Boy_00002" },
             { 10004, "User_Girl_00002" },
             { 10005, "User_Boy_00003" },
             { 10006, "User_Girl_00003" },

             { 10007, "User_Boy_00001" },
             { 10008, "User_Girl_00001" },
             { 10009, "User_Boy_00002" },
             { 10010, "User_Girl_00002" },
             { 10011, "User_Boy_00003" },
             { 10012, "User_Girl_00003" },
             { 10013, "User_Boy_00001" },
             { 10014, "User_Girl_00001" },
             { 10015, "User_Boy_00002" },
             { 10016, "User_Girl_00002" },

             { 10017, "User_Boy_00003" },
             { 10018, "User_Girl_00003" },
             { 10019, "User_Boy_00001" },
             { 10020, "User_Girl_00001" },

             { 10021, "NPC_Boy_00001" },
             { 10022, "NPC_Boy_00002" },
             { 10023, "NPC_Boy_00003" },
             { 10024, "NPC_Boy_00004" },
             { 10025, "NPC_Boy_00005" },
             { 10026, "NPC_Boy_00006" },
             { 10027, "NPC_Boy_00007" },
             { 10028, "NPC_Boy_00008" },
             { 10029, "NPC_Boy_00009" },
             { 10030, "NPC_Boy_00012" },
             { 10031, "NPC_Boy_00013" },
             { 10032, "NPC_Boy_00014" },
             { 10033, "NPC_Boy_00015" },
             { 10034, "NPC_Boy_00016" },
             { 10035, "NPC_Boy_00017" },
             { 10036, "NPC_Boy_00018" },
             { 10037, "NPC_Boy_00019" },
             { 10038, "NPC_Boy_00020" },
             { 10039, "NPC_Boy_00021" },
             { 10040, "NPC_Boy_00022" },
             { 10041, "NPC_Boy_00023" },
             { 10042, "NPC_Boy_00024" },
             { 10043, "NPC_Boy_00025" },
             { 10044, "NPC_Boy_00026" },
             { 10045, "NPC_Boy_00027" },
             { 10046, "NPC_Boy_00028" },
             { 10047, "NPC_Girl_00001" },
             { 10048, "NPC_Girl_00002" },
             { 10049, "NPC_Girl_00003" },
             { 10050, "NPC_Girl_00004" },
             { 10051, "NPC_Girl_00005" },
             { 10052, "NPC_Girl_00006" },
             { 10053, "NPC_Girl_00007" },
             { 10054, "NPC_Girl_00008" },
             { 10055, "NPC_Girl_00009" },
             { 10056, "NPC_Girl_00010" },
             { 10057, "NPC_Girl_00011" },
             { 10058, "NPC_Girl_00012" },
             { 10059, "NPC_Girl_00013" },
             { 10060, "NPC_Girl_00014" },
             { 10061, "NPC_Girl_00015" },
             { 10062, "NPC_Girl_00016" },
             { 10063, "NPC_Girl_00017" },
             { 10064, "NPC_Girl_00018" },
             { 10065, "NPC_Girl_00019" },
             { 10066, "NPC_Girl_00020" },
             { 10067, "NPC_Girl_00021" },
             { 10068, "NPC_Girl_00022" },
             { 10069, "NPC_Girl_00023" },
             { 10070, "NPC_Girl_00024" },
             { 10071, "NPC_Girl_00025" },
             { 10072, "NPC_Girl_00026" },
             { 10073, "NPC_Girl_00027" },
             { 10080, "NPC_Boy_00010" },
             { 10081, "NPC_Boy_00011" },
             { 20001, "laotou" } ,//因为不是捏脸编辑器的任务给了个2开头
             { 20002, "Louis" } ,
             { 20003, "Emily" } ,
             { 2147483647, "NPC_Girl_00028" } 
        };
    }
}


