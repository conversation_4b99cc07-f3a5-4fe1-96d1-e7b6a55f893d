/*
****************************************************
# 作者：Huangshiwen
# 创建时间：   2024/05/16 17:46:51 星期四
# 功能：Nothing
****************************************************
*/

using CommonUI;
using Firebase;
using Firebase.Analytics;
using Game;
using Modules.DataDot;
using Msg.economic;
using ScriptsHot.Game.UGUI.WebView;
using UnityEngine;
using UnityEngine.Purchasing;

namespace ScriptsHot.Game.Modules.Shop
{
    public class ShopUIBase<T> : BaseUI<T> where T : UIBindT
    {
        protected ShopUIBase(string name) : base(name)
        {
        }

        protected long users_diamond_amount;
        protected long users_temp_diamond_amount;
        protected string cur_subscribe_status;
        protected string cur_member_type;
        protected string product_id;
        protected string product_type;
        
        protected void OnBuySuccess()
        {
            GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
            GetController<ShopController>(ModelConsts.Shop).ReqShopInfo();
            if (product_type.Equals(PB_ProductType.PB_ProductType_Subscribe.ToString()))
            {
                GetController<HomepageController>(ModelConsts.Homepage).ReqGetIncentiveData();
            }
            // 新埋点：用户购买成功
            DataDotPurchaseSucceed dot = new DataDotPurchaseSucceed();
            dot.product_id = product_id;
        
            DataDotMgr.Collect(dot);
            AFDots.Cut_Purchase_succeed();
            // var a = FirebaseAnalytics.GetAnalyticsInstanceIdAsync();
            MyFirebaseAnalytics.LogEvent("trial_start", new Parameter[]
            {
                new Parameter("users_diamond_amount", users_diamond_amount),
                new Parameter("users_temp_diamond_amount", users_temp_diamond_amount),
                new Parameter("cur_subscribe_status", cur_subscribe_status),
                new Parameter("cur_member_type", cur_member_type),
                new Parameter("subscribe_status", cur_subscribe_status),
                new Parameter("member_type", cur_member_type),
                new Parameter("product_id", product_id),
                new Parameter("purchase_source",  PayWallDotHelper.LastSourcePage.ToString()),//实际是source page 之尧要求暂不调整firebase的属性名 20250714 1446
            });
        }

        protected void OnBuyFail(string msg, bool showError)
        {
            if (showError)
            {
                GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToast(msg, true);
            }
            GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
            // 新埋点：用户购买成功
            DataDotPurchaseFailed dot = new DataDotPurchaseFailed();
            dot.product_id = product_id;
            
            DataDotMgr.Collect(dot);
        }

        protected void OnRestorePurchaseClicked()
        {
            GetUI<CommBusyUI>(UIConsts.CommBusy).LazyShow();
            PurchasingManager.instance.Restore(OnRestore);
        }
        
        private void OnRestore(bool success, string msg)
        {
            GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
            if (!success)
            {
                GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToast(msg, true);
            }
        }

        protected void OnServiceDetailClicked()
        {
#if UNITY_EDITOR
            OpenUrl(LoginConst.usertermsUrl);
#else
            OpenUrl(LoginConst.usertermsUrl);
            //DeviceAdapter.OpenWebPage(LoginConst.usertermsUrl);
#endif
        }

        protected void OnPrivacyClicked()
        {
#if UNITY_EDITOR
            OpenUrl(LoginConst.privacyUrl);
#else
            OpenUrl(LoginConst.privacyUrl);
            //DeviceAdapter.OpenWebPage(LoginConst.privacyUrl);
#endif
        }
        
        private void OpenUrl(string url)
        {
            MainModel mainModel = GetModel<MainModel>(ModelConsts.Main);

            // GameObject ctlPrefab = Resources.Load<GameObject>("Prefabs/WebViewCtl");
            GameObject ctlPrefab = GResManager.instance.LoadPrefab("WebViewCtl");     
            GameObject newCtl = GameObject.Instantiate(ctlPrefab);
            
            WebViewCtl ctl = newCtl.GetComponent<WebViewCtl>();
            if (ctl == null)
            {
                ctl = newCtl.AddComponent<WebViewCtl>();
            }
            ctl.Init(10f, I18N.inst.MotherLanguageStr, I18N.inst.ForeignLanguageStr, mainModel.toKen, I18N.inst.TempUILanguageStr,
                true,
                true,
                () =>
                { 
                    GetController<CurrencyController>(ModelConsts.CurrencyController).SendGetEconomicInfoReqAsync(GameEventName.GameEnter, () =>
                    {
                        SendNotification(NotifyConsts.MainHeadRefreshEvent);
                    });
                },() =>
                {
                    GetUI<CommBusyUI>(UIConsts.CommBusy).LazyShow();
                },
                () =>
                {
                    GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
                }
            );
            ctl.LoadUrl(url);
        }
    }
}