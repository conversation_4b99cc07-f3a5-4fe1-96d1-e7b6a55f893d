/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class TapAnswer : AFragAnswer
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "TapAnswer";
        public static string url => "ui://cmoz5osjz7rm2u";

        public GList lineList;
        public GList finishList;
        public GList selectList;
        public GGraph fromPoint;
        public GGraph toPoint;
        public ClozeOptionBtn worker;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(TapAnswer));
        }

        public override void ConstructFromXML(XML xml)
        {
            lineList = GetChildAt(4) as GList;
            finishList = GetChildAt(5) as GList;
            selectList = GetChildAt(6) as GList;
            fromPoint = GetChildAt(7) as GGraph;
            toPoint = GetChildAt(8) as GGraph;
            worker = GetChildAt(9) as ClozeOptionBtn;
        }
        public override void Dispose()
        {
            lineList = null;
            finishList = null;
            selectList = null;
            fromPoint = null;
            toPoint = null;
            worker = null;

            base.Dispose();
        }
    }
}