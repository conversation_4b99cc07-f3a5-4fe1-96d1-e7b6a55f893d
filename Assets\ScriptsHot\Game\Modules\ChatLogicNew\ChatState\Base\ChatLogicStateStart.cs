﻿using Msg.basic;

namespace ScriptsHot.Game.Modules.ChatLogicNew.ChatState
{
    public class ChatLogicStateStart : ChatLogicStateBase
    {
        private SceneStateChatParam _param;
        // private MainHeaderUI _mainHeaderUI;
        public ChatLogicStateStart(ChatTypeBase chat) : base(ChatStateName.Start,chat)
        {
        }

        public override void OnEnter(params object[] args)
        {
            base.OnEnter(args);
            _param = (SceneStateChatParam)args[0];
            OnUIModelSelect();
            // _mainHeaderUI = UIManager.instance.GetUI<MainHeaderUI>(UIConsts.MainHeader);
            switch (_param.chatMode)
            {
                case PB_DialogMode.Career:
                    // _mainHeaderUI.SetVisible(true, false);
                    _curChat.ChangeState(ChatStateName.TopicPage, _param);
                    break;
                case PB_DialogMode.WorldStory:
                    // _mainHeaderUI.SetVisible(true, false);
                    _curChat.ChangeState(ChatStateName.Story, _param);
                    break;
                case PB_DialogMode.RolePlay:
                    // _mainHeaderUI.SetVisible(true, false);
                    _curChat.ChangeState(ChatStateName.HomePage, _param);
                    break;
                case PB_DialogMode.Tutor:
                    // _mainHeaderUI.SetVisible(true, false);
                    _curChat.ChangeState(ChatStateName.HomePage, _param);
                    break;
                case PB_DialogMode.Flash:
                    _curChat.ChangeState(ChatStateName.AUA, _param);
                    break;
                case PB_DialogMode.Video:
                    _curChat.ChangeState(ChatStateName.VideoTask, _param);
                    break;
            }

            Notifier.instance.SendNotification(NotifyConsts.RefreshAutoHelpEvent);//刷新Auto状态
        }
        
        private void OnUIModelSelect()
        {
            Notifier.instance.SendNotification(NotifyConsts.OpenUI,new UIManager.UIParams(){viewName = UIConsts.RecordUI});
            if (_param.chatMode == PB_DialogMode.Flash) return;
            // if (_param.chatMode == PB_DialogMode.Exercise ||_param.chatMode == PB_DialogMode.Career ||_param.chatMode == PB_DialogMode.RolePlay ||_param.chatMode == PB_DialogMode.Tutor)
            // {
            //     Notifier.instance.SendNotification(NotifyConsts.UpdateHeaderMode,MainHeaderMode.ExerciseTalk);
            // }
            // else
            // {
            //     Notifier.instance.SendNotification(NotifyConsts.UpdateHeaderMode,MainHeaderMode.Talk);
            // }
        }

        public override void OnReEnter(params object[] args)
        {
            base.OnReEnter(args);
        }

        public override void OnExit()
        {
            base.OnExit();
        }
    }
}