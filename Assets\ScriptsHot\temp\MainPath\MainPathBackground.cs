using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AnimationSystem;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using SRF;
using UIBind.MainPath3D;
using UnityEngine;
using UnityEngine.Experimental.Rendering;
using UnityEngine.Rendering.Universal;
using ZTemp;
using Object = UnityEngine.Object;


namespace UIBind.Main
{
    public partial class MainPathBackGround : ExtendedComponent
    {
        #region 静态变量
        //声明挂点名
        private static readonly string cameraS = "CameraStart";
        private static readonly string cameraD = "CameraEnd";
        private static readonly string fxS = "SpecialFXStart";
        private static readonly string fxD = "Destination";
        private static readonly string furRoot = "LevelProps";

        //声明动作名
        private static readonly string greeting = "Path3DGreeting";

        //声明后备角色层级
        private static readonly int avatarLayer = LayerMask.NameToLayer("3DAvatar");

        //声明特效层
        private static readonly string fxLightUp = "EnlightenmentFX";
        private static readonly string fxLightUp2 = "EnlightenmentFX2";

        //声明特殊数据名
        private static readonly string targetFurNum= "targetFurNum";
        private static readonly string lightedUp = "lighted";
        private const float LIGHT_UP_DURATION = 1f;
        private const float LIGHT_UP_DURATION_FX = 1.3f;
        
        #endregion
        
        //基础场景及切换行为
        private BackgroundSceneRoot backgroundSceneRoot;
        private BackgroundSceneRoot zombieSceneRoot;
        private RenderTexture zombieTexture;
        private float lastSceneChangeTime = -1000.0f;
        private static readonly float MIN_SCENE_CHANGE_TIME = 2.0f;
        private Action OnUpdateDelegate;
        private static int SCREEN_WIDTH = 1;
        private static int SCREEN_HEIGHT = 1;
        
        //功能:摄像机移动
        private Tweener cameraTweener; // 用于控制摄像机动画的Tweener

        //处理后台行为
        private bool isActive = true; //自身是不是开启状态。如果自身不在前台那么就是false,此时不会修改场景环境光等等。
        private Color stashedAmbientColor = new Color(0, 0, 0, 0); //自身不在前台时,会把收到的环境光修改请求暂存在这里。之后切换到前台会拿出来。
        private ChangeSceneParams stashedParams; //因为场景频繁切换暂存的情况。
        private bool isInChangeSceneProgress; //场景正后台异步加载着呢,不要乱动东西
        private ChangeSceneParams progressParams; //正在发生场景切换时的参数
        
        //处理点亮行为
        private LightUpParams lastLightUpParams;
        private bool isLightingUp;

        // public MainPathBackGround()
        // {
        //     onAddedToStage.Add(OnAddedToStage);
        //     onRemovedFromStage.Add(OnRemovedFromStage);
        // }

        protected override void OnAddedToStage()
        {
            // ChangeSceneParams demo = new ChangeSceneParams();
            // demo.isLightForever = false;
            // demo.furNum = 0;
            // demo.sceneId = "S0U0";
            // demo.isLightToday = false;

            //ChangeScene(demo);

           // TestEvent().Forget();

            Notifier.instance.RegisterNotification(NotifyConsts.MainPath3DSceneChange, OnReceiveChangeScene);
            Notifier.instance.RegisterNotification(NotifyConsts.MainPath3DSceneLightUp, OnReceiveLightUp);
            Notifier.instance.RegisterNotification(NotifyConsts.MainPath3DLeavePath, OnReceiveLeaveMainPath);
            Notifier.instance.RegisterNotification(NotifyConsts.MainPath3DBackPath, OnReceiveEnterMainPath);
            Notifier.instance.RegisterNotification(NotifyConsts.MainPath3DCompleteLevel, OnReceiveCompleteLevel);
            Notifier.instance.RegisterNotification(NotifyConsts.MainPath3DNodeComplete, OnReceiveLightUp);

            SCREEN_WIDTH = (int)UIManager.instance.width;
            SCREEN_HEIGHT = (int)UIManager.instance.height;
        }

        protected override void OnRemovedFromStage()
        {
            // 清理DOTween动画
            cameraTweener?.Kill();
            cameraTweener = null;
        }

        #region API

        private void OnReceiveChangeScene(string s, object o)
        {
            ChangeSceneParams p = (ChangeSceneParams)o;
            if (!string.IsNullOrEmpty(p.sceneId))
            {
                VFDebug.LogError($"收到了{p.sceneId}的切换事件");
                StashChangeScene(p);
            }
            else
            {
                VFDebug.LogError($"收到了{p.sceneId}的切换事件");
                ChangeSceneParams demo = new ChangeSceneParams();
                demo.isLightForever = true;
                demo.furNum = 0;
                demo.sceneId = "S0U0";
                demo.isLightToday = false;

                StashChangeScene(demo);
            }
        }

        private void OnReceiveLightUp(string s, object o)
        {
            LightUpParams p = (LightUpParams)o;
            if (!string.IsNullOrEmpty(p.sceneId))
            {
                //如果目标场景就是当前场景。那么直接开始点灯.
                if (p.sceneId == this.backgroundSceneRoot.LoadedSceneName)
                {
                    LightUpScene(p);
                }
                //如果目标场景不是当前场景。那么要切换到目标场景再开始点灯。婧晗说会先调用切换场景事件，所以当前应该至少stash的是这个事件。
                else
                {
                    //如果现在正在切换场景,并且目标场景是点灯场景，那就等一小会儿再触发。
                    if (this.isInChangeSceneProgress && this.progressParams.sceneId == p.sceneId)
                    {
                        this.lastLightUpParams = p;
                        OnUpdateDelegate += LightUp_WaitUntilSceneIsLoaded;
                    }
                    //如果现在不在切换场景，但是已经收到了切换场景事件，只不过是在CD中，那就消除CD立刻触发。
                    else if (this.stashedParams.sceneId == p.sceneId)
                    {
                        this.lastLightUpParams = p;
                        OnUpdateDelegate += LightUp_WaitUntilSceneIsLoaded;
                    }
                    //如果不是以上情况，那么就说明有bug。需要报错。
                    else
                    {
                        bool isCD = String.IsNullOrEmpty(this.stashedParams.sceneId);
                        string error = $"<color=red>触发了点亮事件，但是点亮事件不正确，以下是详细信息：\n " +
                                       $"1.当前是否处于切换场景的过程中:{this.isInChangeSceneProgress} 切换目标是:{this.progressParams.sceneId} \n " +
                                       $"2.当前是否有CD中的目标场景:{isCD} 切换目标是:{this.stashedParams.sceneId}";

                        if (!this.isInChangeSceneProgress && !isCD)
                        {
                            error += $"3.当前未收到场景切换命令且当前场景不是点亮目标场景,警报。当前场景是{this.backgroundSceneRoot?.LoadedSceneName}";
                        }

                        VFDebug.LogError(error);
                        
                        //虽然报了错,但只要当前是不是null,就从当前场景开始点灯。
                        LightUpScene(p);
                    }
                }
            }
        }

        private void OnReceiveCompleteLevel(string _, object o)
        {
            CompleteLevel cp = (CompleteLevel)o;
            if (cp.sceneId == this.backgroundSceneRoot.LoadedSceneName)
            {
                this.backgroundSceneRoot.SetSceneData<int>(targetFurNum, cp.furNum);
            }
        }

        private void OnReceiveLeaveMainPath(string s, object o)
        {
            ChangeMainPathActive(false,null);
        }

        private void OnReceiveEnterMainPath(string s, object o)
        {
            ChangeMainPathActive(true,o);
        }

        private void OnReceiveLightDown(string _, object o)
        {
            this.LightDown();
        }

        #endregion

        #region 核心逻辑

        private void StashChangeScene(ChangeSceneParams changeSceneParams)
        {
            if (!string.IsNullOrEmpty(this.stashedParams.sceneId))
            {
                VFDebug.LogError("吃掉了" + this.stashedParams.sceneId + "的切换事件");
            }
            this.stashedParams = changeSceneParams;
        }
        
        /// <summary>
        /// 切换到一个指定场景。
        /// </summary>
        private bool ChangeScene(ChangeSceneParams changeSceneParams)
        {
            //目标一致无需切换场景。
            if (this.backgroundSceneRoot?.LoadedSceneName == changeSceneParams.sceneId)
            {
                // if (this.backgroundSceneRoot.GetSceneData<int>(furNum) != changeSceneParams.furNum)
                // {
                //     CompleteLevel level = new CompleteLevel();
                //     level.furNum = changeSceneParams.furNum;
                //     this.OnReceiveCompleteLevel("", level);
                //     return true;
                // }
                
                bool light = changeSceneParams.isLightForever || changeSceneParams.isLightToday;
                //只检测 暗不暗。亮的等点亮事件。
                if (!light)
                {
                    LightDown();
                }
            }
            else
            {
                //未处于CD.
                if (Time.time - lastSceneChangeTime >= MIN_SCENE_CHANGE_TIME)
                {
                    lastSceneChangeTime = Time.time;

                    if (this.backgroundSceneRoot != null)
                    {
                        zombieSceneRoot = this.backgroundSceneRoot;
                        SetZombieSceneRoot(zombieSceneRoot);
                    }

                    backgroundSceneRoot = new BackgroundSceneRoot();
                    SetSceneRoot(backgroundSceneRoot);

                    //注意：加载完了才有挂点，所以先加载。
                    GenerateRTAsync(changeSceneParams).Forget(); // 使用Forget()来忽略Task，调用者不需要处理异步

                    isInChangeSceneProgress = true;
                    progressParams = changeSceneParams;
                    return true;
                }
                //处于CD,将本次切换stash. 不会stash多次切换. 
                else
                {
                    this.stashedParams = changeSceneParams;
                    return false;
                }
            }

            return false;
        }

        protected override void OnUpdate()
        {
            base.OnUpdate();
            
            // 处理切换场景
            if (!string.IsNullOrEmpty(this.stashedParams.sceneId) &&
                Time.time - lastSceneChangeTime >= MIN_SCENE_CHANGE_TIME)
            {
                ChangeScene(this.stashedParams);
                this.stashedParams = default(ChangeSceneParams);
            }

            // 更新贝塞尔动画
            UpdateBezierAnimations();
            
            OnUpdateDelegate?.Invoke();
        }

        private async UniTaskVoid GenerateRTAsync(ChangeSceneParams changeSceneParams)
        {
            try
            {
                var renderTexture = await this.backgroundSceneRoot
                    .GenerateRT(changeSceneParams.sceneId, changeSceneParams.avatarId,SCREEN_WIDTH,SCREEN_HEIGHT)
                    .AsUniTask();
                OnGenerateRTCompleted(renderTexture, changeSceneParams);
                isInChangeSceneProgress = false;
                progressParams = default(ChangeSceneParams);
            }
            catch (System.Exception ex)
            {
                VFDebug.LogError(ex.ToString());
                OnGenerateRTFailed();
                isInChangeSceneProgress = false;
                progressParams = default(ChangeSceneParams);
            }
        }

        // ReSharper disable Unity.PerformanceAnalysis
        private void OnGenerateRTCompleted(UnityEngine.RenderTexture renderTexture, ChangeSceneParams changeSceneParams,
            Action<bool> OnComplete = null)
        {
            if (!renderTexture)
            {
                OnGenerateRTFailed();
                return;
            }

            //设置相机
            this.backgroundSceneRoot.SetCameraRenderPipeline((int)RendererData.ZombieScene3D);

            if (this.backgroundSceneRoot.MainCamera && this.backgroundSceneRoot.MainCamera.transform.childCount > 0)
            {
                var go = this.backgroundSceneRoot.MainCamera.transform.GetChild(0);
                var com = go.GetComponent<SpriteRenderer>();
                if (com)
                {
                    //[8.19.2025临时,计算2D情况下资源的Z位置,之后资源补齐后不再使用。]
                    Vector2 sshw = new Vector2(SCREEN_WIDTH, SCREEN_HEIGHT);
                    Vector2 sphw = new Vector2(750,1624);
                    float sshwp = sshw.x / sshw.y;
                    float sphwp = sphw.x / sphw.y;
                    float defaultValue = 28.2f;
                    go.transform.localPosition = new Vector3(0,0,(defaultValue*sphwp) / sshwp);
                    go.transform.localRotation = Quaternion.identity;
                    go.transform.localScale = Vector3.one;
                }
            }

            PlayCameraEnterAnimation();

            //加载初始环境光照
            if (backgroundSceneRoot.Lights)
            {
                var lightUpData = backgroundSceneRoot.Lights.GetComponent<SceneLightupData>();
                if (lightUpData)
                {
                    if (changeSceneParams.isLightForever || changeSceneParams.isLightToday)
                    {
                        //防止在后台时触发切换场景操作导致环境光不正常。
                        if (this.isActive)
                        {
                            SceneRenderingManager.instance.ambientMode = UnityEngine.Rendering.AmbientMode.Flat;
                            SceneRenderingManager.instance.ambientLight = lightUpData.ambientLight.brightColor;
                        }
                        else
                        {
                            this.stashedAmbientColor = lightUpData.ambientLight.brightColor;
                        }

                        foreach (var lightData in lightUpData.lightDatas)
                        {
                            var go = lightData.go;
                            if (!go) continue;
                            var light = go.GetComponent<Light>();
                            if (light)
                            {
                                light.color = lightData.brightColor;
                            }
                        }
                    }
                    else
                    {
                        if (this.isActive)
                        {
                            SceneRenderingManager.instance.ambientMode = UnityEngine.Rendering.AmbientMode.Flat;
                            SceneRenderingManager.instance.ambientLight = lightUpData.ambientLight.darkColor;
                        }
                        else
                        {
                            this.stashedAmbientColor = lightUpData.ambientLight.darkColor;
                        }

                        foreach (var lightData in lightUpData.lightDatas)
                        {
                            var go = lightData.go;
                            if (!go) continue;
                            var light = go.GetComponent<Light>();
                            if (light)
                            {
                                light.color = lightData.darkColor;
                            }
                        }
                    }
                }
            }

            //切换角色层级&验证是否解锁
            if (this.backgroundSceneRoot.Character1)
            {
                this.backgroundSceneRoot.Character1.SetLayerRecursive(avatarLayer);
                //this.backgroundSceneRoot.Character1.SetActive(changeSceneParams.isUnlocked);
            }

            //切换场景站起(根据角色挂状态机即可,但是不能是DialogManager的状态机,可参考StarX5PlayAnimationState) 
            //如果是在前台,那么播放站起动画;如果是在后台,那么不播放站起动画。
            if (this.backgroundSceneRoot.Manager)
            {
                if (this.isActive)
                {
                    this.backgroundSceneRoot.PlayAnimationByExcel(greeting,
                        (_) => { this.backgroundSceneRoot.Manager.SetState(new DialogIdleState()); });
                }
                else
                {
                    this.backgroundSceneRoot.Manager.SetState(new DialogIdleState());
                }
            }
            else
            {
                VFDebug.LogError("加载的场景角色有问题,没有AnimationManager");
            }

            //加载家具
            var env = this.backgroundSceneRoot.Environment.transform;
            if (env)
            {
                for (int i = 0; i < env.childCount; i++)
                {
                    var child = env.GetChild(i);
                    if (child.name == furRoot)
                    {
                        this.backgroundSceneRoot.SetSceneData<GameObject>(furRoot, child.gameObject);
                    }
                }
            }

            this.backgroundSceneRoot.SetSceneData<int>(targetFurNum,changeSceneParams.furNum);
            UpdateFurniture(false);

            //配置混合(只在active时进行)
            if (this.zombieTexture && this.isActive)
            {
                if (this.backgroundSceneRoot.MainCamera)
                {
                    var additionalCameraData = this.backgroundSceneRoot.MainCamera.GetUniversalAdditionalCameraData();
                    if (additionalCameraData)
                    {
                        var renderingFeatures = additionalCameraData.scriptableRenderer.rendererFeatures;

                        BlendBlitRenderFeature existingFeature = null;

                        // 查找已存在的BlendBlurRenderFeature
                        for (int i = 0; i < renderingFeatures.Count; i++)
                        {
                            if (renderingFeatures[i] is BlendBlitRenderFeature)
                            {
                                existingFeature = renderingFeatures[i] as BlendBlitRenderFeature;
                                break;
                            }
                        }

                        if (existingFeature == null)
                        {
                            existingFeature = ScriptableObject.CreateInstance<BlendBlitRenderFeature>();
                            renderingFeatures.Add(existingFeature);
                        }
                        //
                        // RTHandle handle = RTHandles.Alloc(750, 1624, depthBufferBits: 0,
                        //     colorFormat: GraphicsFormat.R8G8B8A8_SRGB,name:"SSS");

                        RenderTexture rt = new RenderTexture(SCREEN_WIDTH, SCREEN_HEIGHT, GraphicsFormat.R8G8B8A8_SRGB, 0)
                        {
                            name = $"RTBlendingTexture{this.backgroundSceneRoot.LoadedSceneName}"
                        };
                        this.MainPathLoader!.texture = new FairyGUI.NTexture(rt);
                        existingFeature.StartBlendTransition(1.0f, 0.0f, 0.4f, rt, this.zombieTexture, () =>
                        {
                            this.MainPathLoader.texture =
                                new FairyGUI.NTexture(renderTexture);
                            this.zombieSceneRoot.ClearScene();
                            this.zombieTexture?.Release();
                            this.zombieTexture = null;
                            DOVirtual.DelayedCall(1.0f, () =>
                            {
                                rt?.Release();
                                rt = null;
                            });
                        });
                    }
                    else
                    {
                        VFDebug.LogError("MainPathBackground: 无法获取UniversalAdditionalCameraData");
                        return;
                    }
                }
                else
                {
                    VFDebug.LogError("指定场景无约定的MainCamera");
                    return;
                }
            }
            else
            {
                if (this.zombieTexture)
                {
                    this.zombieSceneRoot.ClearScene();
                    this.zombieTexture = null;
                }

                if (MainPathLoader != null)
                {
                    MainPathLoader.texture = new FairyGUI.NTexture(renderTexture);
                    if (OnComplete != null) OnComplete(true);
                }
            }

            //基于当前开闭状态切换渲染
            if (this.isActive)
            {
                this.backgroundSceneRoot.EnableRendering();
            }
            else
            {
                this.backgroundSceneRoot.CloseRendering();
            }

            //向场景写入数据（家具数据已经在其他地方写入了）
            this.backgroundSceneRoot.SetSceneData<bool>("lighted",
                changeSceneParams.isLightToday || changeSceneParams.isLightForever);
        }

        private void OnGenerateRTFailed(Action<bool> OnComplete = null)
        {
            if (OnComplete != null) OnComplete(false);
            
            //兜底逻辑：立刻触发S0U0加载。
            this.lastSceneChangeTime = -2f;
            ChangeSceneParams demo = new ChangeSceneParams();
            demo.isLightForever = true;
            demo.furNum = 0;
            demo.sceneId = "S0U0";
            demo.isLightToday = false;
            
            //立刻清理之前的僵尸场景,防止场景增生。
            this.zombieTexture = null;
            this.zombieSceneRoot?.ClearScene();

            StashChangeScene(demo);
        }

        private void SetZombieSceneRoot(BackgroundSceneRoot root)
        {
            if (root != null)
            {
                this.zombieSceneRoot = root;
                this.zombieTexture = zombieSceneRoot.RenderTexture;
                if(this.zombieSceneRoot.Lights) this.zombieSceneRoot.Lights.SetActive(false);
                if(this.zombieSceneRoot.MainCamera) this.zombieSceneRoot.MainCamera.enabled = false;
                //this.zombieSceneRoot.DisposeScene();
                //   this.zombieSceneRoot.SetCameraRenderPipeline((int)RendererData.ZombieScene3D);
            }
        }

        private void SetSceneRoot(BackgroundSceneRoot root)
        {
            if (root != null)
            {
                this.backgroundSceneRoot = root;
            }
        }

        private void UpdateFurniture(bool willFX = false)
        {
            var count = Math.Min(188, this.backgroundSceneRoot.GetSceneData<int>(targetFurNum));

            if (this.backgroundSceneRoot != null)
            {
                GameObject furnitureRoot = this.backgroundSceneRoot.GetSceneData<GameObject>(furRoot);
                if (furnitureRoot != null)
                {
                    int furnitureCount = furnitureRoot.transform.childCount;
                    for (int i = 0; i < furnitureCount; i++)
                    {
                        var go = furnitureRoot.transform.GetChild(i);
                        if (int.TryParse(go.gameObject.name, out int furnitureId))
                        {
                            bool wasInActive = !go.gameObject.activeInHierarchy;
                            go.gameObject.SetActive(furnitureId <= count);
                            var fx = go.transform.Find("FX");
                            fx?.gameObject.SetActive(willFX && wasInActive);
                        }
                        else
                        {
                            go.gameObject.SetActive(false);
                        }
                    }
                }
            }
        }

        

        private void ChangeMainPathActive(bool active,object o)
        {
            //开启->关闭时:环境光调整为Room
            if (!active)
            {
                this.isActive = false;
                SceneRenderingManager.instance.SetToRoomScene();
                this.backgroundSceneRoot?.CloseRendering();
            }
            //关闭->开启时:环境光调整为本节点环境光、检查场景点亮状态、触发摄像机位移。
            else
            {
                this.isActive = true;
                if (this.stashedAmbientColor.a != 0)
                    SceneRenderingManager.instance.ambientLight = this.stashedAmbientColor;
                SceneRenderingManager.instance.ambientMode = UnityEngine.Rendering.AmbientMode.Flat;
                this.backgroundSceneRoot?.EnableRendering();

                if (o is ChangeSceneParams)
                {
                    var param = (ChangeSceneParams)o;
                    StashChangeScene(param);
                }
                
                PlayCameraEnterAnimation();
            }
        }

        #endregion
        
        #region CameraAnim
        
        private void PlayCameraEnterAnimation()
        {
            //实施相机运动
            var s = backgroundSceneRoot?.GetLocationMountPoint(cameraS);
            var d = backgroundSceneRoot?.GetLocationMountPoint(cameraD);

            if (s && d && this.backgroundSceneRoot.MainCamera)
            {
                StartCameraMovement(s.transform.position, d.transform.position, 0.5f);
            }
        }
        
        private void StartCameraMovement(Vector3 startPos, Vector3 endPos, float duration = 0.5f)
        {
            var camera = backgroundSceneRoot.MainCamera;
            if (!camera)
            {
                VFDebug.LogError("MainPathBackground: 摄像机不存在，无法执行位移动画");
                return;
            }

            // 停止之前的动画
            cameraTweener?.Kill();

            // 设置起始位置
            camera.transform.position = startPos;

            // 开始DOTween位移动画
            cameraTweener = camera.transform.DOMove(endPos, duration)
                .SetEase(Ease.OutCubic) // 使用缓动效果，可以根据需要调整
                //.OnComplete(OnCameraMovementCompleted)
                .OnKill(() =>
                {
                    camera.transform.position = endPos;
                    cameraTweener = null;
                });
        }
        
        #endregion

        #region LightUp

        // 当前正在执行的贝塞尔动画列表
        private List<BezierAnimationData> activeBezierAnimations = new List<BezierAnimationData>();

        private async void LightUpScene(LightUpParams param)
        {
            //0.翻转
            param.lightScale.y = 1 - param.lightScale.y;
            
            //1.首先加载特效
            GameObject go;
            if (GAvatarResManager.instance.CheckPath(fxLightUp))
            {
                go = await GAvatarResManager.instance.LoadAsset<GameObject>(fxLightUp);
            }
            else
            {
                VFDebug.LogError("点亮特效无法播放:无fx资源。");
                return;
            }

            //2.获取起点和末点
            Vector3 dest = Vector3.zero;
            Vector3 start = Vector3.zero;
            if (this.backgroundSceneRoot != null)
            {
                Camera camera = backgroundSceneRoot.MainCamera;
                GameObject gameObject = backgroundSceneRoot.GetLocationMountPoint(fxD);
                dest = (gameObject != null) ? gameObject.transform.position : Vector3.zero;

                if (camera != null)
                {
                    if (param.lightScale == Vector2.zero)
                    {
                        gameObject = backgroundSceneRoot.GetLocationMountPoint(fxS);
                        start = (gameObject != null) ? gameObject.transform.position : Vector3.zero;
                    }
                    else
                    {
                        Vector2 startPosSS = param.lightScale;
                        Ray cameraRay = GetCameraRayFromNormalizedPosition(camera, startPosSS);
                        float distance = Vector3.Distance(camera.transform.position, dest);
                        start = cameraRay.origin + cameraRay.direction * (distance * 0.4f);
                    }
                }
            }
            else
            {
                VFDebug.LogError("点亮特效无法播放:相机不正确/场景挂点未找到");
            }

            //3.执行飞行流程
            if (dest != Vector3.zero && start != Vector3.zero)
            {
                StartBezierAnimation(
                    go,
                    start,
                    dest,
                    LIGHT_UP_DURATION_FX,
                    param,
                    3.0f,
                    0.1f,
                    0.3f,
                    (p) =>
                    {
                        VFDebug.Log("特效飞行完成");
                        LightUpScene2(p,go);
                    }
                );
            }
        }

        /// <summary>
        /// 更新所有贝塞尔动画（在Update中调用）
        /// </summary>
        private void UpdateBezierAnimations()
        {
            for (int i = activeBezierAnimations.Count - 1; i >= 0; i--)
            {
                var anim = activeBezierAnimations[i];

                if (anim.targetObject == null)
                {
                    activeBezierAnimations.RemoveAt(i);
                    continue;
                }

                if (!anim.isActive)
                {
                    activeBezierAnimations.RemoveAt(i);
                    continue;
                }

                anim.elapsedTime += Time.deltaTime;

                Vector3 currentPosition = CalculateCubicBezierPoint(
                    anim.startPoint,
                    anim.controlPointA,
                    anim.controlPointB,
                    anim.endPoint,
                    anim.Progress);

                anim.targetObject.transform.position = currentPosition;

                if (anim.IsCompleted)
                {
                    anim.targetObject.transform.position = anim.endPoint;
                    anim.onComplete?.Invoke(anim.lightUpParams);
                    activeBezierAnimations.RemoveAt(i);
                }
            }
        }

        /// <summary>
        /// 点亮特效之后...
        /// </summary>
		private async Task LightUpScene2(LightUpParams param,GameObject fx)
        {
            Object.Destroy(fx,1f);

            void OnError()
            {
                this.backgroundSceneRoot.SetSceneData<bool>(lightedUp, true);
                this.backgroundSceneRoot.PlayAnimationByExcel("Path3DLightUp", (_) =>
                {
                    this.backgroundSceneRoot.Manager.SetState(new DialogIdleState());
                });
                param.callback?.Invoke();
            }
            
            GameObject go2;
            if (GAvatarResManager.instance.CheckPath(fxLightUp2))
            {
                go2 = await GAvatarResManager.instance.LoadAsset<GameObject>(fxLightUp2);
            }
            else
            {
                OnError();
                VFDebug.LogError("点亮特效无法播放:无fx资源。");
                return;
            }
            
            Vector3 dest = Vector3.zero;
            if (this.backgroundSceneRoot != null)
            {
                GameObject gameObject = backgroundSceneRoot.GetLocationMountPoint(fxD);
                dest = gameObject ? gameObject.transform.position : Vector3.zero;
            }
            else
            {
                VFDebug.LogError("点亮特效无法播放:相机不正确/场景挂点未找到");
            }

            if (go2)
            {
                go2.SetActive(true);
                go2.transform.position = dest;
            }
            
            UpdateFurniture(true);
            bool wasLightUp = this.backgroundSceneRoot.GetSceneData<bool>(lightedUp);
            if (wasLightUp)
            {
                OnError();
                return;
            }
            else if (backgroundSceneRoot.Lights)
            {
                var lightUpData = backgroundSceneRoot.Lights.GetComponent<SceneLightupData>();
                if (lightUpData)
                {
					SceneRenderingManager.instance.ambientMode = UnityEngine.Rendering.AmbientMode.Flat;
					float duration = LIGHT_UP_DURATION;
					
					// 构建并行缓动序列
					Sequence seq = DOTween.Sequence();
					// 环境光从 dark -> bright
					Color ambientStart = lightUpData.ambientLight.darkColor;
					Color ambientEnd = lightUpData.ambientLight.brightColor;
					// 确保起点一致
					SceneRenderingManager.instance.ambientLight = ambientStart;
					seq.Join(DOTween.To(
						() => SceneRenderingManager.instance.ambientLight,
						c => SceneRenderingManager.instance.ambientLight = c,
						ambientEnd,
						duration
					).SetEase(Ease.InOutSine));
					
					// 每个灯光从 dark -> bright
					foreach (var lightData in lightUpData.lightDatas)
					{
						var go = lightData.go;
						if (!go) continue;
						var light = go.GetComponent<Light>();
						if (light)
						{
							light.color = lightData.darkColor;
							seq.Join(light.DOColor(lightData.brightColor, duration).SetEase(Ease.InOutSine));
						}
					}
					
					seq.OnComplete(() =>
					{
						// 标记已点亮并回调
						this.backgroundSceneRoot.SetSceneData<bool>(lightedUp, true);
                        this.backgroundSceneRoot.PlayAnimationByExcel("Path3DLightUp", (_) =>
                        {
                            this.backgroundSceneRoot.Manager.SetState(new DialogIdleState());
                        });
						param.callback?.Invoke();
					});
					seq.Play();
                }
            }
        }

        private void LightUp_WaitUntilSceneIsLoaded()
        {
            this.OnUpdateDelegate -= LightUp_WaitUntilSceneIsLoaded;
            //若既没有排队中、也没有加载中
            if (string.IsNullOrEmpty(this.stashedParams.sceneId) && string.IsNullOrEmpty(progressParams.sceneId))
            {
                LightUpScene(this.lastLightUpParams);
            }
        }

        //立即切换到暗。(24点)
        private void LightDown()
        {
            if (backgroundSceneRoot?.Lights && this.isActive)
            {
                var lightUpData = backgroundSceneRoot.Lights.GetComponent<SceneLightupData>();
                if (lightUpData)
                {
                    SceneRenderingManager.instance.ambientMode = UnityEngine.Rendering.AmbientMode.Flat;
                    SceneRenderingManager.instance.ambientLight = lightUpData.ambientLight.brightColor;
                    
                    foreach (var lightData in lightUpData.lightDatas)
                    {
                        var go = lightData.go;
                        if (!go) continue;
                        var light = go.GetComponent<Light>();
                        if (light)
                        {
                            light.color = lightData.darkColor;
                        }
                    }
                }
                
                this.backgroundSceneRoot.SetSceneData<bool>(lightedUp,false);
            }
        }

        #endregion

        #region Math

        private Ray GetCameraRayFromNormalizedPosition(Camera camera, Vector2 ss)
        {
            if (camera == null)
            {
                VFDebug.LogError("Camera is null");
                return new Ray();
            }
            
            float screenX = ss.x * camera.pixelWidth;
            float screenY = ss.y * camera.pixelHeight;
            Vector3 screenPoint = new Vector3(screenX, screenY, 0);
            
            return camera.ScreenPointToRay(screenPoint);
        }

        /// <summary>
        /// 贝塞尔曲线动画数据
        /// </summary>
        private class BezierAnimationData
        {
            public GameObject targetObject;
            public Vector3 startPoint;
            public Vector3 controlPointA;
            public Vector3 controlPointB;
            public Vector3 endPoint;
            public float duration;
            public float elapsedTime;
            public Action<LightUpParams> onComplete;
            public LightUpParams lightUpParams;
            public bool isActive;

            public float Progress => Mathf.Clamp01(elapsedTime / duration);
            public bool IsCompleted => elapsedTime >= duration;
        }

        /// <summary>
        /// 开始贝塞尔曲线动画
        /// </summary>
        /// <param name="targetObject">要移动的游戏对象</param>
        /// <param name="startPoint">起始点</param>
        /// <param name="endPoint">终点</param>
        /// <param name="duration">动画持续时间（秒）</param>
        /// <param name="lightUpParams"></param>
        /// <param name="heightOffset">弧线高度偏移</param>
        /// <param name="controlAOffset">控制点A偏移比例</param>
        /// <param name="controlBOffset">控制点B偏移比例</param>
        /// <param name="onComplete">动画完成回调</param>
        public void StartBezierAnimation(
            GameObject targetObject,
            Vector3 startPoint,
            Vector3 endPoint,
            float duration,
            LightUpParams lightUpParams,
            float heightOffset = 5.0f,
            float controlAOffset = 0.1f,
            float controlBOffset = 0.3f,
            System.Action<LightUpParams> onComplete = null)
        {
            if (targetObject == null)
            {
                VFDebug.LogError("BezierAnimation: 目标对象为空");
                onComplete?.Invoke(lightUpParams);
                return;
            }
            
            Vector3 controlPointA, controlPointB;
            GenerateBezierControlPoints(startPoint, endPoint, out controlPointA, out controlPointB,
                heightOffset, controlAOffset, controlBOffset);
            
            BezierAnimationData animData = new BezierAnimationData
            {
                targetObject = targetObject,
                startPoint = startPoint,
                controlPointA = controlPointA,
                controlPointB = controlPointB,
                endPoint = endPoint,
                duration = duration,
                elapsedTime = 0f,
                onComplete = onComplete,
                isActive = true
            };
            
            targetObject.transform.position = startPoint;
            activeBezierAnimations.Add(animData);
        }

        /// <summary>
        /// 停止指定对象的贝塞尔动画
        /// </summary>
        /// <param name="targetObject">要停止动画的对象</param>
        /// <param name="snapToEnd">是否瞬间移动到终点</param>
        public void StopBezierAnimation(GameObject targetObject, bool snapToEnd = false)
        {
            for (int i = activeBezierAnimations.Count - 1; i >= 0; i--)
            {
                var anim = activeBezierAnimations[i];
                if (anim.targetObject == targetObject)
                {
                    if (snapToEnd && anim.targetObject != null)
                    {
                        anim.targetObject.transform.position = anim.endPoint;
                    }

                    anim.isActive = false;
                    activeBezierAnimations.RemoveAt(i);
                }
            }
        }

        private void GenerateBezierControlPoints(
            Vector3 start,
            Vector3 destination,
            out Vector3 controlPointA,
            out Vector3 controlPointB,
            float heightOffset = 5.0f,
            float controlAOffset = 0.1f,
            float controlBOffset = 0.3f)
        {
            Vector3 startXZ = new Vector3(start.x, 0, start.z);
            Vector3 destinationXZ = new Vector3(destination.x, 0, destination.z);
            Vector3 directionXZ = destinationXZ - startXZ;

            // 控制点A：基于destination
            // Y轴：destination的Y + 升高量
            // XZ轴：从destination向start方向偏移10%距离
            Vector3 controlAPositionXZ = destinationXZ - directionXZ * controlAOffset;
            controlPointA = new Vector3(
                controlAPositionXZ.x,
                destination.y + heightOffset,
                controlAPositionXZ.z
            );

            // 控制点B：基于start
            // Y轴：与控制点A相同高度
            // XZ轴：从start向destination方向偏移30%距离
            Vector3 controlBPositionXZ = startXZ + directionXZ * controlBOffset;
            controlPointB = new Vector3(
                controlBPositionXZ.x,
                controlPointA.y, // 与控制点A相同高度
                controlBPositionXZ.z
            );

            // ---- 罗德里戈旋转微调（-15° ~ +15°）——在现有控制点计算之后进行 ----
            float dirSqrMag = directionXZ.sqrMagnitude;
            if (dirSqrMag > 1e-6f)
            {
                Vector3 axisDir = new Vector3(directionXZ.x, 0f, directionXZ.z).normalized;
                
                float angleDeg = UnityEngine.Random.Range(-20f, 20f);
                float angleRad = angleDeg * Mathf.Deg2Rad;
                
                float tA = 1f - controlAOffset;
                Vector3 axisPointA = new Vector3(
                    start.x + directionXZ.x * tA,
                    Mathf.Lerp(start.y, destination.y, tA),
                    start.z + directionXZ.z * tA
                );

                float tB = controlBOffset;
                Vector3 axisPointB = new Vector3(
                    start.x + directionXZ.x * tB,
                    Mathf.Lerp(start.y, destination.y, tB),
                    start.z + directionXZ.z * tB
                );

                controlPointA = RotatePointAroundAxisRodrigues(controlPointA, axisPointA, axisDir, angleRad);
                controlPointB = RotatePointAroundAxisRodrigues(controlPointB, axisPointB, axisDir, angleRad);
            }
        }

        public Vector3 CalculateCubicBezierPoint(Vector3 startPoint, Vector3 controlPoint1, Vector3 controlPoint2,
            Vector3 endPoint, float t)
        {
            // 限制t在0-1范围内
            t = Mathf.Clamp01(t);

            float u = 1 - t;
            float tt = t * t;
            float uu = u * u;
            float uuu = uu * u;
            float ttt = tt * t;

            // 三次贝塞尔曲线公式：B(t) = (1-t)³P₀ + 3(1-t)²tP₁ + 3(1-t)t²P₂ + t³P₃
            Vector3 point = uuu * startPoint; // (1-t)³ * P₀
            point += 3 * uu * t * controlPoint1; // 3(1-t)²t * P₁
            point += 3 * u * tt * controlPoint2; // 3(1-t)t² * P₂
            point += ttt * endPoint; // t³ * P₃

            return point;
        }


        // Rodrigues' rotation formula: 将点 point 绕“经过 axisPoint 且方向为 axisDirNormalized（单位向量）”的直线旋转 angleRad（弧度）
        private static Vector3 RotatePointAroundAxisRodrigues(Vector3 point, Vector3 axisPoint,
            Vector3 axisDirNormalized, float angleRad)
        {
            float cosTheta = Mathf.Cos(angleRad);
            float sinTheta = Mathf.Sin(angleRad);

            Vector3 v = point - axisPoint;
            Vector3 k = axisDirNormalized; // 单位向量

            Vector3 vPrime = v * cosTheta + Vector3.Cross(k, v) * sinTheta + k * (Vector3.Dot(k, v) * (1f - cosTheta));
            return axisPoint + vPrime;
        }

        #endregion
        
        #region Debugger
        
        private async UniTaskVoid TestEvent()
        {
            try
            {
                await UniTask.Delay(7000);

                LightUpParams lightUpParams = new LightUpParams();
                lightUpParams.isLightForever = true;
                lightUpParams.lightScale = new Vector2(0.51f, 0.58f);
                lightUpParams.sceneId = this.backgroundSceneRoot.LoadedSceneName;


                // for (int i = 0; i < 100; i++)
                // {
                //     LightUpScene(lightUpParams);
                //     await UniTask.Delay(7000);
                // }

                //
                // ChangeSceneParams demo = new ChangeSceneParams();
                // demo.isLightForever = false;
                // demo.furNum = 0;
                // demo.sceneId = "S1U1";
                // demo.isLightToday = false;
                //
                // ChangeScene(demo);

                // await UniTask.Delay(4000);
                //
                // var demo = new ChangeSceneParams();
                // demo.isLightForever = false;
                // demo.furNum = 5;
                // demo.sceneId = "S2U1";
                // demo.isLightToday = false;
                //
                // ChangeScene(demo);
                //
                // await UniTask.Delay(4000);
                //
                // UpdateFurniture(188);
                //
                // await UniTask.Delay(4000);
                //
                // UpdateFurniture(0);

                // await UniTask.Delay(1200);
                //
                // demo = new ChangeSceneParams();
                // demo.isLightForever = false;
                // demo.furNum = 0;
                // demo.sceneId = "S1U1";
                // demo.isLightToday = false;
                //
                // ChangeScene(demo);


                // Debug.Log("测试：4秒后触发MultiWrong事件");
            }
            catch (System.Exception ex)
            {
                VFDebug.LogError($"延迟触发测试事件失败: {ex}");
            }
        }
        
        #endregion
    }
}