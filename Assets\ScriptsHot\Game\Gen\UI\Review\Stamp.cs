/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.Review
{
    public partial class Stamp : GComponent
    {
        public static string pkgName => "Review";
        public static string comName => "Stamp";
        public static string url => "ui://l7zo233drhje1u";

        public GLoader bg;
        public GTextField i18n;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(Stamp));
        }

        public override void ConstructFromXML(XML xml)
        {
            bg = GetChildAt(0) as GLoader;
            i18n = GetChildAt(1) as GTextField;
        }
        public override void Dispose()
        {
            bg = null;
            i18n = null;

            base.Dispose();
        }
    }
}