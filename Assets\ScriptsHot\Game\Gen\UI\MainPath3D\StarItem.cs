/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.MainPath3D
{
    public partial class StarItem : UIBindT
    {
        public override string pkgName => "MainPath3D";
        public override string comName => "StarItem";

        public Controller state;
        public Controller pos;
        public Controller dotNum;
        public Controller running;
        public CompNode compNode;
        public CompLdrStar compLdr;
        public Transition t0;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            state = com.GetControllerAt(0);
            pos = com.GetControllerAt(1);
            dotNum = com.GetControllerAt(2);
            running = com.GetControllerAt(3);
            compNode = new CompNode();
            compNode.Construct(com.GetChildAt(0).asCom);
            compLdr = new CompLdrStar();
            compLdr.Construct(com.GetChildAt(1).asCom);
            t0 = com.GetTransitionAt(0);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            state = null;
            pos = null;
            dotNum = null;
            running = null;
            compNode.Dispose();
            compNode = null;
            compLdr.Dispose();
            compLdr = null;
            t0 = null;
        }
    }
}