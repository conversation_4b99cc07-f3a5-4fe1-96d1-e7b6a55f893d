/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.Main
{
    public partial class MainPathBackGround : ExtendedComponent
    {
        public static string pkgName => "Main";
        public static string comName => "MainPathBackGround";
        public static string url => "ui://tnbxk55ijrqcv";

        public GLoader MainPathLoader;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(MainPathBackGround));
        }

        public override void ConstructFromXML(XML xml)
        {
            MainPathLoader = GetChildAt(0) as GLoader;

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();

            MainPathLoader = null;

            base.Dispose();
        }
    }
}