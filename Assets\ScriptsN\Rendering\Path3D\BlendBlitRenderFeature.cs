using System;
using FairyGUI;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using ZTemp;

public class BlendBlitRenderFeature : ScriptableRendererFeature
{
    private static readonly int MixFactor = Shader.PropertyToID("_MixFactor");
    private static readonly int MainTex = Shader.PropertyToID("_MainTex");
    private static readonly int SecondTex = Shader.PropertyToID("_SecondTex");
    private RenderTexture secondTex = null;
    private float secondTexAlpha = 0.0f;
    private RenderTexture outTex = null;
    
    // 计时器相关变量
    private bool isTransitioning = false;
    private float transitionStartTime;
    private float transitionDuration;
    private float startValue;
    private float targetValue;
    private System.Action onCompleteCallback;

    class BlendBlitRenderPass : ScriptableRenderPass
    {
        public MaterialLibrary _materialLibrary;
        RenderTargetIdentifier[] renderTargets;
        public int[] renderTargetIDs;
        public RTHandle perRT;
        public ScriptableRenderer renderer;
        public float secondTexAlpha = 0.0f;
        public RenderTexture secondTex = null;
        public RenderTexture outTex = null;

        public override void OnCameraSetup(CommandBuffer cmd, ref RenderingData renderingData)
        {
        }

        public override void Configure(CommandBuffer cmd, RenderTextureDescriptor cameraTextureDescriptor)
        {
        }

        public override void Execute(ScriptableRenderContext context, ref RenderingData renderingData)
        {
            //Unity特性(bug) Scene下CameraTarget有概率返回null。
            //https://discussions.unity.com/t/custom-render-feature-assertion-failed/1530820
            if (renderingData.cameraData.cameraType == CameraType.Game)
            {
                CommandBuffer cmd = CommandBufferPool.Get("E");
                Material mat = _materialLibrary.Get();
                mat.SetFloat(MixFactor, secondTexAlpha);
                mat.SetTexture(SecondTex,this.secondTex);
                
                cmd.SetGlobalTexture(MainTex, this.renderer.cameraColorTarget);
                cmd.Blit(null,this.outTex,mat);
                // cmd.SetGlobalTexture(MainTex, this.outTex);
                // cmd.Blit(null, this.renderer.cameraColorTarget);

                context.ExecuteCommandBuffer(cmd);
                cmd.Clear();
                CommandBufferPool.Release(cmd);
            }
        }

        public override void OnCameraCleanup(CommandBuffer cmd)
        {
            
        }

        public class MaterialLibrary
        {
            public Shader shader = Shader.Find("SpecialUI/RTMixShader");

            public Material blurMaterial;

            public Material Get()
            {
                if (blurMaterial == null)
                {
                    blurMaterial = new Material(shader);
                }

                return blurMaterial;
            }
        }
    }

    BlendBlitRenderPass m_ScriptablePass;
    
    public override void Create()
    {
        if (m_ScriptablePass == null)
        {
            m_ScriptablePass = new BlendBlitRenderPass();
            m_ScriptablePass._materialLibrary = new BlendBlitRenderPass.MaterialLibrary();
        }

        m_ScriptablePass.renderPassEvent = RenderPassEvent.AfterRenderingTransparents;
    }

    public override void AddRenderPasses(ScriptableRenderer renderer, ref RenderingData renderingData)
    {
        if (Application.isPlaying && secondTex != null && secondTexAlpha != 0.0f && this.outTex)
        {
            renderer.EnqueuePass(m_ScriptablePass);
        }
    }

    public override void SetupRenderPasses(ScriptableRenderer renderer, in RenderingData renderingData)
    {
        base.SetupRenderPasses(renderer, in renderingData);
        
        // 更新计时器
        UpdateTransition();
        
        m_ScriptablePass.renderer = renderer;
        m_ScriptablePass.secondTex = this.secondTex;
        m_ScriptablePass.secondTexAlpha = this.secondTexAlpha;
        m_ScriptablePass.outTex = this.outTex;
    }
    
    #region 公共计时器接口
    
    /// <summary>
    /// 启动混合参数动画过渡
    /// </summary>
    /// <param name="startValue">初始值</param>
    /// <param name="targetValue">目标值</param>
    /// <param name="duration">动画时长（秒）</param>
    /// <param name="targetRT">目标渲染纹理</param>
    /// <param name="onComplete">完成回调</param>
    public void StartBlendTransition(float startValue, float targetValue, float duration, 
        RenderTexture outRT, RenderTexture secondRT,System.Action onComplete = null)
    {
        if (duration <= 0f)
        {
            this.secondTexAlpha = targetValue;
            onComplete?.Invoke();
            return;
        }
        
        StopCurrentTransition();
        
        this.outTex = outRT;
        this.secondTex = secondRT;
        
        this.isTransitioning = true;
        this.transitionStartTime = Time.time;
        this.transitionDuration = duration;
        this.startValue = startValue;
        this.targetValue = targetValue;
        this.onCompleteCallback = onComplete;
        this.secondTexAlpha = startValue;
    }
    
    /// <summary>
    /// 停止当前的混合过渡动画
    /// </summary>
    public void StopCurrentTransition()
    {
        this.secondTexAlpha = 0.0f;
        this.secondTex = null;
        this.outTex = null; //只置空,不释放。因为这是外部传入的RT。本功能不管释放。置空也只是本功能内的引用置空。
        isTransitioning = false;
            
        System.Action callback = onCompleteCallback;
        callback?.Invoke();
        onCompleteCallback = null;
    }
    
    /// <summary>
    /// 获取当前是否正在进行动画过渡
    /// </summary>
    public bool IsTransitioning => isTransitioning;
    
    #endregion
    
    #region 私有方法
    
    /// <summary>
    /// 更新混合过渡状态
    /// </summary>
    private void UpdateTransition()
    {
        if (!isTransitioning)
            return;
            
        float elapsedTime = Time.time - transitionStartTime;
        
        if (elapsedTime >= transitionDuration)
        {
            StopCurrentTransition();
        }
        else
        {
            float progress = Mathf.Clamp01(elapsedTime / transitionDuration);
            float smoothProgress = Mathf.SmoothStep(0f, 1f, progress);
            this.secondTexAlpha = Mathf.Lerp(startValue, targetValue, smoothProgress);
        }
    }
    
    #endregion
}