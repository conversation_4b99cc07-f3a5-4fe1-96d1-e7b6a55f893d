/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class ClozeAnswer : AFragAnswer
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "ClozeAnswer";
        public static string url => "ui://cmoz5osjqit529";

        public GList optionsList;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(ClozeAnswer));
        }

        public override void ConstructFromXML(XML xml)
        {
            optionsList = GetChildAt(0) as GList;
        }
        public override void Dispose()
        {
            optionsList = null;

            base.Dispose();
        }
    }
}