using Msg.explore;

public class FriendSlotData
{
    private int slotIndex;
    private bool isOpen;
    private long slotId;
    private long avatarId;
    private long levelId;
    private int closenessValue;

    public int SlotIndex => slotIndex;

    public bool IsOpen => isOpen;

    public long SlotId => slotId;

    public long AvatarId => avatarId;

    public long LevelId => levelId;

    public int ClosenessValue => closenessValue;


    public FriendSlotData(PB_SlotDetailData slotData)
    { 
        slotIndex = slotData.slotIndex;
        isOpen = slotData.slotIsOpen;
        slotId = slotData.slotId;
        avatarId = slotData.avatarId;
        levelId = slotData.levelId;
        closenessValue = slotData.closenessValue;
    }

    public void UpdateData(PB_SlotDetailData slotData)
    { 
        slotIndex = slotData.slotIndex;
        isOpen = slotData.slotIsOpen;
        slotId = slotData.slotId;
        avatarId = slotData.avatarId;
        levelId = slotData.levelId;
        closenessValue = slotData.closenessValue;
        
        if(slotIndex == 2)isOpen = true;
    }

    public void AddClosenessValue(int value)
    {
        closenessValue += value;
    }
    public void UpdateClosenessValue(int value)
    {
        closenessValue = value;
    }
    
    public void UpdateLevel(long value)
    {
        levelId = value;
    }

    public bool IsEmptySlot =>avatarId == 0;

}