/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class MultipleChoiceAnswer : AFragAnswer
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "MultipleChoiceAnswer";
        public static string url => "ui://cmoz5osjfrdcuvptcn";

        public GList optionsList;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(MultipleChoiceAnswer));
        }

        public override void ConstructFromXML(XML xml)
        {
            optionsList = GetChildAt(0) as GList;
        }
        public override void Dispose()
        {
            optionsList = null;

            base.Dispose();
        }
    }
}