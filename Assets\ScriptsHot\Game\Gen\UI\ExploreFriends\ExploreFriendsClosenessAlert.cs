/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreFriends
{
    public partial class ExploreFriendsClosenessAlert : UIBindT
    {
        public override string pkgName => "ExploreFriends";
        public override string comName => "ExploreFriendsClosenessAlert";

        public Controller ctrlLevel;
        public GGraph imgBG;
        public ClosenessLevelUpCom comMain;
        public Transition open;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ctrlLevel = com.GetControllerAt(0);
            imgBG = (GGraph)com.GetChildAt(0);
            comMain = new ClosenessLevelUpCom();
            comMain.Construct(com.GetChildAt(1).asCom);
            open = com.GetTransitionAt(0);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ctrlLevel = null;
            imgBG = null;
            comMain.Dispose();
            comMain = null;
            open = null;
        }
    }
}