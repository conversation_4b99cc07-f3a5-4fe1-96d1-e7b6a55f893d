﻿using System.Collections.Generic;
using FairyGUI;
using Modules.DataDot;
using Msg.basic;
using ScriptsHot.Game.Modules.ChatStart;
using ScriptsHot.Game.Modules.Common;
using UIBind.Settlement;
using Unity.Mathematics;
using UnityEngine;
using Object = UnityEngine.Object;

namespace ScriptsHot.Game.Modules.Settlement.SettlementUI
{
    public class SettlementCommonUI : BaseUI<SettlementCommonPanel>
    {
        public SettlementCommonUI(string name) : base(name)
        {
        }

        private SettlementModel SettlementMod => GetModel<SettlementModel>(ModelConsts.Settlement);
        private SettlementController SettlementCtrl => GetController<SettlementController>(ModelConsts.Settlement);
        public override string uiLayer => UILayerConsts.Top;
        
        private GComponent _showNewWordComp;
        private NewWordComponent _curNewWordComp;

        protected override void OnInit(GComponent uiCom)
        {
            base.OnInit(uiCom);
            
            AddUIEvent(ui.compBottom.btnStart.onClick, OnClickContinue);
            ui.compSettle.compSettleList.DoInit();
            AddEffect();
        }
        
        private List<CompSettleList.SettleListStruct> _settleStrList = new();
        private int exp;
        
        protected override void OnShow()
        {
            base.OnShow();
            SoundManger.instance.PlayUI("settle_common_show");

            // 刘喆说要写死 垃圾代码 真恶心 不知道能活多久 先这么搞了
            // prd : https://visionflow.feishu.cn/wiki/Mx5NwdraIijpGAkTHtmcLfNlnvb
            exp = SettlementMod.SettlementLocalData.JumpState == 1 ? 10 : 40;
            SettlementMod.SetSettlementLocalCanShowNextView(false);
            RefreshSettle();
        }

        protected override void OnHide()
        {
            base.OnHide();
        }

        protected override bool isFullScreen => true;

        private void RefreshSettle()
        {
            DotAppearSessionResultPage dot = new DotAppearSessionResultPage();
            dot.time = SettlementMod.SettlementLocalData.TimeSeconds;
            dot.exp = exp;
            dot.acc = SettlementMod.SettlementLocalData.CorrectRate;
            dot.session_type = SettlementMod.SettlementLocalData.LevelTypeEnum.ToString();
            DataDotMgr.Collect(dot);
            
            RefreshView();
        }

        private void RefreshSettleList()
        {
            _settleStrList.Clear();
            _settleStrList.Add(new CompSettleList.SettleListStruct()
            {
                IconType = CompSettleList.SettleIconType.Time,
                Number = math.max(1,SettlementMod.SettlementLocalData.TimeSeconds / 60),
            });
            _settleStrList.Add(new CompSettleList.SettleListStruct()
            {
                IconType = CompSettleList.SettleIconType.Exp,
                Number = exp,
            });
            if (SettlementMod.SettlementLocalData.CorrectRate > 0f)
            {
                _settleStrList.Add(new CompSettleList.SettleListStruct()
                {
                    IconType = CompSettleList.SettleIconType.CorrectRate,
                    Number = Mathf.CeilToInt(SettlementMod.SettlementLocalData.CorrectRate * 100),
                });
            }
            ui.compSettle.compSettleList.DoShowSettleList(_settleStrList);
        }

        private void RefreshView()
        {
            ui.compBottom.tfReward.SetKey("common_continue");
            if (SettlementMod.SettlementLocalData.JumpState > 0)
            {
                ui.state.SetSelectedPage(SettlementMod.SettlementLocalData.JumpState == 1 ? "normal" : "false");
                if (SettlementMod.SettlementLocalData.JumpState == 2)
                {
                    DotAppearJumpFalse dot = new DotAppearJumpFalse();
                    DataDotMgr.Collect(dot);
                }
                ui.compSettle.tfCongra.SetKey("ui_stamina_settlement_congratulation");
                ui.compFalse.tfText.SetKey("ui_main_path_jump_false");
                GameEntry.MainPathC.OnSettlementSkipOver(SettlementMod.SettlementLocalData.JumpState == 1);
            }
            else
            {
                RefreshSettleList();
                if (SettlementMod.SettlementLocalData.DialogSourceEnum == PB_DialogSourceEnum.DialogSourceReSignEntrance)
                {
                    ui.state.SetSelectedPage("resign");
                    ui.compSettle.sp_flow_resign.spineAnimation.AnimationState.SetAnimation(0, "1", false).Complete += entry =>
                    {
                        ui.compSettle.sp_flow_resign.spineAnimation.AnimationState.SetAnimation(0, "2", true);
                    };
                    ui.compBottom.tfReward.SetKey("ui_fragmentPractice_sign_btn_quit");
                    ui.compSettle.tfCongra.SetKey("ui_stamina_settlement_congratulation");
                }
                else if(SettlementMod.SettlementLocalData.DialogSourceEnum == PB_DialogSourceEnum.DialogSourceMenuBarForStamina)
                {
                    ui.state.SetSelectedPage("stamina");
                    ui.compSettle.sp_flow_stamina.spineAnimation.AnimationState.SetAnimation(0, "1", false).Complete += entry =>
                    {
                        ui.compSettle.sp_flow_stamina.spineAnimation.AnimationState.SetAnimation(0, "2", true);
                    };
                    ui.compBottom.tfReward.SetKey("ui_fragmentPractice_sign_btn_quit");
                    ui.compSettle.tfCongra.SetKey("ui_stamina_settlement_congratulation");
                }
                else
                {
                    ui.state.SetSelectedPage("normal");
                    if (SettlementMod.SettlementLocalData.TimeSeconds <= 180)
                    {
                        ui.compSettle.spineType.selectedPage = "SpineTFast";
                        ui.compSettle.tfCongra.SetKey("ui_fragment_settle_1");
                    }
                    else if (SettlementMod.SettlementLocalData.CorrectRate >= 1)
                    {
                        ui.compSettle.spineType.selectedPage = "SpineTPerfect";
                        ui.compSettle.tfCongra.SetKey("ui_fragment_settle_0");
                    }
                    else if (SettlementMod.SettlementLocalData.ErrorNum > 4)
                    {
                        ui.compSettle.spineType.selectedPage = "SpineTHoldOn";
                        ui.compSettle.tfCongra.SetKey("ui_fragment_settle_3");
                    }
                    else
                    {
                        ui.compSettle.spineType.selectedPage = "SpineTFinish";
                        ui.compSettle.tfCongra.SetKey("ui_fragment_settle_4");
                    }
                        
                }
            }
        }

        private void OnClickContinue()
        {
            SoundManger.instance.PlayUI("button_next");
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);

            if (SettlementMod.SettlementLocalData.JumpState == 2)
            {
                DotClickJumpFalseContinue dot = new DotClickJumpFalseContinue();
                DataDotMgr.Collect(dot);
            }
            else
            {
                DotClickSessionResultContinue dot = new DotClickSessionResultContinue();
                dot.session_type = SettlementMod.SettlementLocalData.LevelTypeEnum.ToString();
                DataDotMgr.Collect(dot);
            }
            
            SettlementMod.SetSettlementLocalCanShowNextView(true);
            if (SettlementMod.DialogResultListData == null)
                GetUI<WarmupLoadingCutInUI>(UIConsts.WarmupLoadingCutInUI)?.Show();// 临时挡一下（可能会挡很久
            else
                SettlementCtrl.ShowNextView();
            Hide();
        }
        
        private GameObject _fxEffectObject;
        private void AddEffect()
        {
            GameObject prefab = GResManager.instance.LoadPrefab("ConffetiFX");
            _fxEffectObject = GameObject.Instantiate(prefab);
            GoWrapper wrapper = new GoWrapper(_fxEffectObject);
            ui.compSettle.holder.SetNativeObject(wrapper);
            
            _fxEffectObject.transform.localPosition = Vector3.zero;
            _fxEffectObject.transform.localScale = new Vector3(1.5f, 1.5f, 1);
            _fxEffectObject.transform.Find("Conffeti_Boost").localScale = new Vector3(100, 100, 1f);
            _fxEffectObject.transform.Find("Conffeti_Stay").localScale = new Vector3(100, 100, 1f);
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            if (_fxEffectObject != null)
            {
                Object.Destroy(_fxEffectObject);
                _fxEffectObject = null;
            }            
        }
    }
}