/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.DrillHub
{
    public partial class CompExampleItem : GComponent
    {
        public static string pkgName => "DrillHub";
        public static string comName => "CompExampleItem";
        public static string url => "ui://9mx0po3ifdph1a";

        public Controller ctrlLineState;
        public GTextField tfNum;
        public GButton btnPlay;
        public GComponent tfDesc;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(CompExampleItem));
        }

        public override void ConstructFromXML(XML xml)
        {
            ctrlLineState = GetControllerAt(0);
            tfNum = GetChildAt(0) as GTextField;
            btnPlay = GetChildAt(1) as GButton;
            tfDesc = GetChildAt(2) as GComponent;
        }
        public override void Dispose()
        {
            ctrlLineState = null;
            tfNum = null;
            btnPlay = null;
            tfDesc = null;

            base.Dispose();
        }
    }
}