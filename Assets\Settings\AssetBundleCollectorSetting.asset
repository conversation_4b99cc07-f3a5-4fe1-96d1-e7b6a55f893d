%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 185f6993d5150494d98da50e26cb1c25, type: 3}
  m_Name: AssetBundleCollectorSetting
  m_EditorClassIdentifier: 
  ShowPackageView: 1
  ShowEditorAlias: 1
  UniqueBundleName: 1
  Packages:
  - PackageName: main
    PackageDesc: 
    EnableAddressable: 1
    LocationToLower: 0
    IncludeAssetGUID: 0
    IgnoreDefaultType: 1
    AutoCollectShaders: 1
    Groups:
    - GroupName: ArtScene
      GroupDesc: 
      AssetTags: main
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/Art/Scenes/Talkit_Town/Room/Room.unity
        CollectorGUID: 5a2421b3bbc5680459b7a5c278e4ab38
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectScene
        AssetTags: 
        UserData: 
      - CollectPath: Assets/Art/Scenes/Talkit_Town/Talkit_Town_SelectRole.unity
        CollectorGUID: b006e35608aa9584795a433e589b1dcb
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectScene
        AssetTags: 
        UserData: 
    - GroupName: UI
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/Build/UI
        CollectorGUID: b81abf8b289ebab4e95c9ef99d59c394
        CollectorType: 0
        AddressRuleName: AddressDisable
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Config
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/Build/Config
        CollectorGUID: bbe04c8e5a7a4fd4da58febcd8b29e5f
        CollectorType: 0
        AddressRuleName: AddressDisable
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Sound
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/Build/Sound
        CollectorGUID: 30369ce8c95d49846ba6a1786d1933d8
        CollectorType: 0
        AddressRuleName: AddressDisable
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Effect
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: DisableGroup
      Collectors:
      - CollectPath: Assets/Build/Effect
        CollectorGUID: 
        CollectorType: 0
        AddressRuleName: AddressDisable
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Spine
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/Build/Spine
        CollectorGUID: f219c26e53eaebf43828519e9b6f067e
        CollectorType: 0
        AddressRuleName: AddressDisable
        PackRuleName: PackSeparately
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Model
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/Build/Model
        CollectorGUID: 6f7921c9bd71d3f46ae5bbe7d24cd4da
        CollectorType: 0
        AddressRuleName: AddressDisable
        PackRuleName: PackSeparately
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Icon
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: DisableGroup
      Collectors:
      - CollectPath: Assets/Build/Icon
        CollectorGUID: 76262abd479f1b542b8ede14c971c130
        CollectorType: 0
        AddressRuleName: AddressDisable
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Scene
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: DisableGroup
      Collectors:
      - CollectPath: Assets/Build/Scene
        CollectorGUID: 262da133b8096c6479dbba3492f5f4c9
        CollectorType: 0
        AddressRuleName: AddressDisable
        PackRuleName: PackSeparately
        FilterRuleName: CollectScene
        AssetTags: 
        UserData: 
    - GroupName: Hotfix
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/Build/Hotfix
        CollectorGUID: 3d67463eec3ecf64b9cdb4ad956cb5fc
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Anim
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/Build/AnimRes
        CollectorGUID: de3b26e4a271bb144a8d16a0b12b46c2
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Shader
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/Build/SVC
        CollectorGUID: 525d4495b7d124f13bddf49aa9e3c7e2
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectShaderVariants
        AssetTags: 
        UserData: 
    - GroupName: LittleGroup
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: DisableGroup
      Collectors:
      - CollectPath: Assets/Build/LittleMap
        CollectorGUID: 9e614afac438b45889ef51c112439fde
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Prefab
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/Build/Prefab
        CollectorGUID: 09baf8e3a35d4a54a9e5ce0ac932eabb
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectPrefab
        AssetTags: 
        UserData: 
    - GroupName: Material
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: DisableGroup
      Collectors:
      - CollectPath: Assets/Build/Material
        CollectorGUID: 5c2525697e4ae46d991622ee87b93a68
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: ScriptableObject
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/Build/ScriptableObject
        CollectorGUID: e341a69e43bf04d32a456cfee73d5709
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: TEMP
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: DisableGroup
      Collectors:
      - CollectPath: Assets/ZTest
        CollectorGUID: e6b258c4438de0a48bf356b094281c35
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Music
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/Build/Music
        CollectorGUID: a05cb49d8d8ac6a48ba255e629e83997
        CollectorType: 0
        AddressRuleName: AddressDisable
        PackRuleName: PackSeparately
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: ExploreBG
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/Build/ExploreBG
        CollectorGUID: d38566d39b888a24d852643fe7f81a21
        CollectorType: 0
        AddressRuleName: AddressDisable
        PackRuleName: PackSeparately
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/Build/ExploreBGLights
        CollectorGUID: de5faa32858f0fe4ba44eded4252d42d
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
      - CollectPath: Assets/Build/ExploreIntro
        CollectorGUID: 7c2b362a53102b940ab83c22fb2953af
        CollectorType: 0
        AddressRuleName: AddressByFileName
        PackRuleName: PackSeparately
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: Avatar
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/Build/Avatar
        CollectorGUID: a83e41126679b2b489e1181264a94053
        CollectorType: 0
        AddressRuleName: AddressDisable
        PackRuleName: PackSeparately
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
    - GroupName: ShaderRes
      GroupDesc: 
      AssetTags: 
      ActiveRuleName: EnableGroup
      Collectors:
      - CollectPath: Assets/Build/ShaderRes
        CollectorGUID: ccf633621c4fdbe419a173c5955c232a
        CollectorType: 0
        AddressRuleName: AddressByFolderAndFileName
        PackRuleName: PackDirectory
        FilterRuleName: CollectAll
        AssetTags: 
        UserData: 
