/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class BtnDialogueAudio : ABtnAudio
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "BtnDialogueAudio";
        public static string url => "ui://cmoz5osjw1ksuvptc6";

        public Transition playing;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(BtnDialogueAudio));
        }

        public override void ConstructFromXML(XML xml)
        {
            playing = GetTransitionAt(0);
        }
        public override void Dispose()
        {
            playing = null;

            base.Dispose();
        }
    }
}