/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreFriends
{
    public partial class ExploreFriendsNewHandPanel : UIBindT
    {
        public override string pkgName => "ExploreFriends";
        public override string comName => "ExploreFriendsNewHandPanel";

        public GGraph imgBG;
        public FriendCommonBtn NextBtn;
        public GTextField ContentTxt1;
        public GTextField ContentTxt2;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            imgBG = (GGraph)com.GetChildAt(0);
            NextBtn = new FriendCommonBtn();
            NextBtn.Construct(com.GetChildAt(2).asCom);
            ContentTxt1 = (GTextField)com.GetChildAt(3);
            ContentTxt2 = (GTextField)com.GetChildAt(4);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            imgBG = null;
            NextBtn.Dispose();
            NextBtn = null;
            ContentTxt1 = null;
            ContentTxt2 = null;
        }
    }
}