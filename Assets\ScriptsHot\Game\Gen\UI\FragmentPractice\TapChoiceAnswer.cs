/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class TapChoiceAnswer : AFragAnswer
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "TapChoiceAnswer";
        public static string url => "ui://cmoz5osjc5f4uvptcj";

        public GList listOptions;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(TapChoiceAnswer));
        }

        public override void ConstructFromXML(XML xml)
        {
            listOptions = GetChildAt(0) as GList;
        }
        public override void Dispose()
        {
            listOptions = null;

            base.Dispose();
        }
    }
}