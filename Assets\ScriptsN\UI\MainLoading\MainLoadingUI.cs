using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;
using System.Security.Cryptography.X509Certificates;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class MainLoadingUI : MonoBehaviour
{
    public static MainLoadingUI Ins;
    private void Awake()
    {
        Ins = this;
        InitUI();
        OnConfirmUIHide();
        SetProgressValue(0f);
    }

    private Func<string,string> CfgStr;
    public void SetCfgAction(Func<string,string> func)
    {
        CfgStr = func;
    }

    private GameObject _canvas4DefaultBg;
    
    //confirmUI
    private GameObject confirmUI;
    private Text contentTxt;

    private Button confirmBtn;
    private Button confirmBtn2;
    private Button cancelBtn;
    private Text confirmBtnTxt;
    private Text confirmBtnTxt2;
    private Text cancelBtnTxt;
    
    //progress
    private GameObject loadingUI;
    private GameObject progressNode;
    private Text progressTxt;
    private Image progressImg;
    private Text progressDescTxt;

    private float curUIProgress = 0f;
    private float targetProgress = 0f;
    
    private string GetStr(string key)
    {
        if (CfgStr != null)
        {
            return CfgStr.Invoke(key);
        }
        return string.Empty;
    }

    private void InitUI()
    {
        if (_canvas4DefaultBg == null)
        {
            _canvas4DefaultBg = GameObject.Find("Canvas4DefaultBg");
            DontDestroyOnLoad(_canvas4DefaultBg);
            
            GameObject rContainer = GameObject.Find("RenderTextureContainer");
            DontDestroyOnLoad(rContainer);

            confirmUI = UIUtil.GetUIGameObject(_canvas4DefaultBg,"ConfirmUI");
            contentTxt = UIUtil.GetUIComponet<Text>(confirmUI, "Content");
            
            cancelBtn = UIUtil.GetUIComponet<Button>(confirmUI, "CancelBtn");
            confirmBtn = UIUtil.GetUIComponet<Button>(confirmUI, "ConfirmBtn");
            confirmBtn2 = UIUtil.GetUIComponet<Button>(confirmUI, "ConfirmBtn2");
            cancelBtnTxt = UIUtil.GetUIComponet<Text>(cancelBtn.gameObject, "BtnTxt");
            confirmBtnTxt = UIUtil.GetUIComponet<Text>(confirmBtn.gameObject, "BtnTxt");
            confirmBtnTxt2 = UIUtil.GetUIComponet<Text>(confirmBtn2.gameObject, "BtnTxt");
            
            loadingUI = UIUtil.GetUIGameObject(_canvas4DefaultBg,"LoadingUI");
            progressNode = UIUtil.GetUIGameObject(loadingUI, "Progress");
            progressTxt = UIUtil.GetUIComponet<Text>(progressNode, "ProgressValue");
            progressDescTxt = UIUtil.GetUIComponet<Text>(progressNode, "ProgressDesc");
            progressImg = UIUtil.GetUIComponet<Image>(progressNode, "ProgressImg");
        }
    }

    #region app背底

    public void HideDefaultCanvas()
    {
        InitUI();
        HideLoading();
        OnConfirmUIHide();
    }
    public bool DefaultCanvasIsShow()
    {
        if (loadingUI != null && loadingUI.activeSelf) return true;
        if (confirmUI != null && confirmUI.activeSelf) return true;
        return false;
    }

    private void HideLoading()
    {
        InitUI();
        if (loadingUI != null && loadingUI.activeSelf)
        {
            loadingUI.SetActive(false);
        }
    }

    #endregion

    #region NetErrorConfirmUI
    public class ConfirmUIData
    {
        public string title;
        public string content;
        public string confirmBtnContent;
    }
    
    public void OnShowNetErrorConfirmUIOpen(string content, Action confirmFunc, Action cancelFunc = null, int type = 2,
        string confirmLabel = "", string cancelLabel = "", bool block = false,int iconType = 0)
    {
        InitUI();
        confirmUI?.SetActive(true);
        
        contentTxt.text = content;

        if (string.IsNullOrEmpty(confirmLabel))
        {
            confirmBtnTxt.text = GetStr("ui_confirm_btn_1");
            confirmBtnTxt2.text = GetStr("ui_confirm_btn_1");
        }
        else
        {
            confirmBtnTxt.text = confirmLabel;
            confirmBtnTxt2.text = confirmLabel;
        }

        if (string.IsNullOrEmpty(cancelLabel))
        {
            cancelBtnTxt.text = GetStr("chat_btn_cancel");
        }
        else
        {
            cancelBtnTxt.text = cancelLabel;
        }
        
        bool oneBtnFlag = cancelFunc == null;
        confirmBtn2.gameObject.SetActive(oneBtnFlag);
        confirmBtn.gameObject.SetActive(!oneBtnFlag);
        cancelBtn.gameObject.SetActive(!oneBtnFlag);

        confirmBtn.onClick.RemoveAllListeners();
        confirmBtn.onClick.AddListener(() =>
        {
            OnConfirmUIHide();
            confirmFunc?.Invoke();
        });
        confirmBtn2.onClick.RemoveAllListeners();
        confirmBtn2.onClick.AddListener(() =>
        {
            OnConfirmUIHide();
            confirmFunc?.Invoke();
        });
        cancelBtn.onClick.RemoveAllListeners();
        cancelBtn.onClick.AddListener(() =>
        {
            OnConfirmUIHide();
            cancelFunc?.Invoke();
        });
        
        confirmUI?.SetActive(true);
    }

    public void OnConfirmUIHide()
    {
        InitUI();
        confirmUI?.SetActive(false);
    }
    #endregion

    #region Progress

    public enum ProgressMode
    {
        CheckVersion,
        Download
    }
    public void SetProgressDesc(ProgressMode mode , string param = null)
    {
        if (mode == ProgressMode.CheckVersion)
        {
            //SetDesc(HotUpdateLangCfg.GetStr(HUKey.checkVersionInfoTip));
            //目前
            SetDesc(HotUpdateLangCfg.GetStr(HUKey.checkVersionInfoTip));
        }
        else
        {
            SetDesc(HotUpdateLangCfg.GetStr(HUKey.underHotUpdateTip , param));
        }
    }

    private void SetDesc(string str)
    {
        progressDescTxt.text = str;
    }
    
    public void SetProgressValue(float value)
    {
        targetProgress = value;
    }
    
    private void RealSetProgressValue(float value)
    {
        progressImg.fillAmount = value/100;
        progressTxt.text = value.ToString("0.00") + "%";
    }
    #endregion


    private void Update()
    {
        if (targetProgress > curUIProgress)
        {
            float interval = targetProgress - curUIProgress;
            if (interval > 50f)
            {
                curUIProgress += 5f;
            }
            else if (interval > 20f)
            {
                curUIProgress += 2f;
            }
            else if (interval > 10f)
            {
                curUIProgress += 1f;
            }
            else
            {
                curUIProgress += 0.5f;
            }

            curUIProgress = Math.Min(curUIProgress, targetProgress);
            RealSetProgressValue(curUIProgress);
        }
    }
}
