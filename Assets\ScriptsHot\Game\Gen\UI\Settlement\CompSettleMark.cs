/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.Settlement
{
    public partial class CompSettleMark : ExtendedComponent
    {
        public static string pkgName => "Settlement";
        public static string comName => "CompSettleMark";
        public static string url => "ui://4zjhpnm4lzxwuvptdc";

        public GTextField tfLevel;
        public GList listMark;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(CompSettleMark));
        }

        public override void ConstructFromXML(XML xml)
        {
            tfLevel = GetChildAt(1) as GTextField;
            listMark = GetChildAt(2) as GList;

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();

            tfLevel = null;
            listMark = null;

            base.Dispose();
        }
    }
}