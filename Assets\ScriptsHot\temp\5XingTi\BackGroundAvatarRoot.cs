using System;
using System.Threading.Tasks;
using AnimationSystem;
using FairyGUI;
using ScriptsHot.Game.Modules.Explore.UI;
using ScriptsHot.Game.Modules.Scene.Level;
using ScriptsHot.Game.Modules.Scene.Level.Component;
using UnityEngine;
using YooAsset;
using FilterMode = UnityEngine.FilterMode;
using Object = UnityEngine.Object;

/// <summary>
/// 独立的3D角色背景渲染组件，每个实例管理自己的渲染环境
/// 移植自 BackGroundAvatarManager，但去除缓存机制和单例模式，支持多实例
/// </summary>
public class BackGroundAvatarRoot
{
    private static int CURRID = 1;
    private static int ACTIVE_COUNT = 0;
    
    // 渲染环境组件
    private GameObject _root;
    private GameObject _backgroundRoot;
    private GameObject _characterRoot;
    private Camera _camera;
    private Light _mainLight;
    private RenderTexture _rt;

    
    
    // 使用新的AvatarRoot管理角色
    private AvatarRoot _avatarRoot;
    
    // 背景相关
    private GameObject _currentBackground;

    public GameObject CurrentAvatar => _avatarRoot?.CurrentAvatar;
    public GameObject CurrentBackground => _currentBackground;
    public AnimationAvatarManager Manager => _avatarRoot?.Manager;
    
    // 常量
  //  private static readonly int UIModelLayer = 30;
    
    // 状态
    private bool _isInitialized = false;

    /// <summary>
    /// 初始化渲染环境
    /// </summary>
    private void InitRenderEnvironment()
    {
        if (_isInitialized) return;
        
        // 创建独立的场景根节点
        _root = new GameObject("5StarBackGroundRoot_Instance");
        _root.transform.position = new Vector3(-CURRID*50, 0, 0);
        _root.transform.rotation = Quaternion.identity;
        GameObject.DontDestroyOnLoad(_root);
        
        // 创建主光源
        GameObject lightRoot = new GameObject("Light");
        lightRoot.transform.SetParent(_root.transform, false);
        lightRoot.transform.localPosition = Vector3.zero;
        lightRoot.transform.localRotation = Quaternion.Euler(new Vector3(27, 18.8f, 0));
        lightRoot.transform.localScale = Vector3.one;
        
        _mainLight = lightRoot.AddComponent<Light>();
        _mainLight.type = UnityEngine.LightType.Directional;
        _mainLight.shadows = LightShadows.Soft;
        _mainLight.shadowStrength = 0.7f;
        _mainLight.intensity = 1.0f;
        _mainLight.color = new Color(1f, 0.98f, 0.915f, 1f);
        
        // 创建摄像机
        GameObject cameraRoot = new GameObject("CameraRoot");
        cameraRoot.transform.SetParent(_root.transform, false);
        cameraRoot.transform.localPosition = new Vector3(0, 1.69f, -2.25f);
        cameraRoot.transform.localRotation = Quaternion.Euler(new Vector3(11.27f, 0, 0));
        cameraRoot.transform.localScale = Vector3.one;
        
        _camera = cameraRoot.AddComponent<Camera>();
        _camera.orthographic = false;
        _camera.fieldOfView = 30;
        _camera.farClipPlane = 10;
        _camera.backgroundColor = Color.black;
        _camera.clearFlags = CameraClearFlags.SolidColor;
        _camera.enabled = false; // 默认关闭，需要时再开启
        
        // 创建背景挂点
        GameObject backgroundRoot = new GameObject("BGImage");
        backgroundRoot.transform.SetParent(cameraRoot.transform, false);
        backgroundRoot.transform.localPosition = new Vector3(0, 0, 5);
        backgroundRoot.transform.localRotation = Quaternion.identity;
        backgroundRoot.transform.localScale = new Vector3(0.1f, 0.1f, 0.1f);
        _backgroundRoot = backgroundRoot;
        
        // 创建角色挂点
        GameObject characterRoot = new GameObject("Character");
        characterRoot.transform.SetParent(_root.transform);
        characterRoot.transform.localPosition = new Vector3(0, -0.143f, 1.553f);
        characterRoot.transform.localRotation = Quaternion.Euler(0, 180, 0);
        _characterRoot = characterRoot;
        
        // 初始化AvatarRoot
        _avatarRoot = new AvatarRoot();
        
        _isInitialized = true;
        ACTIVE_COUNT++;
        //Debug.LogError("ACTIVE" + ACTIVE_COUNT);
        CURRID++;
    }



    /// <summary>
    /// 加载角色
    /// </summary>
    private async Task LoadAvatarAsync(long id, Level level)
    {
        await _avatarRoot.LoadAvatarAsync(id, _characterRoot.transform);
    }

    /// <summary>
    /// 加载背景
    /// </summary>
    private async Task LoadBackgroundAsync(string backgroundName)
    {
        // 清理之前的背景
        if (_currentBackground != null)
        {
            GResManager.instance.ReleaseInstance(_currentBackground);
            _currentBackground = null;
        }

        if (backgroundName != null)
        {
            if (YooAssets.CheckLocationValid(backgroundName))
            {
                var background = YooAssets.LoadAssetAsync<Sprite>(backgroundName);
                await background.Task;

                if (!background.IsDone || !background.IsValid)
                {
                    Debug.LogError("背景加载失败。");
                }
                else
                {
                    Sprite sprite = background.AssetObject as Sprite;
                    _currentBackground = CreateBackgroundGameObject(backgroundName, sprite);
                }
            }
            else
            {
                Debug.LogError("背景加载失败。没有找到对应的资源");
            }
        }
    }

    /// <summary>
    /// 创建背景GameObject
    /// </summary>
    private GameObject CreateBackgroundGameObject(string backgroundName, Sprite sprite)
    {
        GameObject backgroundGo = new GameObject($"BG{backgroundName}");
        backgroundGo.transform.SetParent(_backgroundRoot.transform, false);
        backgroundGo.transform.localPosition = Vector3.zero;
        backgroundGo.transform.localRotation = Quaternion.identity;
        backgroundGo.transform.localScale = Vector3.one;
        var sp = backgroundGo.AddComponent<SpriteRenderer>();
        sp.sprite = sprite;
        
        return backgroundGo;
    }



    /// <summary>
    /// 释放当前角色到缓存池
    /// </summary>
    private void ReleaseCurrentAvatar()
    {
        _avatarRoot?.ReleaseCurrentAvatar();
    }
    
    public void SetBackground(Vector3? position = null, Quaternion? rotation = null, Vector3? scale = null)
    {
        if (position != null ) this._backgroundRoot.transform.localPosition = (Vector3)position;
        if (rotation != null ) this._backgroundRoot.transform.localRotation = (Quaternion)rotation;
        if (scale != null) this._backgroundRoot.transform.localScale = (Vector3)scale;
    }

    public void SetAvatar(Vector3? position = null, Quaternion? rotation = null, Vector3? scale = null)
    {
        if (position != null ) this._characterRoot.transform.localPosition = (Vector3)position;
        if (rotation != null ) this._characterRoot.transform.localRotation = (Quaternion)rotation;
        if (scale != null) this._characterRoot.transform.localScale = (Vector3)scale;
    }

    /// <summary>
    /// 生成RenderTexture - 对应原来的 GenerateRT 方法
    /// </summary>
    public async Task<RenderTexture> GenerateRT(long id = 16978784336289664, string backgroundName = "")
    {
        // 初始化渲染环境
        if (!_isInitialized)
        {
            InitRenderEnvironment();
        }
        
        // 获取场景控制器
        SceneController sCon = ControllerManager.instance.GetController(ModelConsts.Scene) as SceneController;
        Level level = sCon.scene;
        
        // 加载角色和背景
        await LoadAvatarAsync(id, level);
        
        await LoadBackgroundAsync(backgroundName);
        
        // 创建RenderTexture
        if (_rt == null)
        {
            _rt = new RenderTexture((int)GRoot.inst.width, (int)GRoot.inst.height, 24, RenderTextureFormat.ARGB32);
            _rt.autoGenerateMips = false;
            _rt.antiAliasing = 1;
            _rt.filterMode = FilterMode.Bilinear;
            _camera.targetTexture = _rt;
        }
        
        //如果不要背景,相机需要设为透明
        if (string.IsNullOrEmpty(backgroundName))
        {
            this._camera.backgroundColor = new Color(0, 0, 0, 0);
        }
        
        // 暂时注释掉层级设置，确保先复原基本功能
        // SetLayerRecursively();
        
        // 启用渲染
        _camera.enabled = true;
        _mainLight.enabled = true;
        
        return _rt;
    }

    /// <summary>
    /// 清理资源（关闭不释放）
    /// </summary>
    public void ClearRT()
    {
        if (_camera)
        {
            _camera.enabled = false;
            _camera.targetTexture = null;
        }
        
        if (_mainLight)
        {
            _mainLight.enabled = false;
        }
        
        this._root?.gameObject.SetActive(false);
        
        if (_rt)
        {
            _rt.Release();
            _rt = null;
        }
    }
    
    /// <summary>
    /// 清理资源（关闭&释放）释放后再用要重新加载重新传参数
    /// </summary>
    public void Dispose()
    {
        _rt?.Release();
        _rt = null;
        
        // 释放当前角色到缓存池
        ReleaseCurrentAvatar();
        
        // 销毁场景根节点
        if (_root != null)
        {
            Object.DestroyImmediate(_root);
            _root = null;
        }

        // 释放AvatarRoot资源
        _avatarRoot?.Dispose();
        _avatarRoot = null;
        
        ACTIVE_COUNT--;
        //Debug.LogError("ACTIVE" + ACTIVE_COUNT);
        this._isInitialized = false;
    }
    
    /// <summary>
    /// 让现在的指定Avatar播放一个动作。基于DialogGenericDefine配置的Key决定播放什么。如果该Key对应多个动作那么随机播放其中一个。
    /// 可以有callback。（播放完成时/此动作被打断时）
    /// 可以自行指定目标时长,通常传入0代表使用动画默认时长。
    /// 本质上是调用了一个专用于播放特定动画的State。通常有复杂状态机需求建议新增AnimState来支持。这个函数只适用于调用的状态机比较简单的情况。
    /// 指定动画播放完成后会自动返回Idle。
    /// </summary>
    public void PlayAnimationByExcel(string excelAnimation,Action<StarX5PlayAnimationState.AnimCallBack> cb,float targetDuration = 0f)
    {
        _avatarRoot?.PlayAnimationByExcel(excelAnimation, cb, targetDuration);
    }
    
    /// <summary>
    /// 基于动画名字播放动画。比如Idle + 女性(性别自动拾取) = 播放名为GirlIdle的动画。
    /// 其它与PlayAnimationByExcel相同；
    /// </summary>
    /// <param name="nameAnimation"></param>
    /// <param name="cb"></param>
    public void PlayAnimationByName(string nameAnimation,Action<StarX5PlayAnimationState.AnimCallBack> cb,float targetDuration = 0f)
    {
        _avatarRoot?.PlayAnimationByName(nameAnimation, cb, targetDuration);
    }


    /// <summary>
    /// 播放Avatar TTS音频
    /// </summary>
    /// <param name="audioClip">要播放的音频剪辑</param>
    /// <param name="rate">播放速率，默认为1.0</param>
    /// <param name="volume"></param>
    public void PlayAvatarTTS(AudioClip audioClip, float rate = 1f, float volume = 1f)
    {
        _avatarRoot?.PlayAvatarTTS(audioClip, rate, volume);
    }

    /// <summary>
    /// 传入完整路径.
    /// </summary>
    /// <param name="url"></param>
    /// <param name="volume"></param>
    public void PlayAvatarTTS(string url,float volume = 1f)
    {
        _avatarRoot?.PlayAvatarTTS(url, volume);
    }

    /// <summary>
    /// 停止Avatar TTS音频播放
    /// </summary>
    public void StopAvatarTTS()
    {
        _avatarRoot?.StopAvatarTTS();
    }
    
} 