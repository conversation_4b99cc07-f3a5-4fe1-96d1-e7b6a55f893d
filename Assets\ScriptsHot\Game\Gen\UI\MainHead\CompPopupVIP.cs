/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.MainHead
{
    public partial class CompPopupVIP : UIBindT
    {
        public override string pkgName => "MainHead";
        public override string comName => "CompPopupVIP";

        public GImage arrow;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            arrow = (GImage)com.GetChildAt(0);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            arrow = null;
        }
    }
}