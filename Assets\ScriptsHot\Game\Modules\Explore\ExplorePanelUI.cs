﻿using System;using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using FairyGUI;
using Modules.DataDot;
using Msg.economic;
using Msg.explore;
using Msg.incentive;
using ScriptsHot.Game.Modules.Explore;
using ScriptsHot.Game.Modules.Explore.State.Base;
using ScriptsHot.Game.Modules.Explore.UI;
using ScriptsHot.Game.Modules.ExploreSettlement;
using ScriptsHot.Game.Modules.Scene.Level;
using ScriptsHot.Game.Modules.Scene.Level.Component;
using ScriptsHot.Game.Modules.Shop;
using UIBind.Explore;
using UIBind.Explore.Item;
using UnityEngine;

public enum ExploreExperienceState
{
     Normal,
     Vip,
     TimeDown,
     WaitOver,
     Over,
     Forever,
}

public enum ExplorePageType
{
    Mission,
    Friend,
}

public partial class ExplorePanelUI: BaseUI<ExplorePanel>
{
    public override void OnBackBtnClick()
    {
    }

    public override string uiLayer => UILayerConsts.Top;
    protected override bool isFullScreen => true;

    private GList _list;
    
    private int currentIndex = 0;
    private int _currentDisplayIndex = -1;  // 记录当前显示的Item索引

    private ExploreController _controller;
    
    private SceneController _sceneController;
    private Level _gameScene;

    private GameObject _rtContainer;

    private Dictionary<int, ExploreItemUI> _items = new Dictionary<int, ExploreItemUI>();

    /// <summary>
    /// 向上滑动
    /// </summary>
    private bool _UpDrag = true;

    private float _lastOnShowTime = -1f;
    private const float OnShowInterval = 0.5f; // 0.5秒

    public bool IsCreated = false;

    private int _secondLastTime = 0;
    /// <summary>
    /// 体验模式剩余时间
    /// </summary>
    private int _experienceLastTime = 0;

    /// <summary>
    /// 是否是会员
    /// </summary>
    private bool _isVip = false;
    
    /// <summary>
    /// 体验状态
    /// </summary>
    private ExploreExperienceState _experienceState = ExploreExperienceState.Normal;

    // 上一次Update的时间
    private float _lastUpdateTime = -1f;  
    private int _realIntervalMs = 0;

    private string _payNoticekey = "payNoticeOpen";
    
    /// <summary>
    /// 页签Tab
    /// </summary>
    private ExplorePageType _pageType = ExplorePageType.Mission;

    //用于打点
    private string _beforeMoveState = ExploreStateName.Start;
    public ExplorePanelUI(string name) : base(name)
    {
    }

    private void AddEvent()
    {
        Notifier.instance.RegisterNotification(NotifyConsts.RefreshExploreData,OnRefreshExploreList);
        Notifier.instance.RegisterNotification(NotifyConsts.RefresUINet,OnRefreshExploreNet);
        Notifier.instance.RegisterNotification(NotifyConsts.MainTabChange,OnMainTabChange);
        Notifier.instance.RegisterNotification(NotifyConsts.ExploreChatFristAvatarShow,OnShowFirstAvatarCell);
        Notifier.instance.RegisterNotification(NotifyConsts.ExploreBGMopen,OnOpenBGM);
        Notifier.instance.RegisterNotification(NotifyConsts.MainTabIndexBack,OnMainTabSetIndex);
        Notifier.instance.RegisterNotification(NotifyConsts.ShopInfoUpdate,OnShopInfoUpdate);
        Notifier.instance.RegisterNotification(NotifyConsts.ExploreLookAtStory,OnExploreLookAtStory);
        Notifier.instance.RegisterNotification(NotifyConsts.ExploreLookAtNextStory,OnExploreLookAtNextStory);
        Notifier.instance.RegisterNotification(NotifyConsts.RefreshExploreDataAndEnter,OnRefreshExploreDataAndEnter);
        Notifier.instance.RegisterNotification(NotifyConsts.ExploreEndDataRefresh,OnExploreEndDataRefresh);
        Notifier.instance.RegisterNotification(NotifyConsts.ExploreTitleTooolVisible,OnExplorePayNotice);
        
        //推荐下行 - 切换推荐实体
        MsgManager.instance.RegisterCallBack<SC_RecommendSwitchEntity>(this.OnRecommendSwitchEntity);
        
        MsgManager.instance.RegisterCallBack<SC_GetIncentiveDataForExploreAck>(this.OnGetIncentiveDataForExploreAck);

        AddFriendEvent();
    }

    //这部分rm调用先不适用，只保留一次add
    private void RemoveEvent()
    {
        Notifier.instance.UnRegisterNotification(NotifyConsts.RefreshExploreData,OnRefreshExploreList);
        Notifier.instance.UnRegisterNotification(NotifyConsts.RefresUINet,OnRefreshExploreNet);
        Notifier.instance.UnRegisterNotification(NotifyConsts.MainTabChange,OnMainTabChange);
        Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreChatFristAvatarShow,OnShowFirstAvatarCell);
        Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreBGMopen,OnOpenBGM);
        Notifier.instance.UnRegisterNotification(NotifyConsts.MainTabIndexBack,OnMainTabSetIndex);
        Notifier.instance.UnRegisterNotification(NotifyConsts.ShopInfoUpdate,OnShopInfoUpdate);
        Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreLookAtStory,OnExploreLookAtStory);
        Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreLookAtNextStory,OnExploreLookAtNextStory);
        Notifier.instance.UnRegisterNotification(NotifyConsts.RefreshExploreDataAndEnter,OnRefreshExploreDataAndEnter);
        Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreEndDataRefresh,OnExploreEndDataRefresh);
        Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreTitleTooolVisible,OnExplorePayNotice);
        
        //推荐下行 - 切换推荐实体
        MsgManager.instance.UnRegisterCallBack<SC_RecommendSwitchEntity>(this.OnRecommendSwitchEntity);
        
        MsgManager.instance.UnRegisterCallBack<SC_GetIncentiveDataForExploreAck>(this.OnGetIncentiveDataForExploreAck);
        
        RemoveFriendEvent();
    }

  

    /// <summary>
    /// 跳转到指定位置
    /// </summary>
    /// <param name="s"></param>
    /// <param name="body"></param>
    private void OnExploreLookAtStory(string s, object body)
    {
        int index = (int)body;
        // Debug.LogError("跳转到指定位置:" + index);
        _currentDisplayIndex = -1; //保证刷新所有数据
        lastFirstIndex = -1;
        List<ExploreItemData> datas = _controller.Model.allMissionData;
        _list.numItems = datas.Count;
        _list.RefreshVirtualList();
        TimerManager.instance.RegisterNextFrame((a) =>
        {
            _list.scrollPane.pageMode = false;
            // 精确滚动
            _list.ScrollToView(index, false,true);

            _list.scrollPane.pageMode = true;
            TimerManager.instance.RegisterNextFrame((aa) =>
            { 
                OnScrollEnd();
            });
        });
    }
    
    /// <summary>
    /// 滚动到下一个
    /// </summary>
    /// <param name="s"></param>
    /// <param name="body"></param>
    private void OnExploreLookAtNextStory(string s, object body)
    {
        int curIndex = _list.GetFirstChildInView();
        
        _list.scrollPane.pageMode = false;
        _list.ScrollToView(curIndex + 1,false,true);
        _list.scrollPane.pageMode = true;

        TimerManager.instance.RegisterNextFrame((a) =>
        {
            OnScrollEnd();
        });
    }

    /// <summary>
    /// 支付完成 商店状态变化
    /// </summary>
    /// <param name="s"></param>
    /// <param name="body"></param>
    private void OnShopInfoUpdate(string s, object body)
    {
        Debug.Log("商店状态变化,支付成功");
        AskLastTime();
    }

    private void OnGetIncentiveDataForExploreAck(SC_GetIncentiveDataForExploreAck msg)
    {
        VFDebug.Log("收到服务器时间回复: second:" + msg.data.economic_info.remaining_seconds +  "     member_type:" + msg.data.economic_info.member_info.member_type + "  begin:" +msg.data.economic_info.is_consume_time);
        _experienceLastTime = msg.data.economic_info.remaining_seconds;
        _controller.IsConsumeTime = msg.data.economic_info.is_consume_time;
        _isVip = msg.data.economic_info.member_info.is_member;

        _controller.IsFreeTime = _experienceLastTime < 0;
        //test
        // _experienceLastTime = -1;
        //test end
        if (_experienceLastTime < 0)
        {
            _experienceState = ExploreExperienceState.Forever;
            this.ui.ctrlPay.selectedPage = "none";
            return;
        }

        ShowTime();
        if (_isVip)
        {
            this.ui.ctrlPay.selectedPage = "vip"; 
            _experienceState = ExploreExperienceState.Vip;
            
            _controller.RecordLock = false;
            
            HomepageController hCon = ControllerManager.instance.GetController<HomepageController>(ModelConsts.Homepage) as HomepageController;
            RecordUI recUI = this.GetUI<RecordUI>(UIConsts.RecordUI);
            
            if (hCon.IsExploreTab() && recUI.isShow  )
            {
                //isShow判定依旧缺少语义
                //如果会话已经完成or已隐藏时,就不要再做 cancel行为了
                //todo OnCancelRecored 如果作为含状态逻辑切换的能力，是应该具备一定的逻辑切换限制的，现在把限制暴露在外侧本身并不利于未来重构
                var status =recUI.FetchRecordStatus();
                bool notAllowCancel = status == RecordUIStatus.RecordUIStatusFinish   || status == RecordUIStatus.RecordUIStatusFinishAndRepeat ||
                                   status == RecordUIStatus.RecordUIStatusNone ;
                if (!notAllowCancel)
                {
                    _controller.OnCancelRecored();
                }
            }
        }
        else
        {
            if (_experienceLastTime > 0)
            {
                _controller.SaveFlag(_payNoticekey,false);
                _experienceState = ExploreExperienceState.TimeDown;
                this.ui.ctrlPay.selectedPage = "timeDown"; 
            }
            else
            {
                _experienceState = ExploreExperienceState.Over;
                TimeOver();
                
                ExperienceTimeOver();
            }
        }
        
        UpdateBySecond();
    }

    private void TimeOver()
    {
        this.ui.ctrlPay.selectedPage = "timeOver";
        this.ui.txtTime.textFormat.size = 28;
        this.ui.txtTime.SetKey("ui_profile_unlock");
    }
    private void OnMainTabSetIndex(string s, object body)
    {
        HomepageController hCon = ControllerManager.instance.GetController<HomepageController>(ModelConsts.Homepage) as HomepageController;
        if (!hCon.IsExploreTab())
        {
            OutExploreTab();
        }
    }

    protected override void OnInit(GComponent uiCom)
    {
        _controller = ControllerManager.instance.GetController<ExploreController>(ModelConsts.Explore) as ExploreController;
        
        //惯性滚动最短时间（原 0.3f）
        ScrollPane.TWEEN_TIME_DEFAULT = 0.25f;
        //翻页动画时间（原 0.3f）
        ScrollPane.TWEEN_TIME_GO = 0.25f;
        //缓慢拖动超过一半 → 翻页
        UIConfig.defaultScrollPagingThreshold = 0.5f;
        _list = ui.listContainer;
        _list.SetVirtual();
        _list.itemRenderer = RenderItem;
        _list.scrollPane.snapToItem = true; 
        _list.scrollPane.pageMode = true;   
        //降低惯性，让短距离滑动也能翻页
        _list.scrollPane.decelerationRate = 0.5f;  
        //设置滚动步长，避免滑动太大翻多页
        _list.scrollPane.scrollStep = _list.scrollPane.viewHeight / 7; 
        _list.scrollPane.onScrollEnd.Add(OnScrollEnd);

        //这个延迟是 给ui.imgBG 留时间
        TimerManager.instance.RegisterNextFrame((a) =>
        {
            //按注释 有些分辨率顶部会有白边
            _list.y = ui.imgBG.y;//-Screen.safeArea.y - ExploreConst.ScreenOffsetHeight;
            _list.height = GRoot.inst.height + ExploreConst.ScreenOffsetHeight;//高度不能等于imgBG的高 不知道为啥
        });
        _rtContainer = GameObject.Find("RenderTextureContainer");
        
        this._list.scrollPane.onScroll.Add(scrolled);
        
        ui.btnSetting.onClick.Set(OnBtnSettingClicked);
        ui.btnPay.onClick.Set(OnClickBtnPay);
        ui.btnVip.onClick.Set(OnBtnVip);
        
        // ui.btnMissions.com.onClick.Set(OnMissionTab);
        // ui.btnFriends.com.onClick.Set(OnFriendTab);
        ui.btnMissions.txt.SetKey("ui_explore_top_tab_story");
        ui.btnFriends.txt.SetKey("ui_explore_top_tab_friend");
        ui.btnMissions.com.visible = false;
        ui.btnFriends.com.visible = false;
        ui.btnClose.visible = false;
        SetHandVisible(false);

        InitFriendUI();
        this.AddEvent();
        SetTitleToolVisible(false);
        // ui.com.height = GRoot.inst.height + ExploreConst.ScreenOffsetHeight;
    }

    private void OnExplorePayNotice(string s, object body)
    {
        SetTitleToolVisible((bool)body);
    }
    private void SetTitleToolVisible(bool value)
    {
        ui.comTime.visible = value;
        ui.btnSetting.visible = value;
    }

    private void OnMissionTab()
    {
        if (ui.btnMissions.btnCtrl.selectedPage == "Select") return;
        TypeChange(ExplorePageType.Mission);
    }
    
    private void OnFriendTab()
    {
        if (ui.btnFriends.btnCtrl.selectedPage == "Select") return;
        TypeChange(ExplorePageType.Friend);
    }

    private void TypeChange(ExplorePageType type)
    {
        string dtName = String.Empty;
        _pageType = type;
        ui.ctrlType.selectedIndex = (int)type;
        if (type == ExplorePageType.Mission)
        {
            dtName = "missions";
            ui.btnMissions.btnCtrl.selectedPage = "Select";
            ui.btnFriends.btnCtrl.selectedPage = "Normal";
        }
        else
        {
            dtName = "friends";
            ui.btnMissions.btnCtrl.selectedPage = "Normal";
            ui.btnFriends.btnCtrl.selectedPage = "Select";
        }
        
        CLICK_EXPLORE_SWITCH_BAR_TAB dt1 = new CLICK_EXPLORE_SWITCH_BAR_TAB();
        dt1.explore_tab = dtName;
        DataDotMgr.Collect(dt1);
    }

    private void OnBtnSettingClicked(EventContext context)
    {
        Notifier.instance.SendNotification(NotifyConsts.OpenUI,UIConsts.ExploreSettingPanelUI);
    }
    
    private void OnClickBtnPay()
    {
        if (_experienceLastTime > 0)
        {
            PayWallDotHelper.LastSourcePage = PaywallEntrySourceType.explore_countdown;
            CLICK_EXPLORE_MAIN_PAGE_COUNTDOWN_ICON dt1 = new CLICK_EXPLORE_MAIN_PAGE_COUNTDOWN_ICON();
            dt1.countdown_time = _experienceLastTime;
            DataDotMgr.Collect(dt1);
        }
        else
        {
            PayWallDotHelper.LastSourcePage = PaywallEntrySourceType.explore_unlock;
            CLICK_EXPLORE_MAIN_PAGE_UNLOCK_ICON dt1 = new CLICK_EXPLORE_MAIN_PAGE_UNLOCK_ICON();
            DataDotMgr.Collect(dt1);
        }

        Notifier.instance.SendNotification(NotifyConsts.OpenUI, new UIManager.UIParams() {viewName = UIConsts.SpeakPlanBottomSubscribeUI, param = 1});
    }

    private void OnTriggerPayPopUp()
    {
        PayWallDotHelper.LastSourcePage = PaywallEntrySourceType.explore_countdown_end;
        //自动用时耗尽时 不触发任何 click类埋点，老老实实等后续的appear的
        Notifier.instance.SendNotification(NotifyConsts.OpenUI, new UIManager.UIParams() {viewName = UIConsts.SpeakPlanBottomSubscribeUI, param = 1});
    }

    private void OnBtnVip()
    {
        var dot = new DotClickMembershipIcon();
        // dot.source_page = "explore";
        // dot.member_status = DotMemberStatusEnum.member.ToString();
        DataDotMgr.Collect(dot);
        Notifier.instance.SendNotification(NotifyConsts.OpenUI, new UIManager.UIParams() {viewName = UIConsts.SpeakPlanPromotionStep1UI, param = SpeakShowType.Explore});
    }

    #region 思明处理相机
    
    void scrolled(EventContext context)
    {
        if(_controller.CurEnterEntity != null && _controller.CurEnterEntity.LogicEntity.GetCurState() != null)
            _beforeMoveState = _controller.CurEnterEntity.LogicEntity.GetCurState().name;
        // Debug.LogError("scrolled-----------------------" + _beforeMoveState);
        ClearGuideInfo();
        var y = _list.scrollPane.scrollingPosY;
        UpdateScrollDirection(_list);
    }

    //注意：现在一屏只有一个元素，后续入果一屏多个元素，需要基于元素个数甚至单个元素大小来维护此list。
    void UpdateItemToRender()
    {
        int startIndex = _list.GetFirstChildInView();
        _itemToRender.Clear();
        
        if (_isDragging)
        {
            if (_isScrollingDown)
            {
                _itemToRender.Add(startIndex);
                _itemToRender.Add(startIndex + 1);
                //show 2 cams
            }
            else
            {
                _itemToRender.Add(startIndex);
                _itemToRender.Add(startIndex + 1);
                //show 2 cams
            }
        }
        else
        {
            _itemToRender.Add(startIndex);
            //show 1 cam
        }
    }

    void Update3DRendering()
    {
        HomepageController hCon = ControllerManager.instance.GetController<HomepageController>(ModelConsts.Homepage) as HomepageController;
        if (hCon.IsExploreTab())
        {
            UpdateItemToRender();
            foreach (var kvp  in _exploreItemDic)
            {
                if (kvp.Key != null)
                {
                    if (_itemToRender.Contains(kvp.Value))
                    {
                        kvp.Key.SetCamera(true);
                        kvp.Key.SetAvatar(true);
                    }
                    else
                    {
                        kvp.Key.SetCamera(false);
                        kvp.Key.SetAvatar(false);
                    }
                }
            }
        }
        else
        {
            foreach (var kvp  in _exploreItemDic)
            {
                if (kvp.Key != null)
                {
                    kvp.Key.SetCamera(false,999);
                    kvp.Key.SetAvatar(false,999);
                }
            }
        }
        
    }
    
    Dictionary<ExploreItemUI,int> _exploreItemDic = new();
    List<int> _itemToRender = new List<int>(); //要渲染的index
    float _lastScrollPosition = 0;
    bool _isScrollingDown = false;
    bool _isDragging = false;
    

    void UpdateScrollDirection(GList list)
    {
        float currentPos = list.scrollPane.scrollingPosY;
        _isScrollingDown = currentPos > _lastScrollPosition;
        _lastScrollPosition = currentPos;
    
    //    Debug.Log($"滚动方向: {(_isScrollingDown ? "向下" : "向上")}");
    }
    
    #endregion

    private async void RenderItem(int index, GObject obj)
    {
        // 如果是当前显示的Item，跳过刷新
        if (index == _currentDisplayIndex)
        {
            return;
        }
        GComponent modelItem = obj.asCom;
        List<ExploreItemData> datas = _controller.Model.allMissionData; 
         // Debug.LogError("Index:::" + index + "     entityId ::" + datas[index].Data.entityId + "  avatarId:::" + datas[index].Data.dialogTaskPreloadData.avatar.avatarId);

        if (modelItem.data == null)
        {
            ExploreItemUI uiItem = new ExploreItemUI(modelItem, _controller);
            uiItem.SetUI(this.ui.com,ui.listContainer);
            modelItem.data = uiItem;
        }
        datas[index].HasLook = true;
        ExploreItemUI modelItemEx = modelItem.data as ExploreItemUI;
        _items[index] = modelItemEx;
        modelItemEx.SetData(datas[index].Data);
        modelItemEx.UpdateTitle(datas[index].Data.avatar);
        modelItemEx.UpdateDescribe(datas[index].Data.detail,datas[index].Data.storyId);

        if (_exploreItemDic.ContainsKey(modelItemEx))
        {
            //循环列表重用，重设index
            _exploreItemDic[modelItemEx] = index;
        }
        else
        {
            //新增
            _exploreItemDic.Add(modelItemEx,index);
        }

        bool is3d = true;//datas[index].Data.avatar.is3D;
        modelItemEx.SetIs3d(is3d);
        if (is3d)
        {
           // Debug.LogError("+渲染了物体"+ index);
            modelItemEx.SetModel(_gameScene,datas[index].Data.storyId,datas[index].Data.avatar.avatarId,datas[index].Data.scene.bgPicTag);  // 绑定新模型
            await modelItemEx.LoadBackgroundImage(datas[index].Data.scene.bgPicTag);

            modelItemEx.Intro.SetVisible(false);
            if (datas[index].Data.taskIntro.Count > 0)
            {
                // Debug.LogError("预载 taskid::::::::" + datas[index].Data.taskId);
                ExploreIntroCfg cfg = Cfg.T.TBExploreIntro.GetOrDefault(datas[index].Data.taskId.ToString());
                //test
                // if(cfg == null)
                    // cfg = Cfg.T.TBExploreIntro.GetOrDefault("1944663105032294400");
                //test end
                if (cfg != null)
                {
                    await modelItemEx.LoadIntroImage(cfg.introBack2);
                    await modelItemEx.LoadIntroImage(cfg.introBack1);
                    modelItemEx.Intro.SetData(datas[index].Data);
                }
            }
        }
        else
        {
            modelItemEx.ShowImg2d(datas[index].Data.scene.bgPicUrl);
        }
    }
    
    /// <summary>
    /// 收到结算数据刷新
    /// </summary>
    /// <param name="s"></param>
    /// <param name="body"></param>
    private void OnExploreEndDataRefresh(string s, object body)
    {
        //用来处理 数据不刷新问题
        _currentDisplayIndex = -1;
    }
    
    private void OnRefreshExploreNet(string s, object body)
    {
        //点击weak network提示的retry
        CLICK_EXPLORE_WEAK_NETWORK_RETRY data = new CLICK_EXPLORE_WEAK_NETWORK_RETRY();
        DataDotMgr.Collect(data);
        
        //数据会在 ExploreController。OnNetReConn 中获取 ，这里只负责刷新，刷新时候还是没有数据 就继续保持提示界面
        UpdateData();
    }

    private bool _hasShow = false;
    private string _updateTimeFlag = String.Empty;
    protected override void OnShow()
    {
        float now = Time.realtimeSinceStartup;
        if (_lastOnShowTime > 0 && now - _lastOnShowTime < OnShowInterval)
        {
            // Debug.LogError("OnShow 调用过于频繁，已拦截。");
            return;
        }
        _lastOnShowTime = now;
        
        _controller.StartHistory();

        // Debug.LogError("ExplorePanelUI OnShow");
        _inExploreTab = true;
        PayWallDotHelper.LastRootPage = PaywallEntryRootPage.explore;//panel 入口埋点
        
        if (_hasShow)
        {
            //-------------应该不会走到这里了  tab 多次触发界面打开问题 唐雷已经处理
            //处理 没有数据时候 tab切换的情况 ，Net重连界面显示
            List<ExploreItemData> datas = _controller.Model.allMissionData;
            if (datas.Count <= 0)
            {
                UpdateData();
            }
            if (_items.TryGetValue(lastFirstIndex, out ExploreItemUI modelItemEx))
            {
                modelItemEx.DoDescribe();
            } 
            return;
        }

        Notifier.instance.SendNotification(NotifyConsts.ExploreEnter);
        
        _hasShow = true;
        
        _sceneController = this.GetController<SceneController>(ModelConsts.Scene);
        _gameScene = _sceneController.scene;
        UpdateData(true);

        _updateTimeFlag = TimerManager.instance.RegisterTimer((a) => { Update(a);}, 10,0);
        this.ui.ctrlPay.selectedPage = "none";
        AskLastTime();
        
        // ServerPoint();
        
        APPEAR_EXPLORE_MAIN_PAGE data = new APPEAR_EXPLORE_MAIN_PAGE();
        DataDotMgr.Collect(data);
        
    }

    private void AskLastTime()
    {
        _experienceState = ExploreExperienceState.Normal;
        _controller.AskExperienceLastTime();
    }

    private void Update(int interval)
    {
        float now = Time.realtimeSinceStartup;
        if (_lastUpdateTime < 0)
        {
            _realIntervalMs = 0;
        }
        else
        {
            _realIntervalMs = (int)((now - _lastUpdateTime) * 1000f);
        }
        _lastUpdateTime = now;

        for (int i = 0; i < _items.Count; i++)
        {
            _items[i].Update(_realIntervalMs);
        }
        
        _isDragging =_list.scrollPane.isDragged;
        Update3DRendering();

        if (_secondLastTime <= 0)
        {
            _secondLastTime = 1000;
            UpdateBySecond();
        }
        else
        {
            _secondLastTime -= _realIntervalMs;
        }

        if (_experienceState == ExploreExperienceState.WaitOver)
        {
            if (_controller.RecordState != RecordState.Recording)
            {
                _experienceState = ExploreExperienceState.Over;
                ExperienceTimeOver();
            }
        }
        
        if (Input.anyKey || Input.GetMouseButton(0) || Input.touchCount > 0)
        {
            ClearGuideInfo();
        }
        else if (_list.scrollPane.isDragged)
        {
            ClearGuideInfo();
        }
    }
    
    private int idleTime = 0;
    private bool guideShow = false;

    private void UpdateNewHand()
    {
        if (guideShow) return;

        if (_controller.RecordState == RecordState.Recording)
        {
            ClearGuideInfo();
            return;
        }

        if (UIManager.instance.GetUI<ExploreMemoryPanelUI>(UIConsts.ExploreHistoryProgressListUI).isShow)
        {
            ClearGuideInfo();
            return;
        }
        
        if (UIManager.instance.GetUI<ExploreSettingPanelUI>(UIConsts.ExploreSettingPanelUI).isShow)
        {
            ClearGuideInfo();
            return;
        }
        
        if (UIManager.instance.GetUI<ExploreSettlementUI>(UIConsts.ExploreSettlementUI).isShow)
        {
            ClearGuideInfo();
            return;
        }
        
        if (UIManager.instance.GetUI<SpeakPlanPromotionStep1UI>(UIConsts.SpeakPlanPromotionStep1UI).isShow)
        {
            ClearGuideInfo();
            return;
        } 
        if (UIManager.instance.GetUI<SpeakPlanBottomSubscribeUI>(UIConsts.SpeakPlanBottomSubscribeUI).isShow)
        {
            ClearGuideInfo();
            return;
        }
        if (UIManager.instance.GetUI<SubscribeSuccessUI>(UIConsts.SubscribeSuccessUI).isShow)
        {
            ClearGuideInfo();
            return;
        }

        if (_controller.CurEnterEntity != null && _controller.CurEnterEntity.IsPlayAvatarAudio)
        {
            ClearGuideInfo();
            return;
        }


        idleTime += 1;

        if (idleTime > 18f)
        {
            guideShow = true;
            SetHandVisible(true);
        }
    }

    private void ClearGuideInfo()
    {
        idleTime = 0;
        if (guideShow == false) return;
        guideShow = false;
        
        Notifier.instance.SendNotification(NotifyConsts.CloseUI, UIConsts.ExploreNewhandPanelUI);
    }

    private void UpdateBySecond()
    {
        UpdateNewHand();
        if (this.ui.ctrlPay.selectedPage != "timeDown") return;

        if (_experienceState == ExploreExperienceState.Forever)
        {
            return;
        }
        if (!_controller.IsConsumeTime) return;

        if (_experienceLastTime > 0)
        {
            _experienceLastTime--;
            ShowTime();
        }
        else
        {
            TimeOver();
            _experienceState = ExploreExperienceState.WaitOver;
        }
    }

    private void ShowTime()
    {
        int minutes = _experienceLastTime / 60;
        int seconds = _experienceLastTime % 60;
        if (this.ui.txtTime.textFormat.size == 28)
        {
            this.ui.txtTime.textFormat.size = 32;
        }
        ui.txtTime.text = $"{minutes:D2}:{seconds:D2}";
    }

    private void ExperienceTimeOver()
    {
        HomepageController hCon = ControllerManager.instance.GetController<HomepageController>(ModelConsts.Homepage) as HomepageController;
        if (!hCon.IsExploreTab()) return;

        if (!_controller.GetFlag(_payNoticekey))
        {
            _controller.SaveFlag(_payNoticekey,true);
            
            //显示充值界面
            OnTriggerPayPopUp();
        }
        
        //屏蔽麦克风
        _controller.RecordLock = true;
        bool showBar = UIManager.instance.GetUI<MultiTabFrameworkUI>(UIConsts.MultiTabHomepage).CurrTabState ==
                       BottomTabState.showing;
        if (!showBar)
        {
            _controller.OnCancelRecored();
        }
    }

    private void UpdateData(bool isOnshow  = false)
    {
        List<ExploreItemData> datas = _controller.Model.allMissionData;
        _list.numItems = datas.Count;
        
        // 记录当前显示的Item索引  要放到 numItems之后执行 ，防止玩家 再对话结算面板出来之前 通过tab切走 再切回来， 当前界面不刷新问题
        _currentDisplayIndex = _list.GetFirstChildInView();
        
        VFDebug.Log($"Explore刷新数据  初始化数据个数  datas.Count：{datas.Count}");
        if (datas.Count > 0)
        {
            lastFirstIndex = _list.GetFirstChildInView();
            if (isOnshow) return;
            EnterEntity();
            PlayBGM(lastFirstIndex);
        }
        else
        {
            //出现weak network提示
            APPEAR_EXPLORE_WEAK_NETWORK data = new APPEAR_EXPLORE_WEAK_NETWORK();
            DataDotMgr.Collect(data);

            GComponent uiContainer = GetUI<MultiTabFrameworkUI>(UIConsts.MultiTabHomepage).SetUIContainerVisible(true);
            Notifier.instance.SendNotification(NotifyConsts.OpenUI,
                new UIManager.UIParams()
                {
                    viewName = UIConsts.NoNetworkPanelUI, 
                    param = new ContainerUI()
                    {
                        Container = uiContainer
                    }
                });
        }
    }

    private bool _inExploreTab = true;
    private void OnMainTabChange(string s, object body)
    {
        // VFDebug.LogError("OnMainTabChange::_inExploreTab::" + _inExploreTab);
        GameObject cameraContainer = GameObject.Find("CameraPool");
        //Light light = GameObject.Find("RTLight").GetComponent<Light>();
        
        TabData tabData = (TabData) body;

        Camera[] cameras = null;
        if (cameraContainer != null)
        {
            cameras = cameraContainer.GetComponentsInChildren<Camera>();
        }
        
        if (tabData.TargetIndex != TabIndex.Explore)
        {
            SetHandVisible(false);
            OutExploreTab();
            Notifier.instance.SendNotification(NotifyConsts.CloseUI,UIConsts.NoNetworkTipUI);
        }
        else
        {
            ClearGuideInfo();
            _inExploreTab = true;
            _hasShow = false;
            if (cameras != null)
            {
                foreach (var ca in cameras)
                {
                    ca.enabled = true;
                }
            }

            PlayBGM(_list.GetFirstChildInView());
            //TODO  这里 临时这么处理吧 ，解决 新号第一次进入 _list.height 赋值的时候  和执行这里 先后顺序问题
            //之后 解决android适配时候 list刷新问题要彻底解决  这里timer也要删除
            TimerManager.instance.RegisterNextFrame((a) =>
            { 
                EnterEntity();
            });
        }
    }

    private void OutExploreTab()
    {
        if (!_inExploreTab) return;
        //RemoveEvent();
        _inExploreTab = false;
        GameObject cameraContainer = GameObject.Find("CameraPool");
        if (cameraContainer != null)
        {
            Camera[] cameras = cameraContainer.GetComponentsInChildren<Camera>();
            
            if (cameras != null)
            {
                foreach (var ca in cameras)
                {
                    ca.enabled = false;
                }
            }

        }

        _controller.ClearSaffold();
        _controller.CloseRecordUI();
        Notifier.instance.SendNotification(NotifyConsts.procedure_main_break);
        Notifier.instance.SendNotification(NotifyConsts.ExploreSoundStop);
        GSoundManager.instance.Pause(SoundManger.SoundChannel.Scene,true);
        Notifier.instance.SendNotification(NotifyConsts.CloseUI,UIConsts.NoNetworkTipUI);
        ClearAllEntity();
        ExitAllEntity();
            
        RenderTexturePool.ClearPool();
    }

    /// <summary>
    /// 推荐下行 - 切换推荐实体
    /// </summary>
    /// <param name="msg"></param>
    private void OnRecommendSwitchEntity(SC_RecommendSwitchEntity msg)
    {
        // VFDebug.LogError($"推荐下行 - 切换推荐实体：{msg.timestamp}");
    }

    private void OnShowFirstAvatarCell(string s, object body)
    {
        
        //_controller.ClearFlag("NewhandDrag");
        // SetHandVisible(true);
    }

    private void SetHandVisible(bool value)
    {
        // Debug.LogError("SetHandVKisible::" + value);
        HomepageController hCon = ControllerManager.instance.GetController<HomepageController>(ModelConsts.Homepage) as HomepageController;
        if (!hCon.IsExploreTab()) return;
        if (value)
        {
            // if (_controller.GetFlag("NewhandDrag"))
            //     return;
            // _controller.SaveFlag("NewhandDrag", true);
            //--改为滑动结束消失
            TimerManager.instance.RegisterTimer(a =>
            {
                SetHandVisible(false);
            }, ExploreConst.NewhandDragTime);
            
            Notifier.instance.SendNotification(NotifyConsts.OpenUI, UIConsts.ExploreNewhandPanelUI);
        }
        else
        {
            Notifier.instance.SendNotification(NotifyConsts.CloseUI, UIConsts.ExploreNewhandPanelUI);
        }
    }

    private void OnRefreshExploreDataAndEnter(string s, object body)
    {
        // VFDebug.Log("刷新Explore 最新数据----------------------------------------------------------------");
        _currentDisplayIndex = -1; //保证刷新所有数据
        lastFirstIndex = -1;
        OnRefreshExploreList(string.Empty,null);
        OnScrollEnd();
    }
    private void OnRefreshExploreList(string s, object body)
    {
        // 记录当前滚动位置
        float oldPosY = _list.scrollPane.posY;
        _list.numItems = _controller.Model.allMissionData.Count;
        
        //刷新列表，但保持原有位置
        // _list.RefreshVirtualList();
        _list.scrollPane.posY = oldPosY;
    }

    private int lastFirstIndex = -1;
    private void OnScrollEnd()
    {
        // Debug.LogError("OnScrollEnd::" + _list.GetFirstChildInView());
        // **确保翻页才触发**
        int curIndex = _list.GetFirstChildInView();
        if (_currentDisplayIndex != curIndex)
        {
            _controller.HideRecordUI();
        }

        _currentDisplayIndex = curIndex;  // 更新当前显示的Item索引
        _controller.UpdateLookAtIndex(curIndex);
        if (curIndex == lastFirstIndex) return;
        SetUpMove(curIndex < lastFirstIndex);
        lastFirstIndex = curIndex;
    
        
        List<ExploreItemData> datas = _controller.Model.allMissionData;
        ModelPreloader.Instance.PreloadModels(datas, curIndex);
    
        PlayBGM(_list.GetFirstChildInView());
        long curTaskId = _controller.CurEntityId;
        EnterEntity();
        long nextTaskId = _controller.CurEntityId;
        DotDrag(curTaskId,nextTaskId);
        
        SetHandVisible(false);
        ClearGuideInfo();
        
        // ServerPoint();
    }

    private void SetUpMove(bool value)
    {
        _UpDrag = value;
    }

    private void DotDrag(long curTaskId, long nextTaskId)
    {
        bool showBar = _controller.GetUI<MultiTabFrameworkUI>(UIConsts.MultiTabHomepage).CurrTabState ==
                       BottomTabState.showing;
        SWIPE_EXPLORE_FEED data = new SWIPE_EXPLORE_FEED();
        data.previous_page_type = _beforeMoveState == ExploreStateName.ExploreMissionStart ? "intro" :"feed";
        data.swipe_side = _UpDrag ? "up" : "down";
        data.previous_task_id = curTaskId;
        data.target_task_id = nextTaskId;
        data.is_tab_bar = showBar?"appear":"hide";
        DataDotMgr.Collect(data);
    }

    private void OnOpenBGM(string s, object body)
    {
        bool open = (bool) body;
        if (open)
        {
            PlayBGM(_list.GetFirstChildInView());
        }
        else
        {
            GSoundManager.instance.Pause(SoundManger.SoundChannel.Scene,true);
        }
    }
    private void PlayBGM(int index)
    {
        if (_controller.Model.BackgroundMusic == PB_Explore_UserSetting_BackgroundMusic.EO_US_BM_OFF ) return;
        List<ExploreItemData> datas = _controller.Model.allMissionData;
        string bgm = datas[index].Data.scene.bgVoiceTag;
        // VFDebug.Log($"bgm + {bgm}   _list.index::{_list.GetFirstChildInView()}");
        SoundManger.instance.PlayBGM(bgm);
    }

    private void EnterEntity()
    {
        SetTitleToolVisible(false);
        Debug.Log("=== EnterEntity ===");
        //外层的延迟 是为了对应 OnInit 方法中 imgBg 刷新的延迟 
        //内层的times 是为了对应 OnInit 中 _list.height变化后 触发的GList RefreshVirtualList 的处理
        //这两个延迟 必不可少！
        TimerManager.instance.RegisterNextFrame((a) =>
        {
            Timers.inst.CallLater(a =>
            {
                // 因为 这里的Entity  不分 story还是task
                ServerPoint();
                _controller.ClearSaffold();
                ExitAllEntity();
                GSoundManager.instance.StopAvatarTTS();
                if (_items.TryGetValue(lastFirstIndex, out ExploreItemUI modelItemEx))
                {
                    modelItemEx.Enter();
                } 
            });
         
        });
    }

    private void ExitAllEntity()
    {
        foreach (var kv in _items)
        {
            kv.Value.Exit();
        }
    }
    
    private void ClearAllEntity()
    {
        foreach (var kv in _items)
        {
            kv.Value.LogicEntity.Clear();
        }
    }

    /// <summary>
    /// 给服务器打点使用 ，切换story
    /// </summary>
    private void ServerPoint()
    {
        if (_items.TryGetValue(lastFirstIndex, out ExploreItemUI modelItemEx))
        {
            long taskId = modelItemEx.Data.storyId;
            _controller.CurNetStrategy.SendRecommendReq(taskId,PB_Explore_RecommendEntityType.EO_RE_STORY);
            VFDebug.Log($"Explore ConectNet taskId::{taskId}  SendRecommendReq");
        }
    }

    protected override void OnHide()
    {
        base.OnHide();
        // Debug.LogError("ExplorePanelUI OnHide");
        _hasShow = false;
        //this.RemoveEvent();
        _items.Clear();
        if (_updateTimeFlag != String.Empty)
        {
            TimerManager.instance.UnRegisterTimer(_updateTimeFlag);
            _updateTimeFlag = String.Empty;
        }
    }
}


