/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreFriends
{
    public partial class ClosenessLevelUpCom : UIBindT
    {
        public override string pkgName => "ExploreFriends";
        public override string comName => "ClosenessLevelUpCom";

        public Controller ctrlLevel;
        public GGraph holder;
        public GTextField txtName;
        public GTextField txtDesc;
        public GList listContainer;
        public GTextField txtTitle;
        public GButton btnGoOn;
        public GGraph btnClose;
        public GGroup container;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ctrlLevel = com.GetControllerAt(0);
            holder = (GGraph)com.GetChildAt(0);
            txtName = (GTextField)com.GetChildAt(9);
            txtDesc = (GTextField)com.GetChildAt(10);
            listContainer = (GList)com.GetChildAt(11);
            txtTitle = (GTextField)com.GetChildAt(12);
            btnGoOn = (GButton)com.GetChildAt(13);
            btnClose = (GGraph)com.GetChildAt(15);
            container = (GGroup)com.GetChildAt(16);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ctrlLevel = null;
            holder = null;
            txtName = null;
            txtDesc = null;
            listContainer = null;
            txtTitle = null;
            btnGoOn = null;
            btnClose = null;
            container = null;
        }
    }
}