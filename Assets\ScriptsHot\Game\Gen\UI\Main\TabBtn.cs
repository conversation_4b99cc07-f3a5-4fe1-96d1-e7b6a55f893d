/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Main
{
    public partial class TabBtn : UIBindT
    {
        public override string pkgName => "Main";
        public override string comName => "TabBtn";

        public Controller iconType;
        public Controller redState;
        public GGraph clickRegion;
        public GImage courseTabBtn;
        public GImage exploreTabBtn;
        public GImage friendTabBtn;
        public GImage voicechatTabBtn;
        public GImage rankTabBtn;
        public GImage course3D;
        public GImage explore3D;
        public GImage friend3D;
        public GImage voicechat3D;
        public GImage rank3D;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            iconType = com.GetControllerAt(0);
            redState = com.GetControllerAt(1);
            clickRegion = (GGraph)com.GetChildAt(0);
            courseTabBtn = (GImage)com.GetChildAt(1);
            exploreTabBtn = (GImage)com.GetChildAt(2);
            friendTabBtn = (GImage)com.GetChildAt(3);
            voicechatTabBtn = (GImage)com.GetChildAt(4);
            rankTabBtn = (GImage)com.GetChildAt(5);
            course3D = (GImage)com.GetChildAt(7);
            explore3D = (GImage)com.GetChildAt(8);
            friend3D = (GImage)com.GetChildAt(9);
            voicechat3D = (GImage)com.GetChildAt(10);
            rank3D = (GImage)com.GetChildAt(11);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            iconType = null;
            redState = null;
            clickRegion = null;
            courseTabBtn = null;
            exploreTabBtn = null;
            friendTabBtn = null;
            voicechatTabBtn = null;
            rankTabBtn = null;
            course3D = null;
            explore3D = null;
            friend3D = null;
            voicechat3D = null;
            rank3D = null;
        }
    }
}