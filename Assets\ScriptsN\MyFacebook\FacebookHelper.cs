using System.Collections;
using System.Collections.Generic;
using Facebook.Unity;
using UnityEngine;

public partial class FacebookHelper : MonoBehaviour
{
    public static FacebookHelper Ins;
    private void Awake()
    {
        Ins = this;
        DontDestroyOnLoad(this);
    }

    public void Init()
    {
        if (!FB.IsInitialized)
        {
            FB.Init(_OnInitComplete);
            _LogText($"FB.Init called FB.AppId = {Facebook.Unity.Settings.FacebookSettings.AppId}");
        }
        else
        {
            FB.ActivateApp();
        }
    }

    public void OnLogAcessTokenBtnClick()
    {
        AccessToken currentToken = AccessToken.CurrentAccessToken;
        if (currentToken == null)
        {
            _LogText("CurrentAccessToken is empty");
        }
        else
        {
            _LogText("CurrentAccessToken: " + currentToken.ToString());
        }
    }
    public void OnLogPayloadBtnClick()
    {
        _LogText("Getting Payload");
        FBGamingServices.GetPayload(delegate (IPayloadResult result)
        {
            _LogText("payload\n" + JsonUtility.ToJson(result.Payload));
        });
    }

    // private
    private void _LogText(string text)
    {
        Debug.Log($"=========== {text}");
    }
    private void _OnInitComplete()
    {
        if (FB.IsInitialized)
        {
            _LogText("FB.Init Successful;");
            FB.ActivateApp();

            // FBGamingServices.InitCloudGame(_OnInitCloud);


            //OnLogAcessTokenBtnClick();
            //OnLogPayloadBtnClick();
        }
        else
        {
            _LogText("ERR: FB.Init failed");
        }
    }
    private void _OnInitCloud(IInitCloudGameResult result)
    {
        if (result.Error == null && result.ResultDictionary != null)
        {
            _LogText("InitCloudGame Successful");
        }
        else
        {
            _LogText("ERR: InitCloudGame failed\n" + result.Error.ToString());
        }
    }

    
    public static void SendEvent(string EventName,Dictionary<string,string> eventValues = null)
    {
        if (eventValues == null)
            eventValues = new Dictionary<string, string>();
        //AddBaseDatas(ref eventValues);

        // Facebook埋点
        if (FB.IsInitialized)
        {
            // 转换为参数列表
            var parameters = new Dictionary<string, object>();
            foreach (var kv in eventValues)
            {
                parameters[kv.Key] = kv.Value;
            }
            FB.LogAppEvent(EventName, null, parameters);
            Debug.Log($"--------------------facebookHelper -----  SendEvent --- EventName = {EventName}");
        }
        else
        {
            Debug.LogWarning("Facebook SDK未初始化，无法上报埋点事件: " + EventName);
        }
        


    }
    
    private static void AddBaseDatas(ref Dictionary<string,string> eventValues)
    {
  
    }
}
