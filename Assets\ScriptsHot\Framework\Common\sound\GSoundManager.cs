using Cysharp.Threading.Tasks;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Audio;
using YooAsset;
public class GSoundManager : MonoSingleton<GSoundManager>
{
    public const int POOL_CHECK_TIME = 30;
    public const int MAX_POOL_SIZE = 20;
    public const float LOOP_FLAG = 9999f;

    private Dictionary<string, AudioSource> _channels = new Dictionary<string, AudioSource>();
    private Dictionary<string, bool> _channelPauseMap = new Dictionary<string, bool>();

    private Dictionary<string, SoundItem> _pool = new Dictionary<string, SoundItem>();
    private float _lastFreeTime = 0;

    private AudioSource _currAvatarAudioSource = null;
    public AudioSource CurAvatarAudioSource => _currAvatarAudioSource;
    private AudioSource _curFlowAudioSource = null;

    private AudioStreamPlayer _audioStreamPlayer = null;
    public AudioStreamPlayer AudioStreamPlayer => _audioStreamPlayer;

    //口型插件
    private OVRLipSyncContext _oVRLipSyncContext = null;
    public OVRLipSyncContext OVRLipSyncContext => _oVRLipSyncContext;

    //使用口形
    public bool LinkOVRLip = true;

    /// <summary>
    /// 流式声音是否有口形
    /// </summary>
    public bool StreamAudioLinkAvatar = true;
    protected override async UniTask OnInit()
    {
        AudioSource[] channels = this.GetComponentsInChildren<AudioSource>();
        foreach(var audioSource in channels)
        {
            this._channels[audioSource.gameObject.name] = audioSource;
            this._channelPauseMap[audioSource.gameObject.name] = false;
        }
        await base.OnInit();        
    }

    public AudioSource[] GetChannels()
    {
        return GetComponentsInChildren<AudioSource>();
    }

    public AudioSource GetChannel(string channel)
    {
        if (this._channels.ContainsKey(channel))
            return this._channels[channel];
        GameObject go = new GameObject(channel);
        AudioSource audioSource = go.AddComponent<AudioSource>();
        go.transform.parent = this.gameObject.transform;
        this._channels[channel] = audioSource;
        return audioSource;
    }

    public void SetMute(string channel,bool mute)
    {
        if(channel == "All")
        {
            foreach (var _audioSource in this._channels.Values)
                _audioSource.mute = mute;
            return;
        }
        AudioSource audioSource = this.GetChannel(channel);
        audioSource.mute = mute;
    }

    public bool GetMute(string channel)
    {
        AudioSource audioSource = this.GetChannel(channel);
        return audioSource.mute;
    }

    public void SetVolume(string channel, float volume = 1f)
    {
        if (channel == "All")
        {
            foreach (var _audioSource in this._channels.Values)
                _audioSource.volume = volume;
            return;
        }
        AudioSource audioSource = this.GetChannel(channel);
        audioSource.volume = volume;
    }

    public void Pause(string channel,bool isPause)
    {
        if (channel == "All")
        {
            foreach (var _audioChannel in this._channels.Keys)
            {
                AudioSource _audioSource = this._channels[_audioChannel];
                this._channelPauseMap[_audioChannel] = isPause;
                if (isPause)
                    _audioSource.Pause();
                else
                    _audioSource.UnPause();
            }
            return;
        }
        AudioSource audioSource = this.GetChannel(channel);
        if (isPause)
            audioSource.Pause();
        else
            audioSource.UnPause();
        this._channelPauseMap[channel] = isPause;
    }

    public void Play(string channel,string name,bool isLoop = true,float delay = 0f, float volume = -1f)
    {
        if (this._channelPauseMap[channel])
            return;
        AudioSource audioSource = this.GetChannel(channel);

        if (volume >= 0f && audioSource.volume != volume)
            audioSource.volume = volume;

        this.GetAudioClip(name, audioClip =>
        {
            audioSource.loop = isLoop;
            audioSource.clip = audioClip;
            if (delay > 0)
                audioSource.PlayDelayed(delay);
            else
                audioSource.Play();
        }, isLoop, delay);
    }

    public void PlayOneShot(string channel, string name,float delay = 0f, float volume = 1f)
    {
        if (this._channelPauseMap[channel])
            return;
        AudioSource audioSource = this.GetChannel(channel);
        if (audioSource.mute)
            return;
        this.GetAudioClip(name, audioClip =>
        {
            if (delay > 0)
                StartCoroutine(PlayDelayedOneShot(audioSource, audioClip, delay, volume));
            else
                audioSource.PlayOneShot(audioClip, volume);
        }, false, delay);
    }

    private IEnumerator PlayDelayedOneShot(AudioSource audioSource, AudioClip audioClip, float delay,float volume)
    {
        yield return new WaitForSeconds(delay);
        audioSource.PlayOneShot(audioClip, volume);
    }

    public bool IsPlaying(string channel, string url)
    {
        AudioSource audioSource = this.GetChannel(channel);
        if (!audioSource.isPlaying)
        {
            return false;
        }

        if (!this._pool.ContainsKey(url))
        {
            return false;
        }

        var clip = this._pool[url].audioClip;
        return audioSource.clip == clip;
    }

    public bool IsPlaying(string channel)
    {
        if (channel == "Avatar")
        {
            if (_currAvatarAudioSource != null)
                return _currAvatarAudioSource.isPlaying;
            return false;
        }

        if (channel == "Flow")
        {
            if (_curFlowAudioSource != null)
                return _curFlowAudioSource.isPlaying;
            return false;
        }
        AudioSource audioSource = this.GetChannel(channel);
        return audioSource.isPlaying;
    }
    
    private void GetAudioClip(string url, Action<AudioClip> action, bool isLoop = false, float delay = 0f)
    {
        if (this._pool.ContainsKey(url))
        {
            SoundItem nowItem = this._pool[url];
            float length = nowItem.audioClip.length;
            if (isLoop)
                length = LOOP_FLAG;
            length += delay;
            nowItem.timeList.Add(Time.time + length);
            action(nowItem.audioClip);
            return;
        }
        SoundItem item = new SoundItem();
        item.url = url;
        GResManager.instance.LoadAudio(url, (result, handle) =>
        {
            AudioClip audioClip = result as AudioClip;
            if (audioClip == null)
                return;
            float length = audioClip.length;
            if (isLoop)
                length = LOOP_FLAG;
            length += delay;
            item.timeList.Add(Time.time + length);
            item.audioClip = audioClip;
            item.assetHandle = handle;
            action(audioClip);
            this._pool[url] = item;
        });
    }

    private void Update()
    {
        if (Time.time - this._lastFreeTime < POOL_CHECK_TIME)
            return;
        this._lastFreeTime = Time.time;
        this.AutoFree();
    }

    private void AutoFree()
    {
        int cnt = this._pool.Count;
        if (cnt < MAX_POOL_SIZE)
            return;
        ArrayList removeKeys = new ArrayList(); ;
        List<string> keys = new List<string>(this._pool.Keys);
        for (int i = 0; i < keys.Count; i++)
        {
            string key = keys[i];
            SoundItem item = this._pool[key];
            while(item.timeList.Count > 0)
            {
                float time = item.timeList[0];
                if (time >= LOOP_FLAG)
                    break;
                if (time > Time.time)
                    break;
                item.timeList.RemoveAt(0);
            }
            if(item.timeList.Count <= 0)
            {
                removeKeys.Add(key);
                GResManager.instance.ReleaseAudio(item.audioClip, item.assetHandle);
                cnt--;
                if (cnt <= MAX_POOL_SIZE)
                    break;
            }
        }
        foreach (string key in removeKeys)
        {
            this._pool.Remove(key);
        }
    }


    public void ClearAll()
    {
        foreach(var audioSource in this._channels.Values)
        {
            audioSource.Pause();
            audioSource.clip = null;
        }    
        foreach (var item in this._pool)
        {
            GResManager.instance.ReleaseAudio(item.Value.audioClip, item.Value.assetHandle);
        }
        this._pool.Clear();
    }


    public override void Dispose()
    {
        base.Dispose();
        this.ClearAll();
    }

    public void PlayTTS(AudioClip audioClip,float rate = 1f,float volume = 1f)
    {
        //Debug.Log("soundManager  PlayTTS");
        AudioSource audioSource = this.GetChannel("TTS");
        audioSource.clip = audioClip;
        //变速不变音色
        AudioMixer audioMixer = audioSource.outputAudioMixerGroup.audioMixer;
        audioMixer.SetFloat("MASTER_Pitch", rate);//设置混音器下Master的Ptich值(音高)
        audioMixer.SetFloat("PITCHSHIFTER_Pitch", 1/rate); //设置了Master的Ptich值就要相应的设置Pitch Shifter的Pitch值，不然会变音色----二者的对应关系是：1/Master的Pitch值     
        audioSource.volume = volume;
        audioSource.Play();
    }

    public void StopTTS()
    {
        // Debug.LogError(" soundManager  StopTTS");
        AudioSource audioSource = this.GetChannel("TTS");
        audioSource.Stop();
     
        //角色 的停止 不要走 公共的 TTS 频道来触发 ，会打断 avatarTTs
        // StopStreamAudio();
    }
    
    public void StopUI()
    {
        //Debug.Log("soundManager  StopUI");
        AudioSource audioSource = this.GetChannel("UI");
        audioSource.Stop();
    }
    
    public void StopBGM()
    {
        AudioSource audioSource = this.GetChannel("Scene");
        audioSource.Stop();
    }
    
    

    public void PlayAvatarTTS(AudioClip audioClip,float rate = 1f,float volume = 1f)
    {
        //Debug.Log("soundManager PlayAvatarTTS , rate = " + rate);
        var audioSource = this._currAvatarAudioSource;
        if (audioSource == null) return;
        audioSource.clip = audioClip;
        audioSource.volume = volume;
        //变速不变音色
        AudioMixer audioMixer = audioSource.outputAudioMixerGroup.audioMixer;
        audioMixer.SetFloat("MASTER_Pitch", rate);//设置混音器下Master的Ptich值(音高)
        audioMixer.SetFloat("PITCHSHIFTER_Pitch", 1/rate); //设置了Master的Ptich值就要相应的设置Pitch Shifter的Pitch值，不然会变音色----二者的对应关系是：1/Master的Pitch值           
        audioSource.Play();
    }

    public void PlayFlowTTS(AudioClip audioClip, AudioSource audioSource)
    {
        //Debug.Log("soundManager  PlayFlowTTS");
        _curFlowAudioSource = audioSource;
        _curFlowAudioSource.clip = audioClip;
        _curFlowAudioSource.Play();
    }
    
    public void SetCurrAvatarTTS(AudioSource audioSource)
    {
        if (audioSource == null)
        {
            StopAvatarTTS();
        }

       
        this._currAvatarAudioSource = audioSource;
        // var hasBaseChar = audioSource.gameObject.transform.parent.name.IndexOf("BaseCharacter") >= 0;
        //
        // VFDebug.LogError("soundManager  hasBaseChar:"+hasBaseChar);
        
        if (audioSource !=null && audioSource.gameObject.name == "sound"  && audioSource.gameObject.transform.parent !=null)
        {
            string asName = audioSource.gameObject.transform.parent.name + "/" + audioSource.gameObject.name;
            var asp = audioSource.gameObject.transform.parent.GetComponent<AudioStreamPlayer>();
            if (asp == null)
            {
                VFDebug.LogError("GSoundManager get  AudioStreamPlayer failed");
            }
            else
            {
                asp.SetAudioSource(audioSource);//要打通外部audiosource 当前AudioStreamPlayer，不要让当前AudioStreamPlayer新建audiosource
            }
#if !UNITY_EDITOR_OSX //macOS上OVRLipSync无效
            //VFDebug.LogError("Test->soundManager  SetCurrAvatarTTS1:"+ asName);
            _audioStreamPlayer = audioSource.gameObject.transform.parent.GetComponent<AudioStreamPlayer>();
            
            _oVRLipSyncContext = audioSource.gameObject.GetComponent<OVRLipSyncContext>();
#else
            VFDebug.Log("Test->soundManager  SetCurrAvatarTTS2"+asName);
            _audioStreamPlayer = asp;//audioSource.gameObject.transform.parent.GetComponent<AudioStreamPlayer>();
            _oVRLipSyncContext = null;
            VFDebug.Log("soundManager  cant bind OVRLipSyncContext due to that current platform is OSX.");
#endif
        }
        else
        {
            VFDebug.LogError("soundManager not 2 bind oVRLip");
            _audioStreamPlayer = null;
            _oVRLipSyncContext = null;
        }
        
      
    }

    public void StopAvatarTTS()
    {
        StopStreamAudio();
        
        // VFDebug.LogError("soundManager  StopAvatarTTS");
        var audioSource = this._currAvatarAudioSource;
        if (audioSource == null) return;
        audioSource.Stop();
    }

    private void StopStreamAudio()
    {
        _audioStreamPlayer?.StopAudio();
    }

    public void PlaySocialChat(AudioClip audioClip,float rate = 1f)
    {
        // Debug.Log("soundManager  PlaySocialChat");
        AudioSource audioSource = this.GetChannel("SocialChat");
        audioSource.clip = audioClip;
        //变速不变音色
        //AudioMixer audioMixer = audioSource.outputAudioMixerGroup.audioMixer;
        //audioMixer.SetFloat("MASTER_Pitch", rate);//设置混音器下Master的Ptich值(音高)
        //audioMixer.SetFloat("PITCHSHIFTER_Pitch", 1/rate); //设置了Master的Ptich值就要相应的设置Pitch Shifter的Pitch值，不然会变音色----二者的对应关系是：1/Master的Pitch值           
        audioSource.Play();
    }

    public void StopSocialChat()
    {
        Debug.Log("soundManager  StopSocialChat");
        AudioSource audioSource = this.GetChannel("SocialChat");
        audioSource.Stop();
    }
    internal class SoundItem
    {
        public string url;
        public AssetHandle assetHandle;
        public AudioClip audioClip;
        public List<float> timeList = new List<float>();
    }
}
