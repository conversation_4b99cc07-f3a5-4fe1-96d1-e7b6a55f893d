/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class BtnSlowPlay : ABtnAudio
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "BtnSlowPlay";
        public static string url => "ui://cmoz5osjz7rm2s";

        public Controller ctrlPlay;
        public GImage woliu;
        public Transition playing;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(BtnSlowPlay));
        }

        public override void ConstructFromXML(XML xml)
        {
            ctrlPlay = GetControllerAt(1);
            woliu = GetChildAt(3) as GImage;
            playing = GetTransitionAt(0);
        }
        public override void Dispose()
        {
            ctrlPlay = null;
            woliu = null;
            playing = null;

            base.Dispose();
        }
    }
}