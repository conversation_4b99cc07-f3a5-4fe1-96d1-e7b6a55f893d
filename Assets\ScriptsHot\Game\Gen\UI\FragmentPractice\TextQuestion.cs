/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class TextQuestion : AFragQuestion
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "TextQuestion";
        public static string url => "ui://cmoz5osjpa962g";

        public Controller audio;
        public Controller ctrlImage;
        public BtnAudio btnPlayAudio;
        public ImageMaskComp imgQuestion;
        public GRichTextField tfQuestion;
        public GGroup grpOutside;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(TextQuestion));
        }

        public override void ConstructFromXML(XML xml)
        {
            audio = GetControllerAt(0);
            ctrlImage = GetControllerAt(1);
            btnPlayAudio = GetChildAt(1) as BtnAudio;
            imgQuestion = GetChildAt(2) as ImageMaskComp;
            tfQuestion = GetChildAt(3) as GRichTextField;
            grpOutside = GetChildAt(4) as GGroup;
        }
        public override void Dispose()
        {
            audio = null;
            ctrlImage = null;
            btnPlayAudio = null;
            imgQuestion = null;
            tfQuestion = null;
            grpOutside = null;

            base.Dispose();
        }
    }
}