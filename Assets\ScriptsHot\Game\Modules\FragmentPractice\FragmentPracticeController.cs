﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Timers;
using Msg.basic;
using Msg.course;
using Msg.question;
using Msg.question_process;
using ScriptsHot.Game.Modules.ChatStart;
using ScriptsHot.Game.Modules.Debugger;
using ScriptsHot.Game.Modules.MainPath;
using ScriptsHot.Game.Modules.Settlement;
using ScriptsHot.Game.Modules.Settlement.SettlementUI;
using ScriptsHot.Game.Modules.Sign;
using UIBind.FragmentPractice;
using UIBind.Main;
using UnityEngine;

namespace ScriptsHot.Game.Modules.FragmentPractice
{
    public class FragmentPracticeController : BaseController
    {
        public const int MAX_WRONG_COUNT = 2;  // 每道题最多重做的次数

        public FragmentPracticeController() : base(ModelConsts.FragmentPractice) { }
        private FragmentPracticeModel FragmentModel => GetModel<FragmentPracticeModel>(ModelConsts.FragmentPractice);
        private MainPathController MainPathController => GetController<MainPathController>(ModelConsts.MainPath);

        private Action _showCallback = null;
        public override void OnInit()
        {
            RegisterModel(new FragmentPracticeModel());
            RegisterUI(new FragmentPracticeUI(UIConsts.FragmentPracticeUI));
            RegisterUI(new FragmentPracticeConfirmUI(UIConsts.FragmentPracticeConfirmUI));
            RegisterUI(new FragBookPanelUI(UIConsts.FragBookPanelUI));

            BindComponents();
            
            MsgManager.instance.RegisterCallBack<SC_GetQuickPracticeListAck>(SuccessEnterPractices);

            TTSPreloader.Instance.InitAudioCache();
        }

        private void BindComponents()
        {
            BtnAudio.Bind();
            BtnBlueAudio.Bind();
            ClozeOption.Bind();
            BtnBaseAnswer.Bind();
            BtnNormalPlay.Bind();
            BtnSlowPlay.Bind();
            CompleteInput.Bind();
            CompCheck.Bind();
            CompPractice.Bind();
            ValidatingChoice.Bind();
            ClozeOptionText.Bind();
            CompBarPractice.Bind();
            CompTag.Bind();
            JumpTip.Bind();
            ImageMaskComp.Bind();

            TextQuestion.Bind();
            EssayQuestion.Bind();
            AudioQuestion.Bind();
            HideShowQuestion.Bind();
            // SimpleTextQuestion.Bind();
            FastSlowListenQuestion.Bind();
            BookReorderQuestion.Bind();
            BookFillInQuestion.Bind();
            ImageQuestion.Bind();
            WordQuestion.Bind();

            ImageAnswer.Bind();
            ClozeAnswer.Bind();
            RecordAnswer.Bind();
            ChoiceMidAnswer.Bind();
            AudioSelectAnswer.Bind();
            SingleChoiceAnswer.Bind();
            TapAnswer.Bind();
            CompleteAnswer.Bind();
            MatchAnswer.Bind();
            SortAnswer.Bind();
            ValidatingChoiceAnswer.Bind();
            // OnlyTextChoiceAnswer.Bind();
            WordCompleteAnswer.Bind();
            BuildWordAnswer.Bind();

            StarX5Background.Bind();
            FeedbackPopup.Bind();

            OldBtnBaseAnswer.Bind();
            OldClozeOptionBtn.Bind();
            OldMatchAnswer.Bind();
            OldSingleChoiceAnswer.Bind();
            OldTapChoiceAnswer.Bind();
            OldCompCheck.Bind();
            OldBtnNormalPlay.Bind();

            FragBookPanelUI.BindUI();
        }

        public void FailedEnterPractice(GRPCManager.ErrorType et,Google.Protobuf.IMessage msg)
        {
            GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N("ui_socialchat_network_timeout", () =>
            {
                GetController<MainController>(ModelConsts.Main).ChangeState(GameState.SceneIdle, false);
                GetUI(UIConsts.FragmentPracticeUI).Hide();
            }, null, 1, null, "common_check");
        }

        private void SuccessEnterPractices(SC_GetQuickPracticeListAck resp)
        {
            if (resp.code != 0) return;
            if (resp.data == null || resp.data.data == null || resp.data.data.Count == 0)
            {
                GetUI<CommConfirmUI>(UIConsts.CommConfirm).Open($"服务器返回数据为空 sessionId :: {SessionId}", () =>
                {
                    GetController<MainController>(ModelConsts.Main).ChangeState(GameState.SceneIdle, false);
                    GetUI(UIConsts.FragmentPracticeUI).Hide();
                }, null, 1, null, I18N.inst.MoStr("common_check"));
                return;
            }

            SaveRecommendInfo(resp.data);
            ServerRecordId = resp.data.record_id;
            SessionRecordId = resp.data.session_record_id;
            // FragmentQuestionGrp questionGrp = new FragmentQuestionGrp(resp.data);
            // FragmentModel.QuestionGrp = questionGrp;
            var practices = resp.data.data.Select(r=>APracticeData.TryCreate(0, r)).Where(p=>p != null);
            FragmentModel.CurQuestions.AddRange(practices);
            FragmentModel.CurQuestion = FragmentModel.CurQuestions[0];
            FragmentModel.StartTimeStamp = TimeExt.serverTimestamp;
            FragmentModel.SetAlterList(resp.data.alter_list);
            FragmentModel.SetChallengeList(resp.data.hard_list);
            _showCallback?.Invoke();
            FragmentModel.SetIsJumpSpeak(false);
            FragmentModel.SetIsJumpListen(false);
            GetModel<ChatModel>(ModelConsts.Chat).SetDialogId(resp.data.dialog_id);
            
            FragmentModel.ClearCaches();
            FragmentModel.ExtendCaches();

            DotPracticeManager.Instance.recordId = resp.data.record_id;
            
            PrepareTotalTts();

        }
        
        public void DebugEnterPractices()
        {            
            _recommendInfos.Clear();
            ServerRecordId = 0;
            SessionRecordId = 0;
            // FragmentQuestionGrp questionGrp = new FragmentQuestionGrp(resp.data);
            // FragmentModel.QuestionGrp = questionGrp;
            // var practices = resp.data.data.Select(r => APracticeData.TryCreate(0, r)).Where(p => p != null);
            // FragmentModel.CurQuestions.AddRange(practices);
            FragmentModel.CurQuestion = FragmentModel.CurQuestions[0];
            FragmentModel.StartTimeStamp = TimeExt.serverTimestamp;
            FragmentModel.SetAlterList(new PB_QuickPracticeInfo[0]);
            FragmentModel.SetChallengeList(new PB_QuickPracticeInfo[0]);
            _showCallback?.Invoke();
            FragmentModel.SetIsJumpSpeak(false);
            FragmentModel.SetIsJumpListen(false);
            // GetModel<ChatModel>(ModelConsts.Chat).SetDialogId(resp.data.dialog_id);

            FragmentModel.ClearCaches();
            FragmentModel.ExtendCaches();

            // DotPracticeManager.Instance.recordId = resp.data.record_id;

            PrepareTotalTts();

        }

        private void SaveRecommendInfo(PB_QuickPracticeListResp data)
        {
            _recommendInfos.Clear();
            foreach (var practiceInfo in data.data)
                if (practiceInfo.recommend_info != null)
                    _recommendInfos.Add(practiceInfo.recommend_info);
        }

        private List<PB_RecommendInfo> _recommendInfos = new();
        private long ServerRecordId { get; set; }//透传 体力关使用
        public long CourseId { get; private set; }
        public long UnitId { get; private set; }
        private int UnitIndex { get; set; }
        private long LevelId { get; set; }
        private int LevelIndex { get; set; }
        private long SessionId { get; set; }
        private int SessionIndex { get; set; }
        private int SectionIndex { get; set; }
        private bool IsSkip { get; set; }
        private bool isOnboarding = false;
        private PB_LevelTypeEnum LevelType { get; set; }
        private PB_CourseTypeEnum CourseTypeEnum { get; set; }
        public long SessionRecordId { get; set; }
        private float _startTimeSeconds;
        
        public void EnterPractice(long courseId, PB_CourseTypeEnum courseType, int sectionIndex, long unitId,
            int unitIndex, long levelId, int levelIndex, PB_LevelTypeEnum levelType, long sessionId, int sessionIndex,
            bool isSkipTask = false, PB_DialogSourceEnum source = PB_DialogSourceEnum.DialogSourceLearnPath,
            Action callback = null, bool isOnboarding=false)
        {
            _startTimeSeconds = Time.realtimeSinceStartup;
                
            _showCallback = callback;
            CourseId = courseId;
            CourseTypeEnum = courseType;
            SectionIndex = sectionIndex;
            UnitId = unitId;
            UnitIndex = unitIndex;
            LevelId = levelId;
            LevelIndex = levelIndex;
            LevelType = levelType;
            SessionId = sessionId;
            SessionIndex = sessionIndex;
            IsSkip = isSkipTask;
            this.isOnboarding = isOnboarding;

            DotPracticeManager.Instance.levelType = isSkipTask? PB_LevelTypeEnum.LTStar : LevelType;  // 跳关一定是五星关

            FragmentModel.SetJumpMode(isSkipTask, CourseTypeEnum == PB_CourseTypeEnum.CTSectionTest ? 5 : 3);
            FragmentModel.SetDialogSource(source);
            var state = FragmentPracticeUI.PageState.normal;
            var earnStaminaCnt = 0;
            var signDays = 0;
            var breakDays = 0;
            if (source == PB_DialogSourceEnum.DialogSourceMenuBarForStamina)
            {
                state = FragmentPracticeUI.PageState.stamina;
                earnStaminaCnt = (int)GetModel<StaminaModel>(ModelConsts.Stamina).staminaData.cnt_per_dialog;
            }
            else if (source == PB_DialogSourceEnum.DialogSourceReSignEntrance)
            {
                state = FragmentPracticeUI.PageState.resign;
                signDays = GetModel<SignModel>(ModelConsts.Sign).signSummary.checkin_streak_days;
                breakDays = GetModel<SignModel>(ModelConsts.Sign).signSummary.recall_checkin_days_cnt;
            }

            var args = new FragmentPracticeUI.FragmentPracticeUIArgs()
            {
                pageState = state,
                earnStaminaCnt = earnStaminaCnt,
                signDays = signDays,
                breakDays = breakDays,
            };

            FragmentModel.SetTaskId(SessionId);
            GetUI(UIConsts.FragmentPracticeUI).Show(args);
        }

        /// <summary>
        /// 进书本关
        /// </summary>
        public void EnterBook(long courseId,MainPathSectionData.MainPathNodesStruct nodeData,bool isReview)
        {
            _startTimeSeconds = Time.realtimeSinceStartup;
            CourseId = courseId;
            UnitId = nodeData.UnitData.ServerUnitData.unit_id;
            LevelId = nodeData.NodeData.ServerLevelData.level_id;
            LevelIndex = nodeData.NodeData.ServerLevelData.level_index;
            SessionId = nodeData.NodeData.GetNextSessionData().session_id;
            SessionIndex = nodeData.NodeData.GetNextSessionData().session_index;
            LevelType = nodeData.NodeData.ServerLevelData.level_type;
            IsSkip = false;
            isOnboarding = false;
            CourseTypeEnum = isReview ? PB_CourseTypeEnum.CTReview : PB_CourseTypeEnum.CTSession;
            FragmentModel.SetDialogSource(PB_DialogSourceEnum.DialogSourceLearnPath);
            
            DotPracticeManager.Instance.levelType = LevelType;

            GetUI<FragBookPanelUI>(UIConsts.FragBookPanelUI).Show(null, 
                nodeData.NodeData.ServerLevelData.session_data_list[0].session_id);
        }

        /// <summary>
        /// 进耳机关
        /// </summary>
        public void EnterRadio(long courseId, MainPathSectionData.MainPathNodesStruct nodeData, bool isReview)
        {
            _startTimeSeconds = Time.realtimeSinceStartup;
            CourseId = courseId;
            UnitId = nodeData.UnitData.ServerUnitData.unit_id;
            LevelId = nodeData.NodeData.ServerLevelData.level_id;
            LevelIndex = nodeData.NodeData.ServerLevelData.level_index;
            SessionId = nodeData.NodeData.GetNextSessionData().session_id;
            SessionIndex = nodeData.NodeData.GetNextSessionData().session_index;
            LevelType = nodeData.NodeData.ServerLevelData.level_type;
            IsSkip = false;
            isOnboarding = false;
            CourseTypeEnum = isReview ? PB_CourseTypeEnum.CTReview : PB_CourseTypeEnum.CTSession;
            FragmentModel.SetDialogSource(PB_DialogSourceEnum.DialogSourceLearnPath);
            
            DotPracticeManager.Instance.levelType = LevelType;
            
            GetController<PhoneQuestionController>(ModelConsts.PhoneQuestion)
                .GetRadioDataReq(nodeData.NodeData.ServerLevelData.session_data_list[0].session_id);
        }
        

        public void SendGetQuickPracticeListReq()
        {
            CS_GetQuickPracticeListReq msg = new CS_GetQuickPracticeListReq();
            msg.user_id = GetModel<MainModel>(ModelConsts.Main).userID;
            msg.unit_id = UnitId;
            msg.session_id = SessionId;
            msg.level_id = LevelId;
            msg.course_id = CourseId;
            msg.session_index = SessionIndex;
            msg.level_index = LevelIndex;
            msg.is_skip = IsSkip;
            msg.is_stamina_mode = FragmentModel.DialogSource == PB_DialogSourceEnum.DialogSourceMenuBarForStamina;
            //msg.section_id = SectionIndex;
            msg.is_recheckin = FragmentModel.DialogSource == PB_DialogSourceEnum.DialogSourceReSignEntrance;
            MsgManager.instance.SendMsg(msg, FailedEnterPractice);
        }

        public void DoAnswerError()
        {
            if (FragmentModel.CurQuestion == null)
            {
                return;
            }
            
            FragmentModel.ResetComboNum();
            FragmentModel.ComboErrorNum++;
            FragmentModel.AddResult(FragmentModel.CurQuestion.QuestionId, false);
            if (!FragmentModel.JumpMode)
            {
                FragmentModel.CurQuestion.AddWrongTimes();
                if (FragmentModel.CurQuestion.WrongTimes <= MAX_WRONG_COUNT)
                {
                    Debug.Log("DoAnswerError q+1");
                    FragmentModel.NextQuestions.Add(FragmentModel.CurQuestion);
                }
                else
                {
                    UnityEngine.Debug.Log("DoAnswerError WrongTimes:" + FragmentModel.CurQuestion.WrongTimes);
                }

            }
            else
                FragmentModel.CutShieldNum();
            FragmentModel.CurQuestionNum++;
            FragmentModel.ErrorQuestionCount++;
            if (FragmentModel.Round == 0)
            {
                FragmentModel.FirstAnswerDecisions[FragmentModel.CurQuestion.QuestionId] =
                    new FragmentPracticeModel.QuestionAnswerData()
                    {
                        Duration = 1000 * (Time.realtimeSinceStartup - FragmentModel.CurQuestion.StartTimeSecond),
                        Id = FragmentModel.CurQuestion.QuestionId,
                        QuestionType = FragmentModel.CurQuestion.QuestionType,
                        IsRight = false,
                        RecommendInfo = FragmentModel.CurQuestion.ServerRecommend,
                    };
            }
        }

        public void DoAnswerCorrect(bool retry = false, bool useHint=false)
        {
            var isCompletelyRight = !useHint;
            FragmentModel.ComboErrorNum = 0;
            FragmentModel.AddResult(FragmentModel.CurQuestion.QuestionId, true);
            
            if (!retry) FragmentModel.IncreaseComboNum();
            if (isCompletelyRight) FragmentModel.CorrectNum++; 
            FragmentModel.CurQuestionNum++;
            if (FragmentModel.Round == 0)
            {
                FragmentModel.FirstAnswerDecisions[FragmentModel.CurQuestion.QuestionId] =
                    new FragmentPracticeModel.QuestionAnswerData()
                    {
                        Duration = 1000 * (Time.realtimeSinceStartup - FragmentModel.CurQuestion.StartTimeSecond),
                        Id = FragmentModel.CurQuestion.QuestionId,
                        QuestionType = FragmentModel.CurQuestion.QuestionType,
                        IsRight = isCompletelyRight,
                        RecommendInfo = FragmentModel.CurQuestion.ServerRecommend,
                    };
            }

            if (FragmentModel.FirstAnswerDecisions.TryGetValue(FragmentModel.CurQuestion.QuestionId,
                    out FragmentPracticeModel.QuestionAnswerData answerData))
            {
                answerData.CheckTimes = FragmentModel.Round + 1;//从1开始
                FragmentModel.FirstAnswerDecisions[FragmentModel.CurQuestion.QuestionId] = answerData;
            }
        }
        
        private void CheckChangeRound()
        {
            if (FragmentModel.Round != FragmentModel.LastRound)
            {
                FragmentModel.InitProgress += (1f * FragmentModel.CorrectNum / FragmentModel.CurQuestions.Count + 
                                               FragmentModel.ErrorQuestionCount / (2f * FragmentModel.CurQuestions.Count)) *
                                                (100 - FragmentModel.InitProgress);
                
                FragmentModel.CurQuestions.Clear();
                FragmentModel.CurQuestions.AddRange(FragmentModel.NextQuestions);
                FragmentModel.CorrectNum = 0;
                FragmentModel.CurQuestionIdx = 0;
                FragmentModel.ErrorQuestionCount = 0;
                FragmentModel.NextQuestions.Clear();
            }

            FragmentModel.LastRound = FragmentModel.Round;
        }
        
        public bool ToNextQuestion(Action<string> log=null)
        {
            FragmentModel.CurQuestionIdx++;
            int curIndex = FragmentModel.CurQuestionIdx;
            if (curIndex == FragmentModel.CurQuestions.Count)
                FragmentModel.Round++;
            CheckChangeRound();
            if (FragmentModel.Round == 0)
            {
                FragmentModel.CurQuestion = FragmentModel.CurQuestions[curIndex];
                FragmentModel.RequestPractices(SessionId, LevelId, CourseId, UnitId, log);
            }
            else
            {
                if (FragmentModel.CurQuestions.Count == 0)
                    return false;
                FragmentModel.CurQuestion = FragmentModel.CurQuestions[FragmentModel.CurQuestionIdx];
            }
            return true;
        }

        public APracticeData GetNextQuestion()
        {
            int curIndex = FragmentModel.CurQuestionIdx;

            if (curIndex + 1 < FragmentModel.CurQuestions.Count)
            {
                return FragmentModel.CurQuestions[curIndex + 1];
            }

            return FragmentModel.NextQuestions.Count > 0
                ? FragmentModel.NextQuestions[0]
                : FragmentModel.CurQuestions[^1];
        }

        public bool IsLastQuestion()
        {
            return FragmentModel.Round > 0 && FragmentModel.CurQuestions.Count == 0 ||
                FragmentModel.Round == 0 && FragmentModel.CurQuestionIdx == FragmentModel.CurQuestions.Count - 1 ||
                FragmentModel.Round > 0 && FragmentModel.CurQuestionIdx == FragmentModel.CurQuestions.Count - 1 && 
                FragmentModel.NextQuestions.Count == 0;
        }

        public void QuitPractice()
        {
            FragmentModel.CurQuestion = null;
            FragmentModel.Round = 0;
            FragmentModel.LastRound = 0;
            FragmentModel.ResetComboNum();
            FragmentModel.ComboErrorNum = 0;
            FragmentModel.CorrectNum = 0;
            FragmentModel.InitProgress = 7f;
            FragmentModel.CurQuestionIdx = 0;
            FragmentModel.CurQuestionNum = 0;
            FragmentModel.ErrorQuestionCount = 0;
            FragmentModel.SetJumpMode();
            FragmentModel.CurQuestions.Clear();
            FragmentModel.NextQuestions.Clear();
            FragmentModel.FirstAnswerDecisions.Clear();
        }

        public void ReqSettlement()
        {
            int rightNum = 0;
            List<PB_QuestionAnswerData> answerList = new();
            foreach (var data in FragmentModel.FirstAnswerDecisions.Values)
            {
                answerList.Add(new PB_QuestionAnswerData()
                {
                    question_id = data.Id,
                    question_type = data.QuestionType,
                    is_right = data.IsRight,
                    duration = (long)data.Duration,
                    recommend_info = data.RecommendInfo,
                });
                if (data.IsRight) rightNum++;
            }
            
            float time = Time.realtimeSinceStartup - _startTimeSeconds;
            int jumpState = 0;
            if (FragmentModel.JumpMode) jumpState = FragmentModel.JumpTaskShieldCurNum > 0 ? 1 : 2;
            ModelManager.instance.GetModel<SettlementModel>(ModelConsts.Settlement)
                .SetSettlementLocalData(LevelType, FragmentModel.DialogSource, jumpState, (int)time,
                    rightNum * 1.0f / FragmentModel.FirstAnswerDecisions.Count,
                    FragmentModel.FirstAnswerDecisions.Count - rightNum);
            GetUI<SettlementCommonUI>(UIConsts.SettlementCommon).Show();

            CS_GetCourseSettlementReq msg = new CS_GetCourseSettlementReq();
            msg.course_id = CourseId;
            msg.section_index = SectionIndex;
            msg.unit_index = UnitIndex;
            msg.level_index = LevelIndex;
            msg.session_index = SessionIndex;
            msg.question_answer_list.AddRange(answerList);
            msg.level_type = LevelType;
            msg.course_type = CourseTypeEnum;
            msg.unit_id = UnitId;
            msg.is_jump_listen = FragmentModel.IsJumpListen;
            msg.is_jump_speak = FragmentModel.IsJumpSpeak;
            msg.is_skip_success = (FragmentModel.JumpMode && FragmentModel.JumpTaskShieldCurNum > 0) || !FragmentModel.JumpMode;
            msg.record_id = ServerRecordId;
            msg.dialog_source = FragmentModel.DialogSource;
            msg.session_record_id = SessionRecordId;
            msg.is_onboarding = isOnboarding;
            MsgManager.instance.SendMsg(msg, FailedReqSettlement);
        }

        /// <summary>
        /// 中途退出上报服务器 不等回复
        /// </summary>
        public void SendExitQuickPracticeReq()
        {
            PB_SubmitUnit answer = new PB_SubmitUnit();
            foreach (var val in FragmentModel.FirstAnswerDecisions.Values)
            {
                answer.answer_list.Add(new PB_SubmitAnswer()
                {
                    question_id = val.Id,
                    decision = val.IsRight
                        ? PB_PracticeAnswerDecision.PracticeAnswerDecision_Right
                        : PB_PracticeAnswerDecision.PracticeAnswerDecision_Wrong,
                });
            }

            for (int i = FragmentModel.FirstAnswerDecisions.Count; i < FragmentModel.CurQuestions.Count; i++)
            {
                answer.answer_list.Add(new PB_SubmitAnswer()
                {
                    question_id = FragmentModel.CurQuestions[i].QuestionId,
                    decision = PB_PracticeAnswerDecision.PracticeAnswerDecision_Noanswer,
                });
            }

            answer.unit_id = UnitId;
            answer.cost_seconds = (long)((Time.realtimeSinceStartup - FragmentModel.StartTimeStamp));

            var msg = new CS_ExitQuickPracticeReq();
            msg.user_id = GetModel<MainModel>(ModelConsts.Main).userID;
            msg.unit_list.Add(answer);
            msg.session_record_id = SessionRecordId;
            MsgManager.instance.SendMsg(msg, FailedReqSettlement);

            FragmentModel.Clear();
        }

        private void FailedReqSettlement(GRPCManager.ErrorType et,Google.Protobuf.IMessage msg)
        {
            GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N("ui_socialchat_network_timeout", () =>
            {
                //失败也刷新一下路径
                MainPathController.RequestGetUserCourse(MainPathController.MainPathOperateType.SettleRefresh);
                SendNotification(ModelConsts.ToGameIdleState);
                GetUI(UIConsts.FragmentPracticeUI).Hide();
                //VFDebug.LogError($"FailedReqSettlement et {et.ToString()} msg {msg}");
            }, null, 1, null, "common_quit");
        }

        public int GetCurQuestionIndex()
        {
            if (FragmentModel.Round == 0)
            {
                return FragmentModel.CurQuestionIdx;
            }

            return FragmentModel.CurQuestionNum - FragmentModel.CurQuestions.Count;
        }

        public int GetQuestionNum()
        {
            if (FragmentModel.Round == 0)
            {
                return FragmentModel.CurQuestions.Count;
            }

            return FragmentModel.CurQuestionNum - FragmentModel.CurQuestions.Count;
        }
        
        
        //整理全部题目和题干的TTS
        private void PrepareTotalTts()
        {
            List<long> totalTTS = new();
            foreach (var question in FragmentModel.CurQuestions)
            {
                long[] ttsOptions = question.GetAnswerOptionTts();
                if (question.AudioId > 0)
                    totalTTS.Add(question.AudioId);
                foreach (var tts in ttsOptions)
                    if (tts > 0)
                        totalTTS.Add(tts);
            }
            
            Preload(totalTTS);
        }

        private async void Preload(List<long> totalTts)
        {
            TTSPreloader.Instance.Clear();
            await TTSPreloader.Instance.PreloadTTS(totalTts,
                () => { GetUI<FragmentPracticeUI>(UIConsts.FragmentPracticeUI).FinalShow(); });
        }

        public void RemoveAllListenQuestion(List<APracticeData> questions, int sIndex)
        {
            for (int i = questions.Count - 1; i >= sIndex; i--)
            {
                var question = questions[i];
                if (question.IsListenPractice())
                {
                    questions.RemoveAt(i);
                }
            }
        }

        //移除后续所有口语题
        public void RemoveAllSpeakQuestion(List<APracticeData> questions, int sIndex)
        {
            for (int i = questions.Count - 1; i >= sIndex; i--)
            {
                var question = questions[i];
                if (question.IsSpeakPractice())
                {
                    questions.RemoveAt(i);
                }
            }
        }
    }
}