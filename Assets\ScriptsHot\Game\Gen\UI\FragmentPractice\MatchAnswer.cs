/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class MatchAnswer : AFragAnswer
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "MatchAnswer";
        public static string url => "ui://cmoz5osjz7rm3d";

        public GList leftList;
        public GList rightList;
        public Transition t0;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(MatchAnswer));
        }

        public override void ConstructFromXML(XML xml)
        {
            leftList = GetChildAt(0) as GList;
            rightList = GetChildAt(1) as GList;
            t0 = GetTransitionAt(0);
        }
        public override void Dispose()
        {
            leftList = null;
            rightList = null;
            t0 = null;

            base.Dispose();
        }
    }
}