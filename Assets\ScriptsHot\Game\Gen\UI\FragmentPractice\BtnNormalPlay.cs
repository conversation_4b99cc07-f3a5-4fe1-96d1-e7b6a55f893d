/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class BtnNormalPlay : ABtnAudio
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "BtnNormalPlay";
        public static string url => "ui://cmoz5osjz7rm2r";

        public Controller ctrlPlay;
        public GImage laba;
        public Transition playing;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(BtnNormalPlay));
        }

        public override void ConstructFromXML(XML xml)
        {
            ctrlPlay = GetControllerAt(1);
            laba = GetChildAt(3) as GImage;
            playing = GetTransitionAt(0);
        }
        public override void Dispose()
        {
            ctrlPlay = null;
            laba = null;
            playing = null;

            base.Dispose();
        }
    }
}