/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class BookReorderQuestion : AFragQuestion
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "BookReorderQuestion";
        public static string url => "ui://cmoz5osjp3vduvptcc";

        public Controller audio;
        public Controller showStem;
        public ChatText chatItem;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(BookReorderQuestion));
        }

        public override void ConstructFromXML(XML xml)
        {
            audio = GetControllerAt(0);
            showStem = GetControllerAt(1);
            chatItem = GetChildAt(0) as ChatText;
        }
        public override void Dispose()
        {
            audio = null;
            showStem = null;
            chatItem = null;

            base.Dispose();
        }
    }
}