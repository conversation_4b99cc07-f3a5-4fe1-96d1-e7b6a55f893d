/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class FeedbackPopup : ExtendedComponent
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "FeedbackPopup";
        public static string url => "ui://cmoz5osjcz4guvptdd";

        public Controller ctrlState;
        public GGroup grp5inrow;
        public GGroup grpFinish;
        public GGroup grpHeart;
        public GGroup grpMistake;
        public GGroup grpStart;
        public GTextField tfContent;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(FeedbackPopup));
        }

        public override void ConstructFromXML(XML xml)
        {
            ctrlState = GetControllerAt(0);
            grp5inrow = GetChildAt(2) as GGroup;
            grpFinish = GetChildAt(5) as GGroup;
            grpHeart = GetChildAt(8) as GGroup;
            grpMistake = GetChildAt(11) as GGroup;
            grpStart = GetChildAt(14) as GGroup;
            tfContent = GetChildAt(15) as GTextField;

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();

            ctrlState = null;
            grp5inrow = null;
            grpFinish = null;
            grpHeart = null;
            grpMistake = null;
            grpStart = null;
            tfContent = null;

            base.Dispose();
        }
    }
}