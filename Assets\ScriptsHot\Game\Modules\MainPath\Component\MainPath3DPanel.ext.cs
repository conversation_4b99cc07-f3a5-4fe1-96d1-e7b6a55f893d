﻿using System.Collections.Generic;
using FairyGUI;
using Game.Modules.WhatsApp;
using Modules.DataDot;
using Msg.basic;
using Msg.course;
using ScriptsHot.Game.Modules.IncentiveTask;
using ScriptsHot.Game.Modules.MainPath;
using UIBind.Main;
using Unity.Mathematics;
using UnityEngine;

namespace UIBind.MainPath3D
{
    public partial class MainPath3DPanel
    {
        private CenterHomeUI _homePanel;
        private MainPathController _pathController;

        private int _focusIndex; //scroll to view 用
        private int _selectNodeIndex; //主要是用来看打开谁的卡片窗
        private GButton btnToTop;
        private bool _init;

        public void OnInit(CenterHomeUI panel)
        {
            _init = true;
            _homePanel = panel;
            _pathController = panel.GetController<MainPathController>(ModelConsts.MainPath);
            panel.AddUIEvent(compSectionHead.com.onClick, OnClickOpenBookshelf);
            panel.AddUIEvent(pathList.scrollPane.onScroll, OnScroll);
            panel.AddUIEvent(pathList.onTouchMove, OnTouchMove);
            panel.AddUIEvent(pathList.onClickItem, ClearSelect);
            panel.AddUIEvent(closeLdr.onClick, ClearSelect);

            pathList.itemRenderer = OnRendererMainPath;
            pathList.itemProvider = OnProviderMainPath;
            pathList.SetVirtual();

            mainListLeft.itemRenderer = OnRendererLeftList;
            mainListRight.itemRenderer = OnRendererRightList;
            panel.AddUIEvent(mainListLeft.onClickItem, OnClickLeftItem);  
            panel.AddUIEvent(mainListRight.onClickItem, OnClickRightItem);
        } 

        private MainPathUnitData _currentUnitData; //顶部详情
        private Vector2 _lightScale; //从哪飞
        public void OnShow(GButton topBtn)
        {
            ClearData();
            
            RemoveEvent();
            AddEvent();
            RefreshMainPath();
            OnRefreshFeature();


            AdjustToTop(topBtn);

            DotAppearMainPath dot = new DotAppearMainPath();
            DataDotMgr.Collect(dot);
        }

        #region 两侧Feature列表 [数据 & Renderer & Click]方法

        private enum HomepageEntranceTypes
        {
            Whatsapp,
            Quest,
        }

        private struct EntranceData
        {
            public HomepageEntranceTypes Type;
            public string LanguageKey;
        }

        private EntranceData _whatsAppData = new EntranceData()
            { Type = HomepageEntranceTypes.Whatsapp, LanguageKey = "ui_home_WhatsApp_icon" };

        //左侧list 类型 - 多语言
        private List<EntranceData> _leftList = new()
        {
            //待补充
        };

        //右侧list 类型 - 多语言
        private List<EntranceData> _rightList = new()
        {
            new EntranceData() { Type = HomepageEntranceTypes.Quest, LanguageKey = "ui_main_quests_btn", },
            //待补充
        };

        private void OnRefreshFeature()
        {
            if (!_leftList.Contains(_whatsAppData)
                && _homePanel.GetController<WhatsappController>(ModelConsts.Whatsapp).ShowMainWhatsappBtn)
                _leftList.Add(_whatsAppData);

            mainListLeft.numItems = _leftList.Count;
            mainListRight.numItems = _rightList.Count;
        }

        private void OnRendererLeftList(int index, GObject obj)
        {
            CompFeature item = new CompFeature();
            item.Construct(obj as GComponent);
            item.title.SetKey(_leftList[index].LanguageKey);
            item.type.selectedIndex = (int)_leftList[index].Type;
        }

        private void OnRendererRightList(int index, GObject obj)
        {
            CompFeature item = new CompFeature();
            item.Construct(obj as GComponent);
            item.title.SetKey(_rightList[index].LanguageKey);
            item.type.selectedIndex = (int)_rightList[index].Type;
        }

        private void OnClickLeftItem(EventContext evt)
        {
            GComponent comp = evt.sender as GComponent;
            if (comp == null) return;
            int index = comp.GetChildIndex(evt.data as GObject);
            switch (_leftList[index].Type)
            {
                case HomepageEntranceTypes.Whatsapp:
                    OnBtnWhatsappClicked();
                    break;
                case HomepageEntranceTypes.Quest:
                    OnBtnQuestClicked();
                    break;
            }
        }

        private void OnClickRightItem(EventContext evt)
        {
            GComponent comp = evt.sender as GComponent;
            if (comp == null) return;
            int index = comp.GetChildIndex(evt.data as GObject);
            switch (_rightList[index].Type)
            {
                case HomepageEntranceTypes.Whatsapp:
                    OnBtnWhatsappClicked();
                    break;
                case HomepageEntranceTypes.Quest:
                    OnBtnQuestClicked();
                    break;
            }
        }

        private void OnBtnWhatsappClicked()
        {
            UIManager.instance.GetUI<WhatsappGuiderUI>(UIConsts.WhatsappGuider).Show();
        }

        private void OnBtnQuestClicked()
        {
            SoundManger.instance.PlayUI("contacts_click_collection");
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
            _homePanel.GetController<IncentiveTaskController>(ModelConsts.IncentiveTask).EnterIncentiveTask();
        }

        #endregion

        private void AdjustToTop(GButton topBtn)
        {
            btnToTop = topBtn;
            _homePanel.AddUIEvent(btnToTop.onClick, OnClickToTop);
        }

        private void RefreshMainPath()
        {
            ClearSelect();
            SendBackPath();
            
            MainPathSectionData sectionData = _pathController.Model.FocusData.SectionData;
            if (sectionData == null) return;
            pathList.numItems = sectionData.NodeList.Count; //虚拟列表
            TimerManager.instance.RegisterNextFrame((a) =>
            {
                FocusMainPathNode();
                UpdateBtnToTopOnScroll();
            });
        }

        private void RefreshChapterDetail(MainPathUnitData unitData)
        {
            if (unitData == null)
                return;
            
            PB_SectionData sectionData = _pathController.Model.FocusData.SectionData.ServerSectionData;
            compSectionHead.title1.SetKeyArgs("ui_main_path_primary", sectionData.section_title,
                (unitData.ServerUnitData.unit_index + 1).ToString());
            compSectionHead.title2.text = unitData.ServerUnitData.unit_title;
        }

        private string OnProviderMainPath(int index)
        {
            MainPathSectionData sectionData = _pathController.Model.FocusData.SectionData;
            switch (sectionData.NodeList[index].NodeType)
            {
                case MainPathSectionData.NodeType.AUAItem:
                    return "ui://MainPath3D/BtnAUAItem";
                case MainPathSectionData.NodeType.BookItem:
                    return "ui://MainPath3D/BtnBookItem";
                case MainPathSectionData.NodeType.StarItem:
                    return "ui://MainPath3D/BtnStartItem";
                case MainPathSectionData.NodeType.CupItem:
                    return "ui://MainPath3D/BtnCupItem";
                case MainPathSectionData.NodeType.CutItem:
                    return "ui://MainPath3D/CutItem";
                case MainPathSectionData.NodeType.DumbBellItem:
                    return "ui://MainPath3D/BtnDumbBellItem";
                case MainPathSectionData.NodeType.HeadEmptyItem:
                    return "ui://MainPath3D/HeadEmptyItem";
                case MainPathSectionData.NodeType.NextItem:
                    return "ui://MainPath3D/NextItem";
                case MainPathSectionData.NodeType.RadioItem:
                    return "ui://MainPath3D/BtnRadioItem";
                case MainPathSectionData.NodeType.WarmUpItem:
                    return "ui://MainPath3D/BtnWarmUpItem";
                case MainPathSectionData.NodeType.GrammarItem:
                    return "ui://MainPath3D/BtnGrammarItem";
                case MainPathSectionData.NodeType.ReviewItem:
                    return "ui://MainPath3D/BtnReviewItem";
                case MainPathSectionData.NodeType.SentenceItem:
                    return "ui://MainPath3D/BtnSentenceItem";
                case MainPathSectionData.NodeType.WordItem:
                    return "ui://MainPath3D/BtnWordItem";
                default:
                    return "";
            }
        }

        // obj - index 虚拟列表 obj对应的index在滑动的时候一直变化 需要render一直保持对应
        private Dictionary<GObject, int> _dicNodeIndexByObj = new();

        private void OnRendererMainPath(int index, GObject obj)
        {
            MainPathSectionData sectionData = _pathController.Model.FocusData.SectionData;
            MainPathSectionData.MainPathNodesStruct nodeData = sectionData.NodeList[index];
            _dicNodeIndexByObj[obj] = index;

            if (obj.asCom.GetChild("item")?.asCom.GetChild("compLdr") != null)
                _homePanel.AddUIEvent(obj.asCom.GetChild("item").asCom.GetChild("compLdr").onClick, OnClickNode);

            obj.scale = Vector2.one;
            switch (nodeData.NodeType)
            {
                case MainPathSectionData.NodeType.AUAItem:
                case MainPathSectionData.NodeType.BookItem:
                case MainPathSectionData.NodeType.CupItem:
                case MainPathSectionData.NodeType.DumbBellItem:
                case MainPathSectionData.NodeType.RadioItem:
                case MainPathSectionData.NodeType.WarmUpItem:
                case MainPathSectionData.NodeType.StarItem:
                    var item = obj.asCom.GetChild("item");
                    item.asCom.GetController("state").selectedIndex = (int)nodeData.NodeData.ServerLevelData.status - 1;
                    item.asCom.GetController("pos").selectedIndex = nodeData.Position;
                    item.asCom.GetController("dotNum").selectedIndex = nodeData.NodeData.ServerLevelData.session_total - 1;
                    item.asCom.GetChild("compNode").asCom
                            .GetChild($"bar{nodeData.NodeData.ServerLevelData.session_total - 1}")
                            .asLoader.fillAmount = nodeData.NodeData.ServerLevelData.session_completed * 1.0f /
                                                   nodeData.NodeData.ServerLevelData.session_total;
                    if (nodeData.NodeData.ServerLevelData.status == PB_ProgressStatusEnum.PSRunning)
                        item.asCom.GetTransition("t0").Play(-1, 0, null);
                    else
                        item.asCom.GetTransition("t0").Stop();
                    MainPathModel.FocusNodeData fData = _pathController.Model.FocusData;
                    if (fData.UnitData != null && fData.LevelData != null
                                               && fData.LevelData.NodeId == nodeData.NodeData.NodeId
                                               && fData.UnitData.ServerUnitData.unit_index == nodeData.UnitData.ServerUnitData.unit_index)
                        _focusIndex = index;

                    Transition idle = obj.asCom.GetTransition($"idle{nodeData.Position}");
                    for (int i = 0; i <= 5; i++)
                    {
                        var t = obj.asCom.GetTransition($"idle{i}");
                        t.Stop();
                    }
                    idle.Play(-1, 0, null);

                    var btn = obj.asCom.GetChild("item").asCom.GetChild("compLdr");
                    _homePanel.AddUIEvent(btn.onTouchBegin, OnTouchBeginItem);
                    _homePanel.AddUIEvent(btn.onTouchEnd, OnTouchEndItem);
                    break;
                case MainPathSectionData.NodeType.CutItem:
                    obj.asCom.GetChild("compTitle").asCom.GetChild("tfQuestion").asTextField.text =
                        nodeData.UnitData.ServerUnitData.unit_title;
                    break;
                case MainPathSectionData.NodeType.NextItem:
                    if (_pathController.Model.NextSectionSimpleData != null)
                    {
                        obj.visible = true;
                        obj.asCom.GetChild("title1").asTextField.text =
                            _pathController.Model.NextSectionSimpleData.section_title;
                        obj.asCom.GetChild("title2").asTextField.text =
                            _pathController.Model.NextSectionSimpleData.section_subtitle;
                        GButton btnStart = obj.asCom.GetChild("BtnSkipToThis").asButton;
                        btnStart.SetKey(sectionData.ServerSectionData.status == PB_ProgressStatusEnum.PSLock
                            ? "ui_main_path_skip"
                            : "ui_main_path_continue");
                        _homePanel.AddUIEvent(btnStart.onClick, OnClickSkipSection);
                    }
                    else
                    {
                        obj.visible = false; //顶个位置而已 不然弹窗会被卡在下面
                    }

                    break;
            }
        }

        private void StartNodeCallback()
        {
            SendLeavePath();
            ClearSelect();
        }

        #region 点击事件
        private void OnClickOpenBookshelf()
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Medium);

            DotClickMainPathLevel dot = new DotClickMainPathLevel();
            dot.level_id = _pathController.Model.FocusData.SectionData.ID;
            DataDotMgr.Collect(dot);

            ClearSelect();

            _pathController.RequestBookShelf();
        }

        private void OnClickToTop()
        {
            // 找到最后一个开放的节点并聚焦
            MainPathSectionData sectionData = _pathController.Model.FocusData.SectionData;
            if (sectionData != null && sectionData.NodeList != null && sectionData.NodeList.Count > 0)
            {
                int lastOpenIndex = -1;
                for (int i = sectionData.NodeList.Count - 1; i >= 0; i--)
                {
                    var node = sectionData.NodeList[i];
                    if (node.NodeData != null && node.NodeData.ServerLevelData.status != PB_ProgressStatusEnum.PSLock)
                    {
                        lastOpenIndex = i;
                        break;
                    }
                }

                if (lastOpenIndex != -1)
                {
                    _focusIndex = lastOpenIndex;
                    _selectNodeIndex = -1;
                    pathList.ScrollToView(Mathf.Max(0, _focusIndex - 2), true, true); // 往下移动两个图标，以让出气泡的位置
                    RefreshChapterDetail(sectionData.NodeList[_focusIndex].UnitData);
                    return;
                }
            }

            FocusMainPathNode();
        }
        
        private void OnClickNode(EventContext ext)
        {
            ext.StopPropagation();
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Medium);
            GObject obj = ext.sender as GObject;
            if (obj == null) return;
            int index = _dicNodeIndexByObj[obj.parent.parent];
            MainPathSectionData sectionData = _pathController.Model.FocusData.SectionData;
            MainPathSectionData.MainPathNodesStruct nodeData = sectionData.NodeList[index];
            if (index == _selectNodeIndex)
            {
                ClearSelect();
                return;
            }

            _selectNodeIndex = index;
            compNodeStart.ShowStartTips(nodeData, StartNodeCallback, ClearSelect);
            float focusY = FocusMainPathNode();
            compNodeStart.SetPosition(compNodeStart.x, focusY, 0);
            compNodeStart.visible = true;
            compNodeStart.t0.Play();
        }

        private bool _isTouching;
        private GObject _scaleObj;
        private Dictionary<GObject, GTweener> _tweeners = new();
        
        private void OnTouchBeginItem(EventContext ext)
        {
            _scaleObj = ext.sender as GObject;
            if (_scaleObj == null) return;
            _isTouching = true;
        }
        
        private void OnTouchEndItem(EventContext ext)
        {
            _isTouching = false;
            StopTouchTween(ext.sender as GObject);
        }

        private void StopTouchTween(GObject obj)
        {
            // 停止之前的动画
            if (_tweeners.ContainsKey(obj))
            {
                _tweeners[obj].Kill();
            }
                
            // 松手时变回1的动画，使用弹性缓动实现Q弹效果
            var tween = GTween.To(obj.scale, new Vector2(1f, 1f), 0.48f)
                .SetTarget(obj)
                .SetEase(EaseType.ElasticOut)
                .SetEaseOvershootOrAmplitude(0.085f)
                .OnUpdate(t => { obj.SetScale(t.value.x, t.value.y); })
                .OnComplete(() => 
                {
                    // 动画完成后清理
                    if (_tweeners.ContainsKey(obj))
                    {
                        _tweeners.Remove(obj);
                    }
                });
                
            _tweeners[obj] = tween;
        }

        //跳Section
        private void OnClickSkipSection()
        {
            if (_pathController.Model.NextSectionSimpleData != null)
            {
                PB_SectionData section = _pathController.Model.NextSectionSimpleData;
                DotClickSkipItemSkip dot = new DotClickSkipItemSkip();
                dot.unit_id = _pathController.Model.FocusData.SectionData.ID;
                if (section.status == PB_ProgressStatusEnum.PSLock)
                {
                    dot.is_skip = 1;
                    _pathController.EnterJump(section);
                }
                else
                {
                    dot.is_skip = 0;
                    _pathController.Change2FinishOrRunningSection(section.section_id);
                }

                DataDotMgr.Collect(dot);
            }
        }

        private void OnTouchMove()
        {
            ClearSelect();
        }

        private void OnScroll()
        {
            if (pathList.numItems <= 0)
                return;
            
            if (_scaleObj != null)
            {
                _isTouching = false;
                StopTouchTween(_scaleObj);
                _scaleObj = null;
            }
            
            UpdateChapterDetailOnScroll();
            UpdateBtnToTopOnScroll();
        }
        #endregion

        private const int PATH_SCROLL_OFFSET = 24;
        private bool _startFocus;

        //case 1:最开始进入路径的时候 focus running
        //case 2:切换书本 focus 0
        //case 3:点击节点 focus 节点 弹窗
        //返回目标y
        private float FocusMainPathNode()
        {
            if (_selectNodeIndex != -1) //打开tips 要算一下弹窗位置
            {
                GObject focusObj = pathList.GetChildAt(pathList.ItemIndexToChildIndex(_selectNodeIndex));
                if (focusObj == null) return 0;
                var item = focusObj.asCom.GetChild("item");
                var compLdr = item.asCom.GetChild("compLdr");
                Vector2 focusPos = com.GlobalToLocal(compLdr.LocalToGlobal(Vector2.zero) +
                                        new Vector2(compLdr.width / 2, compLdr.height) * compLdr.scale);
                int offset = _pathController.Model.FocusData.SectionData.NodeList[_selectNodeIndex]
                    .NodeData.ServerLevelData.status == PB_ProgressStatusEnum.PSRunning
                    ? PATH_SCROLL_OFFSET : PATH_SCROLL_OFFSET / 2;
                float finalY = focusPos.y + offset;
                if (finalY > targetY.y)
                {
                    pathList.scrollPane.SetPosY(pathList.scrollPane.posY + finalY - targetY.y, false);
                    return targetY.y;
                }

                return focusPos.y + offset;
            }
            
            if (_pathController.Model.FocusData.NeedChange) //不打开tips的话只显示当前节点就ok
            {
                _startFocus = true;
                
                if (_focusIndex == 0) //虚拟列表屏幕外有可能为0
                    _focusIndex = GetRunningIndex();

                MainPathModel.FocusNodeData data = _pathController.Model.FocusData;
                _pathController.Model.SetFocusData(data.SectionData, data.UnitData, data.LevelData); //needChange变false

                pathList.ScrollToView(math.max(0, _focusIndex), false);
                RefreshChapterDetail(data.SectionData.NodeList[_focusIndex].UnitData);
                
                TimerManager.instance.RegisterNextFrame((a) => //等一帧绘制 放中间
                {
                    var focusObj = pathList.GetChildAt(pathList.ItemIndexToChildIndex(_focusIndex));
                    Vector2 focusPos = com.GlobalToLocal(focusObj.LocalToGlobal(Vector2.zero));
                    bool isInRange = focusPos.y >= pathList.y && focusPos.y <= pathList.y + pathList.height * 2 / 3;
                    if (_init || !isInRange) //如果item在一定范围内 就不设置posY
                    {
                        var offset = focusPos.y - (pathList.y + pathList.height / 2);
                        pathList.scrollPane.SetPosY(math.max(0, pathList.scrollPane.posY + offset), false);
                    }
                    
                    TimerManager.instance.RegisterNextFrame((a) => //等一帧绘制
                    {
                        //检查第一个元素是哪个部分的
                        int firstIdx = pathList.ChildIndexToItemIndex(0);
                        MainPathSectionData.MainPathNodesStruct firstData = _pathController.Model.FocusData.SectionData.NodeList[firstIdx];
                
                        void DoChangeAction()
                        {
                            _init = false;
                            _startFocus = false;
                            var selectObj = pathList.GetChildAt(pathList.ItemIndexToChildIndex(_focusIndex));
                            var compLdr = selectObj?.asCom.GetChild("item").asCom.GetChild("compLdr");
                            if (compLdr != null)
                            {
                                var pos = com.parent.GlobalToLocal(compLdr.LocalToGlobal(Vector2.zero) +
                                                            new Vector2(compLdr.width / 2, 0));
                                var imgBg = com.parent.asCom.GetChild("imgBG");
                                if (imgBg == null) return;
                                _lightScale = new Vector2(pos.x / imgBg.width, pos.y / imgBg.height);
                            }

                            var finalIdx = pathList.ChildIndexToItemIndex(0);
                            var finalNode = _pathController.Model.FocusData.SectionData.NodeList[finalIdx];
                            SendChangeScene(finalNode.UnitData.ServerUnitData);
                        }
                        
                        if (!_init || firstData.UnitData == data.UnitData) //是同一个部分就继续
                            DoChangeAction();
                        else if (_init) //切换到focus的部分 先找到的一定是标题
                        {
                            int scrollChildIdx = 0;
                            for (int i = 1; i < pathList.children.Count; i++)
                            {
                                var itemIdx = pathList.ChildIndexToItemIndex(i);
                                MainPathSectionData.MainPathNodesStruct nodeData =
                                    _pathController.Model.FocusData.SectionData.NodeList[itemIdx];
                                if (nodeData.UnitData == data.UnitData)
                                {
                                    scrollChildIdx = i;
                                    break;
                                }
                            }
                
                            var scrollObj = pathList.GetChildAt(scrollChildIdx);
                            var gap = com.GlobalToLocal(scrollObj.LocalToGlobal(Vector2.zero)).y - pathList.y;
                            pathList.scrollPane.SetPosY(math.max(0, pathList.scrollPane.posY + gap), false);//滚动到顶部
                            TimerManager.instance.RegisterNextFrame((a) => DoChangeAction());//等一帧绘制
                        }
                    });
                });
            }

            return 0;
        }

        //最新解锁节点
        private int GetNewUnLockIndex()
        {
            MainPathSectionData sectionData = _pathController.Model.FocusData.SectionData;
            for (int i = sectionData.NodeList.Count - 1; i >= 0; i--)
            {
                var node = sectionData.NodeList[i];
                if (node.NodeData != null && node.NodeData.ServerLevelData.status != PB_ProgressStatusEnum.PSLock)
                    return i;
            }

            return -1;
        }

        private int GetRunningIndex()
        {
            MainPathSectionData sectionData = _pathController.Model.FocusData.SectionData;
            for (int i = sectionData.NodeList.Count - 1; i >= 0; i--)
            {
                var node = sectionData.NodeList[i];
                if (node.NodeData != null && node.NodeData.ServerLevelData.status == PB_ProgressStatusEnum.PSRunning)
                    return i;
            }

            return 0;
        }

        public void Update(int interval)
        {
            if (_isTouching)
            {
                if (_scaleObj != null)
                {
                    // 停止之前的动画
                    if (_tweeners.ContainsKey(_scaleObj))
                    {
                        _tweeners[_scaleObj].Kill();
                    }
                
                    // 开始逐渐变大到1.4的动画
                    var tween = GTween.To(_scaleObj.scale, new Vector2(1.4f, 1.4f), 0.5f)
                        .SetTarget(_scaleObj)
                        .SetEase(EaseType.QuadOut)
                        .OnUpdate(t => { _scaleObj.SetScale(t.value.x, t.value.y); });
                
                    _tweeners[_scaleObj] = tween;
                }
            }
        }

        // 根据滚动位置刷新“回到最新解锁节点”按钮
        private void UpdateBtnToTopOnScroll()
        {
            int latestUnlockedIndex = GetNewUnLockIndex();
            if (latestUnlockedIndex != -1)
            {
                int firstIndex = pathList.ChildIndexToItemIndex(0);
                int lastIndex = pathList.ChildIndexToItemIndex(pathList.children.Count - 2);//多-1是因为底tab有可能挡住1个
                if (firstIndex > latestUnlockedIndex) //上半部分
                {
                    if (!btnToTop.visible)
                        _homePanel.ShowBtnToTop();
                    btnToTop.scaleY = 1f;
                }
                else if (latestUnlockedIndex > lastIndex) //下半部分
                {
                    if (!btnToTop.visible)
                        _homePanel.ShowBtnToTop();
                    btnToTop.visible = true;
                    btnToTop.scaleY = -1f;
                }
                else
                {
                    if (btnToTop.visible)
                        _homePanel.HideBtnToTop();
                    btnToTop.visible = false;
                }
            }
        }
        
        // 根据滚动位置刷新章节详情
        private void UpdateChapterDetailOnScroll()
        {
            int index = pathList.ChildIndexToItemIndex(0);
            MainPathSectionData.MainPathNodesStruct firstNode = _pathController.Model.FocusData.SectionData.NodeList[index];
            if (firstNode.UnitData == null)
                return;
            if (firstNode.UnitData == _currentUnitData)
                return;
            _currentUnitData = firstNode.UnitData;
            RefreshChapterDetail(_currentUnitData);
            SendChangeScene(_currentUnitData.ServerUnitData);
        }

        private void ClearSelect()
        {
            if (_selectNodeIndex == -1) return;
            compNodeStart.HideStartTips();
            _selectNodeIndex = -1;
        }

        #region 发送场景相关事件

        //刷新 & scroll 切换场景
        private void SendChangeScene(PB_UnitData unitData)
        {
            if(_startFocus) return;
            
            _pathController.Model.SetCurSceneUnitData(unitData);
            
            PB_UnitSceneData sceneData = unitData.unit_scene_data;
            VFDebug.LogError($"FUCK 3D Change Scene -- sceneId -> {sceneData.scene_name} avatarId -> {sceneData.avatar_id} forNum -> {sceneData.completed_furniture_count}");
            Notifier.instance.SendNotification(NotifyConsts.MainPath3DSceneChange, new ChangeSceneParams
            {
                sceneId = sceneData.scene_name,
                avatarId = sceneData.avatar_id,
                furNum = sceneData.completed_furniture_count,
                isLightForever = unitData.status == PB_ProgressStatusEnum.PSPass,
                isLightToday = _pathController.Model.LastIsLight, //没有forever亮的时候考虑这个参数 今日是否打卡
                isUnlocked = unitData.status != PB_ProgressStatusEnum.PSLock
            });
            if (_pathController.Model.LastIsLight != GameEntry.SignC.SignModel.signSummary.finish_checkin)
                SendLightUpScene(unitData, _lightScale);
            
            _pathController.Model.SetLastIsLight(GameEntry.SignC.SignModel.signSummary.finish_checkin);

            if (_pathController.Model.RunningLevelData.level_id != _pathController.Model.LastRunningLevelData.level_id) //最新通关的要通知
            {
                SendNodeComplete(unitData, _lightScale);
                _pathController.Model.UpdateLastRunning();
            }
        }

        // 连胜亮了 需考虑explore和path
        private void SendLightUpScene(PB_UnitData unitData, Vector2 lightScale)
        {
            VFDebug.LogError($"FUCK 3D Light Up -- sceneId -> {unitData.unit_scene_data.scene_name} lightScale -> {lightScale}");
            Notifier.instance.SendNotification(NotifyConsts.MainPath3DSceneLightUp, new LightUpParams
            {
                sceneId = unitData.unit_scene_data.scene_name,
                lightScale = lightScale,
                isLightForever = unitData.status == PB_ProgressStatusEnum.PSPass,
                callback = null
            });

            _homePanel.GetUI<MultiTabFrameworkUI>(UIConsts.MultiTabHomepage).LockFrameworkScreen(2500, TabIndex.Course);
        }
        
        // 节点完成特效
        private void SendNodeComplete(PB_UnitData unitData, Vector2 lightScale)
        {
            VFDebug.LogError($"FUCK 3D Node Complete -- sceneId -> {unitData.unit_scene_data.scene_name} lightScale -> {lightScale}");
            Notifier.instance.SendNotification(NotifyConsts.MainPath3DNodeComplete, new LightUpParams
            {
                sceneId = unitData.unit_scene_data.scene_name,
                lightScale = lightScale,
                isLightForever = unitData.status == PB_ProgressStatusEnum.PSPass,
                callback = null 
            });
            
            _homePanel.GetUI<MultiTabFrameworkUI>(UIConsts.MultiTabHomepage).LockFrameworkScreen(2500, TabIndex.Course);
        }

        //切换tab & 进关
        private void SendLeavePath()
        {
            VFDebug.LogError($"FUCK 3D Leave Path");
            Notifier.instance.SendNotification(NotifyConsts.MainPath3DLeavePath);
        }

        private void SendBackPath()
        {
            VFDebug.LogError($"FUCK 3D Back Path");
            Notifier.instance.SendNotification(NotifyConsts.MainPath3DBackPath);
        }

        #endregion

        #region 外部接收事件
        private void OnTabChange(string str, object obj)
        {
            TabData data = (TabData)obj;
            if (data.LastIndex == TabIndex.Course)
            {
                ClearSelect();
                ClearData();
                SendLeavePath();
            }
            if (data.TargetIndex == TabIndex.Course)
            {
                SendBackPath();
                SendChangeScene(_pathController.Model.FocusData.UnitData.ServerUnitData);
            }
            if (data.LastIndex == TabIndex.Explore && data.TargetIndex == TabIndex.Course)
            {
                //看看是否点亮了
                if (_pathController.Model.LastIsLight != GameEntry.SignC.SignModel.signSummary.finish_checkin)
                    SendLightUpScene(_pathController.Model.CurSceneUnitData, Vector3.zero);

                _pathController.Model.SetLastIsLight(GameEntry.SignC.SignModel.signSummary.finish_checkin);
            }
        }

        private void OnHideTips(string str, object obj)
        {
            ClearSelect();
        }

        private void OnExitDialog(string str, object obj)
        {
            SendBackPath();
        }
        
        private void OnUpdateMainPath(string str, object obj)
        {
            if ((string)obj == "ClearData") //切换语言和切换section需要触发clear
                ClearData();
            RefreshMainPath();
        }
        
        #endregion

        private void ClearData()
        {
            _focusIndex = 0;
            _selectNodeIndex = -1;
            _startFocus = false;
            _isTouching = false;
            _currentUnitData = _pathController.Model.FocusData.UnitData;
            _lightScale = Vector2.zero;
            _dicNodeIndexByObj.Clear();
            
            // 清理所有scale动画
            foreach (var tween in _tweeners.Values)
            {
                if (tween != null)
                {
                    tween.Kill();
                }
            }
            _tweeners.Clear();
            if (_scaleObj != null)
            {
                _scaleObj.scale = Vector2.one;
                _scaleObj = null;
            }
        }

        private void AddEvent()
        {
            Notifier.instance.RegisterNotification(NotifyConsts.ExitDialog, OnExitDialog);
            Notifier.instance.RegisterNotification(NotifyConsts.MainTabChange, OnTabChange);
            Notifier.instance.RegisterNotification(NotifyConsts.MainPathHideTipsEvent,OnHideTips);
            Notifier.instance.RegisterNotification(NotifyConsts.UpdateMainPathEvent, OnUpdateMainPath);
        }

        private void RemoveEvent()
        {
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExitDialog, OnExitDialog);
            Notifier.instance.UnRegisterNotification(NotifyConsts.MainTabChange, OnTabChange);
            Notifier.instance.UnRegisterNotification(NotifyConsts.MainPathHideTipsEvent,OnHideTips);
            Notifier.instance.UnRegisterNotification(NotifyConsts.UpdateMainPathEvent, OnUpdateMainPath);
        }
    }
}