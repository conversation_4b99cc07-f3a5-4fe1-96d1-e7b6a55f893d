/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Main
{
    public partial class MultiTabFramework : UIBindT
    {
        public override string pkgName => "Main";
        public override string comName => "MultiTabFramework";

        public BgMask BgOfBg;
        public GComponent TabGList;
        public MuLtiTabInsertContainer insertContainer;
        public BottomTabRegion BottomTabRegion;
        public showTabBtn showTabsBtn;
        public GList VoiceChatInviteMsgBoxList;
        public GGraph imgBG;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            BgOfBg = new BgMask();
            BgOfBg.Construct(com.GetChildAt(0).asCom);
            TabGList = (GComponent)com.GetChildAt(1);
            insertContainer = new MuLtiTabInsertContainer();
            insertContainer.Construct(com.GetChildAt(2).asCom);
            BottomTabRegion = new BottomTabRegion();
            BottomTabRegion.Construct(com.GetChildAt(3).asCom);
            showTabsBtn = new showTabBtn();
            showTabsBtn.Construct(com.GetChildAt(4).asCom);
            VoiceChatInviteMsgBoxList = (GList)com.GetChildAt(5);
            imgBG = (GGraph)com.GetChildAt(6);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            BgOfBg.Dispose();
            BgOfBg = null;
            TabGList = null;
            insertContainer.Dispose();
            insertContainer = null;
            BottomTabRegion.Dispose();
            BottomTabRegion = null;
            showTabsBtn.Dispose();
            showTabsBtn = null;
            VoiceChatInviteMsgBoxList = null;
            imgBG = null;
        }
    }
}