/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.Main
{
    public partial class UIBlur : ExtendedComponent
    {
        public static string pkgName => "Main";
        public static string comName => "UIBlur";
        public static string url => "ui://tnbxk55i9xcdsxxxc6";

        public GGraph UIBlurComp;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(UIBlur));
        }

        public override void ConstructFromXML(XML xml)
        {
            UIBlurComp = GetChildAt(0) as GGraph;

            OnConstructed();

            SetMultiLanguageInChildren();
        }
        public override void Dispose()
        {
            OnWillDispose();

            UIBlurComp = null;

            base.Dispose();
        }
        public void SetMultiLanguageInChildren()
        {
            this.UIBlurComp.SetKey("extends=ExtendedComponent");  // ""
        }
    }
}