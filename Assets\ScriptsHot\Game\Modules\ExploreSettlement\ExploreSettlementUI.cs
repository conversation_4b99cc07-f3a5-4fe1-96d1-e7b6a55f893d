﻿using System;
using System.Collections.Generic;
using System.Linq;
using FairyGUI;
using Msg.explore;
using ScriptsHot.Game.Modules.Explore;
using ScriptsHot.Game.Modules.Sign;
using UIBind.ExploreSettlement;
using UnityEngine;

namespace ScriptsHot.Game.Modules.ExploreSettlement
{
    public class ExploreSettlementUI : BaseUI<ExploreSettlementPanel>,IBaseUIUpdate
    {
        public ExploreSettlementUI(string name) : base(name)
        {
        }

        private ExploreSettlementController Controller =>
            GetController<ExploreSettlementController>(ModelConsts.ExploreSettlement);

        private SignController SignController => GetController<SignController>(ModelConsts.Sign);
        
        private ExploreController ExploreController =>
            GetController<ExploreController>(ModelConsts.Explore);

        public override string uiLayer => UILayerConsts.Top;

        protected override void OnInit(GComponent uiCom)
        {
            base.OnInit(uiCom);
            AddUIEvent(ui.imgBG.onClick, () => Hide());
        }

        private int _curMarkIndex;
        protected override void OnShow()
        {
            base.OnShow();
            SoundManger.instance.PlayUI("question_settle");
            
            ui.mask.visible = true;
            _curMarkIndex = -1;
            OnRefreshSettle();
            
            APPEAR_EXPLORE_MISSION_RESULT_PAGE dot = new APPEAR_EXPLORE_MISSION_RESULT_PAGE();
            dot.task_id = Controller.Model.SettlementInfo.commonData.taskId;
            dot.next_task_id = Controller.Model.SettlementInfo.nextEntityInfo.storyPreloadData.taskId;
            dot.continue_days = Controller.Model.SettlementInfo.checkin_item?.continue_days ?? 0;
            dot.speech_time = Controller.Model.SettlementInfo.taskSpeechTime;
            DataDotMgr.Collect(dot);
            _countdownTime = 0;
            _countdownTimer = 0f;
            _onCountdownFinished = () => { DoNext(Controller.Model.SettlementInfo); };
        }

        protected override void OnHide()
        {
            base.OnHide();
            ui.list.RemoveChildren(0, -1, true);
        }
        
        private readonly List<string> _markNormal = new()
        {
            "ui://Settlement/icon_terrible_normal",
            "ui://Settlement/icon_fine_normal",
            "ui://Settlement/icon_awesome_normal",
        };
        
        private readonly List<string> _markSelect = new()
        {
            "ui://Settlement/icon_terrible_selected",
            "ui://Settlement/icon_fine_selected",
            "ui://Settlement/icon_awesome_selected",
        };

        private readonly List<string> _markLanguage = new()
        {
            "ui_settle_mark_terrible",
            "ui_settle_mark_fine",
            "ui_settle_mark_awesome",
        };
        
        //不存gap
        private Dictionary<ExploreSettlementModel.ExploreSettle, GComponent> _componentDic = new();
        private CompSettleExploreNextItem _nextItem;
        protected override bool isFullScreen => true;

        private float _initGapItemHeight;
        private float _lastHeight;
        private void OnRefreshSettle()
        {
            var settleData = Controller.Model.SettlementInfo;
            
            int index = 0;

            var showDic = RefreshShowDic();
            
            foreach (var info in showDic)
            {
                ui.list.defaultItem = info.Value;
                GComponent com = ui.list.AddItemFromPool().asCom;
                com.visible = false;
                switch (info.Key)
                {
                    case ExploreSettlementModel.ExploreSettle.GapItemHead:
                        com.visible = true;
                        CompSettleExploreGapHead gapH = new CompSettleExploreGapHead();
                        gapH.Construct(com);
                        _initGapItemHeight = com.height;
                        break;
                    case ExploreSettlementModel.ExploreSettle.GapItemTail:
                        com.visible = true;
                        CompSettleExploreGapTail gapT = new CompSettleExploreGapTail();
                        gapT.Construct(com);
                        break;
                    case ExploreSettlementModel.ExploreSettle.DetailItem:
                        CompSettleExploreDetailItem detail = new CompSettleExploreDetailItem();
                        detail.Construct(com);
                        detail.rightHide.Play();
                        detail.tfTime.text = "00:00";
                        
                        if (settleData.nextEntityInfo.nextEntityType ==
                            PB_Explore_MissionStoryChatNextEntityType.EO_MSC_NET_TASK)
                        {
                            detail.tfTitle.SetKeyArgs("ui_explore_settlement_finish_task_text",
                                settleData.finishedTaskInfo.taskTitle);
                        }
                        else if (settleData.nextEntityInfo.nextEntityType ==
                                 PB_Explore_MissionStoryChatNextEntityType.EO_MSC_NET_STORY)
                        {
                            detail.tfTitle.SetKeyArgs("ui_explore_settlement_finish_story_text",
                                settleData.finishedTaskInfo.storyTitle);
                        }
                        
                        detail.tfTitleDesc.SetKeyArgs("ui_explore_settlement_incentive_text",
                            settleData.exceedUserPercent, settleData.userCefrLevel);
                        detail.tfTimeDesc.SetKey("ui_explore_settlement_chat_len");
                        detail.tfAppoint.text = settleData.finishedTaskInfo.storyTitle;
                        detail.tfAppointDesc.text = settleData.finishedTaskInfo.taskTitle;
                        break;
                    case ExploreSettlementModel.ExploreSettle.DayItem:
                        CompSettleExploreDayItem day = new CompSettleExploreDayItem();
                        day.Construct(com);
                        day.tfDays.SetVar("day", settleData.checkin_item.continue_days.ToString())
                            .FlushVars();
                        
                        DateTime dateTime = DateTime.Parse(settleData.checkin_item.checkin_list[^1].date);
                        var signDay = SignController.GetDayOfWeekKey(dateTime.DayOfWeek);
                        var startDate = dateTime.AddDays(signDay == 7 ? 0 : -signDay);
                        var curDay = 7;
                        var startDate2 = startDate;
                        var isContinue = true;
                        for (int i = 0; i < 7; i++)
                        {
                            var date = startDate2.AddDays(i).ToString("yyyy-MM-dd");
                            var state = SignController.GetDateState(date, settleData.checkin_item.checkin_list.ToList());
                            if (state == SignController.DayCmpState.none || state == SignController.DayCmpState.repair)
                                isContinue = false;
                            if (date == dateTime.ToString("yyyy-MM-dd"))
                                break;
                        }
                        
                        day.listDay.itemRenderer = (tIndex, tObj) =>
                        {
                            SignPersistDayComponent dayItem = new SignPersistDayComponent();
                            dayItem.Construct(tObj as GComponent);
                            
                            var date = startDate.AddDays(tIndex).ToString("yyyy-MM-dd");
                            SignController.DayCmpState curState = isContinue
                                ? SignController.DayCmpState.hide
                                : SignController.GetDateState(date, settleData.checkin_item.checkin_list.ToList());
                            bool isNew = SignController.IsNew(date, settleData.checkin_item.checkin_list.ToList());
                            dayItem.tfDay.SetKey($"common_day_of_week_short_{curDay}");
                            dayItem.dummy_index.text = tIndex.ToString();
                            dayItem.state.selectedPage = curState.ToString();
                            if (curState == SignController.DayCmpState.done)
                            {
                                dayItem.iceHide.Play();
                                if (isNew)
                                {
                                    dayItem.spCheckin.spineAnimation.AnimationState.ClearTracks();
                                    dayItem.spCheckin.spineAnimation.AnimationState.SetAnimation(0, "yellow1", false).Complete += (t) =>
                                    {
                                        dayItem.spCheckin.spineAnimation.AnimationState.SetAnimation(0, "yellow2", false);
                                    };
                                }
                                else
                                {
                                    dayItem.spCheckin.spineAnimation.AnimationState.SetAnimation(0, "yellow2", false);
                                }
                            }
                            else if (curState == SignController.DayCmpState.repair)
                            {
                                dayItem.spCheckin.visible = false;
                                if (isNew)
                                {
                                    dayItem.iceShow.Play();
                                }
                                else
                                {
                                    dayItem.iceShow.Play();
                                    dayItem.iceShow.Stop();
                                }
                            }
                            
                            curDay = curDay >= 7 ? 1 : curDay + 1;
                        };
                        day.listDay.numItems = 7;
                        break;
                    case ExploreSettlementModel.ExploreSettle.MarkItem:
                        CompSettleExploreMarkItem mark = new CompSettleExploreMarkItem();
                        mark.Construct(com);
                        mark.grp.EnsureBoundsCorrect();
                        mark.listMark.itemRenderer = (mIndex, mObj) =>
                        {
                            CompMarkSmallItem item = new CompMarkSmallItem();
                            item.Construct(mObj as GComponent);
                            item.ldrTerrible.url = mIndex == _curMarkIndex ? _markSelect[mIndex] : _markNormal[mIndex];
                            item.tfText.SetKey(_markLanguage[mIndex]);
                        };
                        AddUIEvent(mark.listMark.onClickItem,(ext)=>
                        {
                            GComponent comp = ext.sender as GComponent;
                            if (comp == null) return;
                            int tIndex = comp.GetChildIndex(ext.data as GObject);
                            if (tIndex == _curMarkIndex)
                                return;
                            _curMarkIndex = tIndex;
                            _componentDic[ExploreSettlementModel.ExploreSettle.MarkItem].GetChild("listMark").asList
                                .numItems = _markNormal.Count;

                            CLICK_EXPLORE_MISSION_RESULT_FEEDBACK_PANEL_BUTTON dot =
                                new CLICK_EXPLORE_MISSION_RESULT_FEEDBACK_PANEL_BUTTON();
                            dot.task_id = settleData.commonData.taskId;
                            dot.feedback_type = (FeedbackType)tIndex;
                            DataDotMgr.Collect(dot);
                        });
                        mark.tfTitle.SetKey("ui_explore_settlement_difficulty_feedback_text");
                        mark.listMark.numItems = _markNormal.Count;
                        break;
                    case ExploreSettlementModel.ExploreSettle.NextItem:
                        CompSettleExploreNextItem next = new CompSettleExploreNextItem();
                        next.Construct(com);
                        _nextItem = next;
                        RefreshNextItem(next, settleData);
                        break;
                }

                _componentDic[info.Key] = com;
                if (index == 1)
                {
                    GObject obj = ui.list.GetChildAt(0);
                    obj.height = Mathf.Max(ui.list.height / 2 - ui.list.lineGap - com.height / 2,
                        _initGapItemHeight);
                    _lastHeight = obj.height;
                    com.visible = true;
                    int totalSeconds = settleData.taskSpeechTime;
                    com.GetTransition("show")?.Play(1,0, () =>
                    {
                        GTextField tfTime = com.GetChild("tfTime").asTextField;
                        
                        GTween.To(0, totalSeconds, 0.5f)
                            .SetTarget(tfTime)
                            .OnUpdate((t) => {
                                int curSec = Mathf.RoundToInt(t.value.x);
                                int min = curSec / 60;
                                int sec = curSec % 60;
                                tfTime.text = $"{min:D2}:{sec:D2}";
                            })
                            .OnComplete(() => {
                                int min = totalSeconds / 60;
                                int sec = totalSeconds % 60;
                                tfTime.text = $"{min:D2}:{sec:D2}";
                                com.GetTransition("rightShow").Play();
                            });
                    });
                }
                else if (index > 1)
                {
                    if (index == showDic.Count - 1)
                    {
                        ui.mask.visible = false;
                        return;
                    }
                    
                    TimerManager.instance.RegisterTimer((a) =>
                    {
                        GObject obj = ui.list.GetChildAt(0);
                        float fromHeight = obj.height;
                        float toHeight = Mathf.Max(_lastHeight - ui.list.lineGap - 200, _initGapItemHeight);
                        GTween.To(fromHeight, _lastHeight, 0.2f)
                            .SetTarget(obj)
                            .OnUpdate((t) =>
                            {
                                obj.height = t.value.x;
                            })
                            .OnComplete(() =>
                            {
                                obj.height = toHeight; // 最后确保精确
                                _lastHeight = obj.height;
                            });

                        com.visible = true;
                        com.GetTransition("show")?.Play();
                    }, 1200 + (index - 1) * 500);
                }
                
                index++;
            }
        }

        private Dictionary<ExploreSettlementModel.ExploreSettle, string> RefreshShowDic()
        {
            Dictionary<ExploreSettlementModel.ExploreSettle, string> tempDic = new();
            foreach (var info in Controller.Model.SettleShowListUrl)
            {
                if (info.Key == ExploreSettlementModel.ExploreSettle.DayItem &&
                    Controller.Model.SettlementInfo.checkin_item == null)
                    continue;
                
                tempDic.Add(info.Key, info.Value);
            }
            return tempDic;
        }
        
        private void RefreshNextItem(CompSettleExploreNextItem next ,SC_MissionStoryChatDownMsgForSettlement settleData)
        {
            _nextItem.show.SetHook("doCountDown", () => _countdownTime = 10);
            _nextItem.tfStart.SetVar("start", I18N.inst.MoStr("common_start"))
                .SetVar("time", "10").FlushVars();
            _nextItem.tfNext.SetKey("ui_explore_settlement_next");
            
            // Debug.LogError($"avatar txt 下一关：{settleData.nextEntityInfo.nextEntityType}");
            if (settleData.nextEntityInfo.nextEntityType ==
                PB_Explore_MissionStoryChatNextEntityType.EO_MSC_NET_TASK)
            {
                next.tfTitle.text = settleData.nextEntityInfo.storyTitle;
                next.tfDesc.text = settleData.nextEntityInfo.taskTitle;
            }

            else if (settleData.nextEntityInfo.nextEntityType ==
                     PB_Explore_MissionStoryChatNextEntityType.EO_MSC_NET_STORY)
            {
                ExploreItemData nextData = ExploreController.GetNextEntityInfo();
                if (nextData != null)
                {
                    next.tfTitle.text = nextData.Data.detail.storyTitle;
                    next.tfDesc.text = nextData.Data.detail.storyProgress.currentTaskTitle;
                }
                else
                {
                    next.tfTitle.text = string.Empty;
                    next.tfDesc.text = string.Empty;
                }
            }

            next.btnStart.onClick.Add(() => { DoNext(settleData); });
        }

        private void DoNext(SC_MissionStoryChatDownMsgForSettlement settleData)
        {
            CLICK_EXPLORE_MISSION_RESULT_NEXT_TASK_CARD_BUTTON dot =
                new CLICK_EXPLORE_MISSION_RESULT_NEXT_TASK_CARD_BUTTON();
            dot.task_id = settleData.commonData.taskId;
            dot.next_task_id = settleData.nextEntityInfo.storyPreloadData.taskId;
            DataDotMgr.Collect(dot);

            if (settleData.nextEntityInfo.nextEntityType ==
                PB_Explore_MissionStoryChatNextEntityType.EO_MSC_NET_TASK)
                SendNotification(NotifyConsts.ExploreTaskChange, settleData.nextEntityInfo);
            else if (settleData.nextEntityInfo.nextEntityType ==
                     PB_Explore_MissionStoryChatNextEntityType.EO_MSC_NET_STORY)
                SendNotification(NotifyConsts.ExploreLookAtNextStory);
            this.Hide();
        }

        private int _countdownTime;
        private float _countdownTimer; // 用于累计时间
        private Action _onCountdownFinished;

        public void Update(int interval)
        {
            if (_countdownTime > 0)
            {
                _countdownTimer += interval / 1000f;

                if (_countdownTimer >= 1f)
                {
                    _countdownTimer -= 1f;
                    _countdownTime--;
                    _nextItem.tfStart.SetVar("start", I18N.inst.MoStr("common_start"))
                        .SetVar("time", _countdownTime.ToString()).FlushVars();

                    if (_countdownTime == 0)
                        _onCountdownFinished?.Invoke();
                }
            }
        }
    }
}