/*
****************************************************
# 作者：XuHui
# 创建时间：2024/03/05 17:19:29 星期二
# 功能：Nothing
****************************************************
*/

using System.Collections.Generic;
using FairyGUI;
using Msg;
using Msg.task_process;
using UIBind.Progress;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Task
{
    public class ProgressStrengthUI : BaseUI<UIBind.Progress.ProgressStrengthenPanel>
    {
        public override void OnBackBtnClick()
        {
            // var mainHeaderUI = GetUI<MainHeaderUI>(UIConsts.MainHeader);
            // if (mainHeaderUI.isShow)
            // {
            //     mainHeaderUI.OnBtnLeftTopClick();
            // }
        }
        
        private bool _isShowDetail = false;

        private static Color COLOR_GREEN = new Color(33 / 255f, 208 / 255f, 107 / 255f, 1f);
        private static Color COLOR_RED = new Color(1, 0, 122 / 255f, 1f);

        public ProgressStrengthUI(string name) : base(name)
        {
        }

        public override string uiLayer => UILayerConsts.Home;
        protected override bool isFullScreen => true;
        private TaskModel model => this.GetModel<TaskModel>(ModelConsts.Task);

        private int _nodeOneIndex = 0;

        protected override void OnInit(GComponent uiCom)
        {
            
        }

        protected override void OnShow()
        {
            this.SetUI();

            RevertAfterAnimation();

            Debug.Log("ProgressStrengthUI OnShow");
        }

        protected override void OnHide()
        {
            base.OnHide();
            ClearUI();
        }

        private void SetUI()
        {
            this.InitBarUI();
        }

        //初始化进度条信息
        private void InitBarUI()
        {
            if (this.model.taskTotalNum <= 0) return;
            this.ui.barTaskProgress.max = this.model.taskTotalNum;
            this.ui.barTaskProgress.min = 0;
            this.ui.barTaskProgress.value = 0;
            var bar = this.ui.barTaskProgress.GetChild("bar") as GImage;
            bar.pivot = new Vector2(0, 0);
            bar.color = COLOR_GREEN;
            bar.scale = Vector2.one;

            this.ui.barTaskProgress.value = 0.3f / this.ui.barTaskProgress.max ;
        }


        //进度条增长
        public void SetBarUI()
        {
            if(ui.barTaskProgress.max != this.model.taskTotalNum)
                this.ui.barTaskProgress.max = this.model.taskTotalNum;
        
            //进度增长
            if (this.model.curTaskStatus != TaskStepStatus.NoneStep)
            {
                if (model.curTaskStatus == TaskStepStatus.UnFinishStep)
                {
                    PlayWrongEffect();
                    return;
                }
                int targetIndex = this.model.curTaskIndex + 1;
                this.ui.barTaskProgress.TweenValue(targetIndex, 0.6f).OnComplete(() =>
                {
                    if (this.model.curTaskStatus == TaskStepStatus.FinishStep)
                    {
                        SetNodeUI(true);
                    }
                    else if (this.model.curTaskStatus == TaskStepStatus.UnFinishStep)
                    {
                        SetNodeUI(false);
                    }
                });
                this.ui.barTaskProgress.GetChild("bar").pivot = new Vector2(0, 0.5f);
                this.ui.barTaskProgress.GetChild("bar").TweenScale(new Vector2(1, 1f), 0.2f).OnComplete(() =>
                {
                    this.ui.barTaskProgress.GetChild("bar").TweenScale(new Vector2(1, 1.8f), 0.2f).OnComplete(() =>
                    {
                        this.ui.barTaskProgress.GetChild("bar").TweenScale(new Vector2(1, 1f), 0.2f);
                        this.ui.barTaskProgress.GetChild("bar").pivot = new Vector2(0, 0);
                    });
                });
            }
        }

        public void PlayWrongEffect()
        {
            var com = this.ui.barTaskProgress.GetChild("bar") as GImage;

            com.color = COLOR_RED;
            com.TweenMoveY(1f, 0.05f).OnComplete(() => com.TweenMoveY(8f, 0.05f).OnComplete(() =>
                com.TweenMoveY(5f, 0.05f).OnComplete(() =>
                    com.TweenMoveY(9f, 0.05f).OnComplete(() => com.TweenMoveY(8f, 0.05f).OnComplete(() =>
                    {
                        com.color = COLOR_GREEN;
                    }))
                )));
        }

        //播放节点动画
        void SetNodeUI(bool isSuccess)
        {
           
        }

        //播放任务切换动画
        void PlayTaskSwitch()
        {
            this.model.SetTaskStatus(TaskStepStatus.UnStartStep);
        }

        public void ShowAllTaskInfo(bool isShow)
        {
        }


        //进入强化模式动效
        public void EnterStrengthenMode()
        {
        }


        //处理事件
        protected override void HandleNotification(string name, object body)
        {
            if (this.ui.com.visible)
            {
                if (name == NotifyConsts.TaskStatusChange)
                {
                    this.SetBarUI();
                }
            }
        }

        //绑定事件
        protected override string[] ListNotificationInterests()
        {
            return new string[]
            {
                NotifyConsts.TaskStatusChange,
            };
        }

        public void DismissWithAnimation()
        {
            ui.Out.Play(() => { });
        }

        public void RevertAfterAnimation()
        {
            ui.Reset.Play(() => { });
        }

        void ClearUI()
        {
            this.ui.barTaskProgress.value = 0;
        }
    }
}