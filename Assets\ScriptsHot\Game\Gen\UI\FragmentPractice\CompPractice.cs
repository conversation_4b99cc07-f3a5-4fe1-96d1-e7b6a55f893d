/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class CompPractice : ExtendedComponent
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "CompPractice";
        public static string url => "ui://cmoz5osjpy44q";

        public Controller previous;
        public Controller ctrlQuestion;
        public GGraph bgNoUse;
        public GImage bg;
        public GTextField tfQuestionId;
        public CompTag compTag;
        public GTextField tfQuestType;
        public GComponent loaderQuestion;
        public GComponent loaderAnswer;
        public GGroup grpContent;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(CompPractice));
        }

        public override void ConstructFromXML(XML xml)
        {
            previous = GetControllerAt(0);
            ctrlQuestion = GetControllerAt(1);
            bgNoUse = GetChildAt(0) as GGraph;
            bg = GetChildAt(1) as GImage;
            tfQuestionId = GetChildAt(3) as GTextField;
            compTag = GetChildAt(5) as CompTag;
            tfQuestType = GetChildAt(7) as GTextField;
            loaderQuestion = GetChildAt(9) as GComponent;
            loaderAnswer = GetChildAt(11) as GComponent;
            grpContent = GetChildAt(13) as GGroup;

            OnConstructed();

            SetMultiLanguageInChildren();
        }
        public override void Dispose()
        {
            OnWillDispose();

            previous = null;
            ctrlQuestion = null;
            bgNoUse = null;
            bg = null;
            tfQuestionId = null;
            compTag = null;
            tfQuestType = null;
            loaderQuestion = null;
            loaderAnswer = null;
            grpContent = null;

            base.Dispose();
        }
        public void SetMultiLanguageInChildren()
        {
            this.bgNoUse.SetKey("BLUR");  // ""
            this.bg.SetKey("BLUR");  // ""
        }
    }
}