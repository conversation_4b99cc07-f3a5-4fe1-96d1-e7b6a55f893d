Shader "SpecialUI/RTMixShader"
{
    Properties
    {
        _MainTex ("Main Texture", 2D) = "white" {}
       _SecondTex ("Second Texture", 2D) = "white" {}
      _MixFactor ("Mix Factor", Range(0, 1)) = 0.5
        [KeywordEnum(Normal, Multiply, Screen, Overlay, SoftLight, HardLight, ColorDodge, ColorBurn, Darken, Lighten, Difference, Exclusion)]
        _BlendMode ("Blend Mode", Float) = 0
    }
    
    SubShader
    {
        Tags
        {
            "RenderType" = "Opaque"
            "RenderPipeline" = "UniversalPipeline"
        }
        
        Pass
        {
            Name "RTMix"
            
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            
            struct Attributes
            {
                float4 positionOS : POSITION;
                float2 uv : TEXCOORD0;
            };
            
            struct Varyings
            {
                float2 uv : TEXCOORD0;
                float4 positionHCS : SV_POSITION;
            };
            
            TEXTURE2D(_MainTex);
            SAMPLER(sampler_MainTex);
            TEXTURE2D(_SecondTex);
            SAMPLER(sampler_SecondTex);
            
            float _MixFactor;
            
            Varyings vert(Attributes input)
            {
                Varyings output;
                output.positionHCS = TransformObjectToHClip(input.positionOS.xyz);
                output.uv = input.uv;
                return output;
            }
            
         
            
            half4 frag(Varyings input) : SV_Target
            {
                float2 uv = input.uv;
                
                half4 mainColor = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, uv);
                half4 secondColor = SAMPLE_TEXTURE2D(_SecondTex, sampler_SecondTex, uv);
                
                float3 blendedColor = secondColor.rgb;
                
                float3 finalColor = lerp(mainColor.rgb, blendedColor, _MixFactor);
                
                float finalAlpha = lerp(mainColor.a, secondColor.a, _MixFactor);
                
                return half4(finalColor, finalAlpha);
            }
            
            ENDHLSL
        }
    }
}
