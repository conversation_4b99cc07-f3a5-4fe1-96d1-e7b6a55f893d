﻿
using ScriptsHot.Game.Modules.Explore.ExploreType.Base;
using ScriptsHot.Game.Modules.Explore.State.Base;
using ScriptsHot.Game.Modules.Procedure;
using UnityEngine;
using NotImplementedException = System.NotImplementedException;

namespace ScriptsHot.Game.Modules.Explore.State.ExploreMission
{
    public class ExploreMissionStart:ExploreStateBase
    {
        private ExploreController _controller;
        
        private ExploreParam _param;

        private bool _isRuning = false;
        public ExploreMissionStart( ExploreEntityBase chat) : base(ExploreStateName.ExploreMissionStart, chat)
        {
        }
        
        public override void OnEnter(params object[] args)
        {
            base.OnEnter(args);
            _isRuning = false;
            _param = (ExploreParam)args[0];
            _controller = ControllerManager.instance.GetController<ExploreController>(ModelConsts.Explore) as ExploreController;
           
            //隐藏顶部工具栏
            Notifier.instance.SendNotification(NotifyConsts.ExploreTitleTooolVisible,false);
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreEnterEntityState,OnEnterEnterState);
        }

        private void OnEnterEnterState(string s, object body)
        {  
            _param.Entity.ChangeState(ExploreStateName.ExploreMissionPlaying,_param);
        }

        override public void Update(int interval)
        {
            if (_param != null && _param.Entity.UI.GetModelLoaded())
            {
                Run();
            }
        }

        private void Run()
        {
            if (_isRuning) return;
            _isRuning = true;
            
            VFDebug.Log($"ExploreMissionStart:: Run  模型加载完毕进入");
            ProcedureParams p = new ProcedureParams();
            p.param = _param;
            p.type = EProcedureType.ExploreEnterAudio;
            //清空队列
            Notifier.instance.SendNotification(NotifyConsts.procedure_main_break);
            
            Notifier.instance.SendNotification(NotifyConsts.ExploreEnterIntroState);
            Notifier.instance.SendNotification(NotifyConsts.ExploreStepStateChange,ExploreStep.Intro);
        }

        public override void OnReEnter(params object[] args)
        {
            base.OnReEnter(args);
        }

        public override void OnExit()
        {
            base.OnExit();
            _isRuning = false;
            
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreEnterEntityState,OnEnterEnterState);
        }
    }
}