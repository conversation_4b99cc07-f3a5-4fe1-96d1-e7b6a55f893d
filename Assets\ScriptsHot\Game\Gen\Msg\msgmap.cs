using System;
using System.Collections.Generic;

using Msg.achievement;
using Msg.aitown;
using Msg.dialog_base;
using Msg.dialog_flow;
using Msg.dialog_task;
using Msg.economic;
using Msg.learn_assist;
using Msg.question;
using Msg.question_process;
using Msg.speech;
using Msg.task;
using Msg.task_process;
using Msg.translate;
using Msg.tts;
using Msg.world;
using Msg.GM;
using Msg.core;
using Msg.recommend;
using Msg.incentive;
using Msg.notification;
using Msg.talkitpush;
using Msg.center;
using Msg.social;
using Msg.login;
using Msg.social;
using Msg.abtest;
using Msg.course;
using Msg.explore;

namespace Msg
{
	public class MsgMap
	{
		public static Dictionary<short, Type> map = new Dictionary<short, Type>
		{
			
			
			[(short)GameMSGID.CS_LoginReq_ID] = typeof(CS_LoginReq),
			[(short)GameMSGID.SC_LoginAck_ID] = typeof(SC_LoginAck),
			[(short)GameMSGID.CS_GetRoleInfoReq_ID] = typeof(CS_GetRoleInfoReq),
			[(short)GameMSGID.SC_GetRoleInfoAck_ID] = typeof(SC_GetRoleInfoAck),
			[(short)GameMSGID.CS_EnterGameZoneReq_ID] = typeof(CS_EnterGameZoneReq),
			[(short)GameMSGID.SC_EnterGameZoneAck_ID] = typeof(SC_EnterGameZoneAck),
			
			
			
			
			[(short)GameMSGID.CS_ExitGameReq_ID] = typeof(CS_ExitGameReq),
			[(short)GameMSGID.SC_ExitGameAck_ID] = typeof(SC_ExitGameAck),
			[(short)GameMSGID.CS_ChangeRoleNameReq_ID] = typeof(CS_ChangeRoleNameReq),
			[(short)GameMSGID.SC_ChangeRoleNameAck_ID] = typeof(SC_ChangeRoleNameAck),
			[(short)GameMSGID.SC_ChangeRoleNameNtf_ID] = typeof(SC_ChangeRoleNameNtf),
			
			
			
			
			
			
			[(short)GameMSGID.CS_HeartBeatReq_ID] = typeof(CS_HeartBeatReq),
			[(short)GameMSGID.SC_HeartBeatAck_ID] = typeof(SC_HeartBeatAck),
			[(short)GameMSGID.CS_CreateRoleReq_ID] = typeof(CS_CreateRoleReq),
			[(short)GameMSGID.SC_CreateRoleAck_ID] = typeof(SC_CreateRoleAck),
			[(short)GameMSGID.CS_SelectRoleReq_ID] = typeof(CS_SelectRoleReq),
			[(short)GameMSGID.SC_SelectRoleAck_ID] = typeof(SC_SelectRoleAck),
			
			[(short)GameMSGID.SC_RoleOnlineInfoNtf_ID] = typeof(SC_RoleOnlineInfoNtf),
			[(short)GameMSGID.SC_SelfInfoNtf_ID] = typeof(SC_SelfInfoNtf),
			[(short)GameMSGID.SC_RoleAppearInfoNtf_ID] = typeof(SC_RoleAppearInfoNtf),
			[(short)GameMSGID.SC_AvatarAppearInfoNtf_ID] = typeof(SC_AvatarAppearInfoNtf),
			[(short)GameMSGID.SC_ObjMoveNtf_ID] = typeof(SC_ObjMoveNtf),
			[(short)GameMSGID.SC_ObjDisappearNtf_ID] = typeof(SC_ObjDisappearNtf),
			[(short)GameMSGID.CS_RoleMoveReq_ID] = typeof(CS_RoleMoveReq),
			[(short)GameMSGID.SC_RoleMoveAck_ID] = typeof(SC_RoleMoveAck),
			[(short)GameMSGID.CS_RoleChangeSceneReq_ID] = typeof(CS_RoleChangeSceneReq),
			[(short)GameMSGID.SC_RoleChangeSceneAck_ID] = typeof(SC_RoleChangeSceneAck),
			[(short)GameMSGID.SC_BubblingInfoNtf_ID] = typeof(SC_BubblingInfoNtf),
			[(short)GameMSGID.SC_ActionInfoNtf_ID] = typeof(SC_ActionInfoNtf),
			[(short)GameMSGID.CS_TalkToAvatarReq_ID] = typeof(CS_TalkToAvatarReq),
			[(short)GameMSGID.SC_TalkToAvatarAck_ID] = typeof(SC_TalkToAvatarAck),
			[(short)GameMSGID.CS_GoPlayerHomeReq_ID] = typeof(CS_GoPlayerHomeReq),
			[(short)GameMSGID.SC_GoPlayerHomeAck_ID] = typeof(SC_GoPlayerHomeAck),
			[(short)GameMSGID.CS_LeavePlayerHomeReq_ID] = typeof(CS_LeavePlayerHomeReq),
			[(short)GameMSGID.SC_LeavePlayerHomeAck_ID] = typeof(SC_LeavePlayerHomeAck),
			
			
			
			
			[(short)GameMSGID.CS_GetCurLineAvatarInfoReq_ID] = typeof(CS_GetCurLineAvatarInfoReq),
			[(short)GameMSGID.SC_GetCurLineAvatarInfoAck_ID] = typeof(SC_GetCurLineAvatarInfoAck),
			[(short)GameMSGID.CS_PlayerInTaskReq_ID] = typeof(CS_PlayerInTaskReq),
			[(short)GameMSGID.SC_PlayerInTaskAck_ID] = typeof(SC_PlayerInTaskAck),
			[(short)GameMSGID.SC_AvatarMoveToSelfNtf_ID] = typeof(SC_AvatarMoveToSelfNtf),
			[(short)GameMSGID.CS_AgreeAvatarMoveReq_ID] = typeof(CS_AgreeAvatarMoveReq),
			[(short)GameMSGID.SC_AgreeAvatarMoveAck_ID] = typeof(SC_AgreeAvatarMoveAck),
			[(short)GameMSGID.CS_GetBuildingAvatarInfoReq_ID] = typeof(CS_GetBuildingAvatarInfoReq),
			[(short)GameMSGID.SC_GetBuildingAvatarInfoAck_ID] = typeof(SC_GetBuildingAvatarInfoAck),
			
			
			[(short)GameMSGID.SC_PlayerLadderLevelChange_ID] = typeof(SC_PlayerLadderLevelChange),
			
			[(short)GameMSGID.SC_MemberTypeChangeNtf_ID] = typeof(SC_MemberTypeChangeNtf),
			[(short)GameMSGID.CS_GetAvatarJobInfoReq_ID] = typeof(CS_GetAvatarJobInfoReq),
			[(short)GameMSGID.SC_GetAvatarJobInfoAck_ID] = typeof(SC_GetAvatarJobInfoAck),
			
			[(short)GameMSGID.CS_VoiceDialogReq_ID] = typeof(CS_VoiceDialogReq),
			[(short)GameMSGID.SC_VoiceDialogAck_ID] = typeof(SC_VoiceDialogAck),
			[(short)GameMSGID.SC_VoiceDialogNtf_ID] = typeof(SC_VoiceDialogNtf),
			[(short)GameMSGID.CS_DealVoiceDialogReq_ID] = typeof(CS_DealVoiceDialogReq),
			[(short)GameMSGID.SC_DealVoiceDialogAck_ID] = typeof(SC_DealVoiceDialogAck),
			[(short)GameMSGID.SC_DealVoiceDialogNtf_ID] = typeof(SC_DealVoiceDialogNtf),
			[(short)GameMSGID.CS_GetVoiceDialogRecordReq_ID] = typeof(CS_GetVoiceDialogRecordReq),
			[(short)GameMSGID.SC_GetVoiceDialogRecordAck_ID] = typeof(SC_GetVoiceDialogRecordAck),
			[(short)GameMSGID.SC_PlayerStateInfoNtf_ID] = typeof(SC_PlayerStateInfoNtf),
			[(short)GameMSGID.SC_StartVoiceDialogNtf_ID] = typeof(SC_StartVoiceDialogNtf),
			[(short)GameMSGID.CS_VoiceDialogStateChangeReq_ID] = typeof(CS_VoiceDialogStateChangeReq),
			[(short)GameMSGID.SC_VoiceDialogStateChangeAck_ID] = typeof(SC_VoiceDialogStateChangeAck),
			[(short)GameMSGID.SC_VoiceDialogStateChangeNtf_ID] = typeof(SC_VoiceDialogStateChangeNtf),
			[(short)GameMSGID.CS_StopVoiceDialogReq_ID] = typeof(CS_StopVoiceDialogReq),
			[(short)GameMSGID.SC_StopVoiceDialogAck_ID] = typeof(SC_StopVoiceDialogAck),
			[(short)GameMSGID.SC_StopVoiceDialogNtf_ID] = typeof(SC_StopVoiceDialogNtf),
			[(short)GameMSGID.CS_CancelVoiceDialogReq_ID] = typeof(CS_CancelVoiceDialogReq),
			[(short)GameMSGID.SC_CancelVoiceDialogAck_ID] = typeof(SC_CancelVoiceDialogAck),
			[(short)GameMSGID.SC_CancelVoiceDialogNtf_ID] = typeof(SC_CancelVoiceDialogNtf),
			[(short)GameMSGID.SC_ForbidVoiceDialogChangeNtf_ID] = typeof(SC_ForbidVoiceDialogChangeNtf),
			[(short)GameMSGID.CS_VoiceDialogInfoReq_ID] = typeof(CS_VoiceDialogInfoReq),
			[(short)GameMSGID.SC_VoiceDialogInfoAck_ID] = typeof(SC_VoiceDialogInfoAck),
			[(short)GameMSGID.SC_VoiceDialogInfoNtf_ID] = typeof(SC_VoiceDialogInfoNtf),
			[(short)GameMSGID.CS_RecvVoiceDialogInfoReq_ID] = typeof(CS_RecvVoiceDialogInfoReq),
			[(short)GameMSGID.SC_RecvVoiceDialogInfoAck_ID] = typeof(SC_RecvVoiceDialogInfoAck),
			[(short)GameMSGID.SC_VoiceDialogTextNtf_ID] = typeof(SC_VoiceDialogTextNtf),
			[(short)GameMSGID.CS_RecvVoiceDialogTextReq_ID] = typeof(CS_RecvVoiceDialogTextReq),
			[(short)GameMSGID.SC_RecvVoiceDialogTextAck_ID] = typeof(SC_RecvVoiceDialogTextAck),
			[(short)GameMSGID.CS_GetReconnectVoiceDialogReq_ID] = typeof(CS_GetReconnectVoiceDialogReq),
			[(short)GameMSGID.SC_GetReconnectVoiceDialogAck_ID] = typeof(SC_GetReconnectVoiceDialogAck),
			[(short)GameMSGID.CS_ForbidVoiceDialogChangeReq_ID] = typeof(CS_ForbidVoiceDialogChangeReq),
			[(short)GameMSGID.SC_ForbidVoiceDialogChangeAck_ID] = typeof(SC_ForbidVoiceDialogChangeAck),
			[(short)GameMSGID.CS_VoiceDialogTickReq_ID] = typeof(CS_VoiceDialogTickReq),
			[(short)GameMSGID.SC_VoiceDialogTickAck_ID] = typeof(SC_VoiceDialogTickAck),
			[(short)GameMSGID.SC_VoiceDialogTickNtf_ID] = typeof(SC_VoiceDialogTickNtf),
			[(short)GameMSGID.SC_VoiceDialogTimeoutNtf_ID] = typeof(SC_VoiceDialogTimeoutNtf),
			[(short)GameMSGID.CS_LeaveGSReq_ID] = typeof(CS_LeaveGSReq),
			[(short)GameMSGID.SC_LeaveGSAck_ID] = typeof(SC_LeaveGSAck),
			[(short)GameMSGID.CS_QueryPopularityListReq_ID] = typeof(CS_QueryPopularityListReq),
			[(short)GameMSGID.SC_QueryPopularityListAck_ID] = typeof(SC_QueryPopularityListAck),
			
			
			[(short)GameMSGID.SC_RoleInstChatPush_ID] = typeof(SC_RoleInstChatPush),
			[(short)GameMSGID.CS_AvatarCommandReq_ID] = typeof(CS_AvatarCommandReq),
			[(short)GameMSGID.SC_AvatarCommandAck_ID] = typeof(SC_AvatarCommandAck),
			[(short)GameMSGID.SC_PlayerCommandMovePush_ID] = typeof(SC_PlayerCommandMovePush),
			[(short)GameMSGID.SC_CommandQuitChatDramaNtf_ID] = typeof(SC_CommandQuitChatDramaNtf),
			[(short)GameMSGID.CS_GetTalkitPushReq_ID] = typeof(CS_GetTalkitPushReq),
			[(short)GameMSGID.SC_GetTalkitPushAck_ID] = typeof(SC_GetTalkitPushAck),
			[(short)GameMSGID.CS_JumpServerReq_ID] = typeof(CS_JumpServerReq),
			[(short)GameMSGID.SC_JumpServerAck_ID] = typeof(SC_JumpServerAck),
			
			
			[(short)GameMSGID.CS_CreateDialogTaskCacheReq_ID] = typeof(CS_CreateDialogTaskCacheReq),
			[(short)GameMSGID.SC_CreateDialogTaskCacheAck_ID] = typeof(SC_CreateDialogTaskCacheAck),
			[(short)GameMSGID.CS_GetDialogTaskModeListReq_ID] = typeof(CS_GetDialogTaskModeListReq),
			[(short)GameMSGID.SC_GetDialogTaskModeListAck_ID] = typeof(SC_GetDialogTaskModeListAck),
			[(short)GameMSGID.CS_GetModeInfoReq_ID] = typeof(CS_GetModeInfoReq),
			[(short)GameMSGID.SC_GetModeInfoAck_ID] = typeof(SC_GetModeInfoAck),
			[(short)GameMSGID.CS_CreateDialogTaskReq_ID] = typeof(CS_CreateDialogTaskReq),
			[(short)GameMSGID.SC_CreateDialogTaskAck_ID] = typeof(SC_CreateDialogTaskAck),
			[(short)GameMSGID.CS_DialogTaskMsgHandleReq_ID] = typeof(CS_DialogTaskMsgHandleReq),
			[(short)GameMSGID.SC_DialogTaskMsgHandleAck_ID] = typeof(SC_DialogTaskMsgHandleAck),
			[(short)GameMSGID.SC_TaskStepContentPushReq_ID] = typeof(SC_TaskStepContentPushReq),
			[(short)GameMSGID.SC_TaskStepStatusPushReq_ID] = typeof(SC_TaskStepStatusPushReq),
			[(short)GameMSGID.CS_TranslateReq_ID] = typeof(CS_TranslateReq),
			[(short)GameMSGID.SC_TranslateAck_ID] = typeof(SC_TranslateAck),
			[(short)GameMSGID.CS_ScaffoldFeedbackReq_ID] = typeof(CS_ScaffoldFeedbackReq),
			[(short)GameMSGID.SC_ScaffoldFeedbackAck_ID] = typeof(SC_ScaffoldFeedbackAck),
			[(short)GameMSGID.CS_QuestionFeedbackReq_ID] = typeof(CS_QuestionFeedbackReq),
			[(short)GameMSGID.SC_QuestionFeedbackAck_ID] = typeof(SC_QuestionFeedbackAck),
			[(short)GameMSGID.CS_TaskFeedbackReq_ID] = typeof(CS_TaskFeedbackReq),
			[(short)GameMSGID.SC_TaskFeedbackAck_ID] = typeof(SC_TaskFeedbackAck),
			[(short)GameMSGID.CS_FreeTalkFeedbackReq_ID] = typeof(CS_FreeTalkFeedbackReq),
			[(short)GameMSGID.SC_FreeTalkFeedbackAck_ID] = typeof(SC_FreeTalkFeedbackAck),
			[(short)GameMSGID.CS_Content2AudioReq_ID] = typeof(CS_Content2AudioReq),
			[(short)GameMSGID.SC_Content2AudioAck_ID] = typeof(SC_Content2AudioAck),
			[(short)GameMSGID.CS_ExitDialogTaskReq_ID] = typeof(CS_ExitDialogTaskReq),
			[(short)GameMSGID.SC_ExitDialogTaskAck_ID] = typeof(SC_ExitDialogTaskAck),
			[(short)GameMSGID.CS_GetMsgAssessResultReq_ID] = typeof(CS_GetMsgAssessResultReq),
			[(short)GameMSGID.SC_GetMsgAssessResultAck_ID] = typeof(SC_GetMsgAssessResultAck),
			[(short)GameMSGID.CS_DialogFlowCreateReq_ID] = typeof(CS_DialogFlowCreateReq),
			[(short)GameMSGID.SC_DialogFlowCreateAck_ID] = typeof(SC_DialogFlowCreateAck),
			[(short)GameMSGID.CS_DialogFlowMsgHandleReq_ID] = typeof(CS_DialogFlowMsgHandleReq),
			[(short)GameMSGID.SC_DialogFlowMsgHandleAck_ID] = typeof(SC_DialogFlowMsgHandleAck),
			[(short)GameMSGID.CS_WordTransReq_ID] = typeof(CS_WordTransReq),
			[(short)GameMSGID.SC_WordTransAck_ID] = typeof(SC_WordTransAck),
			[(short)GameMSGID.CS_GetDialogTaskNextRoundMsgReq_ID] = typeof(CS_GetDialogTaskNextRoundMsgReq),
			[(short)GameMSGID.SC_GetDialogTaskNextRoundMsgAck_ID] = typeof(SC_GetDialogTaskNextRoundMsgAck),
			[(short)GameMSGID.CS_StrengthenDialogTaskMsgHandleReq_ID] = typeof(CS_StrengthenDialogTaskMsgHandleReq),
			[(short)GameMSGID.SC_StrengthenDialogTaskMsgHandleAck_ID] = typeof(SC_StrengthenDialogTaskMsgHandleAck),
			[(short)GameMSGID.CS_GetStrengthenDialogTaskNextRoundMsgReq_ID] = typeof(CS_GetStrengthenDialogTaskNextRoundMsgReq),
			[(short)GameMSGID.SC_GetStrengthenDialogTaskNextRoundMsgAck_ID] = typeof(SC_GetStrengthenDialogTaskNextRoundMsgAck),
			[(short)GameMSGID.CS_GetTTSAudioReq_ID] = typeof(CS_GetTTSAudioReq),
			[(short)GameMSGID.SC_GetTTSAudioAck_ID] = typeof(SC_GetTTSAudioAck),
			[(short)GameMSGID.CS_StartASRStreamReq_ID] = typeof(CS_StartASRStreamReq),
			[(short)GameMSGID.SC_StartASRStreamAck_ID] = typeof(SC_StartASRStreamAck),
			[(short)GameMSGID.CS_StrengthenCreateDialogTaskReq_ID] = typeof(CS_StrengthenCreateDialogTaskReq),
			[(short)GameMSGID.SC_StrengthenCreateDialogTaskAck_ID] = typeof(SC_StrengthenCreateDialogTaskAck),
			[(short)GameMSGID.CS_GetSpeechAudioReq_ID] = typeof(CS_GetSpeechAudioReq),
			[(short)GameMSGID.SC_GetSpeechAudioAck_ID] = typeof(SC_GetSpeechAudioAck),
			[(short)GameMSGID.CS_GetFailedMsgReq_ID] = typeof(CS_GetFailedMsgReq),
			[(short)GameMSGID.SC_GetFailedMsgACK_ID] = typeof(SC_GetFailedMsgACK),
			[(short)GameMSGID.CS_FlowHelpReq_ID] = typeof(CS_FlowHelpReq),
			[(short)GameMSGID.SC_FlowHelpAck_ID] = typeof(SC_FlowHelpAck),
			[(short)GameMSGID.CS_SubmitQuestionFeedbackReq_ID] = typeof(CS_SubmitQuestionFeedbackReq),
			[(short)GameMSGID.SC_SubmitQuestionFeedbackAck_ID] = typeof(SC_SubmitQuestionFeedbackAck),
			[(short)GameMSGID.CS_DialogFlowStopReq_ID] = typeof(CS_DialogFlowStopReq),
			[(short)GameMSGID.SC_DialogFlowStopAck_ID] = typeof(SC_DialogFlowStopAck),
			[(short)GameMSGID.CS_FeedbackReq_ID] = typeof(CS_FeedbackReq),
			[(short)GameMSGID.SC_FeedbackAck_ID] = typeof(SC_FeedbackAck),
			[(short)GameMSGID.CS_CancelAssessReq_ID] = typeof(CS_CancelAssessReq),
			[(short)GameMSGID.SC_CancelAssessAck_ID] = typeof(SC_CancelAssessAck),
			[(short)GameMSGID.CS_CancelASRReq_ID] = typeof(CS_CancelASRReq),
			[(short)GameMSGID.SC_CancelASRAck_ID] = typeof(SC_CancelASRAck),
			[(short)GameMSGID.CS_SubmitTaskFeedbackReq_ID] = typeof(CS_SubmitTaskFeedbackReq),
			[(short)GameMSGID.SC_SubmitTaskFeedbackAck_ID] = typeof(SC_SubmitTaskFeedbackAck),
			[(short)GameMSGID.CS_GetTTSAudioTranscriptReq_ID] = typeof(CS_GetTTSAudioTranscriptReq),
			[(short)GameMSGID.SC_GetTTSAudioTranscriptAck_ID] = typeof(SC_GetTTSAudioTranscriptAck),
			[(short)GameMSGID.CS_SwitchTaskDifficultyReq_ID] = typeof(CS_SwitchTaskDifficultyReq),
			[(short)GameMSGID.SC_SwitchTaskDifficultyAck_ID] = typeof(SC_SwitchTaskDifficultyAck),
			[(short)GameMSGID.CS_AssessWithAudioStreamReq_ID] = typeof(CS_AssessWithAudioStreamReq),
			[(short)GameMSGID.SC_AssessWithAudioStreamAck_ID] = typeof(SC_AssessWithAudioStreamAck),
			[(short)GameMSGID.CS_RecommendTaskReq_ID] = typeof(CS_RecommendTaskReq),
			[(short)GameMSGID.SC_RecommendTaskAck_ID] = typeof(SC_RecommendTaskAck),
			[(short)GameMSGID.CS_UploadUserTaskActionReq_ID] = typeof(CS_UploadUserTaskActionReq),
			[(short)GameMSGID.SC_UploadUserTaskActionAck_ID] = typeof(SC_UploadUserTaskActionAck),
			[(short)GameMSGID.CS_GetUserInfoReq_ID] = typeof(CS_GetUserInfoReq),
			[(short)GameMSGID.SC_GetUserInfoAck_ID] = typeof(SC_GetUserInfoAck),
			[(short)GameMSGID.CS_GetAudioForFlowReq_ID] = typeof(CS_GetAudioForFlowReq),
			[(short)GameMSGID.SC_GetAudioForFlowAck_ID] = typeof(SC_GetAudioForFlowAck),
			[(short)GameMSGID.CS_GetTaskInfoReq_ID] = typeof(CS_GetTaskInfoReq),
			[(short)GameMSGID.SC_GetTaskInfoAck_ID] = typeof(SC_GetTaskInfoAck),
			[(short)GameMSGID.CS_GetModeInfoForCareerReq_ID] = typeof(CS_GetModeInfoForCareerReq),
			[(short)GameMSGID.SC_GetModeInfoForCareerAck_ID] = typeof(SC_GetModeInfoForCareerAck),
			[(short)GameMSGID.CS_ChangeMarkInfoReq_ID] = typeof(CS_ChangeMarkInfoReq),
			[(short)GameMSGID.SC_ChangeMarkInfoAck_ID] = typeof(SC_ChangeMarkInfoAck),
			[(short)GameMSGID.CS_SandTableListReq_ID] = typeof(CS_SandTableListReq),
			[(short)GameMSGID.SC_SandTableListAck_ID] = typeof(SC_SandTableListAck),
			[(short)GameMSGID.CS_QueryMemberInfoReq_ID] = typeof(CS_QueryMemberInfoReq),
			[(short)GameMSGID.SC_QueryMemberInfoAck_ID] = typeof(SC_QueryMemberInfoAck),
			[(short)GameMSGID.SC_MemberTypePushReq_ID] = typeof(SC_MemberTypePushReq),
			[(short)GameMSGID.CS_MemberTypePushAck_ID] = typeof(CS_MemberTypePushAck),
			[(short)GameMSGID.CS_GetEconomicInfoReq_ID] = typeof(CS_GetEconomicInfoReq),
			[(short)GameMSGID.SC_GetEconomicInfoAck_ID] = typeof(SC_GetEconomicInfoAck),
			[(short)GameMSGID.CS_BuyDialogTicketReq_ID] = typeof(CS_BuyDialogTicketReq),
			[(short)GameMSGID.SC_BuyDialogTicketAck_ID] = typeof(SC_BuyDialogTicketAck),
			[(short)GameMSGID.CS_GetInProgressDialogReq_ID] = typeof(CS_GetInProgressDialogReq),
			[(short)GameMSGID.SC_GetInProgressDialogAck_ID] = typeof(SC_GetInProgressDialogAck),
			[(short)GameMSGID.SC_NotifyMemberBenefitReq_ID] = typeof(SC_NotifyMemberBenefitReq),
			[(short)GameMSGID.CS_NotifyMemberBenefitAck_ID] = typeof(CS_NotifyMemberBenefitAck),
			[(short)GameMSGID.CS_GetPopListInfoReq_ID] = typeof(CS_GetPopListInfoReq),
			[(short)GameMSGID.SC_GetPopListInfoAck_ID] = typeof(SC_GetPopListInfoAck),
			[(short)GameMSGID.CS_SandTableHomepageReq_ID] = typeof(CS_SandTableHomepageReq),
			[(short)GameMSGID.SC_SandTableHomepageAck_ID] = typeof(SC_SandTableHomepageAck),
			[(short)GameMSGID.CS_QueryDialogRecordReq_ID] = typeof(CS_QueryDialogRecordReq),
			[(short)GameMSGID.SC_QueryDialogRecordAck_ID] = typeof(SC_QueryDialogRecordAck),
			
			
			
			
			[(short)GameMSGID.CS_DialogHelpReq_ID] = typeof(CS_DialogHelpReq),
			[(short)GameMSGID.SC_DialogHelpAck_ID] = typeof(SC_DialogHelpAck),
			[(short)GameMSGID.CS_GetDialogScaffoldReq_ID] = typeof(CS_GetDialogScaffoldReq),
			[(short)GameMSGID.SC_GetDialogScaffoldAck_ID] = typeof(SC_GetDialogScaffoldAck),
			[(short)GameMSGID.CS_GetAvatarListByDialogModeReq_ID] = typeof(CS_GetAvatarListByDialogModeReq),
			[(short)GameMSGID.SC_GetAvatarListByDialogModeAck_ID] = typeof(SC_GetAvatarListByDialogModeAck),
			[(short)GameMSGID.CS_WarmupPracticeCreateDialogTaskReq_ID] = typeof(CS_WarmupPracticeCreateDialogTaskReq),
			[(short)GameMSGID.SC_WarmupPracticeCreateDialogTaskAck_ID] = typeof(SC_WarmupPracticeCreateDialogTaskAck),
			[(short)GameMSGID.CS_WarmupPracticeDialogTaskMsgHandleReq_ID] = typeof(CS_WarmupPracticeDialogTaskMsgHandleReq),
			[(short)GameMSGID.SC_WarmupPracticeDialogTaskMsgHandleAck_ID] = typeof(SC_WarmupPracticeDialogTaskMsgHandleAck),
			[(short)GameMSGID.CS_GetWarmupPracticeDialogTaskNextRoundMsgReq_ID] = typeof(CS_GetWarmupPracticeDialogTaskNextRoundMsgReq),
			[(short)GameMSGID.SC_GetWarmupPracticeDialogTaskNextRoundMsgAck_ID] = typeof(SC_GetWarmupPracticeDialogTaskNextRoundMsgAck),
			[(short)GameMSGID.CS_GetASRAudioReq_ID] = typeof(CS_GetASRAudioReq),
			[(short)GameMSGID.SC_GetASRAudioAck_ID] = typeof(SC_GetASRAudioAck),
			[(short)GameMSGID.CS_UploadAsrAudioReq_ID] = typeof(CS_UploadAsrAudioReq),
			[(short)GameMSGID.SC_UploadAsrAudioAck_ID] = typeof(SC_UploadAsrAudioAck),
			[(short)GameMSGID.CS_SubmitSpeakUserAnswerReq_ID] = typeof(CS_SubmitSpeakUserAnswerReq),
			[(short)GameMSGID.SC_SubmitSpeakUserAnswerAck_ID] = typeof(SC_SubmitSpeakUserAnswerAck),
			[(short)GameMSGID.CS_GetUserChapterInfoReq_ID] = typeof(CS_GetUserChapterInfoReq),
			[(short)GameMSGID.SC_GetUserChapterInfoAck_ID] = typeof(SC_GetUserChapterInfoAck),
			[(short)GameMSGID.CS_GetGoalInfoReq_ID] = typeof(CS_GetGoalInfoReq),
			[(short)GameMSGID.SC_GetGoalInfoAck_ID] = typeof(SC_GetGoalInfoAck),
			[(short)GameMSGID.CS_GetChapterRecommendListReq_ID] = typeof(CS_GetChapterRecommendListReq),
			[(short)GameMSGID.SC_GetChapterRecommendListAck_ID] = typeof(SC_GetChapterRecommendListAck),
			[(short)GameMSGID.CS_SelectChapterReq_ID] = typeof(CS_SelectChapterReq),
			[(short)GameMSGID.SC_SelectChapterAck_ID] = typeof(SC_SelectChapterAck),
			[(short)GameMSGID.CS_GetChapterProgressInfoReq_ID] = typeof(CS_GetChapterProgressInfoReq),
			[(short)GameMSGID.SC_GetChapterProgressInfoAck_ID] = typeof(SC_GetChapterProgressInfoAck),
			[(short)GameMSGID.CS_GetChapterRewardReq_ID] = typeof(CS_GetChapterRewardReq),
			[(short)GameMSGID.SC_GetChapterRewardAck_ID] = typeof(SC_GetChapterRewardAck),
			[(short)GameMSGID.CS_GetLearnPathPageTypeReq_ID] = typeof(CS_GetLearnPathPageTypeReq),
			[(short)GameMSGID.SC_GetLearnPathPageTypeAck_ID] = typeof(SC_GetLearnPathPageTypeAck),
			[(short)GameMSGID.CS_NotifyAnimationStateReq_ID] = typeof(CS_NotifyAnimationStateReq),
			[(short)GameMSGID.SC_NotifyAnimationStateAck_ID] = typeof(SC_NotifyAnimationStateAck),
			[(short)GameMSGID.CS_GetUserGoalDetailReq_ID] = typeof(CS_GetUserGoalDetailReq),
			[(short)GameMSGID.SC_GetUserGoalDetailAck_ID] = typeof(SC_GetUserGoalDetailAck),
			[(short)GameMSGID.CS_LearnPathRewardReq_ID] = typeof(CS_LearnPathRewardReq),
			[(short)GameMSGID.SC_LearnPathRewardAck_ID] = typeof(SC_LearnPathRewardAck),
			[(short)GameMSGID.CS_DialogSuggestionReq_ID] = typeof(CS_DialogSuggestionReq),
			[(short)GameMSGID.SC_DialogSuggestionAck_ID] = typeof(SC_DialogSuggestionAck),
			[(short)GameMSGID.CS_DialogTranslateReq_ID] = typeof(CS_DialogTranslateReq),
			[(short)GameMSGID.SC_DialogTranslateAck_ID] = typeof(SC_DialogTranslateAck),
			[(short)GameMSGID.CS_GetAvatarTaskInfoReq_ID] = typeof(CS_GetAvatarTaskInfoReq),
			[(short)GameMSGID.SC_GetAvatarTaskInfoAck_ID] = typeof(SC_GetAvatarTaskInfoAck),
			[(short)GameMSGID.CS_GetStartPageInfoReq_ID] = typeof(CS_GetStartPageInfoReq),
			[(short)GameMSGID.SC_GetStartPageInfoAck_ID] = typeof(SC_GetStartPageInfoAck),
			[(short)GameMSGID.CS_GetUserAssistLevelListReq_ID] = typeof(CS_GetUserAssistLevelListReq),
			[(short)GameMSGID.SC_GetUserAssistLevelListAck_ID] = typeof(SC_GetUserAssistLevelListAck),
			[(short)GameMSGID.CS_SetUserAssistLevelReq_ID] = typeof(CS_SetUserAssistLevelReq),
			[(short)GameMSGID.SC_SetUserAssistLevelAck_ID] = typeof(SC_SetUserAssistLevelAck),
			[(short)GameMSGID.CS_GetUserGoalNodeReq_ID] = typeof(CS_GetUserGoalNodeReq),
			[(short)GameMSGID.SC_GetUserGoalNodeAck_ID] = typeof(SC_GetUserGoalNodeAck),
			[(short)GameMSGID.CS_GetUserContactsReq_ID] = typeof(CS_GetUserContactsReq),
			[(short)GameMSGID.SC_GetUserContactsAck_ID] = typeof(SC_GetUserContactsAck),
			[(short)GameMSGID.CS_AvatarFavoriteSettingReq_ID] = typeof(CS_AvatarFavoriteSettingReq),
			[(short)GameMSGID.SC_AvatarFavoriteSettingAck_ID] = typeof(SC_AvatarFavoriteSettingAck),
			[(short)GameMSGID.CS_GetUserQuestionExtraListReq_ID] = typeof(CS_GetUserQuestionExtraListReq),
			[(short)GameMSGID.SC_GetUserQuestionExtraListAck_ID] = typeof(SC_GetUserQuestionExtraListAck),
			[(short)GameMSGID.CS_GetKnowledgePointListReq_ID] = typeof(CS_GetKnowledgePointListReq),
			[(short)GameMSGID.SC_GetKnowledgePointListAck_ID] = typeof(SC_GetKnowledgePointListAck),
			[(short)GameMSGID.CS_GetKnowledgePointFrontPageReq_ID] = typeof(CS_GetKnowledgePointFrontPageReq),
			[(short)GameMSGID.SC_GetKnowledgePointFrontPageAck_ID] = typeof(SC_GetKnowledgePointFrontPageAck),
			[(short)GameMSGID.CS_GetUserQuestionExtraFrontPageReq_ID] = typeof(CS_GetUserQuestionExtraFrontPageReq),
			[(short)GameMSGID.SC_GetUserQuestionExtraFrontPageAck_ID] = typeof(SC_GetUserQuestionExtraFrontPageAck),
			[(short)GameMSGID.CS_BatchGetUserQuestionExtraFrontPageReq_ID] = typeof(CS_BatchGetUserQuestionExtraFrontPageReq),
			[(short)GameMSGID.SC_BatchGetUserQuestionExtraFrontPageAck_ID] = typeof(SC_BatchGetUserQuestionExtraFrontPageAck),
			[(short)GameMSGID.CS_ReviewUserQuestionExtraReq_ID] = typeof(CS_ReviewUserQuestionExtraReq),
			[(short)GameMSGID.SC_ReviewUserQuestionExtraAck_ID] = typeof(SC_ReviewUserQuestionExtraAck),
			[(short)GameMSGID.CS_OpenRewardBoxReq_ID] = typeof(CS_OpenRewardBoxReq),
			[(short)GameMSGID.SC_OpenRewardBoxAck_ID] = typeof(SC_OpenRewardBoxAck),
			[(short)GameMSGID.CS_GetDialogSettlementReq_ID] = typeof(CS_GetDialogSettlementReq),
			[(short)GameMSGID.SC_GetDialogSettlementAck_ID] = typeof(SC_GetDialogSettlementAck),
			[(short)GameMSGID.CS_StartBatchQuestionDialogReq_ID] = typeof(CS_StartBatchQuestionDialogReq),
			[(short)GameMSGID.SC_StartBatchQuestionDialogAck_ID] = typeof(SC_StartBatchQuestionDialogAck),
			[(short)GameMSGID.CS_SubmitBatchQuestionDialogReq_ID] = typeof(CS_SubmitBatchQuestionDialogReq),
			[(short)GameMSGID.SC_SubmitBatchQuestionDialogAck_ID] = typeof(SC_SubmitBatchQuestionDialogAck),
			[(short)GameMSGID.CS_GetBoxRewardReq_ID] = typeof(CS_GetBoxRewardReq),
			[(short)GameMSGID.SC_GetBoxRewardAck_ID] = typeof(SC_GetBoxRewardAck),
			[(short)GameMSGID.CS_GetUserDialogHistoryReq_ID] = typeof(CS_GetUserDialogHistoryReq),
			[(short)GameMSGID.SC_GetUserDialogHistoryAck_ID] = typeof(SC_GetUserDialogHistoryAck),
			[(short)GameMSGID.CS_OnboardingNewStoryReq_ID] = typeof(CS_OnboardingNewStoryReq),
			[(short)GameMSGID.SC_OnboardingNewStoryAck_ID] = typeof(SC_OnboardingNewStoryAck),
			[(short)GameMSGID.CS_GetRedPodReq_ID] = typeof(CS_GetRedPodReq),
			[(short)GameMSGID.SC_GetRedPodAck_ID] = typeof(SC_GetRedPodAck),
			[(short)GameMSGID.CS_ClickRedPodReq_ID] = typeof(CS_ClickRedPodReq),
			[(short)GameMSGID.SC_ClickRedPodAck_ID] = typeof(SC_ClickRedPodAck),
			[(short)GameMSGID.CS_GetUserTopicDialogHistoryReq_ID] = typeof(CS_GetUserTopicDialogHistoryReq),
			[(short)GameMSGID.SC_GetUserTopicDialogHistoryAck_ID] = typeof(SC_GetUserTopicDialogHistoryAck),
			[(short)GameMSGID.CS_CreateQuestionDialogReq_ID] = typeof(CS_CreateQuestionDialogReq),
			[(short)GameMSGID.SC_CreateQuestionDialogAck_ID] = typeof(SC_CreateQuestionDialogAck),
			[(short)GameMSGID.CS_DelLearnPathDataReq_ID] = typeof(CS_DelLearnPathDataReq),
			[(short)GameMSGID.SC_DelLearnPathDataAck_ID] = typeof(SC_DelLearnPathDataAck),
			[(short)GameMSGID.CS_GetQuickPracticeListReq_ID] = typeof(CS_GetQuickPracticeListReq),
			[(short)GameMSGID.SC_GetQuickPracticeListAck_ID] = typeof(SC_GetQuickPracticeListAck),
			[(short)GameMSGID.CS_SubmitQuickPracticeReq_ID] = typeof(CS_SubmitQuickPracticeReq),
			[(short)GameMSGID.SC_SubmitQuickPracticeAck_ID] = typeof(SC_SubmitQuickPracticeAck),
			[(short)GameMSGID.CS_SubmitQuickPracticeNewReq_ID] = typeof(CS_SubmitQuickPracticeNewReq),
			[(short)GameMSGID.SC_SubmitQuickPracticeNewAck_ID] = typeof(SC_SubmitQuickPracticeNewAck),
			[(short)GameMSGID.CS_ExitQuickPracticeReq_ID] = typeof(CS_ExitQuickPracticeReq),
			[(short)GameMSGID.SC_ExitQuickPracticeAck_ID] = typeof(SC_ExitQuickPracticeAck),
			[(short)GameMSGID.CS_GetUserCourseReq_ID] = typeof(CS_GetUserCourseReq),
			[(short)GameMSGID.SC_GetUserCourseAck_ID] = typeof(SC_GetUserCourseAck),
			[(short)GameMSGID.CS_SkipCourseReq_ID] = typeof(CS_SkipCourseReq),
			[(short)GameMSGID.SC_SkipCourseAck_ID] = typeof(SC_SkipCourseAck),
			[(short)GameMSGID.CS_RewardBoxReq_ID] = typeof(CS_RewardBoxReq),
			[(short)GameMSGID.SC_RewardBoxAck_ID] = typeof(SC_RewardBoxAck),
			[(short)GameMSGID.CS_GetBookDataReq_ID] = typeof(CS_GetBookDataReq),
			[(short)GameMSGID.SC_GetBookDataAck_ID] = typeof(SC_GetBookDataAck),
			[(short)GameMSGID.CS_GetRadioDataReq_ID] = typeof(CS_GetRadioDataReq),
			[(short)GameMSGID.SC_GetRadioDataAck_ID] = typeof(SC_GetRadioDataAck),
			[(short)GameMSGID.CS_GetCourseSettlementReq_ID] = typeof(CS_GetCourseSettlementReq),
			[(short)GameMSGID.SC_GetCourseSettlementAck_ID] = typeof(SC_GetCourseSettlementAck),
			[(short)GameMSGID.CS_BuyCourseTicketReq_ID] = typeof(CS_BuyCourseTicketReq),
			[(short)GameMSGID.SC_BuyCourseTicketAck_ID] = typeof(SC_BuyCourseTicketAck),
			[(short)GameMSGID.CS_GetUserShelfReq_ID] = typeof(CS_GetUserShelfReq),
			[(short)GameMSGID.SC_GetUserShelfAck_ID] = typeof(SC_GetUserShelfAck),
			[(short)GameMSGID.CS_GetDynamicQpListReq_ID] = typeof(CS_GetDynamicQpListReq),
			[(short)GameMSGID.SC_GetDynamicQpListAck_ID] = typeof(SC_GetDynamicQpListAck),
			[(short)GameMSGID.SC_TaskResultPushReq_ID] = typeof(SC_TaskResultPushReq),
			[(short)GameMSGID.CS_TaskResultPushAck_ID] = typeof(CS_TaskResultPushAck),
			[(short)GameMSGID.CS_GetTaskModeBatchReq_ID] = typeof(CS_GetTaskModeBatchReq),
			[(short)GameMSGID.SC_GetTaskModeBatchAck_ID] = typeof(SC_GetTaskModeBatchAck),
			[(short)GameMSGID.CS_GetDailyTaskInfoReq_ID] = typeof(CS_GetDailyTaskInfoReq),
			[(short)GameMSGID.SC_GetDailyTaskInfoAck_ID] = typeof(SC_GetDailyTaskInfoAck),
			[(short)GameMSGID.CS_DataBatchReq_ID] = typeof(CS_DataBatchReq),
			[(short)GameMSGID.SC_DataBatchAck_ID] = typeof(SC_DataBatchAck),
			[(short)GameMSGID.CS_GMToolReq_ID] = typeof(CS_GMToolReq),
			[(short)GameMSGID.SC_GMToolAck_ID] = typeof(SC_GMToolAck),
			[(short)GameMSGID.CS_TreeListForHomepageReq_ID] = typeof(CS_TreeListForHomepageReq),
			[(short)GameMSGID.SC_TreeListForHomepageAck_ID] = typeof(SC_TreeListForHomepageAck),
			[(short)GameMSGID.CS_TreeDetailForHomepageReq_ID] = typeof(CS_TreeDetailForHomepageReq),
			[(short)GameMSGID.SC_TreeDetailForHomepageAck_ID] = typeof(SC_TreeDetailForHomepageAck),
			[(short)GameMSGID.CS_GetUserProfileReq_ID] = typeof(CS_GetUserProfileReq),
			[(short)GameMSGID.SC_GetUserProfileResponse_ID] = typeof(SC_GetUserProfileResponse),
			[(short)GameMSGID.CS_SetUserProfileAuthorityReq_ID] = typeof(CS_SetUserProfileAuthorityReq),
			[(short)GameMSGID.SC_SetUserProfileAuthorityResp_ID] = typeof(SC_SetUserProfileAuthorityResp),
			[(short)GameMSGID.CS_GetUserProfileAuthorityReq_ID] = typeof(CS_GetUserProfileAuthorityReq),
			[(short)GameMSGID.SC_GetUserProfileAuthorityResp_ID] = typeof(SC_GetUserProfileAuthorityResp),
			[(short)GameMSGID.CS_SendFeedbackReq_ID] = typeof(CS_SendFeedbackReq),
			[(short)GameMSGID.SC_SendFeedbackResp_ID] = typeof(SC_SendFeedbackResp),
			[(short)GameMSGID.CS_GetMyProfileReq_ID] = typeof(CS_GetMyProfileReq),
			[(short)GameMSGID.SC_GetMyProfileResponse_ID] = typeof(SC_GetMyProfileResponse),
			[(short)GameMSGID.CS_SetUserPortraitsReq_ID] = typeof(CS_SetUserPortraitsReq),
			[(short)GameMSGID.SC_SetUserPortraitsAck_ID] = typeof(SC_SetUserPortraitsAck),
			[(short)GameMSGID.CS_SetUserLanguageLevelReq_ID] = typeof(CS_SetUserLanguageLevelReq),
			[(short)GameMSGID.SC_SetUserLanguageLevelAck_ID] = typeof(SC_SetUserLanguageLevelAck),
			[(short)GameMSGID.CS_GetUserPortraitsReq_ID] = typeof(CS_GetUserPortraitsReq),
			[(short)GameMSGID.SC_GetUserPortraitsAck_ID] = typeof(SC_GetUserPortraitsAck),
			[(short)GameMSGID.CS_SetUserVoiceInformationReq_ID] = typeof(CS_SetUserVoiceInformationReq),
			[(short)GameMSGID.SC_SetUserVoiceInformationAck_ID] = typeof(SC_SetUserVoiceInformationAck),
			[(short)GameMSGID.CS_SetUserDressUpReq_ID] = typeof(CS_SetUserDressUpReq),
			[(short)GameMSGID.SC_SetUserDressUpAck_ID] = typeof(SC_SetUserDressUpAck),
			[(short)GameMSGID.CS_SetCheckinMilestoneReq_ID] = typeof(CS_SetCheckinMilestoneReq),
			[(short)GameMSGID.SC_SetCheckinMilestoneAck_ID] = typeof(SC_SetCheckinMilestoneAck),
			[(short)GameMSGID.CS_GetCheckinSummaryReq_ID] = typeof(CS_GetCheckinSummaryReq),
			[(short)GameMSGID.SC_GetCheckinSummaryAck_ID] = typeof(SC_GetCheckinSummaryAck),
			[(short)GameMSGID.CS_GetUserCheckinPortalDataReq_ID] = typeof(CS_GetUserCheckinPortalDataReq),
			[(short)GameMSGID.SC_GetUserCheckinPortalDataAck_ID] = typeof(SC_GetUserCheckinPortalDataAck),
			[(short)GameMSGID.CS_DrawRewardReq_ID] = typeof(CS_DrawRewardReq),
			[(short)GameMSGID.SC_DrawRewardAck_ID] = typeof(SC_DrawRewardAck),
			[(short)GameMSGID.CS_RecheckinReq_ID] = typeof(CS_RecheckinReq),
			[(short)GameMSGID.SC_RecheckinAck_ID] = typeof(SC_RecheckinAck),
			[(short)GameMSGID.CS_RecheckinByDiamondReq_ID] = typeof(CS_RecheckinByDiamondReq),
			[(short)GameMSGID.SC_RecheckinByDiamondAck_ID] = typeof(SC_RecheckinByDiamondAck),
			[(short)GameMSGID.CS_RecheckinByTaskReq_ID] = typeof(CS_RecheckinByTaskReq),
			[(short)GameMSGID.SC_RecheckinByTaskAck_ID] = typeof(SC_RecheckinByTaskAck),
			[(short)GameMSGID.CS_GetUserCheckinDataForTaskFinishReq_ID] = typeof(CS_GetUserCheckinDataForTaskFinishReq),
			[(short)GameMSGID.SC_GetUserCheckinDataForTaskFinishAck_ID] = typeof(SC_GetUserCheckinDataForTaskFinishAck),
			[(short)GameMSGID.CS_GetUserRankingPortalDataReq_ID] = typeof(CS_GetUserRankingPortalDataReq),
			[(short)GameMSGID.SC_GetUserRankingPortalDataAck_ID] = typeof(SC_GetUserRankingPortalDataAck),
			[(short)GameMSGID.CS_GetIncentiveDataForPortalReq_ID] = typeof(CS_GetIncentiveDataForPortalReq),
			[(short)GameMSGID.SC_GetIncentiveDataForPortalAck_ID] = typeof(SC_GetIncentiveDataForPortalAck),
			[(short)GameMSGID.CS_JoinRankingReq_ID] = typeof(CS_JoinRankingReq),
			[(short)GameMSGID.SC_JoinRankingAck_ID] = typeof(SC_JoinRankingAck),
			[(short)GameMSGID.CS_GetUserCheckinCalendarReq_ID] = typeof(CS_GetUserCheckinCalendarReq),
			[(short)GameMSGID.SC_GetUserCheckinCalendarAck_ID] = typeof(SC_GetUserCheckinCalendarAck),
			[(short)GameMSGID.CS_RefuseRecheckinReq_ID] = typeof(CS_RefuseRecheckinReq),
			[(short)GameMSGID.SC_RefuseRecheckinAck_ID] = typeof(SC_RefuseRecheckinAck),
			[(short)GameMSGID.CS_GetUserDressUpMerchandiseDataReq_ID] = typeof(CS_GetUserDressUpMerchandiseDataReq),
			[(short)GameMSGID.SC_GetUserDressUpMerchandiseDataAck_ID] = typeof(SC_GetUserDressUpMerchandiseDataAck),
			[(short)GameMSGID.CS_GetGrowthWeeklyGiftReq_ID] = typeof(CS_GetGrowthWeeklyGiftReq),
			[(short)GameMSGID.SC_GetGrowthWeeklyGiftAck_ID] = typeof(SC_GetGrowthWeeklyGiftAck),
			[(short)GameMSGID.CS_SetRankingChangeClickReq_ID] = typeof(CS_SetRankingChangeClickReq),
			[(short)GameMSGID.SC_SetRankingChangeClickAck_ID] = typeof(SC_SetRankingChangeClickAck),
			[(short)GameMSGID.CS_SetUserHeadItemReq_ID] = typeof(CS_SetUserHeadItemReq),
			[(short)GameMSGID.SC_SetUserHeadItemAck_ID] = typeof(SC_SetUserHeadItemAck),
			[(short)GameMSGID.CS_GetUserFriendListReq_ID] = typeof(CS_GetUserFriendListReq),
			[(short)GameMSGID.SC_GetUserFriendListAck_ID] = typeof(SC_GetUserFriendListAck),
			[(short)GameMSGID.CS_AddFriendReq_ID] = typeof(CS_AddFriendReq),
			[(short)GameMSGID.SC_AddFriendAck_ID] = typeof(SC_AddFriendAck),
			[(short)GameMSGID.CS_RemoveFriendReq_ID] = typeof(CS_RemoveFriendReq),
			[(short)GameMSGID.SC_RemoveFriendAck_ID] = typeof(SC_RemoveFriendAck),
			[(short)GameMSGID.CS_GetRecommendedFriendListReq_ID] = typeof(CS_GetRecommendedFriendListReq),
			[(short)GameMSGID.SC_GetRecommendedFriendListAck_ID] = typeof(SC_GetRecommendedFriendListAck),
			[(short)GameMSGID.CS_SearchUserReq_ID] = typeof(CS_SearchUserReq),
			[(short)GameMSGID.SC_SearchUserAck_ID] = typeof(SC_SearchUserAck),
			[(short)GameMSGID.CS_SendGiftForFriendReq_ID] = typeof(CS_SendGiftForFriendReq),
			[(short)GameMSGID.SC_SendGiftForFriendAck_ID] = typeof(SC_SendGiftForFriendAck),
			[(short)GameMSGID.CS_GetUserGiftPortalDataReq_ID] = typeof(CS_GetUserGiftPortalDataReq),
			[(short)GameMSGID.SC_GetUserGiftPortalDataAck_ID] = typeof(SC_GetUserGiftPortalDataAck),
			[(short)GameMSGID.CS_MatchFriendShipTaskReq_ID] = typeof(CS_MatchFriendShipTaskReq),
			[(short)GameMSGID.SC_MatchFriendShipTaskAck_ID] = typeof(SC_MatchFriendShipTaskAck),
			[(short)GameMSGID.CS_FriendShipNotifyReq_ID] = typeof(CS_FriendShipNotifyReq),
			[(short)GameMSGID.SC_FriendShipNotifyAck_ID] = typeof(SC_FriendShipNotifyAck),
			[(short)GameMSGID.CS_SetShowStateReq_ID] = typeof(CS_SetShowStateReq),
			[(short)GameMSGID.SC_SetShowStateAck_ID] = typeof(SC_SetShowStateAck),
			[(short)GameMSGID.CS_GetFriendStreakRecommendListReq_ID] = typeof(CS_GetFriendStreakRecommendListReq),
			[(short)GameMSGID.SC_GetFriendStreakRecommendListAck_ID] = typeof(SC_GetFriendStreakRecommendListAck),
			[(short)GameMSGID.CS_UpdateFriendStreakRelationReq_ID] = typeof(CS_UpdateFriendStreakRelationReq),
			[(short)GameMSGID.SC_UpdateFriendStreakRelationAck_ID] = typeof(SC_UpdateFriendStreakRelationAck),
			[(short)GameMSGID.CS_GetFriendStreakPortalReq_ID] = typeof(CS_GetFriendStreakPortalReq),
			[(short)GameMSGID.SC_GetFriendStreakPortalAck_ID] = typeof(SC_GetFriendStreakPortalAck),
			[(short)GameMSGID.CS_GetIncentiveDataForExploreReq_ID] = typeof(CS_GetIncentiveDataForExploreReq),
			[(short)GameMSGID.SC_GetIncentiveDataForExploreAck_ID] = typeof(SC_GetIncentiveDataForExploreAck),
			[(short)GameMSGID.CS_StartConsumeReq_ID] = typeof(CS_StartConsumeReq),
			[(short)GameMSGID.SC_StartConsumeAck_ID] = typeof(SC_StartConsumeAck),
			[(short)GameMSGID.CS_GetUserVoiceCloneAudioReq_ID] = typeof(CS_GetUserVoiceCloneAudioReq),
			[(short)GameMSGID.SC_GetUserVoiceCloneAudioAck_ID] = typeof(SC_GetUserVoiceCloneAudioAck),
			[(short)GameMSGID.CS_ReportInvalidUserAudioReq_ID] = typeof(CS_ReportInvalidUserAudioReq),
			[(short)GameMSGID.SC_ReportInvalidUserAudioAck_ID] = typeof(SC_ReportInvalidUserAudioAck),
			[(short)GameMSGID.SC_EconomicCoinSettlementPush_ID] = typeof(SC_EconomicCoinSettlementPush),
			[(short)GameMSGID.CS_EconomicCoinGetBalanceReq_ID] = typeof(CS_EconomicCoinGetBalanceReq),
			[(short)GameMSGID.SC_EconomicCoinGetBalanceAck_ID] = typeof(SC_EconomicCoinGetBalanceAck),
			[(short)GameMSGID.CS_CreateOrderReq_ID] = typeof(CS_CreateOrderReq),
			[(short)GameMSGID.SC_CreateOrderAck_ID] = typeof(SC_CreateOrderAck),
			[(short)GameMSGID.CS_CancelOrderReq_ID] = typeof(CS_CancelOrderReq),
			[(short)GameMSGID.SC_CancelOrderAck_ID] = typeof(SC_CancelOrderAck),
			[(short)GameMSGID.CS_GetUnpaidOrderReq_ID] = typeof(CS_GetUnpaidOrderReq),
			[(short)GameMSGID.SC_GetUnpaidOrderAck_ID] = typeof(SC_GetUnpaidOrderAck),
			[(short)GameMSGID.CS_VerifyReceiptDataReq_ID] = typeof(CS_VerifyReceiptDataReq),
			[(short)GameMSGID.SC_VerifyReceiptDataResp_ID] = typeof(SC_VerifyReceiptDataResp),
			[(short)GameMSGID.SC_UserRewardItem_ID] = typeof(SC_UserRewardItem),
			[(short)GameMSGID.CS_GetShopInfoReq_ID] = typeof(CS_GetShopInfoReq),
			[(short)GameMSGID.SC_GetShopInfoResp_ID] = typeof(SC_GetShopInfoResp),
			[(short)GameMSGID.CS_PurchaseStaminaReq_ID] = typeof(CS_PurchaseStaminaReq),
			[(short)GameMSGID.SC_PurchaseStaminaAck_ID] = typeof(SC_PurchaseStaminaAck),
			[(short)GameMSGID.CS_PayMerchandiseReq_ID] = typeof(CS_PayMerchandiseReq),
			[(short)GameMSGID.SC_PayMerchandiseResp_ID] = typeof(SC_PayMerchandiseResp),
			[(short)GameMSGID.CS_GetAllShopMerchandiseReq_ID] = typeof(CS_GetAllShopMerchandiseReq),
			[(short)GameMSGID.SC_GetAllShopMerchandiseResp_ID] = typeof(SC_GetAllShopMerchandiseResp),
			[(short)GameMSGID.CS_ClickWordReq_ID] = typeof(CS_ClickWordReq),
			[(short)GameMSGID.SC_ClickWordAck_ID] = typeof(SC_ClickWordAck),
			[(short)GameMSGID.CS_GetUserCheckinDataReq_ID] = typeof(CS_GetUserCheckinDataReq),
			[(short)GameMSGID.SC_GetUserCheckinDataAck_ID] = typeof(SC_GetUserCheckinDataAck),
			[(short)GameMSGID.CS_DrawCheckinRewardReq_ID] = typeof(CS_DrawCheckinRewardReq),
			[(short)GameMSGID.SC_DrawCheckinRewardAck_ID] = typeof(SC_DrawCheckinRewardAck),
			[(short)GameMSGID.CS_RegisterUserDeviceReq_ID] = typeof(CS_RegisterUserDeviceReq),
			[(short)GameMSGID.SC_RegisterUserDeviceAck_ID] = typeof(SC_RegisterUserDeviceAck),
			[(short)GameMSGID.CS_GetSystemNoticeReq_ID] = typeof(CS_GetSystemNoticeReq),
			[(short)GameMSGID.SC_GetSystemNoticeAck_ID] = typeof(SC_GetSystemNoticeAck),
			[(short)GameMSGID.CS_RedeemCouponReq_ID] = typeof(CS_RedeemCouponReq),
			[(short)GameMSGID.SC_RedeemCouponAck_ID] = typeof(SC_RedeemCouponAck),
			[(short)GameMSGID.CS_GetSingleChatDialogReq_ID] = typeof(CS_GetSingleChatDialogReq),
			[(short)GameMSGID.SC_GetSingleChatDialogAck_ID] = typeof(SC_GetSingleChatDialogAck),
			[(short)GameMSGID.CS_QueryChatListReq_ID] = typeof(CS_QueryChatListReq),
			[(short)GameMSGID.SC_QueryChatListAck_ID] = typeof(SC_QueryChatListAck),
			[(short)GameMSGID.CS_QueryChatMessageListReq_ID] = typeof(CS_QueryChatMessageListReq),
			[(short)GameMSGID.SC_QueryChatMessageListAck_ID] = typeof(SC_QueryChatMessageListAck),
			[(short)GameMSGID.CS_SendChatMessageReq_ID] = typeof(CS_SendChatMessageReq),
			[(short)GameMSGID.SC_SendChatMessageAck_ID] = typeof(SC_SendChatMessageAck),
			[(short)GameMSGID.SC_ChatMessageNtf_ID] = typeof(SC_ChatMessageNtf),
			[(short)GameMSGID.CS_UpdateChatIndexReq_ID] = typeof(CS_UpdateChatIndexReq),
			[(short)GameMSGID.SC_UpdateChatIndexAck_ID] = typeof(SC_UpdateChatIndexAck),
			[(short)GameMSGID.CS_QueryChatUserListReq_ID] = typeof(CS_QueryChatUserListReq),
			[(short)GameMSGID.SC_QueryChatUserListAck_ID] = typeof(SC_QueryChatUserListAck),
			
			
			
			
			[(short)GameMSGID.CS_ChatScaffoldReq_ID] = typeof(CS_ChatScaffoldReq),
			[(short)GameMSGID.SC_ChatScaffoldAck_ID] = typeof(SC_ChatScaffoldAck),
			[(short)GameMSGID.CS_GenTestSingleChatReq_ID] = typeof(CS_GenTestSingleChatReq),
			[(short)GameMSGID.SC_GenTestSingleChatAck_ID] = typeof(SC_GenTestSingleChatAck),
			[(short)GameMSGID.CS_QueryUnReadMessageNumReq_ID] = typeof(CS_QueryUnReadMessageNumReq),
			[(short)GameMSGID.SC_QueryUnReadMessageNumAck_ID] = typeof(SC_QueryUnReadMessageNumAck),
			[(short)GameMSGID.CS_ChatRecordStatusSyncReq_ID] = typeof(CS_ChatRecordStatusSyncReq),
			[(short)GameMSGID.SC_ChatRecordStatusSyncAck_ID] = typeof(SC_ChatRecordStatusSyncAck),
			[(short)GameMSGID.SC_ChatRecordStatusNtf_ID] = typeof(SC_ChatRecordStatusNtf),
			[(short)GameMSGID.CS_GetBuildingTopicListReq_ID] = typeof(CS_GetBuildingTopicListReq),
			[(short)GameMSGID.SC_GetBuildingTopicListAck_ID] = typeof(SC_GetBuildingTopicListAck),
			[(short)GameMSGID.CS_GetAreaAchievementListReq_ID] = typeof(CS_GetAreaAchievementListReq),
			[(short)GameMSGID.SC_GetAreaAchievementListAck_ID] = typeof(SC_GetAreaAchievementListAck),
			[(short)GameMSGID.CS_CreateWorldStoryTaskReq_ID] = typeof(CS_CreateWorldStoryTaskReq),
			[(short)GameMSGID.SC_CreateWorldStoryTaskAck_ID] = typeof(SC_CreateWorldStoryTaskAck),
			[(short)GameMSGID.CS_StartWorldDialogReq_ID] = typeof(CS_StartWorldDialogReq),
			[(short)GameMSGID.SC_StartWorldDialogAck_ID] = typeof(SC_StartWorldDialogAck),
			[(short)GameMSGID.CS_FinishWorldStoryTaskReq_ID] = typeof(CS_FinishWorldStoryTaskReq),
			[(short)GameMSGID.SC_FinishWorldStoryTaskAck_ID] = typeof(SC_FinishWorldStoryTaskAck),
			[(short)GameMSGID.SC_UserDialogContentNtf_ID] = typeof(SC_UserDialogContentNtf),
			[(short)GameMSGID.SC_AvatarDialogContentNtf_ID] = typeof(SC_AvatarDialogContentNtf),
			[(short)GameMSGID.SC_AvatarAudioNtf_ID] = typeof(SC_AvatarAudioNtf),
			[(short)GameMSGID.SC_AvatarDialogTranslateNtf_ID] = typeof(SC_AvatarDialogTranslateNtf),
			[(short)GameMSGID.SC_AvatarAdviceNtf_ID] = typeof(SC_AvatarAdviceNtf),
			[(short)GameMSGID.SC_AvatarExampleNtf_ID] = typeof(SC_AvatarExampleNtf),
			[(short)GameMSGID.SC_WorldStoryProcessNtf_ID] = typeof(SC_WorldStoryProcessNtf),
			[(short)GameMSGID.CS_DialogSettingReq_ID] = typeof(CS_DialogSettingReq),
			[(short)GameMSGID.CS_GetWorldRoleInfoReq_ID] = typeof(CS_GetWorldRoleInfoReq),
			[(short)GameMSGID.SC_GetWorldRoleInfoAck_ID] = typeof(SC_GetWorldRoleInfoAck),
			[(short)GameMSGID.CS_CreateWorldRoleReq_ID] = typeof(CS_CreateWorldRoleReq),
			[(short)GameMSGID.SC_CreateWorldRoleAck_ID] = typeof(SC_CreateWorldRoleAck),
			[(short)GameMSGID.CS_SendVerificationCodeReq_ID] = typeof(CS_SendVerificationCodeReq),
			[(short)GameMSGID.SC_SendVerificationCodeResp_ID] = typeof(SC_SendVerificationCodeResp),
			[(short)GameMSGID.CS_GetAbTestResultReq_ID] = typeof(CS_GetAbTestResultReq),
			[(short)GameMSGID.SC_GetAbTestResultAck_ID] = typeof(SC_GetAbTestResultAck),
			[(short)GameMSGID.CS_GetRecommendListReq_ID] = typeof(CS_GetRecommendListReq),
			[(short)GameMSGID.SC_GetRecommendListResp_ID] = typeof(SC_GetRecommendListResp),
			[(short)GameMSGID.SC_RecommendSwitchEntity_ID] = typeof(SC_RecommendSwitchEntity),
			[(short)GameMSGID.CS_GetRecommendListV2Req_ID] = typeof(CS_GetRecommendListV2Req),
			[(short)GameMSGID.SC_GetRecommendListV2Resp_ID] = typeof(SC_GetRecommendListV2Resp),
			[(short)GameMSGID.CS_GetUserHistoryProgressListReq_ID] = typeof(CS_GetUserHistoryProgressListReq),
			[(short)GameMSGID.SC_GetUserHistoryProgressListResp_ID] = typeof(SC_GetUserHistoryProgressListResp),
			[(short)GameMSGID.CS_ExploreUpMsg_ID] = typeof(CS_ExploreUpMsg),
			[(short)GameMSGID.SC_ExploreDownMsg_ID] = typeof(SC_ExploreDownMsg),
			[(short)GameMSGID.SC_ExploreDownMsgForServerBasic_ID] = typeof(SC_ExploreDownMsgForServerBasic),
			[(short)GameMSGID.SC_ExploreDownMsgForHeartbeat_ID] = typeof(SC_ExploreDownMsgForHeartbeat),
			[(short)GameMSGID.SC_DialogDownMsgForAvatarReply_ID] = typeof(SC_DialogDownMsgForAvatarReply),
			[(short)GameMSGID.SC_DialogDownMsgForAvatarReplyTranslate_ID] = typeof(SC_DialogDownMsgForAvatarReplyTranslate),
			[(short)GameMSGID.SC_DialogDownMsgForAvatarReplyTTS_ID] = typeof(SC_DialogDownMsgForAvatarReplyTTS),
			[(short)GameMSGID.SC_DialogDownMsgForUserReplyExample_ID] = typeof(SC_DialogDownMsgForUserReplyExample),
			[(short)GameMSGID.SC_DialogDownMsgForUserReplyExampleTranslate_ID] = typeof(SC_DialogDownMsgForUserReplyExampleTranslate),
			[(short)GameMSGID.SC_DialogDownMsgForUserReplyExampleTTS_ID] = typeof(SC_DialogDownMsgForUserReplyExampleTTS),
			[(short)GameMSGID.SC_DialogDownMsgForASR_ID] = typeof(SC_DialogDownMsgForASR),
			[(short)GameMSGID.SC_DialogDownMsgForBizEvent_ID] = typeof(SC_DialogDownMsgForBizEvent),
			[(short)GameMSGID.SC_DialogDownMsgForFeedback_ID] = typeof(SC_DialogDownMsgForFeedback),
			[(short)GameMSGID.SC_DialogDownMsgForTaskGoalStatusChange_ID] = typeof(SC_DialogDownMsgForTaskGoalStatusChange),
			[(short)GameMSGID.SC_DialogDownMsgForAdvice_ID] = typeof(SC_DialogDownMsgForAdvice),
			[(short)GameMSGID.SC_UserSettingDownMsgForSaveUserSettings_ID] = typeof(SC_UserSettingDownMsgForSaveUserSettings),
			[(short)GameMSGID.CS_UpdateAppsflyerCallbackDataReq_ID] = typeof(CS_UpdateAppsflyerCallbackDataReq),
			[(short)GameMSGID.SC_UpdateAppsflyerCallbackDataResp_ID] = typeof(SC_UpdateAppsflyerCallbackDataResp),
			[(short)GameMSGID.CS_GetHomepageGuideItemReq_ID] = typeof(CS_GetHomepageGuideItemReq),
			[(short)GameMSGID.SC_GetHomepageGuideItemResp_ID] = typeof(SC_GetHomepageGuideItemResp),
			[(short)GameMSGID.SC_UserChatDownMsgForUserRecognizing_ID] = typeof(SC_UserChatDownMsgForUserRecognizing),
			[(short)GameMSGID.SC_UserChatDownMsgForUserRecognized_ID] = typeof(SC_UserChatDownMsgForUserRecognized),
			[(short)GameMSGID.SC_UserChatDownMsgForOtherUserRecognizing_ID] = typeof(SC_UserChatDownMsgForOtherUserRecognizing),
			[(short)GameMSGID.SC_UserChatDownMsgForOtherUserRecognized_ID] = typeof(SC_UserChatDownMsgForOtherUserRecognized),
			[(short)GameMSGID.SC_UserChatDownMsgForOtherUserReplyTranslate_ID] = typeof(SC_UserChatDownMsgForOtherUserReplyTranslate),
			[(short)GameMSGID.SC_UserChatDownMsgForUserReplyExample_ID] = typeof(SC_UserChatDownMsgForUserReplyExample),
			[(short)GameMSGID.SC_UserChatDownMsgForUserReplyExampleTranslate_ID] = typeof(SC_UserChatDownMsgForUserReplyExampleTranslate),
			[(short)GameMSGID.SC_UserChatDownMsgForBizEvent_ID] = typeof(SC_UserChatDownMsgForBizEvent),
			[(short)GameMSGID.SC_MatchingDown_MatchStatus_ID] = typeof(SC_MatchingDown_MatchStatus),
			[(short)GameMSGID.SC_MatchingDown_MatchedResult_ID] = typeof(SC_MatchingDown_MatchedResult),
			[(short)GameMSGID.SC_MatchingDown_MatchFailed_ID] = typeof(SC_MatchingDown_MatchFailed),
			[(short)GameMSGID.SC_NativeSpeakerDown_MatchStatus_ID] = typeof(SC_NativeSpeakerDown_MatchStatus),
			[(short)GameMSGID.SC_NativeSpeakerDown_MatchedResult_ID] = typeof(SC_NativeSpeakerDown_MatchedResult),
			[(short)GameMSGID.SC_NativeSpeakerDown_MatchFailed_ID] = typeof(SC_NativeSpeakerDown_MatchFailed),
			[(short)GameMSGID.SC_OnboardingChatDownMsgForAvatarReply_ID] = typeof(SC_OnboardingChatDownMsgForAvatarReply),
			[(short)GameMSGID.SC_OnboardingChatDownMsgForAvatarReplyTranslate_ID] = typeof(SC_OnboardingChatDownMsgForAvatarReplyTranslate),
			[(short)GameMSGID.SC_OnboardingChatDownMsgForAvatarReplyTTS_ID] = typeof(SC_OnboardingChatDownMsgForAvatarReplyTTS),
			[(short)GameMSGID.SC_OnboardingChatDownMsgForASR_ID] = typeof(SC_OnboardingChatDownMsgForASR),
			[(short)GameMSGID.SC_OnboardingChatDownMsgForBizEvent_ID] = typeof(SC_OnboardingChatDownMsgForBizEvent),
			[(short)GameMSGID.SC_OnboardingChatDownMsgForSettlement_ID] = typeof(SC_OnboardingChatDownMsgForSettlement),
			[(short)GameMSGID.CS_GetOnboardingChatPreloadDataReq_ID] = typeof(CS_GetOnboardingChatPreloadDataReq),
			[(short)GameMSGID.SC_GetOnboardingChatPreloadDataResp_ID] = typeof(SC_GetOnboardingChatPreloadDataResp),
			[(short)GameMSGID.SC_SkipOnboardingChat_ID] = typeof(SC_SkipOnboardingChat),
			[(short)GameMSGID.SC_MissionStoryChatDownMsgForAvatarReply_ID] = typeof(SC_MissionStoryChatDownMsgForAvatarReply),
			[(short)GameMSGID.SC_MissionStoryChatDownMsgForAvatarReplyTranslate_ID] = typeof(SC_MissionStoryChatDownMsgForAvatarReplyTranslate),
			[(short)GameMSGID.SC_MissionStoryChatDownMsgForAvatarReplyTTS_ID] = typeof(SC_MissionStoryChatDownMsgForAvatarReplyTTS),
			[(short)GameMSGID.SC_MissionStoryChatDownMsgForUserReplyExample_ID] = typeof(SC_MissionStoryChatDownMsgForUserReplyExample),
			[(short)GameMSGID.SC_MissionStoryChatDownMsgForUserReplyExampleTranslate_ID] = typeof(SC_MissionStoryChatDownMsgForUserReplyExampleTranslate),
			[(short)GameMSGID.SC_MissionStoryChatDownMsgForUserReplyExampleTTS_ID] = typeof(SC_MissionStoryChatDownMsgForUserReplyExampleTTS),
			[(short)GameMSGID.SC_MissionStoryChatDownMsgForAdvice_ID] = typeof(SC_MissionStoryChatDownMsgForAdvice),
			[(short)GameMSGID.SC_MissionStoryChatDownMsgForASR_ID] = typeof(SC_MissionStoryChatDownMsgForASR),
			[(short)GameMSGID.SC_MissionStoryChatDownMsgForBizEvent_ID] = typeof(SC_MissionStoryChatDownMsgForBizEvent),
			[(short)GameMSGID.SC_MissionStoryChatDownMsgForStepProgressChange_ID] = typeof(SC_MissionStoryChatDownMsgForStepProgressChange),
			[(short)GameMSGID.SC_MissionStoryChatDownMsgForSettlement_ID] = typeof(SC_MissionStoryChatDownMsgForSettlement),
		};

		public static Dictionary<Type, short> msgT2ID = new Dictionary<Type, short>
		{
			
			
			[typeof(CS_LoginReq)] = (short)GameMSGID.CS_LoginReq_ID,
			[typeof(SC_LoginAck)] = (short)GameMSGID.SC_LoginAck_ID,
			[typeof(CS_GetRoleInfoReq)] = (short)GameMSGID.CS_GetRoleInfoReq_ID,
			[typeof(SC_GetRoleInfoAck)] = (short)GameMSGID.SC_GetRoleInfoAck_ID,
			[typeof(CS_EnterGameZoneReq)] = (short)GameMSGID.CS_EnterGameZoneReq_ID,
			[typeof(SC_EnterGameZoneAck)] = (short)GameMSGID.SC_EnterGameZoneAck_ID,
			
			
			
			
			[typeof(CS_ExitGameReq)] = (short)GameMSGID.CS_ExitGameReq_ID,
			[typeof(SC_ExitGameAck)] = (short)GameMSGID.SC_ExitGameAck_ID,
			[typeof(CS_ChangeRoleNameReq)] = (short)GameMSGID.CS_ChangeRoleNameReq_ID,
			[typeof(SC_ChangeRoleNameAck)] = (short)GameMSGID.SC_ChangeRoleNameAck_ID,
			[typeof(SC_ChangeRoleNameNtf)] = (short)GameMSGID.SC_ChangeRoleNameNtf_ID,
			
			
			
			
			
			
			[typeof(CS_HeartBeatReq)] = (short)GameMSGID.CS_HeartBeatReq_ID,
			[typeof(SC_HeartBeatAck)] = (short)GameMSGID.SC_HeartBeatAck_ID,
			[typeof(CS_CreateRoleReq)] = (short)GameMSGID.CS_CreateRoleReq_ID,
			[typeof(SC_CreateRoleAck)] = (short)GameMSGID.SC_CreateRoleAck_ID,
			[typeof(CS_SelectRoleReq)] = (short)GameMSGID.CS_SelectRoleReq_ID,
			[typeof(SC_SelectRoleAck)] = (short)GameMSGID.SC_SelectRoleAck_ID,
			
			[typeof(SC_RoleOnlineInfoNtf)] = (short)GameMSGID.SC_RoleOnlineInfoNtf_ID,
			[typeof(SC_SelfInfoNtf)] = (short)GameMSGID.SC_SelfInfoNtf_ID,
			[typeof(SC_RoleAppearInfoNtf)] = (short)GameMSGID.SC_RoleAppearInfoNtf_ID,
			[typeof(SC_AvatarAppearInfoNtf)] = (short)GameMSGID.SC_AvatarAppearInfoNtf_ID,
			[typeof(SC_ObjMoveNtf)] = (short)GameMSGID.SC_ObjMoveNtf_ID,
			[typeof(SC_ObjDisappearNtf)] = (short)GameMSGID.SC_ObjDisappearNtf_ID,
			[typeof(CS_RoleMoveReq)] = (short)GameMSGID.CS_RoleMoveReq_ID,
			[typeof(SC_RoleMoveAck)] = (short)GameMSGID.SC_RoleMoveAck_ID,
			[typeof(CS_RoleChangeSceneReq)] = (short)GameMSGID.CS_RoleChangeSceneReq_ID,
			[typeof(SC_RoleChangeSceneAck)] = (short)GameMSGID.SC_RoleChangeSceneAck_ID,
			[typeof(SC_BubblingInfoNtf)] = (short)GameMSGID.SC_BubblingInfoNtf_ID,
			[typeof(SC_ActionInfoNtf)] = (short)GameMSGID.SC_ActionInfoNtf_ID,
			[typeof(CS_TalkToAvatarReq)] = (short)GameMSGID.CS_TalkToAvatarReq_ID,
			[typeof(SC_TalkToAvatarAck)] = (short)GameMSGID.SC_TalkToAvatarAck_ID,
			[typeof(CS_GoPlayerHomeReq)] = (short)GameMSGID.CS_GoPlayerHomeReq_ID,
			[typeof(SC_GoPlayerHomeAck)] = (short)GameMSGID.SC_GoPlayerHomeAck_ID,
			[typeof(CS_LeavePlayerHomeReq)] = (short)GameMSGID.CS_LeavePlayerHomeReq_ID,
			[typeof(SC_LeavePlayerHomeAck)] = (short)GameMSGID.SC_LeavePlayerHomeAck_ID,
			
			
			
			
			[typeof(CS_GetCurLineAvatarInfoReq)] = (short)GameMSGID.CS_GetCurLineAvatarInfoReq_ID,
			[typeof(SC_GetCurLineAvatarInfoAck)] = (short)GameMSGID.SC_GetCurLineAvatarInfoAck_ID,
			[typeof(CS_PlayerInTaskReq)] = (short)GameMSGID.CS_PlayerInTaskReq_ID,
			[typeof(SC_PlayerInTaskAck)] = (short)GameMSGID.SC_PlayerInTaskAck_ID,
			[typeof(SC_AvatarMoveToSelfNtf)] = (short)GameMSGID.SC_AvatarMoveToSelfNtf_ID,
			[typeof(CS_AgreeAvatarMoveReq)] = (short)GameMSGID.CS_AgreeAvatarMoveReq_ID,
			[typeof(SC_AgreeAvatarMoveAck)] = (short)GameMSGID.SC_AgreeAvatarMoveAck_ID,
			[typeof(CS_GetBuildingAvatarInfoReq)] = (short)GameMSGID.CS_GetBuildingAvatarInfoReq_ID,
			[typeof(SC_GetBuildingAvatarInfoAck)] = (short)GameMSGID.SC_GetBuildingAvatarInfoAck_ID,
			
			
			[typeof(SC_PlayerLadderLevelChange)] = (short)GameMSGID.SC_PlayerLadderLevelChange_ID,
			
			[typeof(SC_MemberTypeChangeNtf)] = (short)GameMSGID.SC_MemberTypeChangeNtf_ID,
			[typeof(CS_GetAvatarJobInfoReq)] = (short)GameMSGID.CS_GetAvatarJobInfoReq_ID,
			[typeof(SC_GetAvatarJobInfoAck)] = (short)GameMSGID.SC_GetAvatarJobInfoAck_ID,
			
			[typeof(CS_VoiceDialogReq)] = (short)GameMSGID.CS_VoiceDialogReq_ID,
			[typeof(SC_VoiceDialogAck)] = (short)GameMSGID.SC_VoiceDialogAck_ID,
			[typeof(SC_VoiceDialogNtf)] = (short)GameMSGID.SC_VoiceDialogNtf_ID,
			[typeof(CS_DealVoiceDialogReq)] = (short)GameMSGID.CS_DealVoiceDialogReq_ID,
			[typeof(SC_DealVoiceDialogAck)] = (short)GameMSGID.SC_DealVoiceDialogAck_ID,
			[typeof(SC_DealVoiceDialogNtf)] = (short)GameMSGID.SC_DealVoiceDialogNtf_ID,
			[typeof(CS_GetVoiceDialogRecordReq)] = (short)GameMSGID.CS_GetVoiceDialogRecordReq_ID,
			[typeof(SC_GetVoiceDialogRecordAck)] = (short)GameMSGID.SC_GetVoiceDialogRecordAck_ID,
			[typeof(SC_PlayerStateInfoNtf)] = (short)GameMSGID.SC_PlayerStateInfoNtf_ID,
			[typeof(SC_StartVoiceDialogNtf)] = (short)GameMSGID.SC_StartVoiceDialogNtf_ID,
			[typeof(CS_VoiceDialogStateChangeReq)] = (short)GameMSGID.CS_VoiceDialogStateChangeReq_ID,
			[typeof(SC_VoiceDialogStateChangeAck)] = (short)GameMSGID.SC_VoiceDialogStateChangeAck_ID,
			[typeof(SC_VoiceDialogStateChangeNtf)] = (short)GameMSGID.SC_VoiceDialogStateChangeNtf_ID,
			[typeof(CS_StopVoiceDialogReq)] = (short)GameMSGID.CS_StopVoiceDialogReq_ID,
			[typeof(SC_StopVoiceDialogAck)] = (short)GameMSGID.SC_StopVoiceDialogAck_ID,
			[typeof(SC_StopVoiceDialogNtf)] = (short)GameMSGID.SC_StopVoiceDialogNtf_ID,
			[typeof(CS_CancelVoiceDialogReq)] = (short)GameMSGID.CS_CancelVoiceDialogReq_ID,
			[typeof(SC_CancelVoiceDialogAck)] = (short)GameMSGID.SC_CancelVoiceDialogAck_ID,
			[typeof(SC_CancelVoiceDialogNtf)] = (short)GameMSGID.SC_CancelVoiceDialogNtf_ID,
			[typeof(SC_ForbidVoiceDialogChangeNtf)] = (short)GameMSGID.SC_ForbidVoiceDialogChangeNtf_ID,
			[typeof(CS_VoiceDialogInfoReq)] = (short)GameMSGID.CS_VoiceDialogInfoReq_ID,
			[typeof(SC_VoiceDialogInfoAck)] = (short)GameMSGID.SC_VoiceDialogInfoAck_ID,
			[typeof(SC_VoiceDialogInfoNtf)] = (short)GameMSGID.SC_VoiceDialogInfoNtf_ID,
			[typeof(CS_RecvVoiceDialogInfoReq)] = (short)GameMSGID.CS_RecvVoiceDialogInfoReq_ID,
			[typeof(SC_RecvVoiceDialogInfoAck)] = (short)GameMSGID.SC_RecvVoiceDialogInfoAck_ID,
			[typeof(SC_VoiceDialogTextNtf)] = (short)GameMSGID.SC_VoiceDialogTextNtf_ID,
			[typeof(CS_RecvVoiceDialogTextReq)] = (short)GameMSGID.CS_RecvVoiceDialogTextReq_ID,
			[typeof(SC_RecvVoiceDialogTextAck)] = (short)GameMSGID.SC_RecvVoiceDialogTextAck_ID,
			[typeof(CS_GetReconnectVoiceDialogReq)] = (short)GameMSGID.CS_GetReconnectVoiceDialogReq_ID,
			[typeof(SC_GetReconnectVoiceDialogAck)] = (short)GameMSGID.SC_GetReconnectVoiceDialogAck_ID,
			[typeof(CS_ForbidVoiceDialogChangeReq)] = (short)GameMSGID.CS_ForbidVoiceDialogChangeReq_ID,
			[typeof(SC_ForbidVoiceDialogChangeAck)] = (short)GameMSGID.SC_ForbidVoiceDialogChangeAck_ID,
			[typeof(CS_VoiceDialogTickReq)] = (short)GameMSGID.CS_VoiceDialogTickReq_ID,
			[typeof(SC_VoiceDialogTickAck)] = (short)GameMSGID.SC_VoiceDialogTickAck_ID,
			[typeof(SC_VoiceDialogTickNtf)] = (short)GameMSGID.SC_VoiceDialogTickNtf_ID,
			[typeof(SC_VoiceDialogTimeoutNtf)] = (short)GameMSGID.SC_VoiceDialogTimeoutNtf_ID,
			[typeof(CS_LeaveGSReq)] = (short)GameMSGID.CS_LeaveGSReq_ID,
			[typeof(SC_LeaveGSAck)] = (short)GameMSGID.SC_LeaveGSAck_ID,
			[typeof(CS_QueryPopularityListReq)] = (short)GameMSGID.CS_QueryPopularityListReq_ID,
			[typeof(SC_QueryPopularityListAck)] = (short)GameMSGID.SC_QueryPopularityListAck_ID,
			
			
			[typeof(SC_RoleInstChatPush)] = (short)GameMSGID.SC_RoleInstChatPush_ID,
			[typeof(CS_AvatarCommandReq)] = (short)GameMSGID.CS_AvatarCommandReq_ID,
			[typeof(SC_AvatarCommandAck)] = (short)GameMSGID.SC_AvatarCommandAck_ID,
			[typeof(SC_PlayerCommandMovePush)] = (short)GameMSGID.SC_PlayerCommandMovePush_ID,
			[typeof(SC_CommandQuitChatDramaNtf)] = (short)GameMSGID.SC_CommandQuitChatDramaNtf_ID,
			[typeof(CS_GetTalkitPushReq)] = (short)GameMSGID.CS_GetTalkitPushReq_ID,
			[typeof(SC_GetTalkitPushAck)] = (short)GameMSGID.SC_GetTalkitPushAck_ID,
			[typeof(CS_JumpServerReq)] = (short)GameMSGID.CS_JumpServerReq_ID,
			[typeof(SC_JumpServerAck)] = (short)GameMSGID.SC_JumpServerAck_ID,
			
			
			[typeof(CS_CreateDialogTaskCacheReq)] = (short)GameMSGID.CS_CreateDialogTaskCacheReq_ID,
			[typeof(SC_CreateDialogTaskCacheAck)] = (short)GameMSGID.SC_CreateDialogTaskCacheAck_ID,
			[typeof(CS_GetDialogTaskModeListReq)] = (short)GameMSGID.CS_GetDialogTaskModeListReq_ID,
			[typeof(SC_GetDialogTaskModeListAck)] = (short)GameMSGID.SC_GetDialogTaskModeListAck_ID,
			[typeof(CS_GetModeInfoReq)] = (short)GameMSGID.CS_GetModeInfoReq_ID,
			[typeof(SC_GetModeInfoAck)] = (short)GameMSGID.SC_GetModeInfoAck_ID,
			[typeof(CS_CreateDialogTaskReq)] = (short)GameMSGID.CS_CreateDialogTaskReq_ID,
			[typeof(SC_CreateDialogTaskAck)] = (short)GameMSGID.SC_CreateDialogTaskAck_ID,
			[typeof(CS_DialogTaskMsgHandleReq)] = (short)GameMSGID.CS_DialogTaskMsgHandleReq_ID,
			[typeof(SC_DialogTaskMsgHandleAck)] = (short)GameMSGID.SC_DialogTaskMsgHandleAck_ID,
			[typeof(SC_TaskStepContentPushReq)] = (short)GameMSGID.SC_TaskStepContentPushReq_ID,
			[typeof(SC_TaskStepStatusPushReq)] = (short)GameMSGID.SC_TaskStepStatusPushReq_ID,
			[typeof(CS_TranslateReq)] = (short)GameMSGID.CS_TranslateReq_ID,
			[typeof(SC_TranslateAck)] = (short)GameMSGID.SC_TranslateAck_ID,
			[typeof(CS_ScaffoldFeedbackReq)] = (short)GameMSGID.CS_ScaffoldFeedbackReq_ID,
			[typeof(SC_ScaffoldFeedbackAck)] = (short)GameMSGID.SC_ScaffoldFeedbackAck_ID,
			[typeof(CS_QuestionFeedbackReq)] = (short)GameMSGID.CS_QuestionFeedbackReq_ID,
			[typeof(SC_QuestionFeedbackAck)] = (short)GameMSGID.SC_QuestionFeedbackAck_ID,
			[typeof(CS_TaskFeedbackReq)] = (short)GameMSGID.CS_TaskFeedbackReq_ID,
			[typeof(SC_TaskFeedbackAck)] = (short)GameMSGID.SC_TaskFeedbackAck_ID,
			[typeof(CS_FreeTalkFeedbackReq)] = (short)GameMSGID.CS_FreeTalkFeedbackReq_ID,
			[typeof(SC_FreeTalkFeedbackAck)] = (short)GameMSGID.SC_FreeTalkFeedbackAck_ID,
			[typeof(CS_Content2AudioReq)] = (short)GameMSGID.CS_Content2AudioReq_ID,
			[typeof(SC_Content2AudioAck)] = (short)GameMSGID.SC_Content2AudioAck_ID,
			[typeof(CS_ExitDialogTaskReq)] = (short)GameMSGID.CS_ExitDialogTaskReq_ID,
			[typeof(SC_ExitDialogTaskAck)] = (short)GameMSGID.SC_ExitDialogTaskAck_ID,
			[typeof(CS_GetMsgAssessResultReq)] = (short)GameMSGID.CS_GetMsgAssessResultReq_ID,
			[typeof(SC_GetMsgAssessResultAck)] = (short)GameMSGID.SC_GetMsgAssessResultAck_ID,
			[typeof(CS_DialogFlowCreateReq)] = (short)GameMSGID.CS_DialogFlowCreateReq_ID,
			[typeof(SC_DialogFlowCreateAck)] = (short)GameMSGID.SC_DialogFlowCreateAck_ID,
			[typeof(CS_DialogFlowMsgHandleReq)] = (short)GameMSGID.CS_DialogFlowMsgHandleReq_ID,
			[typeof(SC_DialogFlowMsgHandleAck)] = (short)GameMSGID.SC_DialogFlowMsgHandleAck_ID,
			[typeof(CS_WordTransReq)] = (short)GameMSGID.CS_WordTransReq_ID,
			[typeof(SC_WordTransAck)] = (short)GameMSGID.SC_WordTransAck_ID,
			[typeof(CS_GetDialogTaskNextRoundMsgReq)] = (short)GameMSGID.CS_GetDialogTaskNextRoundMsgReq_ID,
			[typeof(SC_GetDialogTaskNextRoundMsgAck)] = (short)GameMSGID.SC_GetDialogTaskNextRoundMsgAck_ID,
			[typeof(CS_StrengthenDialogTaskMsgHandleReq)] = (short)GameMSGID.CS_StrengthenDialogTaskMsgHandleReq_ID,
			[typeof(SC_StrengthenDialogTaskMsgHandleAck)] = (short)GameMSGID.SC_StrengthenDialogTaskMsgHandleAck_ID,
			[typeof(CS_GetStrengthenDialogTaskNextRoundMsgReq)] = (short)GameMSGID.CS_GetStrengthenDialogTaskNextRoundMsgReq_ID,
			[typeof(SC_GetStrengthenDialogTaskNextRoundMsgAck)] = (short)GameMSGID.SC_GetStrengthenDialogTaskNextRoundMsgAck_ID,
			[typeof(CS_GetTTSAudioReq)] = (short)GameMSGID.CS_GetTTSAudioReq_ID,
			[typeof(SC_GetTTSAudioAck)] = (short)GameMSGID.SC_GetTTSAudioAck_ID,
			[typeof(CS_StartASRStreamReq)] = (short)GameMSGID.CS_StartASRStreamReq_ID,
			[typeof(SC_StartASRStreamAck)] = (short)GameMSGID.SC_StartASRStreamAck_ID,
			[typeof(CS_StrengthenCreateDialogTaskReq)] = (short)GameMSGID.CS_StrengthenCreateDialogTaskReq_ID,
			[typeof(SC_StrengthenCreateDialogTaskAck)] = (short)GameMSGID.SC_StrengthenCreateDialogTaskAck_ID,
			[typeof(CS_GetSpeechAudioReq)] = (short)GameMSGID.CS_GetSpeechAudioReq_ID,
			[typeof(SC_GetSpeechAudioAck)] = (short)GameMSGID.SC_GetSpeechAudioAck_ID,
			[typeof(CS_GetFailedMsgReq)] = (short)GameMSGID.CS_GetFailedMsgReq_ID,
			[typeof(SC_GetFailedMsgACK)] = (short)GameMSGID.SC_GetFailedMsgACK_ID,
			[typeof(CS_FlowHelpReq)] = (short)GameMSGID.CS_FlowHelpReq_ID,
			[typeof(SC_FlowHelpAck)] = (short)GameMSGID.SC_FlowHelpAck_ID,
			[typeof(CS_SubmitQuestionFeedbackReq)] = (short)GameMSGID.CS_SubmitQuestionFeedbackReq_ID,
			[typeof(SC_SubmitQuestionFeedbackAck)] = (short)GameMSGID.SC_SubmitQuestionFeedbackAck_ID,
			[typeof(CS_DialogFlowStopReq)] = (short)GameMSGID.CS_DialogFlowStopReq_ID,
			[typeof(SC_DialogFlowStopAck)] = (short)GameMSGID.SC_DialogFlowStopAck_ID,
			[typeof(CS_FeedbackReq)] = (short)GameMSGID.CS_FeedbackReq_ID,
			[typeof(SC_FeedbackAck)] = (short)GameMSGID.SC_FeedbackAck_ID,
			[typeof(CS_CancelAssessReq)] = (short)GameMSGID.CS_CancelAssessReq_ID,
			[typeof(SC_CancelAssessAck)] = (short)GameMSGID.SC_CancelAssessAck_ID,
			[typeof(CS_CancelASRReq)] = (short)GameMSGID.CS_CancelASRReq_ID,
			[typeof(SC_CancelASRAck)] = (short)GameMSGID.SC_CancelASRAck_ID,
			[typeof(CS_SubmitTaskFeedbackReq)] = (short)GameMSGID.CS_SubmitTaskFeedbackReq_ID,
			[typeof(SC_SubmitTaskFeedbackAck)] = (short)GameMSGID.SC_SubmitTaskFeedbackAck_ID,
			[typeof(CS_GetTTSAudioTranscriptReq)] = (short)GameMSGID.CS_GetTTSAudioTranscriptReq_ID,
			[typeof(SC_GetTTSAudioTranscriptAck)] = (short)GameMSGID.SC_GetTTSAudioTranscriptAck_ID,
			[typeof(CS_SwitchTaskDifficultyReq)] = (short)GameMSGID.CS_SwitchTaskDifficultyReq_ID,
			[typeof(SC_SwitchTaskDifficultyAck)] = (short)GameMSGID.SC_SwitchTaskDifficultyAck_ID,
			[typeof(CS_AssessWithAudioStreamReq)] = (short)GameMSGID.CS_AssessWithAudioStreamReq_ID,
			[typeof(SC_AssessWithAudioStreamAck)] = (short)GameMSGID.SC_AssessWithAudioStreamAck_ID,
			[typeof(CS_RecommendTaskReq)] = (short)GameMSGID.CS_RecommendTaskReq_ID,
			[typeof(SC_RecommendTaskAck)] = (short)GameMSGID.SC_RecommendTaskAck_ID,
			[typeof(CS_UploadUserTaskActionReq)] = (short)GameMSGID.CS_UploadUserTaskActionReq_ID,
			[typeof(SC_UploadUserTaskActionAck)] = (short)GameMSGID.SC_UploadUserTaskActionAck_ID,
			[typeof(CS_GetUserInfoReq)] = (short)GameMSGID.CS_GetUserInfoReq_ID,
			[typeof(SC_GetUserInfoAck)] = (short)GameMSGID.SC_GetUserInfoAck_ID,
			[typeof(CS_GetAudioForFlowReq)] = (short)GameMSGID.CS_GetAudioForFlowReq_ID,
			[typeof(SC_GetAudioForFlowAck)] = (short)GameMSGID.SC_GetAudioForFlowAck_ID,
			[typeof(CS_GetTaskInfoReq)] = (short)GameMSGID.CS_GetTaskInfoReq_ID,
			[typeof(SC_GetTaskInfoAck)] = (short)GameMSGID.SC_GetTaskInfoAck_ID,
			[typeof(CS_GetModeInfoForCareerReq)] = (short)GameMSGID.CS_GetModeInfoForCareerReq_ID,
			[typeof(SC_GetModeInfoForCareerAck)] = (short)GameMSGID.SC_GetModeInfoForCareerAck_ID,
			[typeof(CS_ChangeMarkInfoReq)] = (short)GameMSGID.CS_ChangeMarkInfoReq_ID,
			[typeof(SC_ChangeMarkInfoAck)] = (short)GameMSGID.SC_ChangeMarkInfoAck_ID,
			[typeof(CS_SandTableListReq)] = (short)GameMSGID.CS_SandTableListReq_ID,
			[typeof(SC_SandTableListAck)] = (short)GameMSGID.SC_SandTableListAck_ID,
			[typeof(CS_QueryMemberInfoReq)] = (short)GameMSGID.CS_QueryMemberInfoReq_ID,
			[typeof(SC_QueryMemberInfoAck)] = (short)GameMSGID.SC_QueryMemberInfoAck_ID,
			[typeof(SC_MemberTypePushReq)] = (short)GameMSGID.SC_MemberTypePushReq_ID,
			[typeof(CS_MemberTypePushAck)] = (short)GameMSGID.CS_MemberTypePushAck_ID,
			[typeof(CS_GetEconomicInfoReq)] = (short)GameMSGID.CS_GetEconomicInfoReq_ID,
			[typeof(SC_GetEconomicInfoAck)] = (short)GameMSGID.SC_GetEconomicInfoAck_ID,
			[typeof(CS_BuyDialogTicketReq)] = (short)GameMSGID.CS_BuyDialogTicketReq_ID,
			[typeof(SC_BuyDialogTicketAck)] = (short)GameMSGID.SC_BuyDialogTicketAck_ID,
			[typeof(CS_GetInProgressDialogReq)] = (short)GameMSGID.CS_GetInProgressDialogReq_ID,
			[typeof(SC_GetInProgressDialogAck)] = (short)GameMSGID.SC_GetInProgressDialogAck_ID,
			[typeof(SC_NotifyMemberBenefitReq)] = (short)GameMSGID.SC_NotifyMemberBenefitReq_ID,
			[typeof(CS_NotifyMemberBenefitAck)] = (short)GameMSGID.CS_NotifyMemberBenefitAck_ID,
			[typeof(CS_GetPopListInfoReq)] = (short)GameMSGID.CS_GetPopListInfoReq_ID,
			[typeof(SC_GetPopListInfoAck)] = (short)GameMSGID.SC_GetPopListInfoAck_ID,
			[typeof(CS_SandTableHomepageReq)] = (short)GameMSGID.CS_SandTableHomepageReq_ID,
			[typeof(SC_SandTableHomepageAck)] = (short)GameMSGID.SC_SandTableHomepageAck_ID,
			[typeof(CS_QueryDialogRecordReq)] = (short)GameMSGID.CS_QueryDialogRecordReq_ID,
			[typeof(SC_QueryDialogRecordAck)] = (short)GameMSGID.SC_QueryDialogRecordAck_ID,
			
			
			
			
			[typeof(CS_DialogHelpReq)] = (short)GameMSGID.CS_DialogHelpReq_ID,
			[typeof(SC_DialogHelpAck)] = (short)GameMSGID.SC_DialogHelpAck_ID,
			[typeof(CS_GetDialogScaffoldReq)] = (short)GameMSGID.CS_GetDialogScaffoldReq_ID,
			[typeof(SC_GetDialogScaffoldAck)] = (short)GameMSGID.SC_GetDialogScaffoldAck_ID,
			[typeof(CS_GetAvatarListByDialogModeReq)] = (short)GameMSGID.CS_GetAvatarListByDialogModeReq_ID,
			[typeof(SC_GetAvatarListByDialogModeAck)] = (short)GameMSGID.SC_GetAvatarListByDialogModeAck_ID,
			[typeof(CS_WarmupPracticeCreateDialogTaskReq)] = (short)GameMSGID.CS_WarmupPracticeCreateDialogTaskReq_ID,
			[typeof(SC_WarmupPracticeCreateDialogTaskAck)] = (short)GameMSGID.SC_WarmupPracticeCreateDialogTaskAck_ID,
			[typeof(CS_WarmupPracticeDialogTaskMsgHandleReq)] = (short)GameMSGID.CS_WarmupPracticeDialogTaskMsgHandleReq_ID,
			[typeof(SC_WarmupPracticeDialogTaskMsgHandleAck)] = (short)GameMSGID.SC_WarmupPracticeDialogTaskMsgHandleAck_ID,
			[typeof(CS_GetWarmupPracticeDialogTaskNextRoundMsgReq)] = (short)GameMSGID.CS_GetWarmupPracticeDialogTaskNextRoundMsgReq_ID,
			[typeof(SC_GetWarmupPracticeDialogTaskNextRoundMsgAck)] = (short)GameMSGID.SC_GetWarmupPracticeDialogTaskNextRoundMsgAck_ID,
			[typeof(CS_GetASRAudioReq)] = (short)GameMSGID.CS_GetASRAudioReq_ID,
			[typeof(SC_GetASRAudioAck)] = (short)GameMSGID.SC_GetASRAudioAck_ID,
			[typeof(CS_UploadAsrAudioReq)] = (short)GameMSGID.CS_UploadAsrAudioReq_ID,
			[typeof(SC_UploadAsrAudioAck)] = (short)GameMSGID.SC_UploadAsrAudioAck_ID,
			[typeof(CS_SubmitSpeakUserAnswerReq)] = (short)GameMSGID.CS_SubmitSpeakUserAnswerReq_ID,
			[typeof(SC_SubmitSpeakUserAnswerAck)] = (short)GameMSGID.SC_SubmitSpeakUserAnswerAck_ID,
			[typeof(CS_GetUserChapterInfoReq)] = (short)GameMSGID.CS_GetUserChapterInfoReq_ID,
			[typeof(SC_GetUserChapterInfoAck)] = (short)GameMSGID.SC_GetUserChapterInfoAck_ID,
			[typeof(CS_GetGoalInfoReq)] = (short)GameMSGID.CS_GetGoalInfoReq_ID,
			[typeof(SC_GetGoalInfoAck)] = (short)GameMSGID.SC_GetGoalInfoAck_ID,
			[typeof(CS_GetChapterRecommendListReq)] = (short)GameMSGID.CS_GetChapterRecommendListReq_ID,
			[typeof(SC_GetChapterRecommendListAck)] = (short)GameMSGID.SC_GetChapterRecommendListAck_ID,
			[typeof(CS_SelectChapterReq)] = (short)GameMSGID.CS_SelectChapterReq_ID,
			[typeof(SC_SelectChapterAck)] = (short)GameMSGID.SC_SelectChapterAck_ID,
			[typeof(CS_GetChapterProgressInfoReq)] = (short)GameMSGID.CS_GetChapterProgressInfoReq_ID,
			[typeof(SC_GetChapterProgressInfoAck)] = (short)GameMSGID.SC_GetChapterProgressInfoAck_ID,
			[typeof(CS_GetChapterRewardReq)] = (short)GameMSGID.CS_GetChapterRewardReq_ID,
			[typeof(SC_GetChapterRewardAck)] = (short)GameMSGID.SC_GetChapterRewardAck_ID,
			[typeof(CS_GetLearnPathPageTypeReq)] = (short)GameMSGID.CS_GetLearnPathPageTypeReq_ID,
			[typeof(SC_GetLearnPathPageTypeAck)] = (short)GameMSGID.SC_GetLearnPathPageTypeAck_ID,
			[typeof(CS_NotifyAnimationStateReq)] = (short)GameMSGID.CS_NotifyAnimationStateReq_ID,
			[typeof(SC_NotifyAnimationStateAck)] = (short)GameMSGID.SC_NotifyAnimationStateAck_ID,
			[typeof(CS_GetUserGoalDetailReq)] = (short)GameMSGID.CS_GetUserGoalDetailReq_ID,
			[typeof(SC_GetUserGoalDetailAck)] = (short)GameMSGID.SC_GetUserGoalDetailAck_ID,
			[typeof(CS_LearnPathRewardReq)] = (short)GameMSGID.CS_LearnPathRewardReq_ID,
			[typeof(SC_LearnPathRewardAck)] = (short)GameMSGID.SC_LearnPathRewardAck_ID,
			[typeof(CS_DialogSuggestionReq)] = (short)GameMSGID.CS_DialogSuggestionReq_ID,
			[typeof(SC_DialogSuggestionAck)] = (short)GameMSGID.SC_DialogSuggestionAck_ID,
			[typeof(CS_DialogTranslateReq)] = (short)GameMSGID.CS_DialogTranslateReq_ID,
			[typeof(SC_DialogTranslateAck)] = (short)GameMSGID.SC_DialogTranslateAck_ID,
			[typeof(CS_GetAvatarTaskInfoReq)] = (short)GameMSGID.CS_GetAvatarTaskInfoReq_ID,
			[typeof(SC_GetAvatarTaskInfoAck)] = (short)GameMSGID.SC_GetAvatarTaskInfoAck_ID,
			[typeof(CS_GetStartPageInfoReq)] = (short)GameMSGID.CS_GetStartPageInfoReq_ID,
			[typeof(SC_GetStartPageInfoAck)] = (short)GameMSGID.SC_GetStartPageInfoAck_ID,
			[typeof(CS_GetUserAssistLevelListReq)] = (short)GameMSGID.CS_GetUserAssistLevelListReq_ID,
			[typeof(SC_GetUserAssistLevelListAck)] = (short)GameMSGID.SC_GetUserAssistLevelListAck_ID,
			[typeof(CS_SetUserAssistLevelReq)] = (short)GameMSGID.CS_SetUserAssistLevelReq_ID,
			[typeof(SC_SetUserAssistLevelAck)] = (short)GameMSGID.SC_SetUserAssistLevelAck_ID,
			[typeof(CS_GetUserGoalNodeReq)] = (short)GameMSGID.CS_GetUserGoalNodeReq_ID,
			[typeof(SC_GetUserGoalNodeAck)] = (short)GameMSGID.SC_GetUserGoalNodeAck_ID,
			[typeof(CS_GetUserContactsReq)] = (short)GameMSGID.CS_GetUserContactsReq_ID,
			[typeof(SC_GetUserContactsAck)] = (short)GameMSGID.SC_GetUserContactsAck_ID,
			[typeof(CS_AvatarFavoriteSettingReq)] = (short)GameMSGID.CS_AvatarFavoriteSettingReq_ID,
			[typeof(SC_AvatarFavoriteSettingAck)] = (short)GameMSGID.SC_AvatarFavoriteSettingAck_ID,
			[typeof(CS_GetUserQuestionExtraListReq)] = (short)GameMSGID.CS_GetUserQuestionExtraListReq_ID,
			[typeof(SC_GetUserQuestionExtraListAck)] = (short)GameMSGID.SC_GetUserQuestionExtraListAck_ID,
			[typeof(CS_GetKnowledgePointListReq)] = (short)GameMSGID.CS_GetKnowledgePointListReq_ID,
			[typeof(SC_GetKnowledgePointListAck)] = (short)GameMSGID.SC_GetKnowledgePointListAck_ID,
			[typeof(CS_GetKnowledgePointFrontPageReq)] = (short)GameMSGID.CS_GetKnowledgePointFrontPageReq_ID,
			[typeof(SC_GetKnowledgePointFrontPageAck)] = (short)GameMSGID.SC_GetKnowledgePointFrontPageAck_ID,
			[typeof(CS_GetUserQuestionExtraFrontPageReq)] = (short)GameMSGID.CS_GetUserQuestionExtraFrontPageReq_ID,
			[typeof(SC_GetUserQuestionExtraFrontPageAck)] = (short)GameMSGID.SC_GetUserQuestionExtraFrontPageAck_ID,
			[typeof(CS_BatchGetUserQuestionExtraFrontPageReq)] = (short)GameMSGID.CS_BatchGetUserQuestionExtraFrontPageReq_ID,
			[typeof(SC_BatchGetUserQuestionExtraFrontPageAck)] = (short)GameMSGID.SC_BatchGetUserQuestionExtraFrontPageAck_ID,
			[typeof(CS_ReviewUserQuestionExtraReq)] = (short)GameMSGID.CS_ReviewUserQuestionExtraReq_ID,
			[typeof(SC_ReviewUserQuestionExtraAck)] = (short)GameMSGID.SC_ReviewUserQuestionExtraAck_ID,
			[typeof(CS_OpenRewardBoxReq)] = (short)GameMSGID.CS_OpenRewardBoxReq_ID,
			[typeof(SC_OpenRewardBoxAck)] = (short)GameMSGID.SC_OpenRewardBoxAck_ID,
			[typeof(CS_GetDialogSettlementReq)] = (short)GameMSGID.CS_GetDialogSettlementReq_ID,
			[typeof(SC_GetDialogSettlementAck)] = (short)GameMSGID.SC_GetDialogSettlementAck_ID,
			[typeof(CS_StartBatchQuestionDialogReq)] = (short)GameMSGID.CS_StartBatchQuestionDialogReq_ID,
			[typeof(SC_StartBatchQuestionDialogAck)] = (short)GameMSGID.SC_StartBatchQuestionDialogAck_ID,
			[typeof(CS_SubmitBatchQuestionDialogReq)] = (short)GameMSGID.CS_SubmitBatchQuestionDialogReq_ID,
			[typeof(SC_SubmitBatchQuestionDialogAck)] = (short)GameMSGID.SC_SubmitBatchQuestionDialogAck_ID,
			[typeof(CS_GetBoxRewardReq)] = (short)GameMSGID.CS_GetBoxRewardReq_ID,
			[typeof(SC_GetBoxRewardAck)] = (short)GameMSGID.SC_GetBoxRewardAck_ID,
			[typeof(CS_GetUserDialogHistoryReq)] = (short)GameMSGID.CS_GetUserDialogHistoryReq_ID,
			[typeof(SC_GetUserDialogHistoryAck)] = (short)GameMSGID.SC_GetUserDialogHistoryAck_ID,
			[typeof(CS_OnboardingNewStoryReq)] = (short)GameMSGID.CS_OnboardingNewStoryReq_ID,
			[typeof(SC_OnboardingNewStoryAck)] = (short)GameMSGID.SC_OnboardingNewStoryAck_ID,
			[typeof(CS_GetRedPodReq)] = (short)GameMSGID.CS_GetRedPodReq_ID,
			[typeof(SC_GetRedPodAck)] = (short)GameMSGID.SC_GetRedPodAck_ID,
			[typeof(CS_ClickRedPodReq)] = (short)GameMSGID.CS_ClickRedPodReq_ID,
			[typeof(SC_ClickRedPodAck)] = (short)GameMSGID.SC_ClickRedPodAck_ID,
			[typeof(CS_GetUserTopicDialogHistoryReq)] = (short)GameMSGID.CS_GetUserTopicDialogHistoryReq_ID,
			[typeof(SC_GetUserTopicDialogHistoryAck)] = (short)GameMSGID.SC_GetUserTopicDialogHistoryAck_ID,
			[typeof(CS_CreateQuestionDialogReq)] = (short)GameMSGID.CS_CreateQuestionDialogReq_ID,
			[typeof(SC_CreateQuestionDialogAck)] = (short)GameMSGID.SC_CreateQuestionDialogAck_ID,
			[typeof(CS_DelLearnPathDataReq)] = (short)GameMSGID.CS_DelLearnPathDataReq_ID,
			[typeof(SC_DelLearnPathDataAck)] = (short)GameMSGID.SC_DelLearnPathDataAck_ID,
			[typeof(CS_GetQuickPracticeListReq)] = (short)GameMSGID.CS_GetQuickPracticeListReq_ID,
			[typeof(SC_GetQuickPracticeListAck)] = (short)GameMSGID.SC_GetQuickPracticeListAck_ID,
			[typeof(CS_SubmitQuickPracticeReq)] = (short)GameMSGID.CS_SubmitQuickPracticeReq_ID,
			[typeof(SC_SubmitQuickPracticeAck)] = (short)GameMSGID.SC_SubmitQuickPracticeAck_ID,
			[typeof(CS_SubmitQuickPracticeNewReq)] = (short)GameMSGID.CS_SubmitQuickPracticeNewReq_ID,
			[typeof(SC_SubmitQuickPracticeNewAck)] = (short)GameMSGID.SC_SubmitQuickPracticeNewAck_ID,
			[typeof(CS_ExitQuickPracticeReq)] = (short)GameMSGID.CS_ExitQuickPracticeReq_ID,
			[typeof(SC_ExitQuickPracticeAck)] = (short)GameMSGID.SC_ExitQuickPracticeAck_ID,
			[typeof(CS_GetUserCourseReq)] = (short)GameMSGID.CS_GetUserCourseReq_ID,
			[typeof(SC_GetUserCourseAck)] = (short)GameMSGID.SC_GetUserCourseAck_ID,
			[typeof(CS_SkipCourseReq)] = (short)GameMSGID.CS_SkipCourseReq_ID,
			[typeof(SC_SkipCourseAck)] = (short)GameMSGID.SC_SkipCourseAck_ID,
			[typeof(CS_RewardBoxReq)] = (short)GameMSGID.CS_RewardBoxReq_ID,
			[typeof(SC_RewardBoxAck)] = (short)GameMSGID.SC_RewardBoxAck_ID,
			[typeof(CS_GetBookDataReq)] = (short)GameMSGID.CS_GetBookDataReq_ID,
			[typeof(SC_GetBookDataAck)] = (short)GameMSGID.SC_GetBookDataAck_ID,
			[typeof(CS_GetRadioDataReq)] = (short)GameMSGID.CS_GetRadioDataReq_ID,
			[typeof(SC_GetRadioDataAck)] = (short)GameMSGID.SC_GetRadioDataAck_ID,
			[typeof(CS_GetCourseSettlementReq)] = (short)GameMSGID.CS_GetCourseSettlementReq_ID,
			[typeof(SC_GetCourseSettlementAck)] = (short)GameMSGID.SC_GetCourseSettlementAck_ID,
			[typeof(CS_BuyCourseTicketReq)] = (short)GameMSGID.CS_BuyCourseTicketReq_ID,
			[typeof(SC_BuyCourseTicketAck)] = (short)GameMSGID.SC_BuyCourseTicketAck_ID,
			[typeof(CS_GetUserShelfReq)] = (short)GameMSGID.CS_GetUserShelfReq_ID,
			[typeof(SC_GetUserShelfAck)] = (short)GameMSGID.SC_GetUserShelfAck_ID,
			[typeof(CS_GetDynamicQpListReq)] = (short)GameMSGID.CS_GetDynamicQpListReq_ID,
			[typeof(SC_GetDynamicQpListAck)] = (short)GameMSGID.SC_GetDynamicQpListAck_ID,
			[typeof(SC_TaskResultPushReq)] = (short)GameMSGID.SC_TaskResultPushReq_ID,
			[typeof(CS_TaskResultPushAck)] = (short)GameMSGID.CS_TaskResultPushAck_ID,
			[typeof(CS_GetTaskModeBatchReq)] = (short)GameMSGID.CS_GetTaskModeBatchReq_ID,
			[typeof(SC_GetTaskModeBatchAck)] = (short)GameMSGID.SC_GetTaskModeBatchAck_ID,
			[typeof(CS_GetDailyTaskInfoReq)] = (short)GameMSGID.CS_GetDailyTaskInfoReq_ID,
			[typeof(SC_GetDailyTaskInfoAck)] = (short)GameMSGID.SC_GetDailyTaskInfoAck_ID,
			[typeof(CS_DataBatchReq)] = (short)GameMSGID.CS_DataBatchReq_ID,
			[typeof(SC_DataBatchAck)] = (short)GameMSGID.SC_DataBatchAck_ID,
			[typeof(CS_GMToolReq)] = (short)GameMSGID.CS_GMToolReq_ID,
			[typeof(SC_GMToolAck)] = (short)GameMSGID.SC_GMToolAck_ID,
			[typeof(CS_TreeListForHomepageReq)] = (short)GameMSGID.CS_TreeListForHomepageReq_ID,
			[typeof(SC_TreeListForHomepageAck)] = (short)GameMSGID.SC_TreeListForHomepageAck_ID,
			[typeof(CS_TreeDetailForHomepageReq)] = (short)GameMSGID.CS_TreeDetailForHomepageReq_ID,
			[typeof(SC_TreeDetailForHomepageAck)] = (short)GameMSGID.SC_TreeDetailForHomepageAck_ID,
			[typeof(CS_GetUserProfileReq)] = (short)GameMSGID.CS_GetUserProfileReq_ID,
			[typeof(SC_GetUserProfileResponse)] = (short)GameMSGID.SC_GetUserProfileResponse_ID,
			[typeof(CS_SetUserProfileAuthorityReq)] = (short)GameMSGID.CS_SetUserProfileAuthorityReq_ID,
			[typeof(SC_SetUserProfileAuthorityResp)] = (short)GameMSGID.SC_SetUserProfileAuthorityResp_ID,
			[typeof(CS_GetUserProfileAuthorityReq)] = (short)GameMSGID.CS_GetUserProfileAuthorityReq_ID,
			[typeof(SC_GetUserProfileAuthorityResp)] = (short)GameMSGID.SC_GetUserProfileAuthorityResp_ID,
			[typeof(CS_SendFeedbackReq)] = (short)GameMSGID.CS_SendFeedbackReq_ID,
			[typeof(SC_SendFeedbackResp)] = (short)GameMSGID.SC_SendFeedbackResp_ID,
			[typeof(CS_GetMyProfileReq)] = (short)GameMSGID.CS_GetMyProfileReq_ID,
			[typeof(SC_GetMyProfileResponse)] = (short)GameMSGID.SC_GetMyProfileResponse_ID,
			[typeof(CS_SetUserPortraitsReq)] = (short)GameMSGID.CS_SetUserPortraitsReq_ID,
			[typeof(SC_SetUserPortraitsAck)] = (short)GameMSGID.SC_SetUserPortraitsAck_ID,
			[typeof(CS_SetUserLanguageLevelReq)] = (short)GameMSGID.CS_SetUserLanguageLevelReq_ID,
			[typeof(SC_SetUserLanguageLevelAck)] = (short)GameMSGID.SC_SetUserLanguageLevelAck_ID,
			[typeof(CS_GetUserPortraitsReq)] = (short)GameMSGID.CS_GetUserPortraitsReq_ID,
			[typeof(SC_GetUserPortraitsAck)] = (short)GameMSGID.SC_GetUserPortraitsAck_ID,
			[typeof(CS_SetUserVoiceInformationReq)] = (short)GameMSGID.CS_SetUserVoiceInformationReq_ID,
			[typeof(SC_SetUserVoiceInformationAck)] = (short)GameMSGID.SC_SetUserVoiceInformationAck_ID,
			[typeof(CS_SetUserDressUpReq)] = (short)GameMSGID.CS_SetUserDressUpReq_ID,
			[typeof(SC_SetUserDressUpAck)] = (short)GameMSGID.SC_SetUserDressUpAck_ID,
			[typeof(CS_SetCheckinMilestoneReq)] = (short)GameMSGID.CS_SetCheckinMilestoneReq_ID,
			[typeof(SC_SetCheckinMilestoneAck)] = (short)GameMSGID.SC_SetCheckinMilestoneAck_ID,
			[typeof(CS_GetCheckinSummaryReq)] = (short)GameMSGID.CS_GetCheckinSummaryReq_ID,
			[typeof(SC_GetCheckinSummaryAck)] = (short)GameMSGID.SC_GetCheckinSummaryAck_ID,
			[typeof(CS_GetUserCheckinPortalDataReq)] = (short)GameMSGID.CS_GetUserCheckinPortalDataReq_ID,
			[typeof(SC_GetUserCheckinPortalDataAck)] = (short)GameMSGID.SC_GetUserCheckinPortalDataAck_ID,
			[typeof(CS_DrawRewardReq)] = (short)GameMSGID.CS_DrawRewardReq_ID,
			[typeof(SC_DrawRewardAck)] = (short)GameMSGID.SC_DrawRewardAck_ID,
			[typeof(CS_RecheckinReq)] = (short)GameMSGID.CS_RecheckinReq_ID,
			[typeof(SC_RecheckinAck)] = (short)GameMSGID.SC_RecheckinAck_ID,
			[typeof(CS_RecheckinByDiamondReq)] = (short)GameMSGID.CS_RecheckinByDiamondReq_ID,
			[typeof(SC_RecheckinByDiamondAck)] = (short)GameMSGID.SC_RecheckinByDiamondAck_ID,
			[typeof(CS_RecheckinByTaskReq)] = (short)GameMSGID.CS_RecheckinByTaskReq_ID,
			[typeof(SC_RecheckinByTaskAck)] = (short)GameMSGID.SC_RecheckinByTaskAck_ID,
			[typeof(CS_GetUserCheckinDataForTaskFinishReq)] = (short)GameMSGID.CS_GetUserCheckinDataForTaskFinishReq_ID,
			[typeof(SC_GetUserCheckinDataForTaskFinishAck)] = (short)GameMSGID.SC_GetUserCheckinDataForTaskFinishAck_ID,
			[typeof(CS_GetUserRankingPortalDataReq)] = (short)GameMSGID.CS_GetUserRankingPortalDataReq_ID,
			[typeof(SC_GetUserRankingPortalDataAck)] = (short)GameMSGID.SC_GetUserRankingPortalDataAck_ID,
			[typeof(CS_GetIncentiveDataForPortalReq)] = (short)GameMSGID.CS_GetIncentiveDataForPortalReq_ID,
			[typeof(SC_GetIncentiveDataForPortalAck)] = (short)GameMSGID.SC_GetIncentiveDataForPortalAck_ID,
			[typeof(CS_JoinRankingReq)] = (short)GameMSGID.CS_JoinRankingReq_ID,
			[typeof(SC_JoinRankingAck)] = (short)GameMSGID.SC_JoinRankingAck_ID,
			[typeof(CS_GetUserCheckinCalendarReq)] = (short)GameMSGID.CS_GetUserCheckinCalendarReq_ID,
			[typeof(SC_GetUserCheckinCalendarAck)] = (short)GameMSGID.SC_GetUserCheckinCalendarAck_ID,
			[typeof(CS_RefuseRecheckinReq)] = (short)GameMSGID.CS_RefuseRecheckinReq_ID,
			[typeof(SC_RefuseRecheckinAck)] = (short)GameMSGID.SC_RefuseRecheckinAck_ID,
			[typeof(CS_GetUserDressUpMerchandiseDataReq)] = (short)GameMSGID.CS_GetUserDressUpMerchandiseDataReq_ID,
			[typeof(SC_GetUserDressUpMerchandiseDataAck)] = (short)GameMSGID.SC_GetUserDressUpMerchandiseDataAck_ID,
			[typeof(CS_GetGrowthWeeklyGiftReq)] = (short)GameMSGID.CS_GetGrowthWeeklyGiftReq_ID,
			[typeof(SC_GetGrowthWeeklyGiftAck)] = (short)GameMSGID.SC_GetGrowthWeeklyGiftAck_ID,
			[typeof(CS_SetRankingChangeClickReq)] = (short)GameMSGID.CS_SetRankingChangeClickReq_ID,
			[typeof(SC_SetRankingChangeClickAck)] = (short)GameMSGID.SC_SetRankingChangeClickAck_ID,
			[typeof(CS_SetUserHeadItemReq)] = (short)GameMSGID.CS_SetUserHeadItemReq_ID,
			[typeof(SC_SetUserHeadItemAck)] = (short)GameMSGID.SC_SetUserHeadItemAck_ID,
			[typeof(CS_GetUserFriendListReq)] = (short)GameMSGID.CS_GetUserFriendListReq_ID,
			[typeof(SC_GetUserFriendListAck)] = (short)GameMSGID.SC_GetUserFriendListAck_ID,
			[typeof(CS_AddFriendReq)] = (short)GameMSGID.CS_AddFriendReq_ID,
			[typeof(SC_AddFriendAck)] = (short)GameMSGID.SC_AddFriendAck_ID,
			[typeof(CS_RemoveFriendReq)] = (short)GameMSGID.CS_RemoveFriendReq_ID,
			[typeof(SC_RemoveFriendAck)] = (short)GameMSGID.SC_RemoveFriendAck_ID,
			[typeof(CS_GetRecommendedFriendListReq)] = (short)GameMSGID.CS_GetRecommendedFriendListReq_ID,
			[typeof(SC_GetRecommendedFriendListAck)] = (short)GameMSGID.SC_GetRecommendedFriendListAck_ID,
			[typeof(CS_SearchUserReq)] = (short)GameMSGID.CS_SearchUserReq_ID,
			[typeof(SC_SearchUserAck)] = (short)GameMSGID.SC_SearchUserAck_ID,
			[typeof(CS_SendGiftForFriendReq)] = (short)GameMSGID.CS_SendGiftForFriendReq_ID,
			[typeof(SC_SendGiftForFriendAck)] = (short)GameMSGID.SC_SendGiftForFriendAck_ID,
			[typeof(CS_GetUserGiftPortalDataReq)] = (short)GameMSGID.CS_GetUserGiftPortalDataReq_ID,
			[typeof(SC_GetUserGiftPortalDataAck)] = (short)GameMSGID.SC_GetUserGiftPortalDataAck_ID,
			[typeof(CS_MatchFriendShipTaskReq)] = (short)GameMSGID.CS_MatchFriendShipTaskReq_ID,
			[typeof(SC_MatchFriendShipTaskAck)] = (short)GameMSGID.SC_MatchFriendShipTaskAck_ID,
			[typeof(CS_FriendShipNotifyReq)] = (short)GameMSGID.CS_FriendShipNotifyReq_ID,
			[typeof(SC_FriendShipNotifyAck)] = (short)GameMSGID.SC_FriendShipNotifyAck_ID,
			[typeof(CS_SetShowStateReq)] = (short)GameMSGID.CS_SetShowStateReq_ID,
			[typeof(SC_SetShowStateAck)] = (short)GameMSGID.SC_SetShowStateAck_ID,
			[typeof(CS_GetFriendStreakRecommendListReq)] = (short)GameMSGID.CS_GetFriendStreakRecommendListReq_ID,
			[typeof(SC_GetFriendStreakRecommendListAck)] = (short)GameMSGID.SC_GetFriendStreakRecommendListAck_ID,
			[typeof(CS_UpdateFriendStreakRelationReq)] = (short)GameMSGID.CS_UpdateFriendStreakRelationReq_ID,
			[typeof(SC_UpdateFriendStreakRelationAck)] = (short)GameMSGID.SC_UpdateFriendStreakRelationAck_ID,
			[typeof(CS_GetFriendStreakPortalReq)] = (short)GameMSGID.CS_GetFriendStreakPortalReq_ID,
			[typeof(SC_GetFriendStreakPortalAck)] = (short)GameMSGID.SC_GetFriendStreakPortalAck_ID,
			[typeof(CS_GetIncentiveDataForExploreReq)] = (short)GameMSGID.CS_GetIncentiveDataForExploreReq_ID,
			[typeof(SC_GetIncentiveDataForExploreAck)] = (short)GameMSGID.SC_GetIncentiveDataForExploreAck_ID,
			[typeof(CS_StartConsumeReq)] = (short)GameMSGID.CS_StartConsumeReq_ID,
			[typeof(SC_StartConsumeAck)] = (short)GameMSGID.SC_StartConsumeAck_ID,
			[typeof(CS_GetUserVoiceCloneAudioReq)] = (short)GameMSGID.CS_GetUserVoiceCloneAudioReq_ID,
			[typeof(SC_GetUserVoiceCloneAudioAck)] = (short)GameMSGID.SC_GetUserVoiceCloneAudioAck_ID,
			[typeof(CS_ReportInvalidUserAudioReq)] = (short)GameMSGID.CS_ReportInvalidUserAudioReq_ID,
			[typeof(SC_ReportInvalidUserAudioAck)] = (short)GameMSGID.SC_ReportInvalidUserAudioAck_ID,
			[typeof(SC_EconomicCoinSettlementPush)] = (short)GameMSGID.SC_EconomicCoinSettlementPush_ID,
			[typeof(CS_EconomicCoinGetBalanceReq)] = (short)GameMSGID.CS_EconomicCoinGetBalanceReq_ID,
			[typeof(SC_EconomicCoinGetBalanceAck)] = (short)GameMSGID.SC_EconomicCoinGetBalanceAck_ID,
			[typeof(CS_CreateOrderReq)] = (short)GameMSGID.CS_CreateOrderReq_ID,
			[typeof(SC_CreateOrderAck)] = (short)GameMSGID.SC_CreateOrderAck_ID,
			[typeof(CS_CancelOrderReq)] = (short)GameMSGID.CS_CancelOrderReq_ID,
			[typeof(SC_CancelOrderAck)] = (short)GameMSGID.SC_CancelOrderAck_ID,
			[typeof(CS_GetUnpaidOrderReq)] = (short)GameMSGID.CS_GetUnpaidOrderReq_ID,
			[typeof(SC_GetUnpaidOrderAck)] = (short)GameMSGID.SC_GetUnpaidOrderAck_ID,
			[typeof(CS_VerifyReceiptDataReq)] = (short)GameMSGID.CS_VerifyReceiptDataReq_ID,
			[typeof(SC_VerifyReceiptDataResp)] = (short)GameMSGID.SC_VerifyReceiptDataResp_ID,
			[typeof(SC_UserRewardItem)] = (short)GameMSGID.SC_UserRewardItem_ID,
			[typeof(CS_GetShopInfoReq)] = (short)GameMSGID.CS_GetShopInfoReq_ID,
			[typeof(SC_GetShopInfoResp)] = (short)GameMSGID.SC_GetShopInfoResp_ID,
			[typeof(CS_PurchaseStaminaReq)] = (short)GameMSGID.CS_PurchaseStaminaReq_ID,
			[typeof(SC_PurchaseStaminaAck)] = (short)GameMSGID.SC_PurchaseStaminaAck_ID,
			[typeof(CS_PayMerchandiseReq)] = (short)GameMSGID.CS_PayMerchandiseReq_ID,
			[typeof(SC_PayMerchandiseResp)] = (short)GameMSGID.SC_PayMerchandiseResp_ID,
			[typeof(CS_GetAllShopMerchandiseReq)] = (short)GameMSGID.CS_GetAllShopMerchandiseReq_ID,
			[typeof(SC_GetAllShopMerchandiseResp)] = (short)GameMSGID.SC_GetAllShopMerchandiseResp_ID,
			[typeof(CS_ClickWordReq)] = (short)GameMSGID.CS_ClickWordReq_ID,
			[typeof(SC_ClickWordAck)] = (short)GameMSGID.SC_ClickWordAck_ID,
			[typeof(CS_GetUserCheckinDataReq)] = (short)GameMSGID.CS_GetUserCheckinDataReq_ID,
			[typeof(SC_GetUserCheckinDataAck)] = (short)GameMSGID.SC_GetUserCheckinDataAck_ID,
			[typeof(CS_DrawCheckinRewardReq)] = (short)GameMSGID.CS_DrawCheckinRewardReq_ID,
			[typeof(SC_DrawCheckinRewardAck)] = (short)GameMSGID.SC_DrawCheckinRewardAck_ID,
			[typeof(CS_RegisterUserDeviceReq)] = (short)GameMSGID.CS_RegisterUserDeviceReq_ID,
			[typeof(SC_RegisterUserDeviceAck)] = (short)GameMSGID.SC_RegisterUserDeviceAck_ID,
			[typeof(CS_GetSystemNoticeReq)] = (short)GameMSGID.CS_GetSystemNoticeReq_ID,
			[typeof(SC_GetSystemNoticeAck)] = (short)GameMSGID.SC_GetSystemNoticeAck_ID,
			[typeof(CS_RedeemCouponReq)] = (short)GameMSGID.CS_RedeemCouponReq_ID,
			[typeof(SC_RedeemCouponAck)] = (short)GameMSGID.SC_RedeemCouponAck_ID,
			[typeof(CS_GetSingleChatDialogReq)] = (short)GameMSGID.CS_GetSingleChatDialogReq_ID,
			[typeof(SC_GetSingleChatDialogAck)] = (short)GameMSGID.SC_GetSingleChatDialogAck_ID,
			[typeof(CS_QueryChatListReq)] = (short)GameMSGID.CS_QueryChatListReq_ID,
			[typeof(SC_QueryChatListAck)] = (short)GameMSGID.SC_QueryChatListAck_ID,
			[typeof(CS_QueryChatMessageListReq)] = (short)GameMSGID.CS_QueryChatMessageListReq_ID,
			[typeof(SC_QueryChatMessageListAck)] = (short)GameMSGID.SC_QueryChatMessageListAck_ID,
			[typeof(CS_SendChatMessageReq)] = (short)GameMSGID.CS_SendChatMessageReq_ID,
			[typeof(SC_SendChatMessageAck)] = (short)GameMSGID.SC_SendChatMessageAck_ID,
			[typeof(SC_ChatMessageNtf)] = (short)GameMSGID.SC_ChatMessageNtf_ID,
			[typeof(CS_UpdateChatIndexReq)] = (short)GameMSGID.CS_UpdateChatIndexReq_ID,
			[typeof(SC_UpdateChatIndexAck)] = (short)GameMSGID.SC_UpdateChatIndexAck_ID,
			[typeof(CS_QueryChatUserListReq)] = (short)GameMSGID.CS_QueryChatUserListReq_ID,
			[typeof(SC_QueryChatUserListAck)] = (short)GameMSGID.SC_QueryChatUserListAck_ID,
			
			
			
			
			[typeof(CS_ChatScaffoldReq)] = (short)GameMSGID.CS_ChatScaffoldReq_ID,
			[typeof(SC_ChatScaffoldAck)] = (short)GameMSGID.SC_ChatScaffoldAck_ID,
			[typeof(CS_GenTestSingleChatReq)] = (short)GameMSGID.CS_GenTestSingleChatReq_ID,
			[typeof(SC_GenTestSingleChatAck)] = (short)GameMSGID.SC_GenTestSingleChatAck_ID,
			[typeof(CS_QueryUnReadMessageNumReq)] = (short)GameMSGID.CS_QueryUnReadMessageNumReq_ID,
			[typeof(SC_QueryUnReadMessageNumAck)] = (short)GameMSGID.SC_QueryUnReadMessageNumAck_ID,
			[typeof(CS_ChatRecordStatusSyncReq)] = (short)GameMSGID.CS_ChatRecordStatusSyncReq_ID,
			[typeof(SC_ChatRecordStatusSyncAck)] = (short)GameMSGID.SC_ChatRecordStatusSyncAck_ID,
			[typeof(SC_ChatRecordStatusNtf)] = (short)GameMSGID.SC_ChatRecordStatusNtf_ID,
			[typeof(CS_GetBuildingTopicListReq)] = (short)GameMSGID.CS_GetBuildingTopicListReq_ID,
			[typeof(SC_GetBuildingTopicListAck)] = (short)GameMSGID.SC_GetBuildingTopicListAck_ID,
			[typeof(CS_GetAreaAchievementListReq)] = (short)GameMSGID.CS_GetAreaAchievementListReq_ID,
			[typeof(SC_GetAreaAchievementListAck)] = (short)GameMSGID.SC_GetAreaAchievementListAck_ID,
			[typeof(CS_CreateWorldStoryTaskReq)] = (short)GameMSGID.CS_CreateWorldStoryTaskReq_ID,
			[typeof(SC_CreateWorldStoryTaskAck)] = (short)GameMSGID.SC_CreateWorldStoryTaskAck_ID,
			[typeof(CS_StartWorldDialogReq)] = (short)GameMSGID.CS_StartWorldDialogReq_ID,
			[typeof(SC_StartWorldDialogAck)] = (short)GameMSGID.SC_StartWorldDialogAck_ID,
			[typeof(CS_FinishWorldStoryTaskReq)] = (short)GameMSGID.CS_FinishWorldStoryTaskReq_ID,
			[typeof(SC_FinishWorldStoryTaskAck)] = (short)GameMSGID.SC_FinishWorldStoryTaskAck_ID,
			[typeof(SC_UserDialogContentNtf)] = (short)GameMSGID.SC_UserDialogContentNtf_ID,
			[typeof(SC_AvatarDialogContentNtf)] = (short)GameMSGID.SC_AvatarDialogContentNtf_ID,
			[typeof(SC_AvatarAudioNtf)] = (short)GameMSGID.SC_AvatarAudioNtf_ID,
			[typeof(SC_AvatarDialogTranslateNtf)] = (short)GameMSGID.SC_AvatarDialogTranslateNtf_ID,
			[typeof(SC_AvatarAdviceNtf)] = (short)GameMSGID.SC_AvatarAdviceNtf_ID,
			[typeof(SC_AvatarExampleNtf)] = (short)GameMSGID.SC_AvatarExampleNtf_ID,
			[typeof(SC_WorldStoryProcessNtf)] = (short)GameMSGID.SC_WorldStoryProcessNtf_ID,
			[typeof(CS_DialogSettingReq)] = (short)GameMSGID.CS_DialogSettingReq_ID,
			[typeof(CS_GetWorldRoleInfoReq)] = (short)GameMSGID.CS_GetWorldRoleInfoReq_ID,
			[typeof(SC_GetWorldRoleInfoAck)] = (short)GameMSGID.SC_GetWorldRoleInfoAck_ID,
			[typeof(CS_CreateWorldRoleReq)] = (short)GameMSGID.CS_CreateWorldRoleReq_ID,
			[typeof(SC_CreateWorldRoleAck)] = (short)GameMSGID.SC_CreateWorldRoleAck_ID,
			[typeof(CS_SendVerificationCodeReq)] = (short)GameMSGID.CS_SendVerificationCodeReq_ID,
			[typeof(SC_SendVerificationCodeResp)] = (short)GameMSGID.SC_SendVerificationCodeResp_ID,
			[typeof(CS_GetAbTestResultReq)] = (short)GameMSGID.CS_GetAbTestResultReq_ID,
			[typeof(SC_GetAbTestResultAck)] = (short)GameMSGID.SC_GetAbTestResultAck_ID,
			[typeof(CS_GetRecommendListReq)] = (short)GameMSGID.CS_GetRecommendListReq_ID,
			[typeof(SC_GetRecommendListResp)] = (short)GameMSGID.SC_GetRecommendListResp_ID,
			[typeof(SC_RecommendSwitchEntity)] = (short)GameMSGID.SC_RecommendSwitchEntity_ID,
			[typeof(CS_GetRecommendListV2Req)] = (short)GameMSGID.CS_GetRecommendListV2Req_ID,
			[typeof(SC_GetRecommendListV2Resp)] = (short)GameMSGID.SC_GetRecommendListV2Resp_ID,
			[typeof(CS_GetUserHistoryProgressListReq)] = (short)GameMSGID.CS_GetUserHistoryProgressListReq_ID,
			[typeof(SC_GetUserHistoryProgressListResp)] = (short)GameMSGID.SC_GetUserHistoryProgressListResp_ID,
			[typeof(CS_ExploreUpMsg)] = (short)GameMSGID.CS_ExploreUpMsg_ID,
			[typeof(SC_ExploreDownMsg)] = (short)GameMSGID.SC_ExploreDownMsg_ID,
			[typeof(SC_ExploreDownMsgForServerBasic)] = (short)GameMSGID.SC_ExploreDownMsgForServerBasic_ID,
			[typeof(SC_ExploreDownMsgForHeartbeat)] = (short)GameMSGID.SC_ExploreDownMsgForHeartbeat_ID,
			[typeof(SC_DialogDownMsgForAvatarReply)] = (short)GameMSGID.SC_DialogDownMsgForAvatarReply_ID,
			[typeof(SC_DialogDownMsgForAvatarReplyTranslate)] = (short)GameMSGID.SC_DialogDownMsgForAvatarReplyTranslate_ID,
			[typeof(SC_DialogDownMsgForAvatarReplyTTS)] = (short)GameMSGID.SC_DialogDownMsgForAvatarReplyTTS_ID,
			[typeof(SC_DialogDownMsgForUserReplyExample)] = (short)GameMSGID.SC_DialogDownMsgForUserReplyExample_ID,
			[typeof(SC_DialogDownMsgForUserReplyExampleTranslate)] = (short)GameMSGID.SC_DialogDownMsgForUserReplyExampleTranslate_ID,
			[typeof(SC_DialogDownMsgForUserReplyExampleTTS)] = (short)GameMSGID.SC_DialogDownMsgForUserReplyExampleTTS_ID,
			[typeof(SC_DialogDownMsgForASR)] = (short)GameMSGID.SC_DialogDownMsgForASR_ID,
			[typeof(SC_DialogDownMsgForBizEvent)] = (short)GameMSGID.SC_DialogDownMsgForBizEvent_ID,
			[typeof(SC_DialogDownMsgForFeedback)] = (short)GameMSGID.SC_DialogDownMsgForFeedback_ID,
			[typeof(SC_DialogDownMsgForTaskGoalStatusChange)] = (short)GameMSGID.SC_DialogDownMsgForTaskGoalStatusChange_ID,
			[typeof(SC_DialogDownMsgForAdvice)] = (short)GameMSGID.SC_DialogDownMsgForAdvice_ID,
			[typeof(SC_UserSettingDownMsgForSaveUserSettings)] = (short)GameMSGID.SC_UserSettingDownMsgForSaveUserSettings_ID,
			[typeof(CS_UpdateAppsflyerCallbackDataReq)] = (short)GameMSGID.CS_UpdateAppsflyerCallbackDataReq_ID,
			[typeof(SC_UpdateAppsflyerCallbackDataResp)] = (short)GameMSGID.SC_UpdateAppsflyerCallbackDataResp_ID,
			[typeof(CS_GetHomepageGuideItemReq)] = (short)GameMSGID.CS_GetHomepageGuideItemReq_ID,
			[typeof(SC_GetHomepageGuideItemResp)] = (short)GameMSGID.SC_GetHomepageGuideItemResp_ID,
			[typeof(SC_UserChatDownMsgForUserRecognizing)] = (short)GameMSGID.SC_UserChatDownMsgForUserRecognizing_ID,
			[typeof(SC_UserChatDownMsgForUserRecognized)] = (short)GameMSGID.SC_UserChatDownMsgForUserRecognized_ID,
			[typeof(SC_UserChatDownMsgForOtherUserRecognizing)] = (short)GameMSGID.SC_UserChatDownMsgForOtherUserRecognizing_ID,
			[typeof(SC_UserChatDownMsgForOtherUserRecognized)] = (short)GameMSGID.SC_UserChatDownMsgForOtherUserRecognized_ID,
			[typeof(SC_UserChatDownMsgForOtherUserReplyTranslate)] = (short)GameMSGID.SC_UserChatDownMsgForOtherUserReplyTranslate_ID,
			[typeof(SC_UserChatDownMsgForUserReplyExample)] = (short)GameMSGID.SC_UserChatDownMsgForUserReplyExample_ID,
			[typeof(SC_UserChatDownMsgForUserReplyExampleTranslate)] = (short)GameMSGID.SC_UserChatDownMsgForUserReplyExampleTranslate_ID,
			[typeof(SC_UserChatDownMsgForBizEvent)] = (short)GameMSGID.SC_UserChatDownMsgForBizEvent_ID,
			[typeof(SC_MatchingDown_MatchStatus)] = (short)GameMSGID.SC_MatchingDown_MatchStatus_ID,
			[typeof(SC_MatchingDown_MatchedResult)] = (short)GameMSGID.SC_MatchingDown_MatchedResult_ID,
			[typeof(SC_MatchingDown_MatchFailed)] = (short)GameMSGID.SC_MatchingDown_MatchFailed_ID,
			[typeof(SC_NativeSpeakerDown_MatchStatus)] = (short)GameMSGID.SC_NativeSpeakerDown_MatchStatus_ID,
			[typeof(SC_NativeSpeakerDown_MatchedResult)] = (short)GameMSGID.SC_NativeSpeakerDown_MatchedResult_ID,
			[typeof(SC_NativeSpeakerDown_MatchFailed)] = (short)GameMSGID.SC_NativeSpeakerDown_MatchFailed_ID,
			[typeof(SC_OnboardingChatDownMsgForAvatarReply)] = (short)GameMSGID.SC_OnboardingChatDownMsgForAvatarReply_ID,
			[typeof(SC_OnboardingChatDownMsgForAvatarReplyTranslate)] = (short)GameMSGID.SC_OnboardingChatDownMsgForAvatarReplyTranslate_ID,
			[typeof(SC_OnboardingChatDownMsgForAvatarReplyTTS)] = (short)GameMSGID.SC_OnboardingChatDownMsgForAvatarReplyTTS_ID,
			[typeof(SC_OnboardingChatDownMsgForASR)] = (short)GameMSGID.SC_OnboardingChatDownMsgForASR_ID,
			[typeof(SC_OnboardingChatDownMsgForBizEvent)] = (short)GameMSGID.SC_OnboardingChatDownMsgForBizEvent_ID,
			[typeof(SC_OnboardingChatDownMsgForSettlement)] = (short)GameMSGID.SC_OnboardingChatDownMsgForSettlement_ID,
			[typeof(CS_GetOnboardingChatPreloadDataReq)] = (short)GameMSGID.CS_GetOnboardingChatPreloadDataReq_ID,
			[typeof(SC_GetOnboardingChatPreloadDataResp)] = (short)GameMSGID.SC_GetOnboardingChatPreloadDataResp_ID,
			[typeof(SC_SkipOnboardingChat)] = (short)GameMSGID.SC_SkipOnboardingChat_ID,
			[typeof(SC_MissionStoryChatDownMsgForAvatarReply)] = (short)GameMSGID.SC_MissionStoryChatDownMsgForAvatarReply_ID,
			[typeof(SC_MissionStoryChatDownMsgForAvatarReplyTranslate)] = (short)GameMSGID.SC_MissionStoryChatDownMsgForAvatarReplyTranslate_ID,
			[typeof(SC_MissionStoryChatDownMsgForAvatarReplyTTS)] = (short)GameMSGID.SC_MissionStoryChatDownMsgForAvatarReplyTTS_ID,
			[typeof(SC_MissionStoryChatDownMsgForUserReplyExample)] = (short)GameMSGID.SC_MissionStoryChatDownMsgForUserReplyExample_ID,
			[typeof(SC_MissionStoryChatDownMsgForUserReplyExampleTranslate)] = (short)GameMSGID.SC_MissionStoryChatDownMsgForUserReplyExampleTranslate_ID,
			[typeof(SC_MissionStoryChatDownMsgForUserReplyExampleTTS)] = (short)GameMSGID.SC_MissionStoryChatDownMsgForUserReplyExampleTTS_ID,
			[typeof(SC_MissionStoryChatDownMsgForAdvice)] = (short)GameMSGID.SC_MissionStoryChatDownMsgForAdvice_ID,
			[typeof(SC_MissionStoryChatDownMsgForASR)] = (short)GameMSGID.SC_MissionStoryChatDownMsgForASR_ID,
			[typeof(SC_MissionStoryChatDownMsgForBizEvent)] = (short)GameMSGID.SC_MissionStoryChatDownMsgForBizEvent_ID,
			[typeof(SC_MissionStoryChatDownMsgForStepProgressChange)] = (short)GameMSGID.SC_MissionStoryChatDownMsgForStepProgressChange_ID,
			[typeof(SC_MissionStoryChatDownMsgForSettlement)] = (short)GameMSGID.SC_MissionStoryChatDownMsgForSettlement_ID,
		};
	}
}