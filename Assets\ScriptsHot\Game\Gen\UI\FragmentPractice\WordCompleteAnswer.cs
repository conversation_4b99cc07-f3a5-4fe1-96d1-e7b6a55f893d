/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class WordCompleteAnswer : AFragAnswer
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "WordCompleteAnswer";
        public static string url => "ui://cmoz5osjcki6uvptd4";

        public GTextField tfPrompt;
        public GTextInput tfInput;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(WordCompleteAnswer));
        }

        public override void ConstructFromXML(XML xml)
        {
            tfPrompt = GetChildAt(1) as GTextField;
            tfInput = GetChildAt(2) as GTextInput;

            SetMultiLanguageInChildren();
        }
        public override void Dispose()
        {
            tfPrompt = null;
            tfInput = null;

            base.Dispose();
        }
        public void SetMultiLanguageInChildren()
        {
            this.tfPrompt.SetKey("word_complete_input_prompt");  // "Use English input"
        }
    }
}