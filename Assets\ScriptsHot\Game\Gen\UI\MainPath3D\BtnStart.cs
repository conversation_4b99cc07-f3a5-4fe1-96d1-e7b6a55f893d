/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.MainPath3D
{
    public partial class BtnStart : UIBindT
    {
        public override string pkgName => "MainPath3D";
        public override string comName => "BtnStart";

        public Controller isUnlimited;
        public GTextField tfStart;
        public GTextField tfCost;
        public GGroup grpExp;
        public GGroup grpRight;
        public GGroup grp;
        public Transition t0;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            isUnlimited = com.GetControllerAt(0);
            tfStart = (GTextField)com.GetChildAt(1);
            tfCost = (GTextField)com.GetChildAt(4);
            grpExp = (GGroup)com.GetChildAt(5);
            grpRight = (GGroup)com.GetChildAt(6);
            grp = (GGroup)com.GetChildAt(7);
            t0 = com.GetTransitionAt(0);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            isUnlimited = null;
            tfStart = null;
            tfCost = null;
            grpExp = null;
            grpRight = null;
            grp = null;
            t0 = null;
        }
    }
}