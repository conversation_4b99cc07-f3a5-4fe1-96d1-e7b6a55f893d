/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.MainPath3D
{
    public partial class CompFeature : UIBindT
    {
        public override string pkgName => "MainPath3D";
        public override string comName => "CompFeature";

        public Controller type;
        public GTextField title;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            type = com.GetControllerAt(0);
            title = (GTextField)com.GetChildAt(2);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            type = null;
            title = null;
        }
    }
}