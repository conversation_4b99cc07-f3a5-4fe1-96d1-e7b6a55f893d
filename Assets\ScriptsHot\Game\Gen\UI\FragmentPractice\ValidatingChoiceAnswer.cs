/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class ValidatingChoiceAnswer : AFragAnswer
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "ValidatingChoiceAnswer";
        public static string url => "ui://cmoz5osjti2auvptcr";

        public Controller title;
        public GTextField tfQuest;
        public GList listAnswers;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(ValidatingChoiceAnswer));
        }

        public override void ConstructFromXML(XML xml)
        {
            title = GetControllerAt(0);
            tfQuest = GetChildAt(2) as GTextField;
            listAnswers = GetChildAt(3) as GList;
        }
        public override void Dispose()
        {
            title = null;
            tfQuest = null;
            listAnswers = null;

            base.Dispose();
        }
    }
}