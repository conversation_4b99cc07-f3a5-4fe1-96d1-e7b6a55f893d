using Msg.basic;
using Msg.task_process;
using ScriptsHot.Game.Modules.Settlement;

using UnityEngine;

public class ChatStateSettlement : BaseChatMachineState
{
    private SettlementController Ctr => owner.GetController<SettlementController>(ModelConsts.Settlement);
    public ChatStateSettlement() : base(ChatState.Settlement)
    {
    }

    public override void OnEnter(params object[] args)
    {
        base.OnEnter(args);
        if (owner.GetModel<ChatModel>(ModelConsts.Chat).chatMode == PB_DialogMode.OnBoarding)
        {
            owner.GetUI(UIConsts.LoginTaskEnd).Show();
        }
        else
        {
            Ctr.DealResultData();
            Ctr.ShowNextView();
        }
        // else if (owner.GetModel<ChatModel>(ModelConsts.Chat).chatMode == PB_DialogMode.Tutor)
        // {
        //     ctr.DealCommonResultInfo();//生产界面队列
        //     ctr.ShowTaskReward();
        // }
        // else
        // {
        //     ctr.DealRewardInfo();//生产界面队列
        //     ctr.ShowTaskReward();
        // }
    }

    public override void OnExit(string nextState)
    {
        base.OnExit(nextState);
        //结算的时候请求每日强化练习
        // this.owner.GetUI<MainHeaderUI>(UIConsts.MainHeader).SetVisible(true, true);
        if (owner.GetModel<ChatModel>(ModelConsts.Chat).chatMode == PB_DialogMode.Intensify)
        {
            //请求每日强化题型数据
            CS_GetDailyTaskInfoReq data = new CS_GetDailyTaskInfoReq(); 
            MsgManager.instance.SendMsg(data);
            Debug.LogError("强化做完 请求题型数据");
        }
        
    }
    
    public override bool OnHandleMsg(MsgData msg)
    {
        DealHandleMsgData(msg);
        return false;
    }


    private void DealHandleMsgData(MsgData msg)
    {
    }
    
    public override void OnHandleError(params object[] args)
    {
        base.OnHandleError();

    }
}