﻿

using ScriptsHot.Game.Modules.ExploreFriends.State.Base;
using ScriptsHot.Game.Modules.ExploreFriends.UI.Alert;
using UIBind.ExploreFriends;
using UnityEngine;

public partial class ExploreFriendsController:BaseController
{
    public ExploreFriendsModel Model => GetModel<ExploreFriendsModel>(ModelConsts.ExploreFriends);
    
    private ExploreFriendsStateMachine _stateMachine;
    
    private bool _enter = false;
    private bool _exit = false;
    public bool IsExit => _exit;

    private bool IsPlayAvatarAudio = false;
    
    private long _enterChatAvatarId = 0;
    public long EnterChatAvatarId => _enterChatAvatarId;
    public ExploreFriendsController() : base(ModelConsts.ExploreFriends) 
    {
        InitStateMachine();
    }
    public override void OnInit()
    {
        ExploreFriendsModel m = new ExploreFriendsModel();
        m.OnInit();
        RegisterModel(m);
        AddEvent();
        InitRegister();
    }

    public override void OnUIInit()
    {
        base.OnUIInit();
        RegisterUI(new ExploreFriendsNewHandPanelUI(UIConsts.ExploreFriendsNewHandPanel));
        RegisterUI(new ExploreFriendsIntroducePanelUI(UIConsts.ExploreFriendsIntroducePanel));
        RegisterUI(new ExploreFriendsDrawCardsResultPanelUI(UIConsts.ExploreFriendsDrawCardsResultPanel));   
        RegisterUI(new ExploreFriendsChatPanelUI(UIConsts.ExploreFriendsChatPanel));
        RegisterUI(new ExploreFriendsBagPanelUI(UIConsts.ExploreFriendsBagPanel)); 
        
        RegisterUI(new ExploreFriendsClosenessAlertUI(UIConsts.ExploreFriendsClosenessAlert)); 
    }

    public override void OnEnterGame()
    {
        SendInitRequest();
    }

    private void InitStateMachine()
    {
        _stateMachine = new ExploreFriendsStateMachine(this);
        _stateMachine.AddState(new ExploreFriendsStateStart(this));
        _stateMachine.AddState(new ExploreFriendsStateExit(this));
        _stateMachine.AddState(new ExploreFriendsStateNormal(this));
    }
        
    public override void OnUpdate(int interval)
    {
        base.OnUpdate(interval);
        _stateMachine?.Update(interval);
        UpdateASR(interval);
        
        // 检查 Avatar 音频播放状态
        if (IsPlayAvatarAudio)
        {
            AudioSource audioSource = GSoundManager.instance.CurAvatarAudioSource;
            // 增加 null 检查
            if (audioSource == null || audioSource.clip == null)
            {
                VFDebug.Log($"Avatar 音频源无效，重置播放状态 ");
                IsPlayAvatarAudio = false;
                return;
            }
                
            // 检查是否停止播放 或 接近音频结束
            if (!audioSource.isPlaying && audioSource.time > 0 || 
                (audioSource.time > 0 && audioSource.time >= audioSource.clip.length - 0.1f))
            {
                VFDebug.Log($"Avatar TTS音频播放完毕 - time: {audioSource.time}, length: {audioSource.clip.length}");
                IsPlayAvatarAudio = false;
                Notifier.instance.SendNotification(NotifyConsts.ExploreFriend_AvatarAudioPlayOver);
            }
        }
    }
    
    private void AddEvent()
    {
        RemoveEvent();
        Notifier.instance.RegisterNotification(NotifyConsts.ExploreFriendsPlayTimeLine,OnPlayTimeLine);
    }

    private void RemoveEvent()
    {
        Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreFriendsPlayTimeLine,OnPlayTimeLine);
    }
    
    public override void OnApplicationQuit()
    {
        base.OnApplicationQuit();
        RemoveEvent();
        UnInitRegister();
    }

    #region 事件

    private void OnPlayTimeLine(string s, object body)
    {
        long avatarId = (long) body;
        ExploreFriendsDrawCards.Instance.PlayTimeline(avatarId);
    }

    #endregion

    #region 接口

    public void ChangeState(string stateName,params object[] args)
    {
        _stateMachine.ChangeState(stateName,args);
    }

    public ExploreFriendsStateBase GetCurState()
    {
        return this._stateMachine.currState != null? this._stateMachine.currState as ExploreFriendsStateBase: null;
    }

    
    public void EnterTalk(long avatarId)
    {
        // Debug.LogError("OnBoardFlowController::EnterTalk");
        _enter = true;
        _exit = false;
        _enterChatAvatarId = avatarId;
        //test 
        _enterChatAvatarId = Model.AllInfo.preloadData[0].avatarId;
        //test end
        StartASRReqTask();
        
        Notifier.instance.SendNotification(NotifyConsts.OpenUI,UIConsts.ExploreFriendsChatPanel);
    }
    
    public void Exit()
    {
        _enter = false;
        _exit = true;
    }

    private ExploreController _exploreController;
    private ExploreController GetExploreController()
    {
        if (_exploreController == null)
        {
            _exploreController = ControllerManager.instance.GetController(ModelConsts.Explore) as ExploreController;
        }

        return _exploreController;
    }
    
        
    public void StopStreamAudioTTs()
    {
        GSoundManager.instance.StopAvatarTTS();
        TTSManager.instance.StopTTS();
        ModelDialogManager?.EnterDialogueIdleState();
    }
    #endregion
}
