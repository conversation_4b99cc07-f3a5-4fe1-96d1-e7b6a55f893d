using UIBind.Main;
using UnityEngine;
using DG.Tweening;
using FairyGUI;
using System;
using Msg.basic;
using ScriptsHot.Game.Modules.DrillHub;
using Game.Core.RedDot;
using Modules.DataDot;
using ScriptsHot.Game.Modules.Shop;
using UIBind.MainHead;
using UIBind.MainPath3D;

public struct ChangeChatAvatarParam
{
    public long avatarID;
    public long taskID;
    public PB_DialogMode mode;
    public string bgTag;//实际充当了背景图名称
}

/*
 * 核心1 仅关心两侧siderBar 以及下方 原enterUI以及 practiceBtn的逻辑
 * 核心2 不要和
 * 
 */

public class CenterHomeUI: BaseUI<HomepagePanel>, IBaseUIUpdate
{
    public override void OnBackBtnClick()
    {
    }

    public override string uiLayer => UILayerConsts.HomePage;//主UI层

    protected override bool isFullScreen => true;
    // private LearnPathModel learnPathModel = ModelManager.instance.GetModel<LearnPathModel>(ModelConsts.LearnPath);
    private MainModel mainModel => GetModel<MainModel>(ModelConsts.Main);
    // private LearnPathController learnPathController = ControllerManager.instance.GetController<LearnPathController>(ModelConsts.LearnPath);

    private MainController _mainController => GetController<MainController>(ModelConsts.Main);
    private ShopModel _shopModel => GetModel<ShopModel>(ModelConsts.Shop);
    private HomepageController _homepageController => GetController<HomepageController>(ModelConsts.Homepage);
    private long _lastUpdateStaminaTime;
    private long _lastUpdateTime;

    private MainPath3DPanel _mainPath;
    private MainHeadPanel _mainHead;

    public CenterHomeUI(string name) : base(name) { }

    public CenterHomeUI(string name, GObject obj) : base(name)
    {
        this.ui = new HomepagePanel();
        this.uiCom =  obj.asCom;
        Debug.Log("comName=" + this.uiCom.name + ",goName=" + this.uiCom.gameObjectName);
        this.ui.Construct(this.uiCom);
    }

    private const float MainListShowDuration = 0.25f;
    private const float MainListShowInterval = 0.067f;
    private const float MainListShowDeltaX = 150f;
    private const Ease MainListShowEase = Ease.OutBack;

    public bool IsFeatureButtonListVisible { get; private set; } = false;
    protected override void OnInit(GComponent uiCom)
    {
        ui.homepageBanner.OnInit();
        _mainPath = new MainPath3DPanel();
        _mainPath.Construct(ui.mainPath);
        _mainPath.OnInit(this);

        _mainHead = new MainHeadPanel();
        _mainHead.Construct(ui.headBar);
        _mainHead.OnInit(this);

        RebuildMainButtons();
        FixFont();

        AddUIEvent(ui.homepageTimeBanner.btnEnter.onClick, OnClickPayWallEnter);
        AddUIEvent(ui.homepageTimeBanner.btnHide.onClick, OnClickPayWallHide);
        AddUIEvent(ui.homepageTimeBanner.btnShow.onClick, OnClickPayWallShow);
    }

    protected override void OnShow()
    {
        base.OnShow();
        _mainPath.OnShow(ui.btnToTop);
        
        //header部分
        _mainHead.OnShow();

        _mainController.RequsetUnReadMessageNum();
        try
        {
            //Todo0
            _ = GetController<CurrencyController>(ModelConsts.CurrencyController)
                .SendGetEconomicInfoReqAsync(GameEventName.GameEnter,
                    () => SendNotification(NotifyConsts.MainHeadRefreshEvent));
        }
        catch (Exception e)
        {
            VFDebug.LogError(e.ToString());
        }



        RedDotManager.Instance.LoadAllDots();
        
        DotAppearHomePageChatIcon dot = new DotAppearHomePageChatIcon();
        DataDotMgr.Collect(dot);
    }

    private void FixFont() {
        if (_mainPath.mainListLeft.numChildren > 0)
        {
            // 修改FairyGUI Streak类型字体. 
            var textField = _mainPath.mainListLeft.GetChildAt(0).asCom.GetChild("title").asTextField;
            var textFormat = textField.textFormat;
            textFormat.font = FontCfg.DinNextBold;// "D-DIN-Bold";
            textField.textFormat = textFormat;
        }
    }
    
    private void RebuildMainButtons()
    {
        _mainController.TryGetCacheLaunchOptions();
    }
    
    public void ShowFeatureButtonList(float startDelay = 0f, float interval = MainListShowInterval, float duration = MainListShowDuration, float deltaX = MainListShowDeltaX, Ease ease = MainListShowEase, bool useAnimation = true, bool ignoreVisibleCheck = false)
    {
        if(IsFeatureButtonListVisible && !ignoreVisibleCheck) return;
        if(useAnimation)
        {
            Debug.LogError("useAnim");
            //Left list
            for(int i = 0; i < _mainPath.mainListLeft.numItems; i++)
            {
                var item = _mainPath.mainListLeft.GetChildAt(i).asCom;
                item.alpha = 0.25f;
                item.x -= deltaX;
                DOTween.To(() => item.alpha, x => item.alpha = x, 1f, duration).SetDelay(startDelay + i * interval);
                DOTween.To(() => item.x, x => item.x = x, 0f, duration).SetDelay(startDelay + i * interval).SetEase(ease)
                .OnComplete(()=> item.touchable = true);
            }
            //Right list
            for(int i = 0; i < _mainPath.mainListRight.numItems; i++)
            {
                var item = _mainPath.mainListRight.GetChildAt(i).asCom;
                item.alpha = 0.25f;
                item.x += deltaX;
                DOTween.To(() => item.alpha, x => item.alpha = x, 1f, duration).SetDelay(startDelay + i * interval);
                DOTween.To(() => item.x, x => item.x = x, 0f, duration).SetDelay(startDelay + i * interval).SetEase(ease)
                .OnComplete(()=> item.touchable = true);
            }
        }
        else
        {
            //Left list
            for(int i = 0; i < _mainPath.mainListLeft.numItems; i++)
            {
                var item = _mainPath.mainListLeft.GetChildAt(i).asCom;
                item.touchable = true;
            }
            //Right list
            _mainPath.mainListRight.visible = true;
            for(int i = 0; i < _mainPath.mainListRight.numItems; i++)
            {
                var item = _mainPath.mainListRight.GetChildAt(i).asCom;
                item.touchable = true;
            }
        }   
        IsFeatureButtonListVisible = true;
    }

    public void HideFeatureButtonList(float startDelay, float interval = MainListShowInterval, float duration = MainListShowDuration, float deltaX = MainListShowDeltaX, Ease ease = MainListShowEase, bool useAnimation = true)
    {
        if(!IsFeatureButtonListVisible) return;
        if(useAnimation)
        {
            //Left list
            for(int i = 0; i < _mainPath.mainListLeft.numItems; i++)
            {
                var item = _mainPath.mainListLeft.GetChildAt(i).asCom;
                item.alpha = 1f;
                item.x = 0f;
                DOTween.To(() => item.alpha, x => item.alpha = x, 0f, duration).SetDelay(startDelay + i * interval);
                DOTween.To(() => item.x, x => item.x = x, -deltaX, duration).SetDelay(startDelay + i * interval).SetEase(ease)
                .OnComplete(()=> item.touchable = false);
            }
            //Right list
            for(int i = 0; i < _mainPath.mainListRight.numItems; i++)
            {
                var item = _mainPath.mainListRight.GetChildAt(i).asCom;
                item.alpha = 1f;
                item.x = 0f;
                DOTween.To(() => item.alpha, x => item.alpha = x, 0f, duration).SetDelay(startDelay + i * interval);
                DOTween.To(() => item.x, x => item.x = x, deltaX, duration).SetDelay(startDelay + i * interval).SetEase(ease)
                .OnComplete(()=> item.touchable = false);
            }
        }
        else
        {
            for(int i = 0; i < _mainPath.mainListLeft.numItems; i++)
            {
                var item = _mainPath.mainListLeft.GetChildAt(i).asCom;
                item.touchable = false;
            }   
            for(int i = 0; i < _mainPath.mainListRight.numItems; i++)
            {
                var item = _mainPath.mainListRight.GetChildAt(i).asCom;
                item.touchable = false;
            }
        }

        IsFeatureButtonListVisible = false;
    }

    public void Update(int interval)
    {
        if (!this.isShow) return;
        _mainPath.Update(interval);
        // 每个小时刷新一次
        var lastUpdateDate = DateTimeOffset.FromUnixTimeSeconds(_lastUpdateStaminaTime / 1000).DateTime;
        if (TimeExt.serverTime.Hour != lastUpdateDate.Hour)
        {
            if (_lastUpdateStaminaTime > 0)
            {
                VFDebug.Log("OnReqGetIncentiveData");
                _homepageController.ReqGetIncentiveData();
            }
            _lastUpdateStaminaTime = TimeExt.serverTimestamp;
        }

        if (TimeExt.serverTimestamp - _lastUpdateTime > 200f)
        {
            if (mainModel.incentiveData == null)
            {
                // 没有incentiveData会产生大问题
                VFDebug.Log("启动进入首页后竟然没有incentiveData？重新请求");
                _homepageController.ReqGetIncentiveData();
            }
            _lastUpdateTime = TimeExt.serverTimestamp;
        }

        long endTimeStamp = _shopModel.PayWallData?.end_milliseconds_in_utc ?? 0;
        if (endTimeStamp > TimeExt.serverTimestamp)
        {
            long curTimeStamp = TimeExt.serverTimestamp;
            long leftSeconds = (endTimeStamp - curTimeStamp) / 1000;
            if (leftSeconds < 0) leftSeconds = 0;
            int minutes = (int)(leftSeconds / 60);
            int seconds = (int)(leftSeconds % 60);
            ui.homepageTimeBanner.tfTime.text = $"{minutes:D2}:{seconds:D2}";
            ui.homepageTimeBanner.tfTimeSmall.text = ui.homepageTimeBanner.tfTime.text;
        }
        else
        {
            ui.homepageTimeBanner.com.visible = false;
        }
    }
    
    public void CheckAutoOpenDrillHub()
    {
        if (mainModel.DrillHubNeedAutoOpen && mainModel.DrillHubAutoShowFlag)
        {
            GetController<DrillHubController>(ModelConsts.DrillHub).EnterDrillHubForJump(mainModel.DrillHubViewType);
            mainModel.SetDrillHubAutoShow(false);
        }
    }

    private Action _enterPayWallAction = null;
    
    private void ShowBanner(Action callback)
    {
        if (ui.homepageTimeBanner.com.visible)
            DataDotMgr.Collect(new DotAppearMonetizationDiscountBottomBar());
        
        _enterPayWallAction = callback;
        ui.homepageTimeBanner.com.visible = true;
        ui.homepageTimeBanner.state.selectedIndex = 1;
        ui.homepageTimeBanner.tfDiscount.text =
            ShopUIUtil.GetSaleOffValue(_shopModel.PayWallData.subscription_infos[0]).ToString();
        ui.homepageTimeBanner.tfExpiring.SetKey("ui_pay_wall_expiring");
        ui.homepageTimeBanner.grp.EnsureBoundsCorrect();
    }

    private void OnClickPayWallEnter()
    {
        DataDotMgr.Collect(new DotClickMonetizationDiscountBottomBar());
        _enterPayWallAction?.Invoke();
    }
    
    private void OnClickPayWallShow()
    {
        DataDotMgr.Collect(new DotClickMonetizationDiscountBottomBarDiscountIcon());
        ui.homepageTimeBanner.state.selectedIndex = 1;
        DataDotMgr.Collect(new DotAppearMonetizationDiscountBottomBar());
    }
    
    private void OnClickPayWallHide()
    {
        DataDotMgr.Collect(new DotClickMonetizationDiscountBottomBarExitButton());
        ui.homepageTimeBanner.state.selectedIndex = 0;
        DataDotMgr.Collect(new DotAppearMonetizationDiscountBottomBarDiscountIcon());
    }

    protected override void HandleNotification(string name, object body)
    {
        var enterPos = mainModel.ChatEnterPos;
        switch (name)
        {
            //todo0
            case EconomicCallEvent.OnRefreshEconomicInfo:
                _mainHead.RefreshGem();
                break;

            case NotifyConsts.DrillHubReShowEvent:
                mainModel.SetDrillHubAutoShow(true, (DrillHubViewEnum)body);
                if (enterPos != ChatEnterPos.DrillHub) return;
                CheckAutoOpenDrillHub();
                break;
            case NotifyConsts.ShowHomepageBanner:
                var args = (HomepageBannerArgs)body;
                ui.homepageBanner.ShowBanner(args);
                break;
            case NotifyConsts.HideHomepageBanner:
                ui.homepageBanner.HideBanner();
                break;
            case NotifyConsts.ShowHomepageTimeBanner:
                ShowBanner((Action)body);
                break;
            case NotifyConsts.HideHomepageTimeBanner:
                ui.homepageTimeBanner.com.visible = false;
                break;
            case NotifyConsts.AfterDealResultData:
                _mainHead.RefreshFire();
                break;
            case SocialChatEvent.OnRefreshUnreadCount:
                _mainController.RequsetUnReadMessageNum();
                break;
            case NotifyConsts.RefreshMainBtns:
                RebuildMainButtons();
                break;           
        }
    }

    public bool IsHomepageBannerVisible()
    {
        return ui.homepageBanner.isShow;
    }

    public bool IsHomepageTimeBannerVisible()
    {
        return ui.homepageTimeBanner.com.visible;
    }

    protected override string[] ListNotificationInterests()
    {
        return new string[]
        {
            //LearnPathCallEvent.OnRefreshEnterUI,
            NotifyConsts.DrillHubReShowEvent,
            NotifyConsts.ShowHomepageBanner,
            NotifyConsts.HideHomepageBanner,
            NotifyConsts.AfterDealResultData,
            SocialChatEvent.OnRefreshUnreadCount,
            NotifyConsts.ShowHomepageTimeBanner,
            NotifyConsts.HideHomepageTimeBanner,
            NotifyConsts.RefreshMainBtns,
        };
    }

    string waitNetConnTimer;
    private void OnEnterWorldTest()
    {
        //如果进入世界前已经 断开tcp了那么需要先恢复tcp再进入
        if (NetManager.instance.IsClose())
        {
            NetManager.instance.Close();
            ControllerManager.instance.GetController<LoginController>(ModelConsts.Login).StartTCPConn();

            waitNetConnTimer = TimerManager.instance.RegisterTimer((count) =>
            {
                if (NetManager.instance.IsConn())
                {
                    
                    ControllerManager.instance.GetController<SceneController>(ModelConsts.Scene).EnterWoard_Test();
                    if (!string.IsNullOrEmpty(waitNetConnTimer))
                    {
                        TimerManager.instance.UnRegisterTimer(waitNetConnTimer);
                    }
                    GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
                    return;
                }

                if (count == 2) {
                    GetUI<CommBusyUI>(UIConsts.CommBusy).Show();
                }
                if (count == 7)
                {
                    Debug.LogError("Fail to recover TCP's conn from close in 10 retry");
                    GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
                    NetManager.instance.Close();
                }
            }, 800, 7);//todo 暂定只重试10轮 tcp连接， 没有复杂兜底逻辑
        }
        else
        {
            if (NetManager.instance.IsConn())
            {
                ControllerManager.instance.GetController<SceneController>(ModelConsts.Scene).EnterWoard_Test();
            }
            else {
                
                waitNetConnTimer = TimerManager.instance.RegisterTimer((count) =>
                {
                    if (NetManager.instance.IsConn())
                    {
                        ControllerManager.instance.GetController<SceneController>(ModelConsts.Scene).EnterWoard_Test();
                        if (!string.IsNullOrEmpty(waitNetConnTimer))
                        {
                            TimerManager.instance.UnRegisterTimer(waitNetConnTimer);
                        }
                        GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
                        return;
                    }
                    if (count == 2)
                    {
                        GetUI<CommBusyUI>(UIConsts.CommBusy).Show();
                    }
                    if (count == 7)
                    {
                        Debug.LogError("Fail to recover TCP's conn from not conn in 10 retry");
                        GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
                    }
                }, 800, 70);//todo 暂定只重试10轮 tcp连接， 没有复杂兜底逻辑
            }
        }
            
        // this.Hide();
    }
    
    public void ShowBtnToTop()
    {
        ui.arrowIn.Play();
    }
    
    public void HideBtnToTop()
    {
        ui.arrowOut.Play();
    }
}
