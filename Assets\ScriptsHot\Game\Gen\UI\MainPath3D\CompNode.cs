/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.MainPath3D
{
    public partial class CompNode : UIBindT
    {
        public override string pkgName => "MainPath3D";
        public override string comName => "CompNode";

        public Controller node;
        public GLoader bar0;
        public GLoader bar1;
        public GLoader bar2;
        public GLoader bar3;
        public GLoader bar4;
        public GLoader bar5;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            node = com.GetControllerAt(0);
            bar0 = (GLoader)com.GetChildAt(6);
            bar1 = (GLoader)com.GetChildAt(7);
            bar2 = (GLoader)com.GetChildAt(8);
            bar3 = (GLoader)com.GetChildAt(9);
            bar4 = (GLoader)com.GetChildAt(10);
            bar5 = (GLoader)com.GetChildAt(11);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            node = null;
            bar0 = null;
            bar1 = null;
            bar2 = null;
            bar3 = null;
            bar4 = null;
            bar5 = null;
        }
    }
}