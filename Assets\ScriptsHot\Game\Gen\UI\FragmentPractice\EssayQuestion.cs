/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class EssayQuestion : AFragQuestion
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "EssayQuestion";
        public static string url => "ui://cmoz5osjqit528";

        public Controller audio;
        public GGraph bg;
        public GComponent compBlank;
        public BtnAudio btnPlayAudio;
        public GRichTextField tfQuestion;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(EssayQuestion));
        }

        public override void ConstructFromXML(XML xml)
        {
            audio = GetControllerAt(0);
            bg = GetChildAt(0) as GGraph;
            compBlank = GetChildAt(1) as GComponent;
            btnPlayAudio = GetChildAt(2) as BtnAudio;
            tfQuestion = GetChildAt(3) as GRichTextField;
        }
        public override void Dispose()
        {
            audio = null;
            bg = null;
            compBlank = null;
            btnPlayAudio = null;
            tfQuestion = null;

            base.Dispose();
        }
    }
}