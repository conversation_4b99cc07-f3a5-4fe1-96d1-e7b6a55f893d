/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.Review
{
    public partial class PageBtn : GComponent
    {
        public static string pkgName => "Review";
        public static string comName => "PageBtn";
        public static string url => "ui://l7zo233drhje1y";

        public Controller color;
        public GImage imgGrey;
        public GTextField i18n;
        public GImage imgLeft;
        public GImage imgRight;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(PageBtn));
        }

        public override void ConstructFromXML(XML xml)
        {
            color = GetControllerAt(0);
            imgGrey = GetChildAt(0) as GImage;
            i18n = GetChildAt(1) as GTextField;
            imgLeft = GetChildAt(2) as GImage;
            imgRight = GetChildAt(3) as GImage;
        }
        public override void Dispose()
        {
            color = null;
            imgGrey = null;
            i18n = null;
            imgLeft = null;
            imgRight = null;

            base.Dispose();
        }
    }
}