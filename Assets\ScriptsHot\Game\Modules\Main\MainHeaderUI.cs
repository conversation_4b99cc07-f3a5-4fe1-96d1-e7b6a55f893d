// using System;
// using FairyGUI;
// using Msg.basic;
// using Msg.incentive;
// using ScriptsHot.Game.Modules.Profile;
// using ScriptsHot.Game.Modules.Sign;
// using UIBind.Main;
// using UnityEngine;
// using System.Collections.Generic;
// using Msg.dialog_task;
// using ScriptsHot.Game.Modules.ChatLogicNew;
// using ScriptsHot.Game.Modules.Setting;
// using ScriptsHot.Game.Modules.Task;
// using Spine;
//
// // 1.SetAction：左上角按钮及显示位置
// // 2.SetGold：设置金币
// // 3.SetPosition：
//
// public enum MainHeaderMode{
//     Outdoor= 0,//室外（地图）模式
//     Indoor,//室内模式
//     Talk,//对话模式
//     ExerciseTalk, //练习对话模式
//     Homepage, //New Homepage
// }
// public class MainHeaderUI : BaseUI<MainHeaderPanel>, IBaseUIUpdate
// {
//     public MainHeaderUI(string name) : base(name){ }
//     public override string uiLayer => UILayerConsts.Home;
//     protected override bool isFullScreen => true;
//
//     //private MainHeaderMode _btnMode;
//     
//     private long _lastUpdateStaminaTime;
//
//     private long _lastUpdateTime;
//     
//     private MainModel _mainModel => GetModel<MainModel>(ModelConsts.Main);
//     
//     private SignModel _signModel => GetModel<SignModel>(ModelConsts.Sign);
//     
//     private StaminaModel _staminaModel => GetModel<StaminaModel>(ModelConsts.Stamina);
//
//     // private LearnPathModel _learnPathModel => GetModel<LearnPathModel>(ModelConsts.LearnPath);
//
//     private CurrencyModel _currencyModel => GetModel<CurrencyModel>(ModelConsts.CurrencyController);
//     
//     private ProfileModel _profileModel => GetModel<ProfileModel>(ModelConsts.Profile);
//     private MainController _mainController => GetController<MainController>(ModelConsts.Main);
//     private HomepageController _homepageController => GetController<HomepageController>(ModelConsts.Homepage);
//    
//     private  string defaultFrameId ;
//
//
//     //private Action growthAniCompleteCallBack;
//     public bool IsVisible { get; private set; }
//
//     protected override void OnInit(GComponent uiCom)
//     {
//         AddUIEvent(this.ui.btnBack.onClick, OnBtnLeftTopClick);
//         AddUIEvent(this.ui.btnOutDoor1.onClick, OnBtnLeftTopClick);
//         AddUIEvent(this.ui.btnSettings.onClick, OnChatSettingsClick);
// 		AddUIEvent(this.ui.btnAutoHelp.com.onClick, OnAutoHelpClick);
//
//
//         this.ui.headBar.OnInit();
//
//         //todo实文 这个spine在哪出发用
//         ui.expSpine.spineAnimation.AnimationState.Complete += HandleAnimationCompleteEvent;
//         
//     }
//
//     private void AddEvent()
//     {
//         Notifier.instance.RegisterNotification(NotifyConsts.UpdateHeaderMode,UpdateHeaderMode);
//        
//     }
//
//     private void RemoveEvent()
//     {
//         Notifier.instance.UnRegisterNotification(NotifyConsts.UpdateHeaderMode,UpdateHeaderMode);
//     }
//
//     protected override void OnShow()
//     {
//         AddEvent();
//         this.RefreshLeftTopButtonState();
//         defaultFrameId = _profileModel.defaultFrameId;
//         SetVisible(true, false);
//         GrowthVisible();
//         ui.headBar.OnShow();
//         Refresh();
//
//         
//         // 引导：在帮助按钮上添加标记 10042
//         var guideController = ControllerManager.instance.GetController<ScriptsHot.Game.Modules.Guide.GuideController>(ModelConsts.Guide);
//         guideController.RegisterMark(10042, ui.btnAutoHelp.com);
//     }
//
//     private void GrowthVisible()
//     {
//         // var rkStr = AppRegionInfo.GetCurrRegionKeyAsStr();
//         //     
//         // if (rkStr == AppRegionKey.jpTest.ToString())
//         // {
//         //     ui.GrowthCtrl.selectedIndex = 1;
//         // }else if (rkStr == AppRegionKey.eu.ToString())
//         // {
//         //     ui.GrowthCtrl.selectedIndex = 0;
//         // }
//     }
//
//
//     //todo 错误的写法，待进一步重构
//     public async void Refresh()
//     {
//         if (!this.isShow) return;
//         ui.expSpine.visible = false;
//         ui.growthExp.visible = false;
//
//         
//         ui.headBar.Refresh();
//         ui.headBar.UpdateStaminaItem();
//         //todo0 临时补丁，通过mainHeader的刷新触发 centerHP的header UI的刷新，非常别扭
//         var centerHomeUI = GetUI<CenterHomeUI>(UIConsts.CenterHome);
//         if(centerHomeUI.isInit)
//         {
//             SendNotification(NotifyConsts.MainHeadRefreshEvent);
//         }
//
//         //todo0
//         //不应该借助MainHeader来刷新Homepage, 由这个页面负责刷streak
//         //目前太多地方直接调用UI的 public当成全局公共函数了
//         // TryRefreshSignAndStreak();
//     }
//
//     public void RefershHeadBar(bool isRefreshHead,bool isRefreshCurrency) {
//         this.ui.headBar.Refresh(isRefreshHead, isRefreshHead);
//     }
//
//     // private async void TryRefreshSignAndStreak() {
//     //
//     //
//     //     try
//     //     {
//     //         var resp = await MsgManager.instance.SendAsyncMsg<SC_GetCheckinSummaryAck>(new CS_GetCheckinSummaryReq());
//     //         VFDebug.Log("HSW CS_GetCheckinSummaryReq");
//     //         if (resp != null)
//     //         {
//     //             _signModel.SetSignSummary(resp.data);
//     //             if (resp.data.recall_checkin_days)
//     //             {
//     //                 GetUI(UIConsts.SignRepairUI).Show();
//     //             }
//     //
//     //             //Also update in Homepage
//     //             _homepageController.RefreshStreak(resp.data.checkin_streak_days.ToString());
//     //         }
//     //     }
//     //     catch (System.Exception e)
//     //     {
//     //         Debug.LogError("Err in Header-CheckinSummary:" + e.ToString());
//     //         _homepageController.RefreshStreak("-");//todo0 跨控制器修订streak btn
//     //     }
//     //   
//     // }
//
//     
//
//    
//     //暂无人调用
//     public void RefreshGrowthExp()
//     {
//         if (_mainModel.aniGrowthExp != 0)
//         {
//             //growthAniCompleteCallBack = aciton;
//             TimerManager.instance.RegisterTimer((interval) =>
//             {
//             ui.growthExp.text = string.Format("<font color=#2C95F7,#013399>+{0}</font>", _mainModel.aniGrowthExp);
//             ui.expSpine.visible = true;
//             ui.growthExp.visible = true;
//             ui.com.GetTransition("GrowthExp").Play();
//             ui.expSpine.playing = true;
//             _mainModel.ClearAddGrowthExp();
//             }, 300);
//         }
//         else
//         {
//             Debug.LogError("已迁移RefreshGrowthLevel功能，如果报错，需要单独修复");
//             //RefreshGrowthLevel();
//             
//         }
//     }
//
//     private void RefreshAutoHelp(MainHeaderMode mode = MainHeaderMode.ExerciseTalk)
//     {
//         if (mode != MainHeaderMode.ExerciseTalk)
//             return;
//         ChatLogicModel chatLogicModel = GetModel<ChatLogicModel>(ModelConsts.ChatLogic);
//         PB_UserAssistLevelEnum assistLevelEnum = chatLogicModel.UserAssistLevel;
//         ui.btnAutoHelp.com.visible = assistLevelEnum != PB_UserAssistLevelEnum.UserAssistLevelNone;
//         if (chatLogicModel.UserAssistLevel == PB_UserAssistLevelEnum.UserAssistLevelHelpAutoOpen)
//             ui.btnAutoHelp.state.selectedIndex = 1;
//         else
//             ui.btnAutoHelp.state.selectedIndex = 0;
//     }
//     
//     private void HandleAnimationCompleteEvent(TrackEntry trackEntry)
//     {
//         if (trackEntry.Animation.Name.Equals("animation"))
//         {
//             ui.expSpine.visible = false;
//             ui.expSpine.playing = false;
//             ui.growthExp.visible = false;
//
//             ui.headBar.RefreshGrowthLevel();
//             // if (growthAniCompleteCallBack != null)
//             //     growthAniCompleteCallBack();
//         }
//     }
//
//     protected override void OnHide()
//     {
//         RemoveEvent();
//     }
//     
//     public void OnBtnLeftTopClick()
//     {
//         SoundManger.instance.PlayUI("back");
//         VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Soft);
//         SendNotification(NotifyConsts.DoTopLeftBack);
//     }
//     
//     private void UpdateHeaderMode(string s, object body)
//     {
//         SetHeader((MainHeaderMode)body);
//     }
//     
//     /// <summary>
//     /// 设置顶部按钮及位置
//     /// </summary>
//     public void SetHeader(MainHeaderMode mode,Action btnCallback = null)
//     {
//         // VFDebug.LogError("mode  "+mode);
//         _mainModel.SetHeaderMode(mode);
//         //如果展示了才刷新内容
//         if(this.isShow) 
//         {
//             RefreshLeftTopButtonState();
//         }
//     }
//     
//     // /// <summary>
//     // /// 播放tween动画
//     // /// </summary>
//     // /// <param name="isOut"> true播放四散的动画 false则是reset</param>
//     // public void ChangeTweenHeaderState(bool isOut)
//     // {
//     //     if(isOut)
//     //         ui.Out.Play();
//     //     else
//     //         ui.Reset.Play();
//     // }
//     // /// <summary>
//     // /// 播放进入动画(退出和重置可以调用ChangeTweenHeaderState())
//     // /// </summary>
//     // /// <param name="delay"></param>
//     // public void PlayInAnimation(float delay = 0f)
//     // {
//     //     ui.In.Play(1, delay, null);
//     // }
//
//     List<Transition> _transitions = new List<Transition>();
//     /// <summary>
//     /// 设置Header的显示状态(在不销毁HeaderUI本身的情况下控制显隐)，可选择是否使用动画。
//     /// </summary>
//     /// <param name="visible"></param>
//     /// <param name="useAnimation"></param>
//     public void SetVisible(bool visible, bool useAnimation = true)
//     {
//         // if(IsVisible == visible) return;
//
//         for(int i = 0; i < _transitions.Count; i++)
//         {
//             _transitions[i].Stop(true,true);
//         }
//         _transitions.Clear();
//         
//         if(visible)
//         {
//             if(useAnimation)
//             {
//                 var transition = ui.com.GetTransition("In");
//                 _transitions.Add(transition);
//                 transition.Play(1, 0f, () =>
//                 {
//               
//                     _transitions.Remove(transition);
//                 });
//             }
//             else
//             {
//                
//                 var transition = ui.com.GetTransition("Visible");
//                 _transitions.Add(transition);
//                 transition.Play(1, 0f, () =>
//                 {
//                     _transitions.Remove(transition);
//                 });
//             }
//         }
//         else
//         {
//             if(useAnimation)
//             {
//                 
//                 var transition = ui.com.GetTransition("Out");
//                 _transitions.Add(transition);
//                 transition.Play(1, 0f, () =>
//                 {
//                  
//                     _transitions.Remove(transition);
//                 });
//             }
//             else
//             {
//               
//                 var transition = ui.com.GetTransition("Invisible");
//                 _transitions.Add(transition);
//                 transition.Play(1, 0f, () =>
//                 {
//                     
//                     _transitions.Remove(transition);
//                 });
//             }
//         }
//
//         // IsVisible = visible;
//         this.uiCom.visible = visible;
//     }
//
//     public void Update(int interval)
//     {
//         if (!this.isShow) return;
//         // 每个小时刷新一次
//         var lastUpdateDate = DateTimeOffset.FromUnixTimeSeconds(_lastUpdateStaminaTime / 1000).DateTime;
//         if (TimeExt.serverTime.Hour != lastUpdateDate.Hour)
//         {
//             if (_lastUpdateStaminaTime > 0)
//             {
//                 VFDebug.Log("OnReqGetIncentiveData");
//                 _homepageController.ReqGetIncentiveData();
//             }
//             _lastUpdateStaminaTime = TimeExt.serverTimestamp;
//         }
//
//         if (TimeExt.serverTimestamp - _lastUpdateTime > 200f)
//         {
//             ui.headBar.UpdateStaminaItem();
//             _lastUpdateTime = TimeExt.serverTimestamp;
//         }
//     }
//
//     private void RefreshLeftTopButtonState()
//     {
//         this.ui.ctrMainHeader.selectedIndex = (int)_mainModel.btnMode;
//         if (_mainModel.btnMode == MainHeaderMode.Homepage)
//             SetVisible(false, false); //回到首页的时候设置可见
//         RefreshAutoHelp(_mainModel.btnMode);
//
//         ResetHeaderListByMode(_mainModel.btnMode);
//
//         SetPageCtrl(_mainModel.btnMode);
//     }
//
//     private void SetPageCtrl(MainHeaderMode mode)
//     {
//         // if (mode == MainHeaderMode.Indoor)
//         // {
//         //     ui.newPagePosCtrl.selectedPage = "indoor";
//         //     _littleMapController.HideLittleMapUI();
//         // }else if (mode == MainHeaderMode.Outdoor)
//         // {
//         //     ui.newPagePosCtrl.selectedPage = "outdoor";
//         //     _littleMapController.ShowLittleMapUI();
//         // }
//         // else
//         // {
//         //     _littleMapController.HideLittleMapUI();
//         // }
//     }
//
//     /// <summary>
//     /// 【新首页】设置顶部按钮状态
//     /// </summary>
//     /// <param name="mode"></param>
//     private void ResetHeaderListByMode(MainHeaderMode mode)
//     {
//         if (mode == MainHeaderMode.Homepage || mode == MainHeaderMode.Outdoor || mode == MainHeaderMode.Indoor)
//         {
//             if (ui.headBar.com != null) {
//                 ui.headBar.ReinitHeaderList();
//             } 
//            
//         }
//         
//         //其他模式下，暂不考虑点击事件问题
//     }
//     
//     //=======================================================事件=========================================================
//     protected override void HandleNotification(string name, object body)
//     {
//         switch (name)
//         {
//             case EconomicCallEvent.OnRefreshEconomicInfo:
//                 // SetDiamond();
//                 ui.headBar.RefreshCurrency();
//                 break;
//             case NotifyConsts.ShowGrowthExp:
//                 //RefreshGrowthExp();  todo 暂时关闭等解决与学练的展示冲突
//                 break;
//             case NotifyConsts.RefreshAutoHelpEvent:
//                 RefreshAutoHelp();
//                 break;
//             case NotifyConsts.MainHeaderVisible:
//                 SetVisible(false, true);
//                 break;
//         }
//     }
//
//     protected override string[] ListNotificationInterests()
//     {
//         return new string[]
//         {
//             EconomicCallEvent.OnRefreshEconomicInfo,
//             NotifyConsts.ShowGrowthExp,
//             NotifyConsts.RefreshAutoHelpEvent,
//             NotifyConsts.MainHeaderVisible,
//         };
//     }
//
//     //废弃 改用AUTO
//     private void OnChatSettingsClick()
//     {
//         VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Soft);
//         if (GetModel<ChatModel>(ModelConsts.Chat).chatMode == PB_DialogMode.Career)
//         {
//             DataDotClick_Option dot = new DataDotClick_Option();
//             dot.Dialogue_id = DataDotMgr.GetDialogId();
//             DataDotMgr.Collect(dot);
//             this.GetUI(UIConsts.SettingFreeChat).Show();
//         }
//         else if(GetModel<ChatModel>(ModelConsts.Chat).chatMode == PB_DialogMode.Exercise)
//             this.GetUI(UIConsts.SettingChat).Show();
//         
//
//         //新埋点：Warmup中对话中打开设置
//         DataDotClickDialogueOptionShow model = new DataDotClickDialogueOptionShow();
//         model.Dialogue_id = DataDotMgr.GetDialogId();
//         DataDotMgr.Collect(model);
//     }
//
// 	private void OnAutoHelpClick()
//     {
//         ChatLogicController chatLogicController = GetController<ChatLogicController>(ModelConsts.ChatLogic);
//         
//         DataDotClick_Help_mode dot = new DataDotClick_Help_mode();
//         dot.Dialogue_id = chatLogicController.CurChat.DialogTaskAck.data.dialog_id;
//         dot.Dialogue_round = chatLogicController.Model.CurRoundId;
//         if (chatLogicController.Model.UserAssistLevel == PB_UserAssistLevelEnum.UserAssistLevelHelpAutoOpen)
//         {
//             dot.Help_mode = "Manual";
//             CS_SetUserAssistLevelReq req = new CS_SetUserAssistLevelReq();
//             req.user_assist_level = PB_UserAssistLevelEnum.UserAssistLevelHelpAutoClose;
//             MsgManager.instance.SendMsg(req);
//             chatLogicController.Model.SetUserAssistLevel(PB_UserAssistLevelEnum.UserAssistLevelHelpAutoClose);
//             GetModel<SettingModel>(ModelConsts.Setting)
//                 .SetFreeTalkDifficultyLevel(PB_UserAssistLevelEnum.UserAssistLevelHelpAutoClose);
//         }
//         else if (chatLogicController.Model.UserAssistLevel == PB_UserAssistLevelEnum.UserAssistLevelHelpAutoClose)
//         {
//             dot.Help_mode = "Auto";
//             CS_SetUserAssistLevelReq req = new CS_SetUserAssistLevelReq();
//             req.user_assist_level = PB_UserAssistLevelEnum.UserAssistLevelHelpAutoOpen;
//             MsgManager.instance.SendMsg(req);
//             chatLogicController.Model.SetUserAssistLevel(PB_UserAssistLevelEnum.UserAssistLevelHelpAutoOpen);
//             GetModel<SettingModel>(ModelConsts.Setting)
//                 .SetFreeTalkDifficultyLevel(PB_UserAssistLevelEnum.UserAssistLevelHelpAutoOpen);
//         }
//         DataDotMgr.Collect(dot);
//         GetController<TaskController>(ModelConsts.Task).HideFreeTalkSettingTips();
//     }
//
//     //todo0 这个枚举数据 center里也有一套应该统一只留一个
//     private enum MainHeaderItemType
//     {
//         Diamond = 0,
//         Stamina100 = 1,
//         Stamina50,
//         Stamina0,
//         EnergyUnlimited
//     }
// }
