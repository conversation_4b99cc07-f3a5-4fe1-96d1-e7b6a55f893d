﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using CommonUI;
using FairyGUI;
using UnityEngine;
using Google;
using UIBind.Login;
using ScriptsHot.Game.UGUI.WebView;
using Object = UnityEngine.Object;

public class LoginCNUI : BaseUI<UIBind.Login.LoginCnPanel>
{
    public LoginCNUI(string name) : base(name) { }
    public override string uiLayer => UILayerConsts.Home;
    protected override bool isFullScreen => true;
    private LoginController controller => this.GetController<LoginController>(ModelConsts.Login);
    private SceneController _sceneController => GetController<SceneController>(ModelConsts.Scene);
    private MainController _MainController => this.GetController<MainController>(ModelConsts.Main);

    private int selectedIndex = 0;

    private Controller startBtnCtrl;
    private Controller startBtnCtrl2;
    private bool isLogining = false;

    private const int REQUEST_VERIFICODE_MAX_ERROR_COUNT = 5;
    private const int REQUEST_VERIFICODE_MAX_TIME = 120;
    private const int REQUEST_VERIFICODE_MAX_ERROR_TIME = 5 * 60;
    private int requestVerifiCodeErrorCount = 0;
    private int requestVerifiCodeCd = 0;
    private string updateTimer = string.Empty;
    private int requestVerifiCodeCnt = 0;
    private bool isRequestVerifiCodeing = false;
    
    protected override void OnInit(GComponent uiCom)
    {
        DataDotAppear_Talkit_entry model = new DataDotAppear_Talkit_entry();
        DataDotMgr.Collect(model);
        
        AddUIEvent(ui.input_phone.onChanged , OnInputPhoneChanged);       
        AddUIEvent(ui.input_verifi.onChanged , OnInputVerifiChanged);   
        
        AddUIEvent(ui.ChangeNormalModeBtn.onClick , OnBtnChangeNormalModeClick); 
        AddUIEvent(ui.btnClear_phone.onClick , OnBtnClearPhoneClick);           
        AddUIEvent(ui.btnClear_verifi.onClick , OnBtnClearVerifiClick);  
        
        AddUIEvent(ui.changePhoneMode.com.onClick, OnBtnChangeModeClick);        
        AddUIEvent(ui.loginBtn.com.onClick, OnBtnStartClick);
        AddUIEvent(ui.waitVerifiBtn.com.onClick, OnBtnWaitVerifiClick);        
        AddUIEvent(ui.tfAgreement.onClickLink, OnTfAgreementClickLink);
        AddUIEvent(ui.btnAgreee.onClick,OnClickAgree);

        ui.input_verifi.enabled = false;
        startBtnCtrl = ui.changePhoneMode.state;
        startBtnCtrl2 = ui.loginBtn.state;

        ui.changePhoneMode.platform.selectedIndex = 2;
        ui.loginBtn.platform.selectedIndex = 2;
        ui.loginBtnBlack.platform.selectedIndex = 2;
    }

    protected override void OnShow()
    {
        AFHelper.Appear_cn_login_page_finished();
        ui.input_phone.text = String.Empty;
        ui.input_verifi.text = String.Empty;

        SetTipDisplay(false);
        SetBtnStartDisable(true);
        
        OnInputPhoneChanged();
        OnInputVerifiChanged();

        ui.changePhoneMode.title_cn.text = I18N.inst.MoStr("ui_cn_login_btn");
        ui.loginBtn.title_cn.text = I18N.inst.MoStr("ui_cn_login_btn2");
        ui.loginBtnBlack.title_cn.text = I18N.inst.MoStr("ui_cn_login_btn2");
        
        ui.input_phone.promptText = $"[color=#B2B2B2][size=32]{I18N.inst.MoStr("ui_cn_login_input_desc")}[/color]";
        ui.input_verifi.promptText = $"[color=#B2B2B2][size=32]{I18N.inst.MoStr("ui_cn_login_input_desc2")}[/color]";

        
        CheckWaitVerifiBtnTxt();
        
        ConfigUI();
        GameEntry.LoginC.HideDefaultCanvas();
        Debug.Log("[LoginCNUI]Opened");
        updateTimer = TimerManager.instance.RegisterTimer(MyUpdate, 1000, int.MaxValue);
        
        Debug.Log($"LoginConst.usertermsUrl = {LoginConst.usertermsUrl}");
        Debug.Log($"LoginConst.privacyUrl = {LoginConst.privacyUrl}");
    }

    private void SetTipDisplay(bool flag)
    {
        ui.tipsObj.visible = flag;
    }
    
    protected override void OnHide()
    {
        if (!string.IsNullOrEmpty(updateTimer))
        {
            TimerManager.instance.UnRegisterTimer(updateTimer);
            updateTimer = string.Empty;
        }
        GetUI(UIConsts.CommBusy).Hide();
    }
    

    private void MyUpdate(int count)
    {
        // Debug.Log(requestVerifiCodeCd);
        CheckWaitVerifiBtnTxt();
        if (requestVerifiCodeCd > 0)
        {
            requestVerifiCodeCd--;
        }
    }
    
    private void CheckWaitVerifiBtnTxt()
    {
        bool isCD = requestVerifiCodeCd > 0;
        ui.waitVerifiBtn.getVerifi.visible = !isCD;
        ui.waitVerifiBtn.waitCD.visible = isCD;
        
        if (isCD)
        {
            RefreshCD();
        }
        else
        {
            OnInputPhoneChanged();
        }
    }

    private void RefreshCD()
    {
        if (requestVerifiCodeCd > 0)
        {
            ui.waitVerifiBtn.waitCD.text = $"{requestVerifiCodeCd}s";
        }
        else
        {
            CheckWaitVerifiBtnTxt();
        }
    }

    public void SetBtnStartDisable(bool flag)
    {
        if (flag)
        {
            startBtnCtrl.selectedIndex = 0;
            startBtnCtrl2.selectedIndex = 0;
        }
        else
        {
            startBtnCtrl.selectedIndex = 1;
            startBtnCtrl2.selectedIndex = 1;
        }

        isLogining = !flag;
        if(isLogining)
            GetUI(UIConsts.CommBusy).Show();
        else
            GetUI(UIConsts.CommBusy).Hide();
        VFDebug.Log("isLogining  "+isLogining);
    }

    private void OnInputPhoneChanged()
    {
        bool isLengthMoreThan1 = ui.input_phone.text.Length > 0;
        ui.btnClear_phone.visible = isLengthMoreThan1;
        if (isLengthMoreThan1)
        {
            ui.waitVerifiBtn.getVerifi.text = $"[color=#6632FF]{I18N.inst.MoStr("ui_cn_login_btn3")}[/color]";
        }
        else
        {
            ui.waitVerifiBtn.getVerifi.text = $"[color=#B2B2B2]{I18N.inst.MoStr("ui_cn_login_btn3")}[/color]";
        }

        if (ui.input_phone.text.Length == 1)
        {
            AFHelper.Type_cn_phone_number();
        }
    }
    private void OnInputVerifiChanged()
    {
        bool isLengthMoreThan1 = ui.input_verifi.text.Length > 0;
        ui.btnClear_verifi.visible = isLengthMoreThan1;
        bool isLength6 = ui.input_verifi.text.Length == 6;        
        ui.loginBtn.com.visible = isLength6;
        ui.loginBtnBlack.com.visible = !isLength6;
        
        if (ui.input_verifi.text.Length == 1)
        {
            AFHelper.Type_cn_verification_code();
        }
    }    
    
    private void OnClickAgree(EventContext context)
    {
        if (!ui.btnAgree.selected)
        {
            AFHelper.Click_cn_read_and_agree();  
            ui.btnAgree.selected = true;
            SetTipDisplay(false);
        }
        else
        {
            ui.btnAgree.selected = false;
        }
    }

    // private void SetAgreePrivacy()
    // {
    //     ui.ctrlTips.selectedPage = "close";
    //     ui.tipsObj.visible = false;
    //     ui.btnAgree.selected = true;
    // }
    
    public void OnBtnWaitVerifiClick()
    {
        if (isRequestVerifiCodeing)
        {
            return;
        }
        if (requestVerifiCodeCd > 0)
        {
            return;
        }
        if (ui.input_phone.text.Length == 0)
        {
            return;
        }

        if (!ui.btnAgree.selected)
        {
            SetTipDisplay(true);
            return;
        }
        bool isNum11 = ui.input_phone.text.Length == 11;
        bool isPhoneNum = IsChinesePhoneNumber(ui.input_phone.text);
        if (!isNum11 || !isPhoneNum)
        {
            GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("ui_cn_login_tip1");
            return;
        }

        requestVerifiCodeCnt++;
        AFHelper.Click_cn_verification_code(requestVerifiCodeCnt);
        RequestVerifiCode();
    }

    public bool IsChinesePhoneNumber(string phoneNumber)
    {
        if (string.IsNullOrEmpty(phoneNumber))
            return false;

        // 正则匹配中国大陆手机号 (1 开头，第二位 3-9，后面 9 位数字)
        string pattern = @"^1[3-9]\d{9}$";
        return Regex.IsMatch(phoneNumber, pattern);
    }

    private async void RequestVerifiCode()
    {
        isRequestVerifiCodeing = true;
        var code = string.Empty;
        var result = await SignVerifiCodeHelper.Ins.GetVerifiCodeAsync(ui.input_phone.text);
        if (result.Item1)
        {
            code = SignVerifiCodeHelper.Ins.CacheVerification_code;
        }
        isRequestVerifiCodeing = false;
        if (string.IsNullOrEmpty(code))
        {   
            GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("ui_cn_login_tip1",true);
        }
        else
        {
            GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("ui_cn_login_tip5",true);
            requestVerifiCodeCd = REQUEST_VERIFICODE_MAX_TIME;
            ui.input_verifi.enabled = true;
        }
    }

    private void OnBtnClearPhoneClick()
    {
        ui.input_phone.text = String.Empty;
        OnInputPhoneChanged();
    }
    private void OnBtnClearVerifiClick()
    {
        ui.input_verifi.text = String.Empty;
        OnInputVerifiChanged();
    }  
    
    public void OnBtnChangeModeClick()
    {
        if (ui.tp.selectedIndex == 0)
        {
            AFHelper.Click_cn_login_button();
            AFHelper.Appear_cn_login_input_unfinished();
            ui.tp.selectedIndex = 1;
            ui.input_phone.RequestFocus();
            AFHelper.Appear_cn_login_input_finished();
        }
    }
    public void OnBtnChangeNormalModeClick()
    {
        if (ui.tp.selectedIndex == 1)
        {
            ui.tp.selectedIndex = 0;
        }
    }    
    //登录逻辑
    public void OnBtnStartClick()
    {
        AFHelper.Click_cn_log_on_button();
        if (!ui.btnAgree.selected)
        {
            SetTipDisplay(true);
            return;
        }

        if (string.IsNullOrEmpty(ui.input_phone.text) )
        {
            GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("ui_cn_login_tip1");
            return;
        }
        if (string.IsNullOrEmpty(ui.input_verifi.text))
        {
            GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("ui_cn_login_tip3");
            return;
        }
        
        Debug.Log($"server = {SignVerifiCodeHelper.Ins.CacheVerification_code}");
        Debug.Log($"ui = {ui.input_verifi.text}");
        if (!SignVerifiCodeHelper.Ins.CachePhoneNum.Equals(ui.input_phone.text))
        {
            GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("ui_cn_login_tip6");            
            return;
        }
        
        if (!SignVerifiCodeHelper.Ins.CacheVerification_code.Equals(ui.input_verifi.text))
        {
            requestVerifiCodeErrorCount++;
            AFHelper.Appear_cn_incorrect_verification(requestVerifiCodeErrorCount);
            OnBtnClearVerifiClick();
            if (requestVerifiCodeErrorCount >= REQUEST_VERIFICODE_MAX_ERROR_COUNT)
            {
                AFHelper.Appear_verification_error_toast();
                GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("ui_cn_login_tip4");
                requestVerifiCodeCd = REQUEST_VERIFICODE_MAX_ERROR_TIME;
            }
            else
            {
                GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("ui_cn_login_tip3");
            }
            return;
        }
        
        if (isLogining)
        {
            return;
        }

        if (ui.tp.selectedIndex == 1)
        {
            ui.tp.selectedIndex = 0;
        }

        controller.DoUILoginStart("");
    }

    //引导逻辑
    public void OnBtnLoginUpClick()  
    {
        LoginBIHelper.Appear_Onboarding_page();
        var be = _MainController.BeOnBoardState(out GameStateOnBoard gameStateOnBoard);
        if (!be)
        {
            _sceneController.LoadSceneById(Cfg.T.TbConst.OnBoardingSceneId);
        }
        else
        {
            Hide();
            // GetUI(UIConsts.LoginChooseRoleUI).Show();
        }
    }
    
    private void OnTfAgreementClickLink(EventContext e)
    {        
        string url = e.data as string;
        if (url == "userterms")
        {
#if UNITY_EDITOR
            OpenUrl(LoginConst.usertermsUrl);
#else
            OpenUrl(LoginConst.usertermsUrl);
            //DeviceAdapter.OpenWebPage(LoginConst.usertermsUrl);
#endif
        }
        else if (url == "privacy")
        {
#if UNITY_EDITOR
            OpenUrl(LoginConst.privacyUrl);
#else
            OpenUrl(LoginConst.privacyUrl);
            //DeviceAdapter.OpenWebPage(LoginConst.privacyUrl);
#endif
        }
    }

    private void ConfigUI()
    {
        string fmtAllStr = string.Format("[color=#111111]{0}[/color]", I18N.inst.MoStr("login_privacy_desc"));
        string fmtUserterms = string.Format("[url=userterms][color=#6632ff]{0}[/color][/url]", I18N.inst.MoStr("login_privacy_userterms_link"));
        string fmtPrivacy = string.Format("[url=privacy] [color=#6632ff]{0}[/color][/url]", I18N.inst.MoStr("login_privacy_link"));
        string content = string.Format(fmtAllStr, fmtUserterms, fmtPrivacy);
        ui.tfAgreement.text = content;
        // ui.tfSignDesc.SetKey("login_notice_desc");
    }

    private void OpenUrl(string url)
    {
        MainModel mainModel = GetModel<MainModel>(ModelConsts.Main);

        // GameObject ctlPrefab = Resources.Load<GameObject>("Prefabs/WebViewCtl");
        GameObject ctlPrefab = GResManager.instance.LoadPrefab("WebViewCtl");     
        GameObject newCtl = Object.Instantiate(ctlPrefab);
            
        WebViewCtl ctl = newCtl.GetComponent<WebViewCtl>();
        if (ctl == null)
        {
            ctl = newCtl.AddComponent<WebViewCtl>();
        }
        ctl.Init(10f, I18N.inst.MotherLanguage.ToString(), I18N.inst.ForeignLanguage.ToString(), mainModel.toKen, I18N.inst.MotherLanguage.ToString(),
            true,
            true,
            () =>
            { 
                GetController<CurrencyController>(ModelConsts.CurrencyController).SendGetEconomicInfoReqAsync(GameEventName.GameEnter, () =>
                {
                    SendNotification(NotifyConsts.MainHeadRefreshEvent);
                });
            },() =>
            {
                GetUI<CommBusyUI>(UIConsts.CommBusy).LazyShow();
            },
            () =>
            {
                GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
            }
        );
        ctl.LoadUrl(url);
    }
    

}
