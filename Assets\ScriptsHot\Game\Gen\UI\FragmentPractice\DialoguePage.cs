/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class DialoguePage : ExtendedComponent
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "DialoguePage";
        public static string url => "ui://cmoz5osjw1ks47";

        public GComponent bgLoader;
        public AvatarHead avatar1;
        public AvatarHead avatar2;
        public GList chatList;
        public OldBtnBottom btnContinue;
        public AnswerPopup answerPanel;
        public OldCompCheck checker;
        public GGraph screenBG;
        public GTextField tfDebugAudio;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(DialoguePage));
        }

        public override void ConstructFromXML(XML xml)
        {
            bgLoader = GetChildAt(0) as GComponent;
            avatar1 = GetChildAt(1) as AvatarHead;
            avatar2 = GetChildAt(2) as AvatarHead;
            chatList = GetChildAt(4) as GList;
            btnContinue = new OldBtnBottom();
            btnContinue.Construct(GetChildAt(5).asCom);
            answerPanel = GetChildAt(6) as AnswerPopup;
            checker = GetChildAt(7) as OldCompCheck;
            screenBG = GetChildAt(8) as GGraph;
            tfDebugAudio = GetChildAt(9) as GTextField;

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();

            bgLoader = null;
            avatar1 = null;
            avatar2 = null;
            chatList = null;
            btnContinue.Dispose();
            btnContinue = null;
            answerPanel = null;
            checker = null;
            screenBG = null;
            tfDebugAudio = null;

            base.Dispose();
        }
    }
}