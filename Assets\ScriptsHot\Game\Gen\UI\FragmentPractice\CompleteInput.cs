/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class CompleteInput : GComponent
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "CompleteInput";
        public static string url => "ui://cmoz5osji0ow3l";

        public GTextInput input;
        public GGraph underline;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(CompleteInput));
        }

        public override void ConstructFromXML(XML xml)
        {
            input = GetChildAt(0) as GTextInput;
            underline = GetChildAt(1) as GGraph;
        }
        public override void Dispose()
        {
            input = null;
            underline = null;

            base.Dispose();
        }
    }
}