/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class OldBtnNormalPlay : ABtnAudio
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "OldBtnNormalPlay";
        public static string url => "ui://cmoz5osjz7rm3m";

        public GImage laba;
        public Transition playing;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(OldBtnNormalPlay));
        }

        public override void ConstructFromXML(XML xml)
        {
            laba = GetChildAt(1) as GImage;
            playing = GetTransitionAt(0);
        }
        public override void Dispose()
        {
            laba = null;
            playing = null;

            base.Dispose();
        }
    }
}