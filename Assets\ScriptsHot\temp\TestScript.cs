using System;
using System.Collections.Generic;
using AnimationSystem;
using demo;
using TMPro;
using UnityEngine;
using UnityEngine.SceneManagement;
using System.Threading.Tasks;

public class AnimTestAvatarLoader : MonoBehaviour
{
    public static List<string> characters = new List<string>
    {
        "NPC_Boy_00001", "NPC_Boy_00003", "NPC_Boy_00004", "NPC_Boy_00005",
        "NPC_Boy_00006", "NPC_Boy_00007", "NPC_Boy_00008", "NPC_Boy_00009", "NPC_Boy_00010",
        "NPC_Boy_00011", "NPC_Boy_00012", "NPC_Boy_00013", "NPC_Boy_00014", "NPC_Boy_00015",
        "NPC_Boy_00016", "NPC_Boy_00017", "NPC_Boy_00018", "NPC_Boy_00019", "NPC_Boy_00020",
        "NPC_Boy_00021", "NPC_Boy_00022", "NPC_Boy_00023", "NPC_Boy_00024", "NPC_Boy_00025", 
        "NPC_Boy_00027", "NPC_Boy_00028",
        "NPC_Girl_00001", "NPC_Girl_00002", "NPC_Girl_00003", "NPC_Girl_00004", "NPC_Girl_00005",
        "NPC_Girl_00006", "NPC_Girl_00007", "NPC_Girl_00008", "NPC_Girl_00009", "NPC_Girl_00010",
        "NPC_Girl_00011", "NPC_Girl_00012", "NPC_Girl_00013", "NPC_Girl_00014", "NPC_Girl_00015",
        "NPC_Girl_00016", "NPC_Girl_00017", "NPC_Girl_00019", "NPC_Girl_00020",
        "NPC_Girl_00021", "NPC_Girl_00022", "NPC_Girl_00023", "NPC_Girl_00024", "NPC_Girl_00025",
        "NPC_Girl_00026", "NPC_Girl_00027"
    };
    
    public AvatarLoader avatarLoader;

    public int currentIndex = 0;
    public TextMeshProUGUI text;
    public TMP_InputField inputField;
    
    public RuntimeAnimatorController overrideAnimatorControllerBoy = null;
    public RuntimeAnimatorController overrideAnimatorControllerGirl = null;
    
    // 当前加载的角色ID
    [Header("当前状态")]
    [SerializeField] private string currentCharacterId = "";

    public GameObject roleSlot;
    
    /// <summary>
    /// 获取当前加载的角色ID
    /// </summary>
    /// <returns>当前角色ID，如果没有加载角色则返回空字符串</returns>
    public string GetCurrentCharacterId()
    {
        return currentCharacterId ?? "";
    }

    public async void OnClickNext()
    {
        try
        {
            currentIndex = (currentIndex + 1) % characters.Count;
            string character = characters[currentIndex];
            text.text = character;
            
            bool success = await LoadCharacterInternal(character);
            if (!success)
            {
                Debug.LogError($"角色加载失败：{character}");
            }
        }
        catch (Exception e)
        {
            Debug.LogError(e);
        }
    }
    
    public async void OnClickGOTO()
    {
        string character = inputField.text;
        if (character.StartsWith("G"))
        {
            character = "NPC_Girl_" + character.Substring(1);
        }
        else if (character.StartsWith("B"))
        {
            character = "NPC_Boy_" + character.Substring(1);
        }
        text.text = character;

        bool success = await LoadCharacterInternal(character);
        if (!success)
        {
            Debug.LogError($"角色加载失败：{character}");
        }
    }
    
    // 设置指定 GameObject 及其所有子物体的层级
    public static void SetLayerRecursively(GameObject go, int layer)
    {
        // 设置当前 GameObject 的层级
        go.layer = layer;

        // 遍历所有子物体，并递归设置它们的层级
        foreach (Transform child in go.transform)
        {
            SetLayerRecursively(child.gameObject, layer);
        }
    }

    public void OnClickPlay()
    {
        //demo请使用女角色。
        GameObject avatarBody = GameObject.Find("Cartoon_Girl_Body_LOD(Clone)");
        if (avatarBody != null)
        {
            var dongzuomoni = avatarBody.GetComponent<dongzuomoni>();
            if (!dongzuomoni)
            {
                dongzuomoni = avatarBody.AddComponent<dongzuomoni>();
            }

            dongzuomoni.demoVoice = true;
        }
    }
    
    /// <summary>
    /// 公共的异步角色加载方法
    /// 供其他脚本调用（如AudioClipImporter）
    /// </summary>
    /// <param name="character">角色ID</param>
    /// <returns>加载成功返回true，失败返回false</returns>
    public async Task<bool> LoadCharacterAsync(string character)
    {
        return await LoadCharacterInternal(character);
    }
    
    /// <summary>
    /// 内部核心角色加载方法
    /// 统一处理所有角色加载逻辑，避免代码重复
    /// </summary>
    /// <param name="character">角色ID</param>
    /// <returns>加载成功返回true，失败返回false</returns>
    private async Task<bool> LoadCharacterInternal(string character)
    {
        try
        {
            // 获取角色根节点
            GameObject avatarRoot = GameObject.Find("RoleSlot");
            if (avatarRoot == null)
            {
                Debug.LogError("未找到RoleSlot对象！");
                return false;
            }
            
            // 清理现有角色
            for (int i = avatarRoot.transform.childCount - 1; i >= 0 ; i--)
            {
                if (avatarRoot.transform.GetChild(i).name != "sound")
                {
                    DestroyImmediate(avatarRoot.transform.GetChild(i).gameObject);
                }
            }

            // 禁用AvatarActionDirector组件
            if (avatarRoot.GetComponent<AvatarActionDirector>() != null)
            {
                var component = avatarRoot.GetComponent<AvatarActionDirector>();
                component.enabled = false;
            }
            
            // 准备加载数据
            // AvatarLoadExData data = new AvatarLoadExData();
            // data.overrideAnimatorControllerBoy = overrideAnimatorControllerBoy;
            // data.overrideAnimatorControllerGirl = overrideAnimatorControllerGirl;
            
            // 异步加载角色
            GameObject go = await avatarLoader.LoadNAvatar(character, avatarRoot.transform, initAnimationManager: Util.CheckDEMO());
            if (!this.roleSlot)
            {
                this.roleSlot = GameObject.Find("RoleSlot");
            }
        
            if (go && roleSlot)
            {
                // 实例化角色容器
                GameObject parentObject = roleSlot;
                go.transform.SetParent(parentObject.transform);
                go.transform.localPosition = Vector3.zero;
                // parentObject.transform.SetParent(roleSlot.transform, false);
                // parentObject.transform.localPosition = Vector3.zero;
                // parentObject.transform.localRotation = Quaternion.identity;
                parentObject.SetActive(true);

                // 初始化口型插件
                GAvatarCtrl avatarCtrl = parentObject.GetComponent<GAvatarCtrl>();
                if (!avatarCtrl) avatarCtrl = parentObject.AddComponent<GAvatarCtrl>();
            
                InitLipSyncPlugin(avatarCtrl);
                MatchLipSyncToHeadNode(go, avatarCtrl);
                
                // 激活并设置层级
                avatarRoot.transform.GetChild(0).gameObject.SetActive(true);
                SetLayerRecursively(avatarRoot.transform.GetChild(0).gameObject, LayerMask.NameToLayer("player"));
                
                // 更新当前角色ID
                currentCharacterId = character;
                
#if UNITY_EDITOR
                // 更新编辑器相关组件引用
                UpdateEditorReferences(avatarRoot);
#endif
                
                Debug.Log($"角色加载成功：{character}");
                return true;
            }
            else
            {
                Debug.LogError($"角色加载失败：{character}");
                return false;
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"角色加载异常：{e}");
            return false;
        }
    }
    
      /// <summary>
    /// 初始化口型同步插件
    /// </summary>
    private void InitLipSyncPlugin(GAvatarCtrl bindLipSyncAvatarCtrl)
    {
        var bindObj = bindLipSyncAvatarCtrl.audioSource.gameObject;
        
#if !UNITY_EDITOR_OSX //macOS上OVRLipSync无效
        bool added = bindObj.TryGetComponent<OVRLipSyncContext>(out OVRLipSyncContext lsc);
        if (!added)
        {
            lsc = bindObj.AddComponent<OVRLipSyncContext>();
        }
        lsc.audioLoopback = true;
        lsc.audioSource = bindLipSyncAvatarCtrl.audioSource;

        added = bindLipSyncAvatarCtrl.audioSource.gameObject.TryGetComponent<OVRLipSyncContextMorphTarget>(out OVRLipSyncContextMorphTarget lsm);
        if (!added)
        {
            lsm = bindLipSyncAvatarCtrl.audioSource.gameObject.AddComponent<OVRLipSyncContextMorphTarget>();
        }
#endif
    }

    /// <summary>
    /// 匹配口型同步到头部节点
    /// </summary>
    private void MatchLipSyncToHeadNode(GameObject modelGo, GAvatarCtrl avatarCtrl)
    {
        var headGo = ObjectUtils.FindChild(modelGo.transform, "Head", true);
        if (headGo != null)
        {
#if !UNITY_EDITOR_OSX //macOS上OVRLipSync无效
            if (avatarCtrl != null)
            {
                var lsm = avatarCtrl.audioSource.GetComponent<OVRLipSyncContextMorphTarget>();
                if (lsm != null)
                {
                    var smr = headGo.GetComponent<SkinnedMeshRenderer>();
                    if (smr != null)
                    {
                        lsm.skinnedMeshRenderer = smr;
                        Debug.Log("Bind lsm.smr succ");
                    }
                    else
                    {
                        Debug.LogError("Failed to get SkinnedMeshRenderer from headGo, ModelName=");
                    }
                }
                else
                {
                    Debug.LogError("Failed to find OVRLipSyncContextMorphTarget on Character.ModelName=");
                }
            }
            else
            {
                Debug.LogError("Failed to find GAvatarCtrl on Character.ModelName=");
            }
#endif
        }
        else
        {
            Debug.LogError("Failed to find head-Node of Model name=");
        }
    }
    
#if UNITY_EDITOR
    /// <summary>
    /// 更新编辑器相关组件引用
    /// </summary>
    /// <param name="avatarRoot">角色根节点</param>
    private void UpdateEditorReferences(GameObject avatarRoot)
    {
        // 更新AnimatorUIController引用（已注释的逻辑）
        GameObject Btns = GameObject.Find("Btns");
        // if (Btns != null)
        // {
        //     AnimatorUIController animatorUIController = Btns.GetComponent<AnimatorUIController>();
        //     animatorUIController.animator = avatarRoot.GetComponentInChildren<Animator>();
        //     if (animatorUIController != null)
        //     {
        //         animatorUIController.ReGen();
        //     }
        // }
        
        // 更新SimpleAnimTest和DialogTest引用
        if (Util.CheckDEMO() && GameObject.Find("SimpleAnimTest") != null)
        {
            var simgo = GameObject.Find("SimpleAnimTest");
            var simpleAnimTest = simgo.GetComponent<SimpleAnimTest>();
            simpleAnimTest.animManager = avatarRoot.GetComponentInChildren<AnimationAvatarManager>();
            var dialogTest = simgo.GetComponent<DialogTest>();
            dialogTest.dialogManager = avatarRoot.GetComponentInChildren<DialogManager>();
        }
    }
#endif
}