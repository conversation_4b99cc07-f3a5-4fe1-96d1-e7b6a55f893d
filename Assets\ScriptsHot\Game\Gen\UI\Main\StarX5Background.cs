/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.Main
{
    public partial class StarX5Background : ExtendedComponent
    {
        public static string pkgName => "Main";
        public static string comName => "StarX5Background";
        public static string url => "ui://tnbxk55iv0e0sxxxc5";

        public GLoader StarX5Loader;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(StarX5Background));
        }

        public override void ConstructFromXML(XML xml)
        {
            StarX5Loader = GetChildAt(0) as GLoader;

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();

            StarX5Loader = null;

            base.Dispose();
        }
    }
}