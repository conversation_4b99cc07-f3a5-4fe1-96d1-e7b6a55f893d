﻿using Modules.DataDot;
using Msg.basic;
using Msg.social;
using ScriptsHot.Game.Modules.Common;
using ScriptsHot.Game.Modules.ReviewQuestion;
using ScriptsHot.Game.Modules.Scene.Level.CommonSceneState;
using ScriptsHot.Game.Modules.Scene.Level.Component;
using SRF;
using UnityEngine;
using NotImplementedException = System.NotImplementedException;

namespace ScriptsHot.Game.Modules.Scene.Level.WorldLevel.State
{
    /// <summary>
    /// 主要处理场景上 聊天时候 模型，相机的逻辑
    /// </summary>
    public class SceneStateWorlfChatNew : SceneStateBase
    {
        private SceneStateChatParam _chatParam;
        private SceneStateTaskParam _taskParam;
        public SceneStateTaskParam TaskParam => _taskParam; 
        public SceneStateWorlfChatNew() : base(SceneState.ChatNew) { }

        public void AddEvent()
        {
            Notifier.instance.RegisterNotification(NotifyConsts.StoryNewFaceOut,StoryNewFaceOut);
            Notifier.instance.RegisterNotification(NotifyConsts.StoryNewFaceEnterChat,StoryNewFaceEnterChat);
        }

        public void RemoveEvent()
        {
            Notifier.instance.UnRegisterNotification(NotifyConsts.StoryNewFaceOut,StoryNewFaceOut);
            Notifier.instance.UnRegisterNotification(NotifyConsts.StoryNewFaceEnterChat,StoryNewFaceEnterChat);
        }

        private void StoryNewFaceOut(string s, object body)
        {
            ChangeToGameStateIdle();
        }

        private void StoryNewFaceEnterChat(string s, object body)
        {
            DoLogic();
        }
        
        public override void OnEnter(params object[] args)
        {
            base.OnEnter(args);
            AddEvent();
            
            if (!this.AssertParamLength(args, 1))
            {
                ChangeToGameStateIdle();
                return;
            }
            
            _taskParam = (SceneStateTaskParam)args[0];
        }

        private void DoLogic()
        {
            UIManager.instance.HideLayerBeyond(UILayerConsts.Home, UILayerConsts.Top, UILayerConsts.Loading, UILayerConsts.Float,UILayerConsts.Guide);
            
            SceneStateChatParam chatParam = new SceneStateChatParam();
            
            Unit unit = this._gameScene.GetComponent<UnitComponent>().GetUnitByAvatarTid(_taskParam.avatarId);
            if (unit != null)
            {
                Avatar avatar = _gameScene.GetComponent<AvatarComponent>().GetAvatar(unit.uid);
                if (avatar == null)
                {
                    ChangeToGameStateIdle();
                    return;
                }

                Avatar mainAvatar = _gameScene.GetComponent<AvatarComponent>().GetAvatar(_gameScene.GetComponent<UnitComponent>().mainUnitID);
                
                Transform temTransform = avatar.transform.Find("tarAvatarPos");
                //每次对话改变玩家位置
                // _gameScene.GetComponent<MainCtrlComponent>().JumpToAndStop(new Vector2(temTransform.position.x, temTransform.position.z));
                var pos = avatar.transform.position;
                
                chatParam.unitUid = unit.uid;
                chatParam.isTask = true;
                chatParam.taskId = _taskParam.taskId;
                chatParam.entranceType = _taskParam.entranceType;
                chatParam.chatMode = _taskParam.chatMode;
                chatParam.avatarId = avatar.belongUnit.avatarTid;
                chatParam.pos = pos;
                chatParam.avatar = avatar;
                
                GameUtils.SetCameraCullingMask(LayerMask.NameToLayer("player"), false);
                ObjectUtils.SetLayer(avatar.gameObject, LayerMask.NameToLayer("chat"));
                ObjectUtils.SetLayer(mainAvatar.gameObject, LayerMask.NameToLayer("chat"));
                
                mainAvatar.LookAt(new Vector2(avatar.transform.position.x, avatar.transform.position.z));
                
                _gameScene.GetComponent<CameraComponent>().TransitInAvatarChat(chatParam.unitUid,null,CameraScript.ViewMode.Story);
                
                // this._sceneController.GetUI<MainHeaderUI>(UIConsts.MainHeader).SetHeader(MainHeaderMode.Talk);
                //指定口型插件播放
                GAvatarCtrl avatarCtrl = avatar.gameObject.GetComponentOrAdd<GAvatarCtrl>();
                if(avatarCtrl != null)
                    GSoundManager.instance.SetCurrAvatarTTS(avatarCtrl.audioSource);
                
                GHeadBarManager.instance.SetWork(false);
                _sceneController.GetController<CurrencyController>(ModelConsts.CurrencyController).SendGetEconomicInfoReq(GameEventName.GameChatPre);//更新金币
                VFDebug.Log("NotifyConsts.ChatNewEnter!!!!!!!!!!!!!!!!!");
                Notifier.instance.SendNotification(NotifyConsts.ChatNewEnter,chatParam);

                _chatParam = chatParam;
            }
            else
            {
                ChangeToGameStateIdle();
                return;
            }
        }

        public override void OnTopLeftBack(bool forceBack = false)
        {
            //点击返回出弹窗
            int iconType = 2;
            if (_chatParam.chatMode == PB_DialogMode.Career || _chatParam.chatMode == PB_DialogMode.WorldStory && forceBack == false)
            {
                _sceneController.GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N(
                    GameEntry.LoginC.GetModel<MainModel>(ModelConsts.Main).incentiveData.homepage_economic_info.member_info.is_member
                        ? "common_confirm_task_premium"
                        : "common_confirm_task", () => DotDialogue(false), () =>
                    {
                        DotDialogue(true);
                        this.owner.StateMachine.ChangeState(SceneState.PostChat, this._chatParam.avatarId);
                        Notifier.instance.SendNotification(NotifyConsts.ExitChatNew);
                    }, 2, "common_continue", "common_btn_giveup", false, iconType);
            }
            else
            {
                DotDialogue(true);
                this.owner.StateMachine.ChangeState(SceneState.PostChat,this._chatParam.avatarId);
            }

            DataDotDialogueCallback dot = new DataDotDialogueCallback();
            DataDotMgr.Collect(dot);
        
            // _sceneController.GetUI<MainHeaderUI>(UIConsts.MainHeader).ChangeTweenHeaderState(false);
            
            //新埋点：对话退出
            DataDotDialogQuit model = new DataDotDialogQuit();
            model.Dialogue_id = DataDotMgr.GetDialogId();
            DataDotMgr.Collect(model);
        }

        public override void OnExitChatNew()
        {
        }
        
        private void DotDialogue(bool isExit)
        {
            long dialog_id = _chatParam.taskId;
            if(isExit)
            {
                DataDotDialogueCallbackCancel dot = new DataDotDialogueCallbackCancel();
                dot.Dialogue_id = dialog_id;
                DataDotMgr.Collect(dot);
            }
            else
            {
                Notifier.instance.SendNotification(NotifyConsts.StoryTwoWayGoOn);
                DataDotDialogueCallbackComfirm dot = new DataDotDialogueCallbackComfirm();
                dot.Dialogue_id = dialog_id;
                DataDotMgr.Collect(dot);
            }
        }
        
        public override void OnNetReConn()
        {
            ChangeToGameStateIdle();
        }

        public override void OnNetDisConn()
        {
            base.OnNetDisConn();
        }

        public override void OnExit()
        {
            base.OnExit();
            RemoveEvent();
        }
    }
}
