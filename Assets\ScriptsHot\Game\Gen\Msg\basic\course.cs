// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/basic/course.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.basic {

  /// <summary>Holder for reflection information generated from protobuf/basic/course.proto</summary>
  public static partial class CourseReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/basic/course.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static CourseReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Chtwcm90b2J1Zi9iYXNpYy9jb3Vyc2UucHJvdG8iWQoRUEJfQXZhdGFyVGV4",
            "dERhdGESEQoJYXZhdGFyX2lkGAEgASgDEgwKBHRleHQYAiABKAkSDgoGdHRz",
            "X2lkGAMgASgDEhMKC3Nsb3dfdHRzX2lkGAQgASgDItQBChhQQl9Db3Vyc2VM",
            "ZWFyblBhdGhQYXJhbXMSEQoJY291cnNlX2lkGAEgASgDEhUKDXNlY3Rpb25f",
            "aW5kZXgYAiABKAUSEgoKdW5pdF9pbmRleBgDIAEoBRITCgtsZXZlbF9pbmRl",
            "eBgEIAEoBRIVCg1zZXNzaW9uX2luZGV4GAUgASgFEiUKCmxldmVsX3R5cGUY",
            "ByABKA4yES5QQl9MZXZlbFR5cGVFbnVtEicKC2NvdXJzZV90eXBlGAggASgO",
            "MhIuUEJfQ291cnNlVHlwZUVudW0qsAEKElBCX1Nlc3Npb25UeXBlRW51bRIM",
            "CghTZXNUTm9uZRAAEg4KClNlc1RMZXNzb24QARITCg9TZXNUTGV2ZWxSZXZp",
            "ZXcQAhIWChJTZXNUTGV4ZW1lUHJhY3RpY2UQAxIUChBTZXNUVW5pdFByYWN0",
            "aWNlEAQSEgoOU2VzVFVuaXRSZXZpZXcQBRIQCgxTZXNUVW5pdFRlc3QQBhIT",
            "Cg9TZXNUU2VjdGlvblRlc3QQBypKChVQQl9Qcm9ncmVzc1N0YXR1c0VudW0S",
            "CgoGUFNOb25lEAASCgoGUFNMb2NrEAESDQoJUFNSdW5uaW5nEAISCgoGUFNQ",
            "YXNzEAMqnwEKEFBCX0xldmVsVHlwZUVudW0SCgoGTFROb25lEAASCgoGTFRT",
            "dGFyEAESCgoGTFRCb29rEAISCwoHTFRSYWRpbxADEgkKBUxUQm94EAQSDgoK",
            "TFREdW1iQmVsbBAFEgkKBUxUQ3VwEAYSCwoHTFRWaWRlbxAHEgkKBUxUQXVh",
            "EAgSDAoITFRXYXJtVXAQCRIOCgpMVFJvbGVQbGF5EAoqSgoSUEJfU2VjdGlv",
            "blR5cGVFbnVtEgwKCFNlY1ROb25lEAASEAoMU2VjVExlYXJuaW5nEAESFAoQ",
            "U2VjVERhaWx5UmVmcmVzaBACKlEKD1BCX1NraXBUeXBlRW51bRINCglTa2lw",
            "VE5vbmUQABIQCgxTa2lwVFNlY3Rpb24QARINCglTa2lwVFVuaXQQAhIOCgpT",
            "a2lwVExldmVsEAMqVwoSUEJfRWxlbWVudFR5cGVFbnVtEgwKCEVsZVROb25l",
            "EAASEgoORWxlVEF2YXRhclRleHQQARIQCgxFbGVUUXVlc3Rpb24QAhINCglF",
            "bGVUUHJvc2UQAypDCg5QQl9FeHBUeXBlRW51bRIKCgZFVE5vbmUQABILCgdF",
            "VEJhc2ljEAESDAoIRVRSZXZpZXcQAhIKCgZFVEp1bXAQAypfChFQQl9Db3Vy",
            "c2VUeXBlRW51bRIKCgZDVE5vbmUQABINCglDVFNlc3Npb24QARIOCgpDVFVu",
            "aXRUZXN0EAISEQoNQ1RTZWN0aW9uVGVzdBADEgwKCENUUmV2aWV3EARCJloY",
            "dmZfcHJvdG9idWYvc2VydmVyL2Jhc2ljqgIJTXNnLmJhc2ljYgZwcm90bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Msg.basic.PB_SessionTypeEnum), typeof(global::Msg.basic.PB_ProgressStatusEnum), typeof(global::Msg.basic.PB_LevelTypeEnum), typeof(global::Msg.basic.PB_SectionTypeEnum), typeof(global::Msg.basic.PB_SkipTypeEnum), typeof(global::Msg.basic.PB_ElementTypeEnum), typeof(global::Msg.basic.PB_ExpTypeEnum), typeof(global::Msg.basic.PB_CourseTypeEnum), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.basic.PB_AvatarTextData), global::Msg.basic.PB_AvatarTextData.Parser, new[]{ "avatar_id", "text", "tts_id", "slow_tts_id" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.basic.PB_CourseLearnPathParams), global::Msg.basic.PB_CourseLearnPathParams.Parser, new[]{ "course_id", "section_index", "unit_index", "level_index", "session_index", "level_type", "course_type" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  /// <summary>
  /// session类型
  /// </summary>
  public enum PB_SessionTypeEnum {
    [pbr::OriginalName("SesTNone")] SesTNone = 0,
    [pbr::OriginalName("SesTLesson")] SesTLesson = 1,
    [pbr::OriginalName("SesTLevelReview")] SesTLevelReview = 2,
    [pbr::OriginalName("SesTLexemePractice")] SesTLexemePractice = 3,
    [pbr::OriginalName("SesTUnitPractice")] SesTUnitPractice = 4,
    [pbr::OriginalName("SesTUnitReview")] SesTUnitReview = 5,
    [pbr::OriginalName("SesTUnitTest")] SesTUnitTest = 6,
    [pbr::OriginalName("SesTSectionTest")] SesTSectionTest = 7,
  }

  /// <summary>
  /// 进度状态类型
  /// </summary>
  public enum PB_ProgressStatusEnum {
    [pbr::OriginalName("PSNone")] PSNone = 0,
    /// <summary>
    /// 锁定
    /// </summary>
    [pbr::OriginalName("PSLock")] PSLock = 1,
    /// <summary>
    /// 进行中
    /// </summary>
    [pbr::OriginalName("PSRunning")] PSRunning = 2,
    /// <summary>
    /// 完成
    /// </summary>
    [pbr::OriginalName("PSPass")] PSPass = 3,
  }

  /// <summary>
  /// level类型
  /// </summary>
  public enum PB_LevelTypeEnum {
    [pbr::OriginalName("LTNone")] LTNone = 0,
    /// <summary>
    /// 五星关卡
    /// </summary>
    [pbr::OriginalName("LTStar")] LTStar = 1,
    /// <summary>
    /// 书本关卡
    /// </summary>
    [pbr::OriginalName("LTBook")] LTBook = 2,
    /// <summary>
    /// 耳机关卡
    /// </summary>
    [pbr::OriginalName("LTRadio")] LTRadio = 3,
    /// <summary>
    /// 宝箱关卡
    /// </summary>
    [pbr::OriginalName("LTBox")] LTBox = 4,
    /// <summary>
    /// 哑铃关卡
    /// </summary>
    [pbr::OriginalName("LTDumbBell")] LTDumbBell = 5,
    /// <summary>
    /// 奖杯关卡
    /// </summary>
    [pbr::OriginalName("LTCup")] LTCup = 6,
    /// <summary>
    /// 视频关卡
    /// </summary>
    [pbr::OriginalName("LTVideo")] LTVideo = 7,
    /// <summary>
    /// AUA关卡
    /// </summary>
    [pbr::OriginalName("LTAua")] LTAua = 8,
    /// <summary>
    /// warm up关卡
    /// </summary>
    [pbr::OriginalName("LTWarmUp")] LTWarmUp = 9,
    /// <summary>
    /// 角色扮演关卡
    /// </summary>
    [pbr::OriginalName("LTRolePlay")] LTRolePlay = 10,
  }

  /// <summary>
  /// section类型
  /// </summary>
  public enum PB_SectionTypeEnum {
    [pbr::OriginalName("SecTNone")] SecTNone = 0,
    /// <summary>
    /// 学习
    /// </summary>
    [pbr::OriginalName("SecTLearning")] SecTLearning = 1,
    /// <summary>
    /// 每日巩固练习
    /// </summary>
    [pbr::OriginalName("SecTDailyRefresh")] SecTDailyRefresh = 2,
  }

  /// <summary>
  /// 跳过类型
  /// </summary>
  public enum PB_SkipTypeEnum {
    [pbr::OriginalName("SkipTNone")] SkipTNone = 0,
    /// <summary>
    /// section
    /// </summary>
    [pbr::OriginalName("SkipTSection")] SkipTSection = 1,
    /// <summary>
    /// unit
    /// </summary>
    [pbr::OriginalName("SkipTUnit")] SkipTUnit = 2,
    /// <summary>
    /// level
    /// </summary>
    [pbr::OriginalName("SkipTLevel")] SkipTLevel = 3,
  }

  /// <summary>
  /// element类型
  /// </summary>
  public enum PB_ElementTypeEnum {
    [pbr::OriginalName("EleTNone")] EleTNone = 0,
    [pbr::OriginalName("EleTAvatarText")] EleTAvatarText = 1,
    [pbr::OriginalName("EleTQuestion")] EleTQuestion = 2,
    /// <summary>
    /// 旁白
    /// </summary>
    [pbr::OriginalName("EleTProse")] EleTProse = 3,
  }

  /// <summary>
  /// 经验值类型
  /// </summary>
  public enum PB_ExpTypeEnum {
    [pbr::OriginalName("ETNone")] ETNone = 0,
    /// <summary>
    /// 基础经验值
    /// </summary>
    [pbr::OriginalName("ETBasic")] ETBasic = 1,
    /// <summary>
    /// 复习经验值
    /// </summary>
    [pbr::OriginalName("ETReview")] ETReview = 2,
    /// <summary>
    /// 跳关经验值
    /// </summary>
    [pbr::OriginalName("ETJump")] ETJump = 3,
  }

  /// <summary>
  /// course类型
  /// </summary>
  public enum PB_CourseTypeEnum {
    [pbr::OriginalName("CTNone")] CTNone = 0,
    [pbr::OriginalName("CTSession")] CTSession = 1,
    [pbr::OriginalName("CTUnitTest")] CTUnitTest = 2,
    [pbr::OriginalName("CTSectionTest")] CTSectionTest = 3,
    [pbr::OriginalName("CTReview")] CTReview = 4,
  }

  #endregion

  #region Messages
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_AvatarTextData : pb::IMessage<PB_AvatarTextData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_AvatarTextData> _parser = new pb::MessageParser<PB_AvatarTextData>(() => new PB_AvatarTextData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_AvatarTextData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.basic.CourseReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_AvatarTextData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_AvatarTextData(PB_AvatarTextData other) : this() {
      avatar_id_ = other.avatar_id_;
      text_ = other.text_;
      tts_id_ = other.tts_id_;
      slow_tts_id_ = other.slow_tts_id_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_AvatarTextData Clone() {
      return new PB_AvatarTextData(this);
    }

    /// <summary>Field number for the "avatar_id" field.</summary>
    public const int avatar_idFieldNumber = 1;
    private long avatar_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long avatar_id {
      get { return avatar_id_; }
      set {
        avatar_id_ = value;
      }
    }

    /// <summary>Field number for the "text" field.</summary>
    public const int textFieldNumber = 2;
    private string text_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string text {
      get { return text_; }
      set {
        text_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "tts_id" field.</summary>
    public const int tts_idFieldNumber = 3;
    private long tts_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long tts_id {
      get { return tts_id_; }
      set {
        tts_id_ = value;
      }
    }

    /// <summary>Field number for the "slow_tts_id" field.</summary>
    public const int slow_tts_idFieldNumber = 4;
    private long slow_tts_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long slow_tts_id {
      get { return slow_tts_id_; }
      set {
        slow_tts_id_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_AvatarTextData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_AvatarTextData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (avatar_id != other.avatar_id) return false;
      if (text != other.text) return false;
      if (tts_id != other.tts_id) return false;
      if (slow_tts_id != other.slow_tts_id) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (avatar_id != 0L) hash ^= avatar_id.GetHashCode();
      if (text.Length != 0) hash ^= text.GetHashCode();
      if (tts_id != 0L) hash ^= tts_id.GetHashCode();
      if (slow_tts_id != 0L) hash ^= slow_tts_id.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (avatar_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(avatar_id);
      }
      if (text.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(text);
      }
      if (tts_id != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(tts_id);
      }
      if (slow_tts_id != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(slow_tts_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (avatar_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(avatar_id);
      }
      if (text.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(text);
      }
      if (tts_id != 0L) {
        output.WriteRawTag(24);
        output.WriteInt64(tts_id);
      }
      if (slow_tts_id != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(slow_tts_id);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (avatar_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(avatar_id);
      }
      if (text.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(text);
      }
      if (tts_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(tts_id);
      }
      if (slow_tts_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(slow_tts_id);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_AvatarTextData other) {
      if (other == null) {
        return;
      }
      if (other.avatar_id != 0L) {
        avatar_id = other.avatar_id;
      }
      if (other.text.Length != 0) {
        text = other.text;
      }
      if (other.tts_id != 0L) {
        tts_id = other.tts_id;
      }
      if (other.slow_tts_id != 0L) {
        slow_tts_id = other.slow_tts_id;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            avatar_id = input.ReadInt64();
            break;
          }
          case 18: {
            text = input.ReadString();
            break;
          }
          case 24: {
            tts_id = input.ReadInt64();
            break;
          }
          case 32: {
            slow_tts_id = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            avatar_id = input.ReadInt64();
            break;
          }
          case 18: {
            text = input.ReadString();
            break;
          }
          case 24: {
            tts_id = input.ReadInt64();
            break;
          }
          case 32: {
            slow_tts_id = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// course结算请求数据
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_CourseLearnPathParams : pb::IMessage<PB_CourseLearnPathParams>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_CourseLearnPathParams> _parser = new pb::MessageParser<PB_CourseLearnPathParams>(() => new PB_CourseLearnPathParams());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_CourseLearnPathParams> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.basic.CourseReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_CourseLearnPathParams() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_CourseLearnPathParams(PB_CourseLearnPathParams other) : this() {
      course_id_ = other.course_id_;
      section_index_ = other.section_index_;
      unit_index_ = other.unit_index_;
      level_index_ = other.level_index_;
      session_index_ = other.session_index_;
      level_type_ = other.level_type_;
      course_type_ = other.course_type_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_CourseLearnPathParams Clone() {
      return new PB_CourseLearnPathParams(this);
    }

    /// <summary>Field number for the "course_id" field.</summary>
    public const int course_idFieldNumber = 1;
    private long course_id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long course_id {
      get { return course_id_; }
      set {
        course_id_ = value;
      }
    }

    /// <summary>Field number for the "section_index" field.</summary>
    public const int section_indexFieldNumber = 2;
    private int section_index_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int section_index {
      get { return section_index_; }
      set {
        section_index_ = value;
      }
    }

    /// <summary>Field number for the "unit_index" field.</summary>
    public const int unit_indexFieldNumber = 3;
    private int unit_index_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int unit_index {
      get { return unit_index_; }
      set {
        unit_index_ = value;
      }
    }

    /// <summary>Field number for the "level_index" field.</summary>
    public const int level_indexFieldNumber = 4;
    private int level_index_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int level_index {
      get { return level_index_; }
      set {
        level_index_ = value;
      }
    }

    /// <summary>Field number for the "session_index" field.</summary>
    public const int session_indexFieldNumber = 5;
    private int session_index_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int session_index {
      get { return session_index_; }
      set {
        session_index_ = value;
      }
    }

    /// <summary>Field number for the "level_type" field.</summary>
    public const int level_typeFieldNumber = 7;
    private global::Msg.basic.PB_LevelTypeEnum level_type_ = global::Msg.basic.PB_LevelTypeEnum.LTNone;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_LevelTypeEnum level_type {
      get { return level_type_; }
      set {
        level_type_ = value;
      }
    }

    /// <summary>Field number for the "course_type" field.</summary>
    public const int course_typeFieldNumber = 8;
    private global::Msg.basic.PB_CourseTypeEnum course_type_ = global::Msg.basic.PB_CourseTypeEnum.CTNone;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_CourseTypeEnum course_type {
      get { return course_type_; }
      set {
        course_type_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_CourseLearnPathParams);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_CourseLearnPathParams other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (course_id != other.course_id) return false;
      if (section_index != other.section_index) return false;
      if (unit_index != other.unit_index) return false;
      if (level_index != other.level_index) return false;
      if (session_index != other.session_index) return false;
      if (level_type != other.level_type) return false;
      if (course_type != other.course_type) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (course_id != 0L) hash ^= course_id.GetHashCode();
      if (section_index != 0) hash ^= section_index.GetHashCode();
      if (unit_index != 0) hash ^= unit_index.GetHashCode();
      if (level_index != 0) hash ^= level_index.GetHashCode();
      if (session_index != 0) hash ^= session_index.GetHashCode();
      if (level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) hash ^= level_type.GetHashCode();
      if (course_type != global::Msg.basic.PB_CourseTypeEnum.CTNone) hash ^= course_type.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (course_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(course_id);
      }
      if (section_index != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(section_index);
      }
      if (unit_index != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(unit_index);
      }
      if (level_index != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(level_index);
      }
      if (session_index != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(session_index);
      }
      if (level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) {
        output.WriteRawTag(56);
        output.WriteEnum((int) level_type);
      }
      if (course_type != global::Msg.basic.PB_CourseTypeEnum.CTNone) {
        output.WriteRawTag(64);
        output.WriteEnum((int) course_type);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (course_id != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(course_id);
      }
      if (section_index != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(section_index);
      }
      if (unit_index != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(unit_index);
      }
      if (level_index != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(level_index);
      }
      if (session_index != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(session_index);
      }
      if (level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) {
        output.WriteRawTag(56);
        output.WriteEnum((int) level_type);
      }
      if (course_type != global::Msg.basic.PB_CourseTypeEnum.CTNone) {
        output.WriteRawTag(64);
        output.WriteEnum((int) course_type);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (course_id != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(course_id);
      }
      if (section_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(section_index);
      }
      if (unit_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(unit_index);
      }
      if (level_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(level_index);
      }
      if (session_index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(session_index);
      }
      if (level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) level_type);
      }
      if (course_type != global::Msg.basic.PB_CourseTypeEnum.CTNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) course_type);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_CourseLearnPathParams other) {
      if (other == null) {
        return;
      }
      if (other.course_id != 0L) {
        course_id = other.course_id;
      }
      if (other.section_index != 0) {
        section_index = other.section_index;
      }
      if (other.unit_index != 0) {
        unit_index = other.unit_index;
      }
      if (other.level_index != 0) {
        level_index = other.level_index;
      }
      if (other.session_index != 0) {
        session_index = other.session_index;
      }
      if (other.level_type != global::Msg.basic.PB_LevelTypeEnum.LTNone) {
        level_type = other.level_type;
      }
      if (other.course_type != global::Msg.basic.PB_CourseTypeEnum.CTNone) {
        course_type = other.course_type;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            course_id = input.ReadInt64();
            break;
          }
          case 16: {
            section_index = input.ReadInt32();
            break;
          }
          case 24: {
            unit_index = input.ReadInt32();
            break;
          }
          case 32: {
            level_index = input.ReadInt32();
            break;
          }
          case 40: {
            session_index = input.ReadInt32();
            break;
          }
          case 56: {
            level_type = (global::Msg.basic.PB_LevelTypeEnum) input.ReadEnum();
            break;
          }
          case 64: {
            course_type = (global::Msg.basic.PB_CourseTypeEnum) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            course_id = input.ReadInt64();
            break;
          }
          case 16: {
            section_index = input.ReadInt32();
            break;
          }
          case 24: {
            unit_index = input.ReadInt32();
            break;
          }
          case 32: {
            level_index = input.ReadInt32();
            break;
          }
          case 40: {
            session_index = input.ReadInt32();
            break;
          }
          case 56: {
            level_type = (global::Msg.basic.PB_LevelTypeEnum) input.ReadEnum();
            break;
          }
          case 64: {
            course_type = (global::Msg.basic.PB_CourseTypeEnum) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
