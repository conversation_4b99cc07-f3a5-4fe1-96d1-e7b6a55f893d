/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class ClozeOption : GComponent
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "ClozeOption";
        public static string url => "ui://cmoz5osjqit526";

        public ClozeOptionBtn optionBtn;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(ClozeOption));
        }

        public override void ConstructFromXML(XML xml)
        {
            optionBtn = GetChildAt(1) as ClozeOptionBtn;
        }
        public override void Dispose()
        {
            optionBtn = null;

            base.Dispose();
        }
    }
}