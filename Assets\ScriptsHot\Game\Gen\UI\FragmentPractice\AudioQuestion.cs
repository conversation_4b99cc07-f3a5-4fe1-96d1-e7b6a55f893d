/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class AudioQuestion : AFragQuestion
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "AudioQuestion";
        public static string url => "ui://cmoz5osjz7rm34";

        public BtnBlueAudio btnPlay;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(AudioQuestion));
        }

        public override void ConstructFromXML(XML xml)
        {
            btnPlay = GetChildAt(0) as BtnBlueAudio;
        }
        public override void Dispose()
        {
            btnPlay = null;

            base.Dispose();
        }
    }
}