/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.LearnPath
{
    public partial class SelectStoryCom : ChapterItem
    {
        public static string pkgName => "LearnPath";
        public static string comName => "SelectStoryCom";
        public static string url => "ui://94jvztftcby3xxx7o";

        public GTextField tfContent;
        public GButton btnStory;
        public GTextField tfBtn;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(SelectStoryCom));
        }

        public override void ConstructFromXML(XML xml)
        {
            tfContent = GetChildAt(1) as GTextField;
            btnStory = GetChildAt(2) as GButton;
            tfBtn = GetChildAt(3) as GTextField;

            SetMultiLanguageInChildren();
        }
        public override void Dispose()
        {
            tfContent = null;
            btnStory = null;
            tfBtn = null;

            base.Dispose();
        }
        public void SetMultiLanguageInChildren()
        {
            this.tfContent.SetKey("ui_LearnPath_Explore");  // "Explore a different story experience"
            this.tfBtn.SetKey("ui_LernPath_SelectStory");  // "Select a story"
        }
    }
}