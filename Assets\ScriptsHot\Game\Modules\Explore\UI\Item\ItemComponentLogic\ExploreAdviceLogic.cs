﻿
using FairyGUI;
using ScriptsHot.Game.Modules.Common;
using ScriptsHot.Game.Modules.Explore;
using UnityEngine;

namespace UIBind.Explore.Item.ItemComponentLogic
{
    /// <summary>
    /// advice
    /// </summary>
    public class ExploreAdviceLogic:ItemComponentLogicBase
    {
        public ExploreAdviceItem Com;
        
        public TextFieldExtension TfOrigin =>  Com.tfOrigin as TextFieldExtension;
        
        private string _bubbleId = string.Empty;
        
        private ExploreCellBase _item;
        
        private string _str;
        
        private long _dialogId_for_Dot;
        private long _round;
        private bool _effecting;

        public override void Init()
        {
            base.Init();
            Com.txtName.SetKey("ui_explore_title_advice");
            TfOrigin.SetFormat(Color.black, 32);
            Com.btnNotice.com.onClick.Add(OnNotice);
            BeforeUIPassManager.AddRenderer(Com.back.displayObject);
        }

        public void SetInfo(long dialogId,long round)
        {
            _dialogId_for_Dot = dialogId;
            _round = round;
        }
        
        public void SetParentItem(ExploreCellBase item)
        {
            _item = item;
        }
        
        public void ShowTxt(string bubbleId,string str)
        {
            _str = str;
             // Debug.LogError("ShowTxt bubbleId:" + str);
            _bubbleId = bubbleId;
            
            ExploreNewWordData newWordData = new ExploreNewWordData();
            newWordData.ui = _ui;
            newWordData.avatarId = 1;
            newWordData.dialogId = _controller.CurEntityId;
            newWordData.bubbleId = bubbleId;
            _translateWordComponent.SetData(newWordData);

            var textContent = new RichContent()
            {
                Content = _str,
                IsLast = true,
            };
            TfOrigin.Reset();
            TfOrigin.OnClickNewWord += _translateWordComponent.ReqNewWordTrans;
            TfOrigin.AppendContent(textContent).Display(ShowMode.Normal);
            
            Com.btnNotice.ctrl.selectedPage = "demo";
        }

        public void Show(string txt,long taskId,long dialogue_id,long roundId)
        {
            // APPEAR_EXPLORE_EXAMPLE dotData = new APPEAR_EXPLORE_EXAMPLE();
            // dotData.example_text = txt;
            // dotData.task_id = taskId;
            // if(dialogue_id > 0)
            //     dotData.dialogue_id = dialogue_id.ToString();
            // dotData.dialogue_round = roundId;
            // DataDotMgr.Collect(dotData);
            
            // _controller.ShowBulbActive();
        }
        
        public void SetEffectVisible(bool visibleValue,bool isClick)
        {
            if (!isClick)
            {
                Com.com.alpha = 1;
                Com.com.visible = visibleValue;
                return;
            }
            
            _effecting = true;
            int fadeValue = visibleValue ? 1 : 0;
            Com.com.alpha = visibleValue ? 0 : 1;
            Com.com.visible = true;
            GTween.Kill(Com.com);
            Com.com.TweenFade(fadeValue, ExploreConst.CellAphla).OnComplete(() =>
            {
                _effecting = false;
                Com.com.visible = visibleValue;
            });
        }
        
        private void OnNotice()
        {
            _controller.OnBulbClick(BulbType.Scaffold,true);
        }
    }
}