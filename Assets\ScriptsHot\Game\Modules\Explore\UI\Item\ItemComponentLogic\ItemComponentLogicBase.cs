﻿using System.Collections.Generic;
using FairyGUI;
using ScriptsHot.Game.Modules.Setting;

namespace UIBind.Explore.Item.ItemComponentLogic
{
    public class ItemComponentLogicBase:ComponentObject,IComponetOwner
    {
        protected ExploreController _controller;

        protected ExploreTranslWordComponent _translateWordComponent;

        protected GComponent _ui;
        public virtual void Init()
        {
            _translateWordComponent = this.AddComponent<ExploreTranslWordComponent>() as ExploreTranslWordComponent;
            InitComponents();
        }

        public void SetController(ExploreController value)
        {
            _controller = value;
        }
        
        public void SetUI(GComponent ui)
        {
            _ui = ui;
            UIReady();
        }
        
        public virtual void UIReady()
        {

        }
        public GComponent GetUI()
        {
            return _ui;
        }

        public void AddEvent()
        {
        }

        public void UnEvent()
        {
        }
    }
}