﻿using System;
using System.Collections.Generic;
using FairyGUI;
using UnityEngine;
using Random = System.Random;

public class SceneLoadingUI : BaseUI<UIBind.Scene.SceneLoadingPanel>
{
    public SceneLoadingUI(string name) : base(name) { }

    public override string uiLayer => UILayerConsts.Loading;

    protected override bool isFullScreen => true;

    public override EUIDeleteRule deleteRule => EUIDeleteRule.Never;
    
    private static readonly string GUIDE_CONTENT_DEFAULT1 = "Let's Flow! Try some new tasks for medal rewards.";
    private static readonly string GUIDE_CONTENT_DEFAULT2 = "Welcome! Let's Flow into new challenges!";
    private static readonly string GUIDE_CONTENT_DEFAULT3 = "Greetings! Dive into tasks, Let's Flow!";
    private static readonly string GUIDE_CONTENT_DEFAULT4 = "Let's Flow in new scenarios!";
    private static readonly string GUIDE_CONTENT_DEFAULT5 = "Ready for a new challenge? Let's Flow!";

    override protected void OnInit(GComponent uiCom)
    {
        
    }

    protected override void OnShow()
    {

        List<string> contentList = new List<string>()
        {
            GUIDE_CONTENT_DEFAULT1, GUIDE_CONTENT_DEFAULT2, GUIDE_CONTENT_DEFAULT3, GUIDE_CONTENT_DEFAULT4, GUIDE_CONTENT_DEFAULT5
        };
        Random rd = new Random();
        int total = contentList.Count + 2;
        var idx = rd.Next(0, total);
        LoginController loginController = GetController<LoginController>(ModelConsts.Login);
        if (loginController.IsNewPlayer)
        {
            ui.ctrlTips.selectedIndex = 0;
            var mtUI= UIManager.instance.GetUI<MultiTabFrameworkUI>(UIConsts.MultiTabHomepage);//注意 这时的 this._mtUI可能还未赋值不要用
            if (mtUI.InitalTabIdx == 0)
            {
                ui.tfText.SetKey("ui_loading_first_level");
            }
            else
            {
                ui.tfText.SetKey("ui_loading_explore_level");
            }
        }
        else
        {
            //如果是前五个就随机播放单词 如果是后两个就是 翻译或者播放提示
            if (idx < contentList.Count)
            {
                ui.tfText.text = contentList[idx];
                ui.ctrlTips.selectedPage = "PlayText";
            }
            //随到最后
            else if(idx == total - 2)
            {
                ui.ctrlTips.selectedPage = "PlayTips"; 
            }
            else if(idx == total - 1)
            {
                ui.ctrlTips.selectedPage = "TranslateTips"; 
            }   
        }
  
    }

    protected override void OnHide()
    {
    }

}

