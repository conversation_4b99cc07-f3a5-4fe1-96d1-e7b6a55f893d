using System.Collections;
using System.Collections.Generic;
using System;
using UnityEngine;

using Agora.Rtc;
using Google.Protobuf;

namespace ScriptsHot.Game.Modules.AgoraRtc
{
    public class RtcEngineUserEventHandler : IRtcEngineEventHandler
    {
        // 你可以在这里添加构造参数，传递UI或业务层引用

        public override void OnError(int err, string msg)
        {
            // 处理错误
        }

        public override void OnJoinChannelSuccess(RtcConnection connection, int elapsed)
        {
            // 处理加入频道成功
            Debug.Log("OnJoinChannelSuccess");
        }

        public override void OnRejoinChannelSuccess(RtcConnection connection, int elapsed)
        {
            // 处理重连成功
        }

        public override void OnLeaveChannel(RtcConnection connection, RtcStats stats)
        {
            // 处理离开频道
        }

        public override void OnClientRoleChanged(RtcConnection connection, CLIENT_ROLE_TYPE oldRole, CLIENT_ROLE_TYPE newRole, ClientRoleOptions newRoleOptions)
        {
            // 处理角色变化
        }

        public override void OnUserJoined(RtcConnection connection, uint uid, int elapsed)
        {
            // 处理远端用户加入
            Debug.Log("OnUserJoined  uid:" + uid);
            //todo-tanglei:未考虑反复join 二次join的问题； 且只适用于首次join
            VoiceChatManager.instance.RecvOtherJoinChannelMsg();
        }

        public override void OnUserOffline(RtcConnection connection, uint uid, USER_OFFLINE_REASON_TYPE reason)
        {
            // 处理远端用户离线

            //todo-tanglei
            // 如何区分是自己 离线 还是对方离线？
            // 是否根据 离线原因 区分处理？USER_OFFLINE_REASON_TYPE

            Debug.Log("OnUserOffline uid:" + uid);
            //todo-tanglei 弹窗提示
            //等待 UI交互后继续处理
            Notifier.instance.SendNotification(NotifyConsts.VoiceChat_RecvOtherEndExitChannel);
            
        }

        public override void OnRtcStats(RtcConnection connection, RtcStats stats)
        {
            // 处理统计信息
        }

        public override void OnLocalAudioStats(RtcConnection connection, LocalAudioStats stats)
        {
            // 处理本地音频统计
        }

        public override void OnRemoteAudioStats(RtcConnection connection, RemoteAudioStats stats)
        {
            // 处理远端音频统计
        }

        public override void OnAudioVolumeIndication(RtcConnection connection, AudioVolumeInfo[] speakers, uint speakerNumber, int totalVolume)
        {
            // 处理音量指示

        }
    }

    internal class AudioFrameObserver : IAudioFrameObserver
    {
        
        private AudioParams _audioParams;


        internal static float[] ConvertByteToFloat16(byte[] byteArray)
        {
            var floatArray = new float[byteArray.Length / 2];
            for (var i = 0; i < floatArray.Length; i++)
            {
                floatArray[i] = BitConverter.ToInt16(byteArray, i * 2) / 32768f; // -Int16.MinValue
            }

            return floatArray;
        }

        internal AudioFrameObserver() { }
        //internal AudioFrameObserver(ProcessAudioRawData agoraAudioRawData)
        //{
        //    _agoraAudioRawData = agoraAudioRawData;

        //}

        public override bool OnRecordAudioFrame(string channelId, AudioFrame audioFrame)
        {
            //不是两个人都进入场景时，不自动上报语音处理
            //20250820 debug期间正克 提的尝试防止后排报错量激增的防御动作
            if (VoiceChatManager.instance.Status == VoiceChatRunningStatus.ChattingBothJoin)
            {
                ByteString strByte = ByteString.CopyFrom(audioFrame.RawBuffer);
            
                VoiceChatManager.instance.CurNetStrategy.SendRawAudioUp(strByte);
            
                //清空
                Array.Clear(audioFrame.RawBuffer, 0, audioFrame.RawBuffer.Length);
            }

    
            return true;
        }
        
        
        //AudioFrame的rawBuffer 是纯PCM数据
        public override bool OnPlaybackAudioFrame(string channelId, AudioFrame audioFrame)
        {
            AudioStreamPlayer sP = GSoundManager.instance.AudioStreamPlayer;
            if (sP)
            {
               
                sP.AddAudioData(audioFrame.RawBuffer,false);
                if (sP.IsBufferReady())
                {
                    //有防止重复start额机制
                    sP.StartAudioSlient();
                }
            }
            else
            {
                Debug.LogWarning("ASP:: OnPlaybackAudioFrame AudioStreamPlayer is null");
            }
            
            Array.Clear(audioFrame.RawBuffer, 0, audioFrame.RawBuffer.Length);
            
            //var floatArray = ConvertByteToFloat16(audioFrame.RawBuffer);

            //lock (_agoraAudioRawData._audioBuffer)
            //{
            //    _agoraAudioRawData._audioBuffer.Put(floatArray);
            //    _agoraAudioRawData._writeCount += floatArray.Length;
            //    _agoraAudioRawData._count++;
            //}

            ////make audoFrame rawdata empty so sdk will not playback any sound
            //Array.Clear(audioFrame.RawBuffer, 0, audioFrame.RawBuffer.Length);
            //System.Runtime.InteropServices.Marshal.Copy(audioFrame.RawBuffer, 0, audioFrame.buffer, audioFrame.RawBuffer.Length);
            return true;
        }

        public override bool OnPlaybackAudioFrameBeforeMixing(string channel_id,
                                                        uint uid,
                                                        AudioFrame audio_frame)
        {
            Debug.Log("OnPlaybackAudioFrameBeforeMixing-----------");
            return false;
        }

        public override bool OnPlaybackAudioFrameBeforeMixing(string channel_id,
                                                        string uid,
                                                        AudioFrame audio_frame)
        {
            Debug.Log("OnPlaybackAudioFrameBeforeMixing2-----------");
            return false;
        }
    }
}