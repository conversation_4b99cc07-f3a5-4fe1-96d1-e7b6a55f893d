﻿using System;
using System.Threading;
using Modules.DataDot;
using Msg.basic;
using Msg.course;
using ScriptsHot.Game.Modules.MainPath;

namespace UIBind.MainPath3D
{
    public partial class CompNodeStart
    {
        private MainPathController PathController =>
            ControllerManager.instance.GetController<MainPathController>(ModelConsts.MainPath);
        private StaminaController StaminaController =>
            ControllerManager.instance.GetController<StaminaController>(ModelConsts.Stamina);
        private MainModel MainModel => ModelManager.instance.GetModel<MainModel>(ModelConsts.Main);
        
        public CompNodeStart()
        {
            onAddedToStage.Add(OnAddToStage);
            onRemovedFromStage.Add(OnRemoveFromStage);
        }
        
        private void OnAddToStage()
        {
            btnStart.com.onClick.Add(OnClickStart);
            btnJump.onClick.Add(OnClickJumpOver);
            btnJump.SetKey("ui_onboard_desc_skip");
        }

        private void OnRemoveFromStage()
        {
            btnStart.com.onClick.Remove(OnClickStart);
            btnJump.onClick.Remove(OnClickJumpOver);
        }

        private MainPathSectionData.MainPathNodesStruct _curNodeData;
        private PB_SessionData _curSessionData;
        private Action _startCallback;
        private Action _jumpCallback;
        
        public void ShowStartTips(MainPathSectionData.MainPathNodesStruct nodeData,Action startCallback,Action jumpCallback)
        {
            _curNodeData = nodeData;
            _jumpCallback = jumpCallback;
            _startCallback = startCallback;
            AppearDot();

            _curSessionData = nodeData.NodeData.GetNextSessionData();
            pos.selectedIndex = nodeData.Position;
            title1.text = nodeData.NodeData.ServerLevelData.level_title;
            bool isUnlimitedStamina = GameEntry.MainC.MainModel.IsUnlimitedStamina();
            btnStart.com.GetController("isUnlimited").selectedIndex = isUnlimitedStamina ? 1 : 0;
            if (nodeData.NodeData.ServerLevelData.status == PB_ProgressStatusEnum.PSPass)
            {
                tfExp.SetVar("exp",_curSessionData.exp.ToString()).FlushVars();
                btnStart.tfStart.SetKey("ui_main_path_review_start");
                state.selectedIndex = 0;
            }
            else if (nodeData.NodeData.ServerLevelData.status == PB_ProgressStatusEnum.PSLock)
            {
                state.selectedIndex = 2;
                tfUnlock.SetKey("ui_main_path_btn_unlock");
            }
            else
            {
                state.selectedIndex =
                    nodeData.NodeData.ServerLevelData.level_type == PB_LevelTypeEnum.LTRadio &&
                    nodeData.NodeData.ServerLevelData.status == PB_ProgressStatusEnum.PSRunning
                        ? 1 : 0;
                tfExp.SetVar("exp",nodeData.NodeData.ServerLevelData.level_exp_data.practice_exp.ToString()).FlushVars();
                btnStart.tfStart.SetKey("common_start");
            }
            tfReward.SetKey("ui_chapter_rewards");
            btnStart.tfCost.SetVar("cost", (_curSessionData?.stamina / 20 ?? 0).ToString()).FlushVars();
            btnStart.grpExp.EnsureBoundsCorrect();
            btnStart.grpRight.EnsureBoundsCorrect();
            btnStart.grp.EnsureBoundsCorrect();
            
            grpLock.EnsureBoundsCorrect();
            grp.EnsureBoundsCorrect();
            grp0.EnsureBoundsCorrect();
            grpAll.EnsureBoundsCorrect();
        }

        public void HideStartTips()
        {
            visible = false;
        }
        
        private void OnClickStart()
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Pop);
            
            if (_curNodeData.NodeData.ServerLevelData.status == PB_ProgressStatusEnum.PSPass)
            {
                DotClickStartTipsReview dot = new DotClickStartTipsReview();
                dot.title = _curNodeData.NodeData.ServerLevelData.level_title;
                dot.information = _curNodeData.NodeData.ServerLevelData.level_subtitle;
                dot.reward = _curNodeData.NodeData.ServerLevelData.level_exp_data.practice_exp.ToString();
                dot.status = _curNodeData.NodeData.ServerLevelData.status == PB_ProgressStatusEnum.PSLock
                    ? "lock"
                    : "unlock";
                DataDotMgr.Collect(dot);
            }
            else
            {
                DotClickStartTipsStart dot = new DotClickStartTipsStart();
                dot.title = _curNodeData.NodeData.ServerLevelData.level_title;
                dot.information = _curNodeData.NodeData.ServerLevelData.level_subtitle;
                dot.reward = _curNodeData.NodeData.ServerLevelData.level_exp_data.practice_exp.ToString();
                dot.status = _curNodeData.NodeData.ServerLevelData.status == PB_ProgressStatusEnum.PSLock
                    ? "lock"
                    : "unlock";
                DataDotMgr.Collect(dot);   
            }
            
            _startCallback?.Invoke();
            
            if (!StaminaController.IsEnough(_curSessionData.stamina) && !MainModel.IsUnlimitedStamina())
            {
                Notifier.instance.SendNotification(NotifyConsts.OnShowStaminaPopupEvent);
                return;
            }

            PathController.EnterStage(_curNodeData, _curSessionData,
                _curNodeData.NodeData.ServerLevelData.status == PB_ProgressStatusEnum.PSPass);
        }

        //跳听力关
        private void OnClickJumpOver()
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Pop);
            
            DotClickStartTipsSkip dot = new DotClickStartTipsSkip();
            dot.title = _curNodeData.NodeData.ServerLevelData.level_title;
            dot.information = _curNodeData.NodeData.ServerLevelData.level_subtitle;
            dot.reward = _curNodeData.NodeData.ServerLevelData.level_exp_data.practice_exp.ToString();
            dot.status = _curNodeData.NodeData.ServerLevelData.status == PB_ProgressStatusEnum.PSLock
                ? "lock"
                : "unlock";
            DataDotMgr.Collect(dot);
            
            _jumpCallback?.Invoke();
            MainPathSectionData.MainPathNodesStruct nodeData = _curNodeData;
            MainPathSectionData sectionData = PathController.Model.FocusData.SectionData;
            if (nodeData.NodeData.ServerLevelData.level_type == PB_LevelTypeEnum.LTRadio)
                PathController.RequestSkipCourse(sectionData.ID, nodeData.UnitData.ServerUnitData.unit_index, 
                    nodeData.NodeData.ServerLevelData.level_index);
        }

        private void AppearDot()
        {
            DotAppearStartTips dot = new DotAppearStartTips();
            dot.title = _curNodeData.NodeData.ServerLevelData.level_title;
            dot.information = _curNodeData.NodeData.ServerLevelData.level_subtitle;
            dot.reward = _curNodeData.NodeData.ServerLevelData.level_exp_data.practice_exp.ToString();
            dot.status = _curNodeData.NodeData.ServerLevelData.status == PB_ProgressStatusEnum.PSLock
                ? "lock"
                : "unlock";
            DataDotMgr.Collect(dot);
        }
    }
}