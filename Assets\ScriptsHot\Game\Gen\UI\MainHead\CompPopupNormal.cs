/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.MainHead
{
    public partial class CompPopupNormal : UIBindT
    {
        public override string pkgName => "MainHead";
        public override string comName => "CompPopupNormal";

        public Controller discount;
        public GImage arrow;
        public GList heartList;
        public GRichTextField tfTime;
        public CompRefill compRefill;
        public CompPractice compPractice;
        public CompUnlimit compUnlimited;
        public GGroup grp;
        public GComponent discountNode;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            discount = com.GetControllerAt(0);
            arrow = (GImage)com.GetChildAt(0);
            heartList = (GList)com.GetChildAt(2);
            tfTime = (GRichTextField)com.GetChildAt(3);
            compRefill = new CompRefill();
            compRefill.Construct(com.GetChildAt(4).asCom);
            compPractice = new CompPractice();
            compPractice.Construct(com.GetChildAt(5).asCom);
            compUnlimited = new CompUnlimit();
            compUnlimited.Construct(com.GetChildAt(6).asCom);
            grp = (GGroup)com.GetChildAt(7);
            discountNode = (GComponent)com.GetChildAt(8);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            discount = null;
            arrow = null;
            heartList = null;
            tfTime = null;
            compRefill.Dispose();
            compRefill = null;
            compPractice.Dispose();
            compPractice = null;
            compUnlimited.Dispose();
            compUnlimited = null;
            grp = null;
            discountNode = null;
        }
    }
}