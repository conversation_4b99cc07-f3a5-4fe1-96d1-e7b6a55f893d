/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.MainHead
{
    public partial class CompPaywall : UIBindT
    {
        public override string pkgName => "MainHead";
        public override string comName => "CompPaywall";

        public Controller state;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            state = com.GetControllerAt(0);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            state = null;
        }
    }
}