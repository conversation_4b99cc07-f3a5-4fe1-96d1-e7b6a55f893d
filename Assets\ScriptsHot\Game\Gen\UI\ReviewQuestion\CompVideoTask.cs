/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.ReviewQuestion
{
    public partial class CompVideoTask : GComponent
    {
        public static string pkgName => "ReviewQuestion";
        public static string comName => "CompVideoTask";
        public static string url => "ui://xlh8p6j0hgirg";

        public GTextField tfNum;
        public GList listTask;
        public Transition show;
        public Transition hide;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(CompVideoTask));
        }

        public override void ConstructFromXML(XML xml)
        {
            tfNum = GetChildAt(1) as GTextField;
            listTask = GetChildAt(2) as GList;
            show = GetTransitionAt(0);
            hide = GetTransitionAt(1);
        }
        public override void Dispose()
        {
            tfNum = null;
            listTask = null;
            show = null;
            hide = null;

            base.Dispose();
        }
    }
}