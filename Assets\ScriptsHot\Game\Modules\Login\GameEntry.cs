using System.Collections;
using System.Collections.Generic;
using ScriptsHot.Game.Modules.MainPath;
using ScriptsHot.Game.Modules.Shop;
using ScriptsHot.Game.Modules.Sign;
using ScriptsHot.Game.Modules.WebView;
using UnityEngine;

public class GameEntry
{
    private static MainController mainC;
    private static LoginController loginC;
    private static WebViewController webviewC;
    private static WidgetController widgetC;
    private static SceneController sceneC;
    private static SignController signC;
    private static MainPathController mainPathC;
    private static HomepageController homepageC;    
    private static NoticeController noticeC;  
    private static ShopController shopC; 
    
    
    public static ShopController ShopC
    {
        get
        {
            if (shopC == null)
            {
                shopC = ControllerManager.instance.GetController<ShopController>(ModelConsts.Shop);
            }
            return shopC;
        }
    }  
    
    public static NoticeController NoticeC
    {
        get
        {
            if (noticeC == null)
            {
                noticeC = ControllerManager.instance.GetController<NoticeController>(ModelConsts.Notice);
            }
            return noticeC;
        }
    }  
    
    public static HomepageController HomepageC
    {
        get
        {
            if (homepageC == null)
            {
                homepageC = ControllerManager.instance.GetController<HomepageController>(ModelConsts.Homepage);
            }
            return homepageC;
        }
    }  
    
    
    public static MainController MainC
    {
        get
        {
            if (mainC == null)
            {
                mainC = ControllerManager.instance.GetController<MainController>(ModelConsts.Main);
            }
            return mainC;
        }
    } 
    public static MainPathController MainPathC
    {
        get
        {
            if (mainPathC == null)
            {
                mainPathC = ControllerManager.instance.GetController<MainPathController>(ModelConsts.MainPath);
            }
            return mainPathC;
        }
    }  
    
    public static SignController SignC
    {
        get
        {
            if (signC == null)
            {
                signC = ControllerManager.instance.GetController<SignController>(ModelConsts.Sign);
            }
            return signC;
        }
    }   
    
    public static SceneController SceneC
    {
        get
        {
            if (sceneC == null)
            {
                sceneC = ControllerManager.instance.GetController<SceneController>(ModelConsts.Scene);
            }
            return sceneC;
        }
    }    
    
    public static LoginController LoginC
    {
        get
        {
            if (loginC == null)
            {
                loginC = ControllerManager.instance.GetController(ModelConsts.Login) as LoginController;
            }
            return loginC;
        }
    }
    public static WebViewController WebviewC
    {
        get
        {
            if (webviewC == null)
            {
                webviewC = ControllerManager.instance.GetController(ModelConsts.WebView) as WebViewController;
            }
            return webviewC;
        }
    }    
    
    public static WidgetController WidgetC
    {
        get
        {
            if (widgetC == null)
            {
                widgetC = ControllerManager.instance.GetController(ModelConsts.Widget) as WidgetController;
            }
            return widgetC;
        }
    }   
}
