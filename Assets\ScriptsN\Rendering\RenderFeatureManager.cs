﻿namespace ZTemp
{
    //动态管理RenderFeature的开关。
    //TODO.如果未来RenderFeature较多的时候,此类可以用于同时控制所有RenderFeature开关,避免RenderFeature之间互相耦合。（如Blur现在开关的判据就是ExtraUI中的Renderers数量大于零）
    //当前RenderFeature并不多,而且通常一个项目中RF很少超过十个,以避免性能问题。所以暂时感觉不用写这个,就先让它耦着吧。
    public class RenderFeatureManager
    {
        private static RenderFeatureManager _instance;
    
        public static RenderFeatureManager instance
        {
            get{
                if (_instance == null)
                {
                    _instance = new RenderFeatureManager();
                }
                return _instance;
            }
        }
        
    }

    public enum RendererData
    {
        Basic3D = 0,
        UI = 1,
        ZombieScene3D = 2,
    }
}