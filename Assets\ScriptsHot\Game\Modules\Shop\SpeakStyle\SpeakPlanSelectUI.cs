/* 
****************************************************
# 作者：Huangshiwen
# 创建时间：   2024/05/16 15:58:36 星期四
# 功能：Nothing
****************************************************
*/

using System;
using FairyGUI;
using Firebase.Analytics;
using Game;
using Modules.DataDot;
using Msg.economic;
using ScriptsHot.Game.Modules.Settlement;
using ScriptsHot.Game.UGUI.WebView;
using UIBind.Shop;
using UnityEngine;
using Object = UnityEngine.Object;

namespace ScriptsHot.Game.Modules.Shop
{
    public class SpeakPlanSelectUI : ShopUIBase<SpeakPlanSelectPanel>,IBaseUIUpdate
    {
        public SpeakPlanSelectUI(string name) : base(name)
        {
        }
        public override string uiLayer => UILayerConsts.Top; //主UI层

        protected override bool isFullScreen => true;

        private ShopModel _shopModel => GetModel<ShopModel>(ModelConsts.Shop);
        
        private MainModel _mainModel => GetModel<MainModel>(ModelConsts.Main);

        private string _curPlan = string.Empty;
        private float _lastUpdateTime;
        private string _preBtnStr;

        protected override void OnInit(GComponent uiCom)
        {
            AddUIEvent(this.ui.btnExit.onClick, OnBtnExitClicked);
            AddUIEvent(this.ui.comPlanSelectedContent.btnNext.onClick, OnBtnNextClicked);
            AddUIEvent(ui.comPlanSelectedContent.tfDesc10.onClickLink, OnLinckClicked);

            AddUIEvent(ui.comPlanSelectedContent.list.onClickItem, OnClickShopItem);

            ui.comPlanSelectedContent.list.itemRenderer = OnRendererShopList;
            ui.comPlanSelectedContent.list.itemProvider = OnProviderShopList;
            InitUI();
        }
        
        private void InitUI()
        {
            
#if UNITY_ANDROID
            ui.comPlanSelectedContent.tfDesc8.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_login_tips1_android");
#else
            ui.comPlanSelectedContent.tfDesc8.SetLanguage(LanguageType.MotherTongue, refresh:false).SetKey("ui_plan_selected_desc9");
#endif
            ui.comPlanSelectedContent.tfExpiring.SetKey("ui_pay_wall_expiring");
            ui.comPlanSelectedContent.tfExclusive.SetKey("ui_pay_wall_discount");
        }

        private void SetUITouchAble(bool able)
        {
            if (ui.comPlanSelectedContent == null)
                return;
            
            if (ui.comPlanSelectedContent.comLoading == null ||
                ui.comPlanSelectedContent.btnNext == null ||
                ui.comPlanSelectedContent.list == null)
                return;
            
            ui.comPlanSelectedContent.comLoading.visible = !able;
            ui.comPlanSelectedContent.btnNext.touchable = able;
            ui.comPlanSelectedContent.list.touchable = able;
            ui.btnExit.touchable = able;
        }

        private int _selectedPlanIndex;
        private bool _init;
        private string _discount;
        
        protected override void OnShow()
        {
            _init = true;
            _selectedPlanIndex = -1;
            DataDotMgr.Collect(new AppearTrialChoosePage());

            ui.comPlanSelectedContent.type.selectedPage = _shopModel.IsPayWallType ? "paywall" : "normal";

            if (_shopModel.shopInfoData.subscription_infos.Count > 1 && _shopModel.shopInfoData.subscription_infos[0].month_count > 0 && _shopModel.shopInfoData.subscription_infos[1].price_in_cents > 0)
            {
                _discount = ((int)Math.Floor(100 - _shopModel.shopInfoData.subscription_infos[0].price_in_cents *
                    1.0f / _shopModel.shopInfoData.subscription_infos[0].month_count *
                    100 / _shopModel.shopInfoData.subscription_infos[1].price_in_cents)).ToString();
            }
            else
            {
                _discount = string.Empty;
            }
            
            RefreshView();
            
            ui.comPlanSelectedContent.grpPaywall.EnsureBoundsCorrect();
            ui.comPlanSelectedContent.grpNormal.EnsureBoundsCorrect();
            ui.comPlanSelectedContent.grpHead.EnsureBoundsCorrect();
            SetUITouchAble(true);
        }

        private void RefreshView()
        {
            ui.comPlanSelectedContent.tfDesc9.SetKey("ui_plan_selected_desc10");
            if (_shopModel.IsPayWallType)
            {
                RefreshCompSpeak();
            }
            else
            {
                RefreshUI();
                PB_SubscriptionInfo subInfo = _shopModel.shopInfoData.subscription_infos[_selectedPlanIndex];
                if (_mainModel.incentiveData != null &&
                    (_mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus ==
                     SubscribeStatus.FreeTrialCanceled ||
                     _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus ==
                     SubscribeStatus.Canceled))
                {
                    _preBtnStr = I18N.inst.MoStr("ui_plan_common_btn_stay");
                    ui.comPlanSelectedContent.btnNext.text = I18N.inst.MoStr("ui_plan_common_btn_stay");
                }
                else
                {
                    if (_shopModel.IsDiscountType)
                    {
                        _preBtnStr = string.Format(I18N.inst.MoStr("ui_shop_discount_01"),
                            $"{ShopUIUtil.GetSaleOffValue(subInfo)}%");
                        ui.comPlanSelectedContent.btnNext.text = string.Format(I18N.inst.MoStr("ui_shop_discount_01"),
                            $"{ShopUIUtil.GetSaleOffValue(subInfo)}%");
                        ui.comPlanSelectedContent.tfDesc9.SetKey("ui_shop_discount_07");
                    }
                    else
                    {
                        _preBtnStr = string.Format(I18N.inst.MoStr("ui_plan_selected_desc16"), subInfo.free_days);
                        ui.comPlanSelectedContent.btnNext.text =
                            string.Format(I18N.inst.MoStr("ui_plan_selected_desc16"), subInfo.free_days);
                    }
                }
            }
        }

        private void RefreshUI()
        {
            // string services = string.Format("[url=services][color=#ffffff][b]{0}[/b][/color][/url]",
            //     I18N.inst.MoStr("ui_plan_selected_desc21"));
            // string privacy = string.Format("[url=privacy][color=#ffffff][b]{0}[/b][/color][/url]",
            //     I18N.inst.MoStr("ui_plan_selected_desc22"));
            ui.comPlanSelectedContent.tfDesc10.visible = false;//先不展示
            //ui.comPlanSelectedContent.tfDesc10.SetKeyArgs("ui_plan_selected_desc20", services, privacy);
            ui.comPlanSelectedContent.list.numItems = _shopModel.shopInfoData.subscription_infos.Count;
        }

        private void RefreshCompSpeak()
        {
            ui.comPlanSelectedContent.list.numItems = _shopModel.PayWallData.subscription_infos.Count;
            _preBtnStr = string.Format(I18N.inst.MoStr("ui_shop_discount_01"),
                $"{ShopUIUtil.GetSaleOffValue(_shopModel.PayWallData.subscription_infos[0])}%");
            ui.comPlanSelectedContent.btnNext.text = string.Format(I18N.inst.MoStr("ui_shop_discount_01"),
                $"{ShopUIUtil.GetSaleOffValue(_shopModel.PayWallData.subscription_infos[0])}%");
            ui.comPlanSelectedContent.tfDesc10.visible = false;//先不展示
            //ui.comPlanSelectedContent.tfDesc10.SetKey("ui_shop_discount_09");
        }

        private string OnProviderShopList(int index)
        {
            if (_shopModel.IsPayWallType || _shopModel.IsDiscountType)
            {
                return "ui://Shop/CompSpeakPlanSelectedItem";
            }
            else
            {
                return "ui://Shop/CompSpeakPopularItem";
                //return "ui://Shop/CompSpeakPlanNormalItem";
            }
        }

        private void RefreshSelectedItem(int index, GComponent comp, PB_SubscriptionInfo subInfo)
        {
            CompSpeakPlanSelectedItem item = new CompSpeakPlanSelectedItem();
            item.Construct(comp);
            if (subInfo.member_type == MemberType.YearPremium)
            {
                item.tfDiscount.SetKeyArgs("ui_shop_discount_08",
                    $"{ShopUIUtil.GetSaleOffValue(subInfo)}%");
            }
            else if (subInfo.member_type == MemberType.MonthPremium)
            {
                item.tfDiscount.SetKeyArgs("ui_shop_discount_12",
                    $"{ShopUIUtil.GetSaleOffValue(subInfo)}%");
            }
            else if (subInfo.member_type == MemberType.QuarterPremium)
            {
                item.tfDiscount.SetKeyArgs("ui_shop_discount_13",
                    $"{ShopUIUtil.GetSaleOffValue(subInfo)}%");
            }
            if (subInfo.member_type == MemberType.YearPremium)
                item.tfTitle.SetKey("ui_plan_selected_desc2");
            else if (subInfo.member_type == MemberType.MonthPremium)
                item.tfTitle.SetKey("ui_plan_selected_desc5");
            else if (subInfo.member_type == MemberType.QuarterPremium)
                item.tfTitle.SetKey("ui_plan_selected_desc14");
            item.tfMo.SetKeyArgs("ui_plan_selected_desc17", subInfo.price_per_month_in_display);
            item.tfCur.text = subInfo.price_in_display;
            item.tfLast.text = subInfo.origin_price_in_display;
            item.tfFirst.SetKeyArgs(subInfo.member_type == MemberType.YearPremium
                ? "ui_shop_discount_11"
                : "ui_shop_discount_15", subInfo.origin_price_in_display);
            item.tfEquivalent.SetKey("ui_plan_selected_desc13");
            if (_init && subInfo.is_recommend)
            {
                _selectedPlanIndex = index;
                _init = false;
            }
            item.choice.selectedIndex = index == _selectedPlanIndex ? 1 : 0;
        }

        private void RefreshNormalItem(int index, GComponent comp, PB_SubscriptionInfo subInfo)
        {
            CompSpeakPlanNormalItem item = new CompSpeakPlanNormalItem();
            item.Construct(comp);
            item.tfMo.SetKeyArgs("ui_plan_selected_desc17", subInfo.price_per_month_in_display);
            if (subInfo.member_type == MemberType.YearPremium)
                item.tfTitle.SetKey("ui_plan_selected_desc2");
            else if (subInfo.member_type == MemberType.MonthPremium)
                item.tfTitle.SetKey("ui_plan_selected_desc5");
            else if (subInfo.member_type == MemberType.QuarterPremium)
                item.tfTitle.SetKey("ui_plan_selected_desc14");
            item.tfEquivalent.SetKey("ui_plan_selected_desc13");
            item.tfCur.text = subInfo.price_in_display;
            if (_init && subInfo.is_recommend)
            {
                _selectedPlanIndex = index;
                _init = false;
            }
            item.choice.selectedIndex = index == _selectedPlanIndex ? 1 : 0;
        }

        private void RefreshPopularItem(int index, GComponent comp, PB_SubscriptionInfo subInfo)
        {
            CompSpeakPopularItem item = new CompSpeakPopularItem();
            item.Construct(comp);
            item.tfMo.SetKeyArgs("ui_plan_selected_desc17", subInfo.price_per_month_in_display);
            if (subInfo.member_type == MemberType.YearPremium)
                item.tfTitle.SetKey("ui_plan_selected_desc2");
            else if (subInfo.member_type == MemberType.MonthPremium)
                item.tfTitle.SetKey("ui_plan_selected_desc5");
            else if (subInfo.member_type == MemberType.QuarterPremium)
                item.tfTitle.SetKey("ui_plan_selected_desc14");
            item.tfEquivalent.SetKey("ui_plan_selected_desc13");
            item.tfCur.text = subInfo.price_in_display;
            item.tfMost.SetKey("ui_plan_selected_desc1");
            item.state.selectedIndex = subInfo.is_recommend ? 0 : 1;
            if (_init && subInfo.is_recommend)
            {
                _selectedPlanIndex = index;
                _init = false;
            }

            if (subInfo.member_type != MemberType.MonthPremium && _discount != string.Empty)
            {
                item.discountTxt.SetVar("discount", _discount).FlushVars();
                item.discount.selectedIndex = 1;
            }
            else
                item.discount.selectedIndex = 0;
            item.choice.selectedIndex = index == _selectedPlanIndex ? 1 : 0;
        }

        private void OnRendererShopList(int index, GObject obj)
        {
            GComponent comp = obj as GComponent;
            if (_shopModel.IsPayWallType)
            {
                RefreshSelectedItem(index, comp, _shopModel.PayWallData.subscription_infos[index]);
            }
            else if (_shopModel.IsDiscountType)
            {
                RefreshSelectedItem(index, comp, _shopModel.shopInfoData.subscription_infos[index]);
            }
            else
            {
                RefreshPopularItem(index, comp, _shopModel.shopInfoData.subscription_infos[index]);
            }
        }
        
        private void OnBtnExitClicked()
        {
            bool needHide = true;
            if (args != null && args.Length > 0)
            {
                var source = (int)args[0];
                var dot = new DataDotTrialChooseQuit();
                
                PB_SubscriptionInfo cfg;
                if (_shopModel.IsPayWallType)
                    cfg = _shopModel.PayWallData.subscription_infos[_selectedPlanIndex];
                else
                    cfg = _shopModel.shopInfoData.subscription_infos[_selectedPlanIndex];
 
                //int selectType = (int)selectItemType;
                //ShopModel.SubscribeTypeFullName typeName = (ShopModel.SubscribeTypeFullName)selectType;
                dot.clicked_member_type = _curPlan;
                dot.subscribe_price =  cfg.price_in_cents;
                dot.price_currency =  OsFuncAdapter.Ins.GetCountryCode();
                
                DataDotMgr.Collect(dot);
                
                //todo 这种不透明参数驱动控制的过程要避免
                if (source == 4)
                {
                    GetController<SettlementController>(ModelConsts.Settlement).ShowNextView(() =>
                    {
                        GetController<ShopController>(ModelConsts.Shop).CheckSendOnBoardOverMsg();
                        Hide();
                    });
                    needHide = false;
                }
            }
            if (needHide) 
            {
                _shopModel.DotIsTTS = 0;
                GetController<ShopController>(ModelConsts.Shop).CheckSendOnBoardOverMsg();
                Hide();
            }
            
        }

        private string purchasingTimerKey;
        private void OnBtnNextClicked()
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Medium);
            SetUITouchAble(false);
            ui.comPlanSelectedContent.btnNext.text = I18N.inst.MoStr("common_shop_create_payment");
            
            PB_SubscriptionInfo cfg;
            if (_shopModel.IsPayWallType)
                cfg = _shopModel.PayWallData.subscription_infos[_selectedPlanIndex];
            else
                cfg = _shopModel.shopInfoData.subscription_infos[_selectedPlanIndex];

            purchasingTimerKey= TimerManager.instance.RegisterTimer((c) =>
            {
                SetUITouchAble(true);
            }, 7000, 1);

            PurchasingManager.instance.StartPurchasingTimeOutEvent(() => {  SetUITouchAble(true); });
                
            PurchasingManager.instance.StartPurchasing(cfg.product_id, PB_ProductType.PB_ProductType_Subscribe, OnSubscribeSuccess, OnSubscribeFail, OnSubScribePaid);
            AFHelper.Click_Trial_choose_next(cfg.product_id);
            //新埋点：未免费订阅过的用户，在免费试用选包页点开始体验
            DataDotTrialChooseNext dot = new DataDotTrialChooseNext();
            
            
            // dot.subscribe_status = _mainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus.ToString();
            // dot.member_type = _mainModel.incentiveData.homepage_economic_info.member_info.member_type.ToString();
             dot.clicked_member_type = _curPlan;
             dot.subscribe_price = cfg.price_in_cents;
             dot.price_currency = OsFuncAdapter.Ins.GetCountryCode();
            
            DataDotMgr.Collect(dot);
            
           
            
            product_id = cfg.product_id;
            product_type = PB_ProductType.PB_ProductType_Subscribe.ToString();
            if (args.Length > 0)
            {
                MyFirebaseAnalytics.LogEvent("trial_get");
            }
            
            AFDots.Click_Package_page_get(_curPlan);
            MyFirebaseAnalytics.LogEvent("trial_package", new Parameter[] { new Parameter("package_type", _curPlan) });
        }

        private void OnSubScribePaid()
        {
            if (ui.comPlanSelectedContent == null || ui.comPlanSelectedContent.btnNext == null)
                return;

            ui.comPlanSelectedContent.btnNext.text = I18N.inst.MoStr("common_shop_verify_payment");
        }

        private void OnSubscribeSuccess()
        {
            SetUITouchAble(true);
            ui.comPlanSelectedContent.btnNext.text = _preBtnStr;
            OnBuySuccess();
            GetUI<SubscribeSuccessUI>(UIConsts.SubscribeSuccessUI).prefix = "Premium";
            GetUI<SubscribeSuccessUI>(UIConsts.SubscribeSuccessUI).Show(args);
            Hide();
            SendNotification(NotifyConsts.HideHomepageBanner);
            SendNotification(NotifyConsts.HideHomepageTimeBanner);
        }

        private void OnSubscribeFail(string msg, bool showError)
        {
            SetUITouchAble(true);
            ui.comPlanSelectedContent.btnNext.text = _preBtnStr;
            OnBuyFail(msg, showError);
        }

        private void OnClickShopItem(EventContext evt)
        {
            GComponent comp = evt.sender as GComponent;
            if (comp == null) return;
            int index = comp.GetChildIndex(evt.data as GObject);
            _selectedPlanIndex = index;
            RefreshView();
            if (_shopModel.IsPayWallType)
            {
                _curPlan = _shopModel.PayWallData.subscription_infos[index].member_type.ToString();
            }
            else
            {
                _curPlan = _shopModel.shopInfoData.subscription_infos[index].member_type.ToString();
                if (_shopModel.shopInfoData.subscription_infos[index].member_type == MemberType.MonthPremium)
                    DataDotMgr.Collect(new DataDotTrialChooseMonthly());
                else if (_shopModel.shopInfoData.subscription_infos[index].member_type == MemberType.YearPremium)
                    DataDotMgr.Collect(new DataDotTrialChooseYearly());
                else if (_shopModel.shopInfoData.subscription_infos[index].member_type == MemberType.QuarterPremium)
                    DataDotMgr.Collect(new DataDotTrialChooseQuarterly());
            }
        }

        private void OnLinckClicked(EventContext e)
        {
            string url = e.data as string;
            if (url == "services")
            {
#if UNITY_EDITOR
                OpenUrl(LoginConst.usertermsUrl);
#else
            OpenUrl(LoginConst.usertermsUrl);
            //DeviceAdapter.OpenWebPage(LoginConst.usertermsUrl);
#endif
            }
            else if (url == "privacy")
            {
#if UNITY_EDITOR
                OpenUrl(LoginConst.privacyUrl);
#else
            OpenUrl(LoginConst.privacyUrl);
            //DeviceAdapter.OpenWebPage(LoginConst.privacyUrl);
#endif
            }
        }
        
        private void OpenUrl(string url)
        {
            MainModel mainModel = GetModel<MainModel>(ModelConsts.Main);

            // GameObject ctlPrefab = Resources.Load<GameObject>("Prefabs/WebViewCtl");
            GameObject ctlPrefab = GResManager.instance.LoadPrefab("WebViewCtl");     
            GameObject newCtl = Object.Instantiate(ctlPrefab);
            
            WebViewCtl ctl = newCtl.GetComponent<WebViewCtl>();
            if (ctl == null)
            {
                ctl = newCtl.AddComponent<WebViewCtl>();
            }
            ctl.Init(10f, I18N.inst.MotherLanguage.ToString(), I18N.inst.ForeignLanguage.ToString(), mainModel.toKen, I18N.inst.MotherLanguage.ToString(),
                true,
                true,
                () =>
                { 
                    GetController<CurrencyController>(ModelConsts.CurrencyController).SendGetEconomicInfoReqAsync(GameEventName.GameEnter, () =>
                    {
                        SendNotification(NotifyConsts.MainHeadRefreshEvent);
                    });
                },() =>
                {
                    GetUI<CommBusyUI>(UIConsts.CommBusy).LazyShow();
                },
                () =>
                {
                    GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
                }
            );
            ctl.LoadUrl(url);
        }

        protected override void OnHide()
        {
            _shopModel.ClearPayWallTye();
            ui.comPlanSelectedContent.list.numItems = 0;
        }

        protected override void HandleNotification(string name, object body)
        {
            switch (name)
            {
                case NotifyConsts.OnApplicationPaused:
                    ui.comPlanSelectedContent.btnNext.text = I18N.inst.MoStr("common_shop_process_payment");
                    break;
            }
        }
        
        protected override string[] ListNotificationInterests()
        {
            return new string[]
            {
                NotifyConsts.OnApplicationPaused
            };
        }

        public void Update(int interval)
        {
            long endTimeStamp = _shopModel.PayWallData?.end_milliseconds_in_utc ?? 0;
            long curTimeStamp = TimeExt.serverTimestamp;
            long leftSeconds = (endTimeStamp - curTimeStamp) / 1000;
            if (leftSeconds < 0) leftSeconds = 0;
            int minutes = (int)(leftSeconds / 60);
            int seconds = (int)(leftSeconds % 60);
            ui.comPlanSelectedContent.tfTime.text = $"{minutes:D2}:{seconds:D2}";
        }
    }
}