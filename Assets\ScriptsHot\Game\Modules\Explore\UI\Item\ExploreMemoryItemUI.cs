﻿using FairyGUI;
using Msg.explore;
using ScriptsHot.Game.Modules.Explore;
using UnityEngine;

namespace UIBind.Explore
{
    public partial class ExploreMemoryItem
    {
        private PB_StoryPreloadData _data;
        public void InitItem()
        {
            this.btnContinue.onClick.Add(OnClickContinue);
        }
        
        private void OnClickContinue( )
        {
            HomepageController hCon = ControllerManager.instance.GetController<HomepageController>(ModelConsts.Homepage) as HomepageController;
            if (!hCon.IsExploreTab())
            {
                Notifier.instance.SendNotification(NotifyConsts.CloseUI,UIConsts.ExploreHistoryProgressListUI);
                return;
            }
            
            CLICK_EXPLORE_MISSION_STORY_GUIDE_POPUP_CONTINUE_BUTTON dt = new CLICK_EXPLORE_MISSION_STORY_GUIDE_POPUP_CONTINUE_BUTTON();
            dt.continue_task_id = _data.taskId;
            DataDotMgr.Collect(dt);
            
            Notifier.instance.SendNotification(NotifyConsts.ExploreJumpMisssion,_data);
        }
        
        public void SetData(PB_StoryPreloadData value)
        {
            _data = value;
            
            // int64 storyId = 1; // story_id
            // PB_DialogMode dialogMode = 2; // 对话模式
            // PB_Story_Avatar avatar = 3; // Avatar信息
            // PB_Story_Scene scene = 4; // 场景信息
            // PB_Story_Detail detail = 5; // story详情
            // PB_Story_First_Round_Data firstRoundData = 6; // 首轮数据
            
            // this.ctrlDescribeBack.SetSelectedPage("back" + _controller.Model.GetDescribeBack(entityId));
            this.ctrl.selectedIndex = 1;
            if (value.detail.storyTitle.Length > 31)
            {
                txtTypeName.width = 426;
                txtTypeName.autoSize = AutoSizeType.Ellipsis;
            }
            else
            {
                txtTypeName.autoSize = AutoSizeType.Both;
            }
  
            this.txtTypeName.text = value.detail.storyTitle;
         

            this.txtTitle.text = value.detail.storyProgress.currentTaskTitle;
            this.txtDesc.text = value.detail.storyProgress.currentStepDesc;
            this.txtLevel.text = value.detail.CefrLevel.ToString();
            
        }
    }
}