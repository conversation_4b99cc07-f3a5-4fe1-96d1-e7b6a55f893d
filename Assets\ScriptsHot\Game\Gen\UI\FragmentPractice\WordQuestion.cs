/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class WordQuestion : AFragQuestion
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "WordQuestion";
        public static string url => "ui://cmoz5osjs2aiuvptd8";

        public GTextField tfWord;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(WordQuestion));
        }

        public override void ConstructFromXML(XML xml)
        {
            tfWord = GetChildAt(1) as GTextField;
        }
        public override void Dispose()
        {
            tfWord = null;

            base.Dispose();
        }
    }
}