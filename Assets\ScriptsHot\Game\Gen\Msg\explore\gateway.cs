// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/explore/gateway.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.explore {

  /// <summary>Holder for reflection information generated from protobuf/explore/gateway.proto</summary>
  public static partial class GatewayReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/explore/gateway.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static GatewayReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Ch5wcm90b2J1Zi9leHBsb3JlL2dhdGV3YXkucHJvdG8aG3Byb3RvYnVmL2V4",
            "cGxvcmUvYmFzZS5wcm90bxogcHJvdG9idWYvZXhwbG9yZS9yZWNvbW1lbmQu",
            "cHJvdG8aHXByb3RvYnVmL2V4cGxvcmUvZGlhbG9nLnByb3RvGiNwcm90b2J1",
            "Zi9leHBsb3JlL3VzZXJfc2V0dGluZy5wcm90bxofcHJvdG9idWYvZXhwbG9y",
            "ZS9tYXRjaGluZy5wcm90bxogcHJvdG9idWYvZXhwbG9yZS91c2VyX2NoYXQu",
            "cHJvdG8aIXByb3RvYnVmL2V4cGxvcmUvb25ib2FyZGluZy5wcm90bxomcHJv",
            "dG9idWYvZXhwbG9yZS9vbmJvYXJkaW5nX2NoYXQucHJvdG8aJHByb3RvYnVm",
            "L2V4cGxvcmUvbWlzc2lvbl9zdG9yeS5wcm90bxoncHJvdG9idWYvZXhwbG9y",
            "ZS91c2VyX2NoYXRfcmVjb3JkLnByb3RvIsQECg9DU19FeHBsb3JlVXBNc2cS",
            "KgoMcmVjb21tZW5kTXNnGAEgASgLMhIuUEJfUmVjb21tZW5kVXBNc2dIABIk",
            "CglkaWFsb2dNc2cYAiABKAsyDy5QQl9EaWFsb2dVcE1zZ0gAEi4KDnVzZXJT",
            "ZXR0aW5nTXNnGAQgASgLMhQuUEJfVXNlclNldHRpbmdVcE1zZ0gAEigKC21h",
            "dGNoaW5nTXNnGAUgASgLMhEuUEJfTWF0Y2hpbmdVcE1zZ0gAEigKC3VzZXJD",
            "aGF0TXNnGAYgASgLMhEuUEJfVXNlckNoYXRVcE1zZ0gAEjQKEW9uYm9hcmRp",
            "bmdDaGF0TXNnGAcgASgLMhcuUEJfT25ib2FyZGluZ0NoYXRVcE1zZ0gAEiwK",
            "DW9uYm9hcmRpbmdNc2cYCSABKAsyEy5QQl9PbmJvYXJkaW5nVXBNc2dIABI4",
            "ChNtaXNzaW9uU3RvcnlDaGF0TXNnGAogASgLMhkuUEJfTWlzc2lvblN0b3J5",
            "Q2hhdFVwTXNnSAASNAoSbmF0aXZlU3BlYWtlclVwTXNnGAsgASgLMhYuUEJf",
            "TmF0aXZlU3BlYWtlclVwTXNnSAASNgoTdXNlckNoYXRSZWNvcmRVcE1zZxgM",
            "IAEoCzIXLlBCX1VzZXJDaGF0UmVjb3JkVXBNc2dIABIRCglmaXJzdExhbmcY",
            "AyABKAkSMAoNc291cmNlQ2hhbm5lbBgIIAEoDjIZLlBCX0V4cGxvcmVfU291",
            "cmNlQ2hhbm5lbEIKCgh1cE1vZE1zZyJTChFTQ19FeHBsb3JlRG93bk1zZxIh",
            "CgRjb2RlGAEgASgOMhMuUEJfRXhwbG9yZV9CaXpDb2RlEg0KBW1zZ0lkGAIg",
            "ASgFEgwKBGRhdGEYAyABKAxCKloadmZfcHJvdG9idWYvc2VydmVyL2V4cGxv",
            "cmWqAgtNc2cuZXhwbG9yZWIGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Msg.explore.BaseReflection.Descriptor, global::Msg.explore.RecommendReflection.Descriptor, global::Msg.explore.DialogReflection.Descriptor, global::Msg.explore.UserSettingReflection.Descriptor, global::Msg.explore.MatchingReflection.Descriptor, global::Msg.explore.UserChatReflection.Descriptor, global::Msg.explore.OnboardingReflection.Descriptor, global::Msg.explore.OnboardingChatReflection.Descriptor, global::Msg.explore.MissionStoryReflection.Descriptor, global::Msg.explore.UserChatRecordReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.CS_ExploreUpMsg), global::Msg.explore.CS_ExploreUpMsg.Parser, new[]{ "recommendMsg", "dialogMsg", "userSettingMsg", "matchingMsg", "userChatMsg", "onboardingChatMsg", "onboardingMsg", "missionStoryChatMsg", "nativeSpeakerUpMsg", "userChatRecordUpMsg", "firstLang", "sourceChannel" }, new[]{ "upModMsg" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_ExploreDownMsg), global::Msg.explore.SC_ExploreDownMsg.Parser, new[]{ "code", "msgId", "data" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  /// <summary>
  ///
  /// Explore长连接上行消息（stream）
  /// 1. 服务于Explore的所有功能，是通用的上行消息容器
  /// 2. 每个功能模块，需要实现一套对应功能的上行消息，比如：对话有两种协议类型：用户录音音频输入、上行业务事件（如：麦克风收音相关事件）
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_ExploreUpMsg : pb::IMessage<CS_ExploreUpMsg>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_ExploreUpMsg> _parser = new pb::MessageParser<CS_ExploreUpMsg>(() => new CS_ExploreUpMsg());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_ExploreUpMsg> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.GatewayReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_ExploreUpMsg() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_ExploreUpMsg(CS_ExploreUpMsg other) : this() {
      firstLang_ = other.firstLang_;
      sourceChannel_ = other.sourceChannel_;
      switch (other.upModMsgCase) {
        case upModMsgOneofCase.recommendMsg:
          recommendMsg = other.recommendMsg.Clone();
          break;
        case upModMsgOneofCase.dialogMsg:
          dialogMsg = other.dialogMsg.Clone();
          break;
        case upModMsgOneofCase.userSettingMsg:
          userSettingMsg = other.userSettingMsg.Clone();
          break;
        case upModMsgOneofCase.matchingMsg:
          matchingMsg = other.matchingMsg.Clone();
          break;
        case upModMsgOneofCase.userChatMsg:
          userChatMsg = other.userChatMsg.Clone();
          break;
        case upModMsgOneofCase.onboardingChatMsg:
          onboardingChatMsg = other.onboardingChatMsg.Clone();
          break;
        case upModMsgOneofCase.onboardingMsg:
          onboardingMsg = other.onboardingMsg.Clone();
          break;
        case upModMsgOneofCase.missionStoryChatMsg:
          missionStoryChatMsg = other.missionStoryChatMsg.Clone();
          break;
        case upModMsgOneofCase.nativeSpeakerUpMsg:
          nativeSpeakerUpMsg = other.nativeSpeakerUpMsg.Clone();
          break;
        case upModMsgOneofCase.userChatRecordUpMsg:
          userChatRecordUpMsg = other.userChatRecordUpMsg.Clone();
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_ExploreUpMsg Clone() {
      return new CS_ExploreUpMsg(this);
    }

    /// <summary>Field number for the "recommendMsg" field.</summary>
    public const int recommendMsgFieldNumber = 1;
    /// <summary>
    /// 推荐消息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_RecommendUpMsg recommendMsg {
      get { return upModMsgCase_ == upModMsgOneofCase.recommendMsg ? (global::Msg.explore.PB_RecommendUpMsg) upModMsg_ : null; }
      set {
        upModMsg_ = value;
        upModMsgCase_ = value == null ? upModMsgOneofCase.None : upModMsgOneofCase.recommendMsg;
      }
    }

    /// <summary>Field number for the "dialogMsg" field.</summary>
    public const int dialogMsgFieldNumber = 2;
    /// <summary>
    /// 对话消息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_DialogUpMsg dialogMsg {
      get { return upModMsgCase_ == upModMsgOneofCase.dialogMsg ? (global::Msg.explore.PB_DialogUpMsg) upModMsg_ : null; }
      set {
        upModMsg_ = value;
        upModMsgCase_ = value == null ? upModMsgOneofCase.None : upModMsgOneofCase.dialogMsg;
      }
    }

    /// <summary>Field number for the "userSettingMsg" field.</summary>
    public const int userSettingMsgFieldNumber = 4;
    /// <summary>
    /// 用户设置消息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_UserSettingUpMsg userSettingMsg {
      get { return upModMsgCase_ == upModMsgOneofCase.userSettingMsg ? (global::Msg.explore.PB_UserSettingUpMsg) upModMsg_ : null; }
      set {
        upModMsg_ = value;
        upModMsgCase_ = value == null ? upModMsgOneofCase.None : upModMsgOneofCase.userSettingMsg;
      }
    }

    /// <summary>Field number for the "matchingMsg" field.</summary>
    public const int matchingMsgFieldNumber = 5;
    /// <summary>
    /// 撮合消息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_MatchingUpMsg matchingMsg {
      get { return upModMsgCase_ == upModMsgOneofCase.matchingMsg ? (global::Msg.explore.PB_MatchingUpMsg) upModMsg_ : null; }
      set {
        upModMsg_ = value;
        upModMsgCase_ = value == null ? upModMsgOneofCase.None : upModMsgOneofCase.matchingMsg;
      }
    }

    /// <summary>Field number for the "userChatMsg" field.</summary>
    public const int userChatMsgFieldNumber = 6;
    /// <summary>
    /// 用户聊天消息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_UserChatUpMsg userChatMsg {
      get { return upModMsgCase_ == upModMsgOneofCase.userChatMsg ? (global::Msg.explore.PB_UserChatUpMsg) upModMsg_ : null; }
      set {
        upModMsg_ = value;
        upModMsgCase_ = value == null ? upModMsgOneofCase.None : upModMsgOneofCase.userChatMsg;
      }
    }

    /// <summary>Field number for the "onboardingChatMsg" field.</summary>
    public const int onboardingChatMsgFieldNumber = 7;
    /// <summary>
    /// onboarding对话消息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_OnboardingChatUpMsg onboardingChatMsg {
      get { return upModMsgCase_ == upModMsgOneofCase.onboardingChatMsg ? (global::Msg.explore.PB_OnboardingChatUpMsg) upModMsg_ : null; }
      set {
        upModMsg_ = value;
        upModMsgCase_ = value == null ? upModMsgOneofCase.None : upModMsgOneofCase.onboardingChatMsg;
      }
    }

    /// <summary>Field number for the "onboardingMsg" field.</summary>
    public const int onboardingMsgFieldNumber = 9;
    /// <summary>
    /// onboarding消息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_OnboardingUpMsg onboardingMsg {
      get { return upModMsgCase_ == upModMsgOneofCase.onboardingMsg ? (global::Msg.explore.PB_OnboardingUpMsg) upModMsg_ : null; }
      set {
        upModMsg_ = value;
        upModMsgCase_ = value == null ? upModMsgOneofCase.None : upModMsgOneofCase.onboardingMsg;
      }
    }

    /// <summary>Field number for the "missionStoryChatMsg" field.</summary>
    public const int missionStoryChatMsgFieldNumber = 10;
    /// <summary>
    /// mission剧情对话消息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_MissionStoryChatUpMsg missionStoryChatMsg {
      get { return upModMsgCase_ == upModMsgOneofCase.missionStoryChatMsg ? (global::Msg.explore.PB_MissionStoryChatUpMsg) upModMsg_ : null; }
      set {
        upModMsg_ = value;
        upModMsgCase_ = value == null ? upModMsgOneofCase.None : upModMsgOneofCase.missionStoryChatMsg;
      }
    }

    /// <summary>Field number for the "nativeSpeakerUpMsg" field.</summary>
    public const int nativeSpeakerUpMsgFieldNumber = 11;
    /// <summary>
    /// NativeSpeaker匹配消息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_NativeSpeakerUpMsg nativeSpeakerUpMsg {
      get { return upModMsgCase_ == upModMsgOneofCase.nativeSpeakerUpMsg ? (global::Msg.explore.PB_NativeSpeakerUpMsg) upModMsg_ : null; }
      set {
        upModMsg_ = value;
        upModMsgCase_ = value == null ? upModMsgOneofCase.None : upModMsgOneofCase.nativeSpeakerUpMsg;
      }
    }

    /// <summary>Field number for the "userChatRecordUpMsg" field.</summary>
    public const int userChatRecordUpMsgFieldNumber = 12;
    /// <summary>
    /// 用户聊天记录消息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_UserChatRecordUpMsg userChatRecordUpMsg {
      get { return upModMsgCase_ == upModMsgOneofCase.userChatRecordUpMsg ? (global::Msg.explore.PB_UserChatRecordUpMsg) upModMsg_ : null; }
      set {
        upModMsg_ = value;
        upModMsgCase_ = value == null ? upModMsgOneofCase.None : upModMsgOneofCase.userChatRecordUpMsg;
      }
    }

    /// <summary>Field number for the "firstLang" field.</summary>
    public const int firstLangFieldNumber = 3;
    private string firstLang_ = "";
    /// <summary>
    /// 用户母语选项
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string firstLang {
      get { return firstLang_; }
      set {
        firstLang_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "sourceChannel" field.</summary>
    public const int sourceChannelFieldNumber = 8;
    private global::Msg.explore.PB_Explore_SourceChannel sourceChannel_ = global::Msg.explore.PB_Explore_SourceChannel.EO_SC_UNKNOWN;
    /// <summary>
    /// 来源渠道（建立连接的渠道，用于支持不同位置创建连接后端做差异逻辑）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_SourceChannel sourceChannel {
      get { return sourceChannel_; }
      set {
        sourceChannel_ = value;
      }
    }

    private object upModMsg_;
    /// <summary>Enum of possible cases for the "upModMsg" oneof.</summary>
    public enum upModMsgOneofCase {
      None = 0,
      recommendMsg = 1,
      dialogMsg = 2,
      userSettingMsg = 4,
      matchingMsg = 5,
      userChatMsg = 6,
      onboardingChatMsg = 7,
      onboardingMsg = 9,
      missionStoryChatMsg = 10,
      nativeSpeakerUpMsg = 11,
      userChatRecordUpMsg = 12,
    }
    private upModMsgOneofCase upModMsgCase_ = upModMsgOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public upModMsgOneofCase upModMsgCase {
      get { return upModMsgCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearupModMsg() {
      upModMsgCase_ = upModMsgOneofCase.None;
      upModMsg_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_ExploreUpMsg);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_ExploreUpMsg other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(recommendMsg, other.recommendMsg)) return false;
      if (!object.Equals(dialogMsg, other.dialogMsg)) return false;
      if (!object.Equals(userSettingMsg, other.userSettingMsg)) return false;
      if (!object.Equals(matchingMsg, other.matchingMsg)) return false;
      if (!object.Equals(userChatMsg, other.userChatMsg)) return false;
      if (!object.Equals(onboardingChatMsg, other.onboardingChatMsg)) return false;
      if (!object.Equals(onboardingMsg, other.onboardingMsg)) return false;
      if (!object.Equals(missionStoryChatMsg, other.missionStoryChatMsg)) return false;
      if (!object.Equals(nativeSpeakerUpMsg, other.nativeSpeakerUpMsg)) return false;
      if (!object.Equals(userChatRecordUpMsg, other.userChatRecordUpMsg)) return false;
      if (firstLang != other.firstLang) return false;
      if (sourceChannel != other.sourceChannel) return false;
      if (upModMsgCase != other.upModMsgCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (upModMsgCase_ == upModMsgOneofCase.recommendMsg) hash ^= recommendMsg.GetHashCode();
      if (upModMsgCase_ == upModMsgOneofCase.dialogMsg) hash ^= dialogMsg.GetHashCode();
      if (upModMsgCase_ == upModMsgOneofCase.userSettingMsg) hash ^= userSettingMsg.GetHashCode();
      if (upModMsgCase_ == upModMsgOneofCase.matchingMsg) hash ^= matchingMsg.GetHashCode();
      if (upModMsgCase_ == upModMsgOneofCase.userChatMsg) hash ^= userChatMsg.GetHashCode();
      if (upModMsgCase_ == upModMsgOneofCase.onboardingChatMsg) hash ^= onboardingChatMsg.GetHashCode();
      if (upModMsgCase_ == upModMsgOneofCase.onboardingMsg) hash ^= onboardingMsg.GetHashCode();
      if (upModMsgCase_ == upModMsgOneofCase.missionStoryChatMsg) hash ^= missionStoryChatMsg.GetHashCode();
      if (upModMsgCase_ == upModMsgOneofCase.nativeSpeakerUpMsg) hash ^= nativeSpeakerUpMsg.GetHashCode();
      if (upModMsgCase_ == upModMsgOneofCase.userChatRecordUpMsg) hash ^= userChatRecordUpMsg.GetHashCode();
      if (firstLang.Length != 0) hash ^= firstLang.GetHashCode();
      if (sourceChannel != global::Msg.explore.PB_Explore_SourceChannel.EO_SC_UNKNOWN) hash ^= sourceChannel.GetHashCode();
      hash ^= (int) upModMsgCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (upModMsgCase_ == upModMsgOneofCase.recommendMsg) {
        output.WriteRawTag(10);
        output.WriteMessage(recommendMsg);
      }
      if (upModMsgCase_ == upModMsgOneofCase.dialogMsg) {
        output.WriteRawTag(18);
        output.WriteMessage(dialogMsg);
      }
      if (firstLang.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(firstLang);
      }
      if (upModMsgCase_ == upModMsgOneofCase.userSettingMsg) {
        output.WriteRawTag(34);
        output.WriteMessage(userSettingMsg);
      }
      if (upModMsgCase_ == upModMsgOneofCase.matchingMsg) {
        output.WriteRawTag(42);
        output.WriteMessage(matchingMsg);
      }
      if (upModMsgCase_ == upModMsgOneofCase.userChatMsg) {
        output.WriteRawTag(50);
        output.WriteMessage(userChatMsg);
      }
      if (upModMsgCase_ == upModMsgOneofCase.onboardingChatMsg) {
        output.WriteRawTag(58);
        output.WriteMessage(onboardingChatMsg);
      }
      if (sourceChannel != global::Msg.explore.PB_Explore_SourceChannel.EO_SC_UNKNOWN) {
        output.WriteRawTag(64);
        output.WriteEnum((int) sourceChannel);
      }
      if (upModMsgCase_ == upModMsgOneofCase.onboardingMsg) {
        output.WriteRawTag(74);
        output.WriteMessage(onboardingMsg);
      }
      if (upModMsgCase_ == upModMsgOneofCase.missionStoryChatMsg) {
        output.WriteRawTag(82);
        output.WriteMessage(missionStoryChatMsg);
      }
      if (upModMsgCase_ == upModMsgOneofCase.nativeSpeakerUpMsg) {
        output.WriteRawTag(90);
        output.WriteMessage(nativeSpeakerUpMsg);
      }
      if (upModMsgCase_ == upModMsgOneofCase.userChatRecordUpMsg) {
        output.WriteRawTag(98);
        output.WriteMessage(userChatRecordUpMsg);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (upModMsgCase_ == upModMsgOneofCase.recommendMsg) {
        output.WriteRawTag(10);
        output.WriteMessage(recommendMsg);
      }
      if (upModMsgCase_ == upModMsgOneofCase.dialogMsg) {
        output.WriteRawTag(18);
        output.WriteMessage(dialogMsg);
      }
      if (firstLang.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(firstLang);
      }
      if (upModMsgCase_ == upModMsgOneofCase.userSettingMsg) {
        output.WriteRawTag(34);
        output.WriteMessage(userSettingMsg);
      }
      if (upModMsgCase_ == upModMsgOneofCase.matchingMsg) {
        output.WriteRawTag(42);
        output.WriteMessage(matchingMsg);
      }
      if (upModMsgCase_ == upModMsgOneofCase.userChatMsg) {
        output.WriteRawTag(50);
        output.WriteMessage(userChatMsg);
      }
      if (upModMsgCase_ == upModMsgOneofCase.onboardingChatMsg) {
        output.WriteRawTag(58);
        output.WriteMessage(onboardingChatMsg);
      }
      if (sourceChannel != global::Msg.explore.PB_Explore_SourceChannel.EO_SC_UNKNOWN) {
        output.WriteRawTag(64);
        output.WriteEnum((int) sourceChannel);
      }
      if (upModMsgCase_ == upModMsgOneofCase.onboardingMsg) {
        output.WriteRawTag(74);
        output.WriteMessage(onboardingMsg);
      }
      if (upModMsgCase_ == upModMsgOneofCase.missionStoryChatMsg) {
        output.WriteRawTag(82);
        output.WriteMessage(missionStoryChatMsg);
      }
      if (upModMsgCase_ == upModMsgOneofCase.nativeSpeakerUpMsg) {
        output.WriteRawTag(90);
        output.WriteMessage(nativeSpeakerUpMsg);
      }
      if (upModMsgCase_ == upModMsgOneofCase.userChatRecordUpMsg) {
        output.WriteRawTag(98);
        output.WriteMessage(userChatRecordUpMsg);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (upModMsgCase_ == upModMsgOneofCase.recommendMsg) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(recommendMsg);
      }
      if (upModMsgCase_ == upModMsgOneofCase.dialogMsg) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(dialogMsg);
      }
      if (upModMsgCase_ == upModMsgOneofCase.userSettingMsg) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(userSettingMsg);
      }
      if (upModMsgCase_ == upModMsgOneofCase.matchingMsg) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(matchingMsg);
      }
      if (upModMsgCase_ == upModMsgOneofCase.userChatMsg) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(userChatMsg);
      }
      if (upModMsgCase_ == upModMsgOneofCase.onboardingChatMsg) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(onboardingChatMsg);
      }
      if (upModMsgCase_ == upModMsgOneofCase.onboardingMsg) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(onboardingMsg);
      }
      if (upModMsgCase_ == upModMsgOneofCase.missionStoryChatMsg) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(missionStoryChatMsg);
      }
      if (upModMsgCase_ == upModMsgOneofCase.nativeSpeakerUpMsg) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(nativeSpeakerUpMsg);
      }
      if (upModMsgCase_ == upModMsgOneofCase.userChatRecordUpMsg) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(userChatRecordUpMsg);
      }
      if (firstLang.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(firstLang);
      }
      if (sourceChannel != global::Msg.explore.PB_Explore_SourceChannel.EO_SC_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) sourceChannel);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_ExploreUpMsg other) {
      if (other == null) {
        return;
      }
      if (other.firstLang.Length != 0) {
        firstLang = other.firstLang;
      }
      if (other.sourceChannel != global::Msg.explore.PB_Explore_SourceChannel.EO_SC_UNKNOWN) {
        sourceChannel = other.sourceChannel;
      }
      switch (other.upModMsgCase) {
        case upModMsgOneofCase.recommendMsg:
          if (recommendMsg == null) {
            recommendMsg = new global::Msg.explore.PB_RecommendUpMsg();
          }
          recommendMsg.MergeFrom(other.recommendMsg);
          break;
        case upModMsgOneofCase.dialogMsg:
          if (dialogMsg == null) {
            dialogMsg = new global::Msg.explore.PB_DialogUpMsg();
          }
          dialogMsg.MergeFrom(other.dialogMsg);
          break;
        case upModMsgOneofCase.userSettingMsg:
          if (userSettingMsg == null) {
            userSettingMsg = new global::Msg.explore.PB_UserSettingUpMsg();
          }
          userSettingMsg.MergeFrom(other.userSettingMsg);
          break;
        case upModMsgOneofCase.matchingMsg:
          if (matchingMsg == null) {
            matchingMsg = new global::Msg.explore.PB_MatchingUpMsg();
          }
          matchingMsg.MergeFrom(other.matchingMsg);
          break;
        case upModMsgOneofCase.userChatMsg:
          if (userChatMsg == null) {
            userChatMsg = new global::Msg.explore.PB_UserChatUpMsg();
          }
          userChatMsg.MergeFrom(other.userChatMsg);
          break;
        case upModMsgOneofCase.onboardingChatMsg:
          if (onboardingChatMsg == null) {
            onboardingChatMsg = new global::Msg.explore.PB_OnboardingChatUpMsg();
          }
          onboardingChatMsg.MergeFrom(other.onboardingChatMsg);
          break;
        case upModMsgOneofCase.onboardingMsg:
          if (onboardingMsg == null) {
            onboardingMsg = new global::Msg.explore.PB_OnboardingUpMsg();
          }
          onboardingMsg.MergeFrom(other.onboardingMsg);
          break;
        case upModMsgOneofCase.missionStoryChatMsg:
          if (missionStoryChatMsg == null) {
            missionStoryChatMsg = new global::Msg.explore.PB_MissionStoryChatUpMsg();
          }
          missionStoryChatMsg.MergeFrom(other.missionStoryChatMsg);
          break;
        case upModMsgOneofCase.nativeSpeakerUpMsg:
          if (nativeSpeakerUpMsg == null) {
            nativeSpeakerUpMsg = new global::Msg.explore.PB_NativeSpeakerUpMsg();
          }
          nativeSpeakerUpMsg.MergeFrom(other.nativeSpeakerUpMsg);
          break;
        case upModMsgOneofCase.userChatRecordUpMsg:
          if (userChatRecordUpMsg == null) {
            userChatRecordUpMsg = new global::Msg.explore.PB_UserChatRecordUpMsg();
          }
          userChatRecordUpMsg.MergeFrom(other.userChatRecordUpMsg);
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            global::Msg.explore.PB_RecommendUpMsg subBuilder = new global::Msg.explore.PB_RecommendUpMsg();
            if (upModMsgCase_ == upModMsgOneofCase.recommendMsg) {
              subBuilder.MergeFrom(recommendMsg);
            }
            input.ReadMessage(subBuilder);
            recommendMsg = subBuilder;
            break;
          }
          case 18: {
            global::Msg.explore.PB_DialogUpMsg subBuilder = new global::Msg.explore.PB_DialogUpMsg();
            if (upModMsgCase_ == upModMsgOneofCase.dialogMsg) {
              subBuilder.MergeFrom(dialogMsg);
            }
            input.ReadMessage(subBuilder);
            dialogMsg = subBuilder;
            break;
          }
          case 26: {
            firstLang = input.ReadString();
            break;
          }
          case 34: {
            global::Msg.explore.PB_UserSettingUpMsg subBuilder = new global::Msg.explore.PB_UserSettingUpMsg();
            if (upModMsgCase_ == upModMsgOneofCase.userSettingMsg) {
              subBuilder.MergeFrom(userSettingMsg);
            }
            input.ReadMessage(subBuilder);
            userSettingMsg = subBuilder;
            break;
          }
          case 42: {
            global::Msg.explore.PB_MatchingUpMsg subBuilder = new global::Msg.explore.PB_MatchingUpMsg();
            if (upModMsgCase_ == upModMsgOneofCase.matchingMsg) {
              subBuilder.MergeFrom(matchingMsg);
            }
            input.ReadMessage(subBuilder);
            matchingMsg = subBuilder;
            break;
          }
          case 50: {
            global::Msg.explore.PB_UserChatUpMsg subBuilder = new global::Msg.explore.PB_UserChatUpMsg();
            if (upModMsgCase_ == upModMsgOneofCase.userChatMsg) {
              subBuilder.MergeFrom(userChatMsg);
            }
            input.ReadMessage(subBuilder);
            userChatMsg = subBuilder;
            break;
          }
          case 58: {
            global::Msg.explore.PB_OnboardingChatUpMsg subBuilder = new global::Msg.explore.PB_OnboardingChatUpMsg();
            if (upModMsgCase_ == upModMsgOneofCase.onboardingChatMsg) {
              subBuilder.MergeFrom(onboardingChatMsg);
            }
            input.ReadMessage(subBuilder);
            onboardingChatMsg = subBuilder;
            break;
          }
          case 64: {
            sourceChannel = (global::Msg.explore.PB_Explore_SourceChannel) input.ReadEnum();
            break;
          }
          case 74: {
            global::Msg.explore.PB_OnboardingUpMsg subBuilder = new global::Msg.explore.PB_OnboardingUpMsg();
            if (upModMsgCase_ == upModMsgOneofCase.onboardingMsg) {
              subBuilder.MergeFrom(onboardingMsg);
            }
            input.ReadMessage(subBuilder);
            onboardingMsg = subBuilder;
            break;
          }
          case 82: {
            global::Msg.explore.PB_MissionStoryChatUpMsg subBuilder = new global::Msg.explore.PB_MissionStoryChatUpMsg();
            if (upModMsgCase_ == upModMsgOneofCase.missionStoryChatMsg) {
              subBuilder.MergeFrom(missionStoryChatMsg);
            }
            input.ReadMessage(subBuilder);
            missionStoryChatMsg = subBuilder;
            break;
          }
          case 90: {
            global::Msg.explore.PB_NativeSpeakerUpMsg subBuilder = new global::Msg.explore.PB_NativeSpeakerUpMsg();
            if (upModMsgCase_ == upModMsgOneofCase.nativeSpeakerUpMsg) {
              subBuilder.MergeFrom(nativeSpeakerUpMsg);
            }
            input.ReadMessage(subBuilder);
            nativeSpeakerUpMsg = subBuilder;
            break;
          }
          case 98: {
            global::Msg.explore.PB_UserChatRecordUpMsg subBuilder = new global::Msg.explore.PB_UserChatRecordUpMsg();
            if (upModMsgCase_ == upModMsgOneofCase.userChatRecordUpMsg) {
              subBuilder.MergeFrom(userChatRecordUpMsg);
            }
            input.ReadMessage(subBuilder);
            userChatRecordUpMsg = subBuilder;
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            global::Msg.explore.PB_RecommendUpMsg subBuilder = new global::Msg.explore.PB_RecommendUpMsg();
            if (upModMsgCase_ == upModMsgOneofCase.recommendMsg) {
              subBuilder.MergeFrom(recommendMsg);
            }
            input.ReadMessage(subBuilder);
            recommendMsg = subBuilder;
            break;
          }
          case 18: {
            global::Msg.explore.PB_DialogUpMsg subBuilder = new global::Msg.explore.PB_DialogUpMsg();
            if (upModMsgCase_ == upModMsgOneofCase.dialogMsg) {
              subBuilder.MergeFrom(dialogMsg);
            }
            input.ReadMessage(subBuilder);
            dialogMsg = subBuilder;
            break;
          }
          case 26: {
            firstLang = input.ReadString();
            break;
          }
          case 34: {
            global::Msg.explore.PB_UserSettingUpMsg subBuilder = new global::Msg.explore.PB_UserSettingUpMsg();
            if (upModMsgCase_ == upModMsgOneofCase.userSettingMsg) {
              subBuilder.MergeFrom(userSettingMsg);
            }
            input.ReadMessage(subBuilder);
            userSettingMsg = subBuilder;
            break;
          }
          case 42: {
            global::Msg.explore.PB_MatchingUpMsg subBuilder = new global::Msg.explore.PB_MatchingUpMsg();
            if (upModMsgCase_ == upModMsgOneofCase.matchingMsg) {
              subBuilder.MergeFrom(matchingMsg);
            }
            input.ReadMessage(subBuilder);
            matchingMsg = subBuilder;
            break;
          }
          case 50: {
            global::Msg.explore.PB_UserChatUpMsg subBuilder = new global::Msg.explore.PB_UserChatUpMsg();
            if (upModMsgCase_ == upModMsgOneofCase.userChatMsg) {
              subBuilder.MergeFrom(userChatMsg);
            }
            input.ReadMessage(subBuilder);
            userChatMsg = subBuilder;
            break;
          }
          case 58: {
            global::Msg.explore.PB_OnboardingChatUpMsg subBuilder = new global::Msg.explore.PB_OnboardingChatUpMsg();
            if (upModMsgCase_ == upModMsgOneofCase.onboardingChatMsg) {
              subBuilder.MergeFrom(onboardingChatMsg);
            }
            input.ReadMessage(subBuilder);
            onboardingChatMsg = subBuilder;
            break;
          }
          case 64: {
            sourceChannel = (global::Msg.explore.PB_Explore_SourceChannel) input.ReadEnum();
            break;
          }
          case 74: {
            global::Msg.explore.PB_OnboardingUpMsg subBuilder = new global::Msg.explore.PB_OnboardingUpMsg();
            if (upModMsgCase_ == upModMsgOneofCase.onboardingMsg) {
              subBuilder.MergeFrom(onboardingMsg);
            }
            input.ReadMessage(subBuilder);
            onboardingMsg = subBuilder;
            break;
          }
          case 82: {
            global::Msg.explore.PB_MissionStoryChatUpMsg subBuilder = new global::Msg.explore.PB_MissionStoryChatUpMsg();
            if (upModMsgCase_ == upModMsgOneofCase.missionStoryChatMsg) {
              subBuilder.MergeFrom(missionStoryChatMsg);
            }
            input.ReadMessage(subBuilder);
            missionStoryChatMsg = subBuilder;
            break;
          }
          case 90: {
            global::Msg.explore.PB_NativeSpeakerUpMsg subBuilder = new global::Msg.explore.PB_NativeSpeakerUpMsg();
            if (upModMsgCase_ == upModMsgOneofCase.nativeSpeakerUpMsg) {
              subBuilder.MergeFrom(nativeSpeakerUpMsg);
            }
            input.ReadMessage(subBuilder);
            nativeSpeakerUpMsg = subBuilder;
            break;
          }
          case 98: {
            global::Msg.explore.PB_UserChatRecordUpMsg subBuilder = new global::Msg.explore.PB_UserChatRecordUpMsg();
            if (upModMsgCase_ == upModMsgOneofCase.userChatRecordUpMsg) {
              subBuilder.MergeFrom(userChatRecordUpMsg);
            }
            input.ReadMessage(subBuilder);
            userChatRecordUpMsg = subBuilder;
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///
  /// Explore长连接下行消息（stream）
  /// 1. 下行消息为通用结构，客户端需要根据消息id来映射使用那种结构进行解析
  /// 2. data字段为bytes的原因是因为客户端的msgHandle解析器同一使用bytes处理
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_ExploreDownMsg : pb::IMessage<SC_ExploreDownMsg>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_ExploreDownMsg> _parser = new pb::MessageParser<SC_ExploreDownMsg>(() => new SC_ExploreDownMsg());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_ExploreDownMsg> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.GatewayReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_ExploreDownMsg() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_ExploreDownMsg(SC_ExploreDownMsg other) : this() {
      code_ = other.code_;
      msgId_ = other.msgId_;
      data_ = other.data_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_ExploreDownMsg Clone() {
      return new SC_ExploreDownMsg(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_Explore_BizCode code_ = global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN;
    /// <summary>
    /// 业务状态码
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_Explore_BizCode code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "msgId" field.</summary>
    public const int msgIdFieldNumber = 2;
    private int msgId_;
    /// <summary>
    /// 消息id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int msgId {
      get { return msgId_; }
      set {
        msgId_ = value;
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 3;
    private pb::ByteString data_ = pb::ByteString.Empty;
    /// <summary>
    /// 消息数据（数据本身对应不同的下行业务数据结构）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString data {
      get { return data_; }
      set {
        data_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_ExploreDownMsg);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_ExploreDownMsg other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (msgId != other.msgId) return false;
      if (data != other.data) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) hash ^= code.GetHashCode();
      if (msgId != 0) hash ^= msgId.GetHashCode();
      if (data.Length != 0) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (msgId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(msgId);
      }
      if (data.Length != 0) {
        output.WriteRawTag(26);
        output.WriteBytes(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (msgId != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(msgId);
      }
      if (data.Length != 0) {
        output.WriteRawTag(26);
        output.WriteBytes(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (msgId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(msgId);
      }
      if (data.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_ExploreDownMsg other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_Explore_BizCode.EO_BIZ_CODE_UNKNOWN) {
        code = other.code;
      }
      if (other.msgId != 0) {
        msgId = other.msgId;
      }
      if (other.data.Length != 0) {
        data = other.data;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 16: {
            msgId = input.ReadInt32();
            break;
          }
          case 26: {
            data = input.ReadBytes();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_Explore_BizCode) input.ReadEnum();
            break;
          }
          case 16: {
            msgId = input.ReadInt32();
            break;
          }
          case 26: {
            data = input.ReadBytes();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
