/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Explore
{
    public partial class ExploreAdviceItem : UIBindT
    {
        public override string pkgName => "Explore";
        public override string comName => "ExploreAdviceItem";

        public GImage back;
        public GComponent tfOrigin;
        public GRichTextField txtName;
        public GGroup grpTitle;
        public ExploreNoticeBtn btnNotice;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            back = (GImage)com.GetChildAt(0);
            tfOrigin = (GComponent)com.GetChildAt(1);
            txtName = (GRichTextField)com.GetChildAt(3);
            grpTitle = (GGroup)com.GetChildAt(4);
            btnNotice = new ExploreNoticeBtn();
            btnNotice.Construct(com.GetChildAt(5).asCom);

            SetMultiLanguageInChildren();
            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            back = null;
            tfOrigin = null;
            txtName = null;
            grpTitle = null;
            btnNotice.Dispose();
            btnNotice = null;
        }

        public void SetMultiLanguageInChildren()
        {
            this.back.SetKey("BLUR");  // ""
        }
    }
}