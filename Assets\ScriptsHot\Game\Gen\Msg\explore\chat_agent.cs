// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/explore/chat_agent.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.explore {

  /// <summary>Holder for reflection information generated from protobuf/explore/chat_agent.proto</summary>
  public static partial class ChatAgentReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/explore/chat_agent.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static ChatAgentReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CiFwcm90b2J1Zi9leHBsb3JlL2NoYXRfYWdlbnQucHJvdG8aG3Byb3RvYnVm",
            "L2Jhc2ljL2RpYWxvZy5wcm90bxogcHJvdG9idWYvc29jaWFsL3dvcmxkX2No",
            "YXQucHJvdG8idAoSUEJfQ2hhdEFnZW50X0ZyYW1lEicKBHRleHQYASABKAsy",
            "Fy5QQl9DaGF0QWdlbnRfVGV4dEZyYW1lSAASLAoFYXVkaW8YAiABKAsyGy5Q",
            "Ql9DaGF0QWdlbnRfQXVkaW9SYXdGcmFtZUgAQgcKBWZyYW1lIsUBChpQQl9D",
            "aGF0QWdlbnRfQXVkaW9SYXdGcmFtZRIKCgJpZBgBIAEoBBIMCgRuYW1lGAIg",
            "ASgJEg0KBWF1ZGlvGAMgASgMEhMKC3NhbXBsZV9yYXRlGAQgASgNEhQKDG51",
            "bV9jaGFubmVscxgFIAEoDRIRCglidWJibGVfaWQYBiABKAkSFAoMaXNfbGFz",
            "dF9jbGlwGAcgASgIEioKCWFsaWdubWVudBgIIAEoCzIXLlBCX0NoYXRBZ2Vu",
            "dF9BbGlnbm1lbnQiSQoWUEJfQ2hhdEFnZW50X0FsaWdubWVudBINCgV3b3Jk",
            "cxgBIAMoCRIgChh3b3JkX3N0YXJ0X3RpbWVzX3NlY29uZHMYAiADKAIiUgoW",
            "UEJfQ2hhdEFnZW50X1RleHRGcmFtZRIKCgJpZBgBIAEoBBIMCgRuYW1lGAIg",
            "ASgJEgwKBHRleHQYAyABKAkSEAoIZHVyYXRpb24YBCABKAUiqQEKGlBCX0No",
            "YXRBZ2VudF9CaXpFdmVudEZyYW1lEgoKAmlkGAEgASgEEgwKBG5hbWUYAiAB",
            "KAkSFAoMYml6RXZlbnROYW1lGAMgASgJEksKFHVzZXJNYW51YWxTdWJtaXRE",
            "YXRhGAQgASgLMisuUEJfQ2hhdEFnZW50X0JpekV2ZW50RGF0YV9Vc2VyTWFu",
            "dWFsU3VibWl0SABCDgoMYml6RXZlbnREYXRhIkMKKlBCX0NoYXRBZ2VudF9C",
            "aXpFdmVudERhdGFfVXNlck1hbnVhbFN1Ym1pdBIVCg1zcGVha2luZ1NwZWVk",
            "GAEgASgFIlcKJ1BCX0NoYXRBZ2VudF9TdWJHb2FsSnVkZ21lbnRSZXN1bHRG",
            "cmFtZRIsCgpnb2FsUmVzdWx0GAEgAygLMhguUEJfQ2hhdEFnZW50X0dvYWxS",
            "ZXN1bHQiTwoXUEJfQ2hhdEFnZW50X0dvYWxSZXN1bHQSDgoGZ29hbElkGAEg",
            "ASgJEhIKCmlzQ29tcGxldGUYAiABKAgSEAoIaXNDaGFuZ2UYAyABKAgiPwom",
            "UEJfQ2hhdEFnZW50X01pc3Npb25TdG9yeVByb2dyZXNzRnJhbWUSFQoNY3Vy",
            "cmVudFN0ZXBObxgBIAEoBSKpAQofQ1NfQ2hhdEFnZW50X0RpYWxvZ1NldHRp",
            "bmdSZXFWMhIOCgZ0YXNrSWQYASABKAkSDgoGdXNlcklkGAIgASgJEhEKCWNl",
            "ZnJMZXZlbBgDIAEoCRIRCglmaXJzdExhbmcYBCABKAkSEgoKYXZhdGFySW5m",
            "bxgFIAEoCRIVCg1jbGllbnRWZXJzaW9uGAYgASgJEhUKDXNwZWFraW5nU3Bl",
            "ZWQYByABKAUigAEKIFNDX0NoYXRBZ2VudF9EaWFsb2dTZXR0aW5nUmVzcFYy",
            "EiAKBGNvZGUYASABKA4yEi5QQl9DaGF0QWdlbnRfQ29kZRILCgNtc2cYAiAB",
            "KAkSLQoEZGF0YRgDIAEoCzIfLlBCX0NoYXRBZ2VudF9EaWFsb2dTZXR0aW5n",
            "RGF0YSJtCh9DU19DaGF0QWdlbnRfVXNlckNoYXRTZXR0aW5nUmVxEhAKCGRp",
            "YWxvZ0lkGAEgASgJEg4KBnVzZXJJZBgCIAEoCRIRCglmaXJzdExhbmcYAyAB",
            "KAkSFQoNY2xpZW50VmVyc2lvbhgEIAEoCSKAAQogU0NfQ2hhdEFnZW50X1Vz",
            "ZXJDaGF0U2V0dGluZ1Jlc3ASIAoEY29kZRgBIAEoDjISLlBCX0NoYXRBZ2Vu",
            "dF9Db2RlEgsKA21zZxgCIAEoCRItCgRkYXRhGAMgASgLMh8uUEJfQ2hhdEFn",
            "ZW50X0RpYWxvZ1NldHRpbmdEYXRhIqMBCiVDU19DaGF0QWdlbnRfT25ib2Fy",
            "ZGluZ0NoYXRTZXR0aW5nUmVxEg4KBnVzZXJJZBgBIAEoCRIaChJsZWFybmlu",
            "Z01vdGl2YXRpb24YAiABKAUSEQoJZmlyc3RMYW5nGAMgASgJEhAKCGF2YXRh",
            "cklkGAQgASgJEhIKCmF2YXRhckluZm8YBSABKAkSFQoNY2xpZW50VmVyc2lv",
            "bhgGIAEoCSKGAQomU0NfQ2hhdEFnZW50X09uYm9hcmRpbmdDaGF0U2V0dGlu",
            "Z1Jlc3ASIAoEY29kZRgBIAEoDjISLlBCX0NoYXRBZ2VudF9Db2RlEgsKA21z",
            "ZxgCIAEoCRItCgRkYXRhGAMgASgLMh8uUEJfQ2hhdEFnZW50X0RpYWxvZ1Nl",
            "dHRpbmdEYXRhIuYBCidDU19DaGF0QWdlbnRfTWlzc2lvblN0b3J5Q2hhdFNl",
            "dHRpbmdSZXESDgoGdGFza0lkGAEgASgJEg4KBnVzZXJJZBgCIAEoCRIRCglj",
            "ZWZyTGV2ZWwYAyABKAkSEQoJZmlyc3RMYW5nGAQgASgJEhAKCGF2YXRhcklk",
            "GAUgASgJEhIKCmF2YXRhckluZm8YBiABKAkSFQoNY2xpZW50VmVyc2lvbhgH",
            "IAEoCRIVCg1zcGVha2luZ1NwZWVkGAggASgFEg8KB3N0b3J5SWQYCSABKAkS",
            "EAoIZGlhbG9nSWQYCiABKAkiiAEKKFNDX0NoYXRBZ2VudF9NaXNzaW9uU3Rv",
            "cnlDaGF0U2V0dGluZ1Jlc3ASIAoEY29kZRgBIAEoDjISLlBCX0NoYXRBZ2Vu",
            "dF9Db2RlEgsKA21zZxgCIAEoCRItCgRkYXRhGAMgASgLMh8uUEJfQ2hhdEFn",
            "ZW50X0RpYWxvZ1NldHRpbmdEYXRhIkUKHlBCX0NoYXRBZ2VudF9EaWFsb2dT",
            "ZXR0aW5nRGF0YRIQCghkaWFsb2dJZBgBIAEoCRIRCglzZXNzaW9uSWQYAiAB",
            "KAkizQEKEkNTX0NoYXRBZ2VudF9JbnB1dBIQCghkaWFsb2dJZBgBIAEoCRIQ",
            "CghidWJibGVJZBgCIAEoCRIsCgVhdWRpbxgDIAEoCzIbLlBCX0NoYXRBZ2Vu",
            "dF9BdWRpb1Jhd0ZyYW1lSAASJwoEdGV4dBgEIAEoCzIXLlBCX0NoYXRBZ2Vu",
            "dF9UZXh0RnJhbWVIABIvCghiaXpFdmVudBgFIAEoCzIbLlBCX0NoYXRBZ2Vu",
            "dF9CaXpFdmVudEZyYW1lSABCCwoJaW5wdXREYXRhItsEChNTQ19DaGF0QWdl",
            "bnRfT3V0cHV0EhAKCGRpYWxvZ0lkGAEgASgJEhEKCXNlc3Npb25JZBgCIAEo",
            "CRINCgVtc2dJZBgDIAEoCRIQCghidWJibGVJZBgEIAEoCRIfCghtc2dPd25l",
            "chgFIAEoDjINLlBCX01zZ0JlbG9uZxIkCgtiaXpEYXRhVHlwZRgGIAEoDjIP",
            "LlBCX0JpekRhdGFUeXBlEiwKBWF1ZGlvGAcgASgLMhsuUEJfQ2hhdEFnZW50",
            "X0F1ZGlvUmF3RnJhbWVIABInCgR0ZXh0GAggASgLMhcuUEJfQ2hhdEFnZW50",
            "X1RleHRGcmFtZUgAEi8KCGJpekV2ZW50GAkgASgLMhsuUEJfQ2hhdEFnZW50",
            "X0JpekV2ZW50RnJhbWVIABJJChVzdWJHb2FsSnVkZ21lbnRSZXN1bHQYDSAB",
            "KAsyKC5QQl9DaGF0QWdlbnRfU3ViR29hbEp1ZGdtZW50UmVzdWx0RnJhbWVI",
            "ABJHChRtaXNzaW9uU3RvcnlQcm9ncmVzcxgQIAEoCzInLlBCX0NoYXRBZ2Vu",
            "dF9NaXNzaW9uU3RvcnlQcm9ncmVzc0ZyYW1lSAASIAoEY29kZRgKIAEoDjIS",
            "LlBCX0NoYXRBZ2VudF9Db2RlEg0KBXJvdW5kGAsgASgFEg4KBnRhc2tJZBgM",
            "IAEoCRIQCgh0YXNrTW9kZRgOIAEoBRISCgppc0ZpbmlzaGVkGA8gASgIEg8K",
            "B3N0b3J5SWQYESABKAkSFQoNdXNlckNlZnJMZXZlbBgSIAEoCUIMCgpvdXRw",
            "dXREYXRhKmwKEVBCX0NoYXRBZ2VudF9Db2RlEhAKDFVOS05PV05fQ09ERRAA",
            "EhAKDFNZU1RFTV9FUlJPUhABEg0KCUxMTV9FUlJPUhACEhYKElNUVF9SRUNP",
            "R05JWkVfRkFJTBADEgwKB1NVQ0NFU1MQyAFCKloadmZfcHJvdG9idWYvc2Vy",
            "dmVyL2V4cGxvcmWqAgtNc2cuZXhwbG9yZWIGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Msg.basic.DialogReflection.Descriptor, global::Msg.social.WorldChatReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Msg.explore.PB_ChatAgent_Code), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_ChatAgent_Frame), global::Msg.explore.PB_ChatAgent_Frame.Parser, new[]{ "text", "audio" }, new[]{ "frame" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_ChatAgent_AudioRawFrame), global::Msg.explore.PB_ChatAgent_AudioRawFrame.Parser, new[]{ "id", "name", "audio", "sample_rate", "num_channels", "bubble_id", "is_last_clip", "alignment" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_ChatAgent_Alignment), global::Msg.explore.PB_ChatAgent_Alignment.Parser, new[]{ "words", "word_start_times_seconds" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_ChatAgent_TextFrame), global::Msg.explore.PB_ChatAgent_TextFrame.Parser, new[]{ "id", "name", "text", "duration" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_ChatAgent_BizEventFrame), global::Msg.explore.PB_ChatAgent_BizEventFrame.Parser, new[]{ "id", "name", "bizEventName", "userManualSubmitData" }, new[]{ "bizEventData" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_ChatAgent_BizEventData_UserManualSubmit), global::Msg.explore.PB_ChatAgent_BizEventData_UserManualSubmit.Parser, new[]{ "speakingSpeed" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_ChatAgent_SubGoalJudgmentResultFrame), global::Msg.explore.PB_ChatAgent_SubGoalJudgmentResultFrame.Parser, new[]{ "goalResult" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_ChatAgent_GoalResult), global::Msg.explore.PB_ChatAgent_GoalResult.Parser, new[]{ "goalId", "isComplete", "isChange" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_ChatAgent_MissionStoryProgressFrame), global::Msg.explore.PB_ChatAgent_MissionStoryProgressFrame.Parser, new[]{ "currentStepNo" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.CS_ChatAgent_DialogSettingReqV2), global::Msg.explore.CS_ChatAgent_DialogSettingReqV2.Parser, new[]{ "taskId", "userId", "cefrLevel", "firstLang", "avatarInfo", "clientVersion", "speakingSpeed" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_ChatAgent_DialogSettingRespV2), global::Msg.explore.SC_ChatAgent_DialogSettingRespV2.Parser, new[]{ "code", "msg", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.CS_ChatAgent_UserChatSettingReq), global::Msg.explore.CS_ChatAgent_UserChatSettingReq.Parser, new[]{ "dialogId", "userId", "firstLang", "clientVersion" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_ChatAgent_UserChatSettingResp), global::Msg.explore.SC_ChatAgent_UserChatSettingResp.Parser, new[]{ "code", "msg", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.CS_ChatAgent_OnboardingChatSettingReq), global::Msg.explore.CS_ChatAgent_OnboardingChatSettingReq.Parser, new[]{ "userId", "learningMotivation", "firstLang", "avatarId", "avatarInfo", "clientVersion" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_ChatAgent_OnboardingChatSettingResp), global::Msg.explore.SC_ChatAgent_OnboardingChatSettingResp.Parser, new[]{ "code", "msg", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.CS_ChatAgent_MissionStoryChatSettingReq), global::Msg.explore.CS_ChatAgent_MissionStoryChatSettingReq.Parser, new[]{ "taskId", "userId", "cefrLevel", "firstLang", "avatarId", "avatarInfo", "clientVersion", "speakingSpeed", "storyId", "dialogId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_ChatAgent_MissionStoryChatSettingResp), global::Msg.explore.SC_ChatAgent_MissionStoryChatSettingResp.Parser, new[]{ "code", "msg", "data" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.PB_ChatAgent_DialogSettingData), global::Msg.explore.PB_ChatAgent_DialogSettingData.Parser, new[]{ "dialogId", "sessionId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.CS_ChatAgent_Input), global::Msg.explore.CS_ChatAgent_Input.Parser, new[]{ "dialogId", "bubbleId", "audio", "text", "bizEvent" }, new[]{ "inputData" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Msg.explore.SC_ChatAgent_Output), global::Msg.explore.SC_ChatAgent_Output.Parser, new[]{ "dialogId", "sessionId", "msgId", "bubbleId", "msgOwner", "bizDataType", "audio", "text", "bizEvent", "subGoalJudgmentResult", "missionStoryProgress", "code", "round", "taskId", "taskMode", "isFinished", "storyId", "userCefrLevel" }, new[]{ "outputData" }, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  /// <summary>
  /// 状态码 
  /// </summary>
  public enum PB_ChatAgent_Code {
    /// <summary>
    /// 无意义
    /// </summary>
    [pbr::OriginalName("UNKNOWN_CODE")] UNKNOWN_CODE = 0,
    /// <summary>
    /// 系统内部异常
    /// </summary>
    [pbr::OriginalName("SYSTEM_ERROR")] SYSTEM_ERROR = 1,
    /// <summary>
    /// LLM异常
    /// </summary>
    [pbr::OriginalName("LLM_ERROR")] LLM_ERROR = 2,
    /// <summary>
    /// STT识别失败
    /// </summary>
    [pbr::OriginalName("STT_RECOGNIZE_FAIL")] STT_RECOGNIZE_FAIL = 3,
    /// <summary>
    /// 成功
    /// </summary>
    [pbr::OriginalName("SUCCESS")] SUCCESS = 200,
  }

  #endregion

  #region Messages
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_ChatAgent_Frame : pb::IMessage<PB_ChatAgent_Frame>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_ChatAgent_Frame> _parser = new pb::MessageParser<PB_ChatAgent_Frame>(() => new PB_ChatAgent_Frame());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_ChatAgent_Frame> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.ChatAgentReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_Frame() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_Frame(PB_ChatAgent_Frame other) : this() {
      switch (other.frameCase) {
        case frameOneofCase.text:
          text = other.text.Clone();
          break;
        case frameOneofCase.audio:
          audio = other.audio.Clone();
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_Frame Clone() {
      return new PB_ChatAgent_Frame(this);
    }

    /// <summary>Field number for the "text" field.</summary>
    public const int textFieldNumber = 1;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_ChatAgent_TextFrame text {
      get { return frameCase_ == frameOneofCase.text ? (global::Msg.explore.PB_ChatAgent_TextFrame) frame_ : null; }
      set {
        frame_ = value;
        frameCase_ = value == null ? frameOneofCase.None : frameOneofCase.text;
      }
    }

    /// <summary>Field number for the "audio" field.</summary>
    public const int audioFieldNumber = 2;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_ChatAgent_AudioRawFrame audio {
      get { return frameCase_ == frameOneofCase.audio ? (global::Msg.explore.PB_ChatAgent_AudioRawFrame) frame_ : null; }
      set {
        frame_ = value;
        frameCase_ = value == null ? frameOneofCase.None : frameOneofCase.audio;
      }
    }

    private object frame_;
    /// <summary>Enum of possible cases for the "frame" oneof.</summary>
    public enum frameOneofCase {
      None = 0,
      text = 1,
      audio = 2,
    }
    private frameOneofCase frameCase_ = frameOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public frameOneofCase frameCase {
      get { return frameCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void Clearframe() {
      frameCase_ = frameOneofCase.None;
      frame_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_ChatAgent_Frame);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_ChatAgent_Frame other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(text, other.text)) return false;
      if (!object.Equals(audio, other.audio)) return false;
      if (frameCase != other.frameCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (frameCase_ == frameOneofCase.text) hash ^= text.GetHashCode();
      if (frameCase_ == frameOneofCase.audio) hash ^= audio.GetHashCode();
      hash ^= (int) frameCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (frameCase_ == frameOneofCase.text) {
        output.WriteRawTag(10);
        output.WriteMessage(text);
      }
      if (frameCase_ == frameOneofCase.audio) {
        output.WriteRawTag(18);
        output.WriteMessage(audio);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (frameCase_ == frameOneofCase.text) {
        output.WriteRawTag(10);
        output.WriteMessage(text);
      }
      if (frameCase_ == frameOneofCase.audio) {
        output.WriteRawTag(18);
        output.WriteMessage(audio);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (frameCase_ == frameOneofCase.text) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(text);
      }
      if (frameCase_ == frameOneofCase.audio) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(audio);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_ChatAgent_Frame other) {
      if (other == null) {
        return;
      }
      switch (other.frameCase) {
        case frameOneofCase.text:
          if (text == null) {
            text = new global::Msg.explore.PB_ChatAgent_TextFrame();
          }
          text.MergeFrom(other.text);
          break;
        case frameOneofCase.audio:
          if (audio == null) {
            audio = new global::Msg.explore.PB_ChatAgent_AudioRawFrame();
          }
          audio.MergeFrom(other.audio);
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            global::Msg.explore.PB_ChatAgent_TextFrame subBuilder = new global::Msg.explore.PB_ChatAgent_TextFrame();
            if (frameCase_ == frameOneofCase.text) {
              subBuilder.MergeFrom(text);
            }
            input.ReadMessage(subBuilder);
            text = subBuilder;
            break;
          }
          case 18: {
            global::Msg.explore.PB_ChatAgent_AudioRawFrame subBuilder = new global::Msg.explore.PB_ChatAgent_AudioRawFrame();
            if (frameCase_ == frameOneofCase.audio) {
              subBuilder.MergeFrom(audio);
            }
            input.ReadMessage(subBuilder);
            audio = subBuilder;
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            global::Msg.explore.PB_ChatAgent_TextFrame subBuilder = new global::Msg.explore.PB_ChatAgent_TextFrame();
            if (frameCase_ == frameOneofCase.text) {
              subBuilder.MergeFrom(text);
            }
            input.ReadMessage(subBuilder);
            text = subBuilder;
            break;
          }
          case 18: {
            global::Msg.explore.PB_ChatAgent_AudioRawFrame subBuilder = new global::Msg.explore.PB_ChatAgent_AudioRawFrame();
            if (frameCase_ == frameOneofCase.audio) {
              subBuilder.MergeFrom(audio);
            }
            input.ReadMessage(subBuilder);
            audio = subBuilder;
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 音频框架
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_ChatAgent_AudioRawFrame : pb::IMessage<PB_ChatAgent_AudioRawFrame>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_ChatAgent_AudioRawFrame> _parser = new pb::MessageParser<PB_ChatAgent_AudioRawFrame>(() => new PB_ChatAgent_AudioRawFrame());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_ChatAgent_AudioRawFrame> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.ChatAgentReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_AudioRawFrame() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_AudioRawFrame(PB_ChatAgent_AudioRawFrame other) : this() {
      id_ = other.id_;
      name_ = other.name_;
      audio_ = other.audio_;
      sample_rate_ = other.sample_rate_;
      num_channels_ = other.num_channels_;
      bubble_id_ = other.bubble_id_;
      is_last_clip_ = other.is_last_clip_;
      alignment_ = other.alignment_ != null ? other.alignment_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_AudioRawFrame Clone() {
      return new PB_ChatAgent_AudioRawFrame(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int idFieldNumber = 1;
    private ulong id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int nameFieldNumber = 2;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "audio" field.</summary>
    public const int audioFieldNumber = 3;
    private pb::ByteString audio_ = pb::ByteString.Empty;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pb::ByteString audio {
      get { return audio_; }
      set {
        audio_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "sample_rate" field.</summary>
    public const int sample_rateFieldNumber = 4;
    private uint sample_rate_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint sample_rate {
      get { return sample_rate_; }
      set {
        sample_rate_ = value;
      }
    }

    /// <summary>Field number for the "num_channels" field.</summary>
    public const int num_channelsFieldNumber = 5;
    private uint num_channels_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint num_channels {
      get { return num_channels_; }
      set {
        num_channels_ = value;
      }
    }

    /// <summary>Field number for the "bubble_id" field.</summary>
    public const int bubble_idFieldNumber = 6;
    private string bubble_id_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string bubble_id {
      get { return bubble_id_; }
      set {
        bubble_id_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "is_last_clip" field.</summary>
    public const int is_last_clipFieldNumber = 7;
    private bool is_last_clip_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool is_last_clip {
      get { return is_last_clip_; }
      set {
        is_last_clip_ = value;
      }
    }

    /// <summary>Field number for the "alignment" field.</summary>
    public const int alignmentFieldNumber = 8;
    private global::Msg.explore.PB_ChatAgent_Alignment alignment_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_ChatAgent_Alignment alignment {
      get { return alignment_; }
      set {
        alignment_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_ChatAgent_AudioRawFrame);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_ChatAgent_AudioRawFrame other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (id != other.id) return false;
      if (name != other.name) return false;
      if (audio != other.audio) return false;
      if (sample_rate != other.sample_rate) return false;
      if (num_channels != other.num_channels) return false;
      if (bubble_id != other.bubble_id) return false;
      if (is_last_clip != other.is_last_clip) return false;
      if (!object.Equals(alignment, other.alignment)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (id != 0UL) hash ^= id.GetHashCode();
      if (name.Length != 0) hash ^= name.GetHashCode();
      if (audio.Length != 0) hash ^= audio.GetHashCode();
      if (sample_rate != 0) hash ^= sample_rate.GetHashCode();
      if (num_channels != 0) hash ^= num_channels.GetHashCode();
      if (bubble_id.Length != 0) hash ^= bubble_id.GetHashCode();
      if (is_last_clip != false) hash ^= is_last_clip.GetHashCode();
      if (alignment_ != null) hash ^= alignment.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(id);
      }
      if (name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(name);
      }
      if (audio.Length != 0) {
        output.WriteRawTag(26);
        output.WriteBytes(audio);
      }
      if (sample_rate != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(sample_rate);
      }
      if (num_channels != 0) {
        output.WriteRawTag(40);
        output.WriteUInt32(num_channels);
      }
      if (bubble_id.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(bubble_id);
      }
      if (is_last_clip != false) {
        output.WriteRawTag(56);
        output.WriteBool(is_last_clip);
      }
      if (alignment_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(alignment);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(id);
      }
      if (name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(name);
      }
      if (audio.Length != 0) {
        output.WriteRawTag(26);
        output.WriteBytes(audio);
      }
      if (sample_rate != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(sample_rate);
      }
      if (num_channels != 0) {
        output.WriteRawTag(40);
        output.WriteUInt32(num_channels);
      }
      if (bubble_id.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(bubble_id);
      }
      if (is_last_clip != false) {
        output.WriteRawTag(56);
        output.WriteBool(is_last_clip);
      }
      if (alignment_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(alignment);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (id != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(id);
      }
      if (name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(name);
      }
      if (audio.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeBytesSize(audio);
      }
      if (sample_rate != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(sample_rate);
      }
      if (num_channels != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(num_channels);
      }
      if (bubble_id.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(bubble_id);
      }
      if (is_last_clip != false) {
        size += 1 + 1;
      }
      if (alignment_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(alignment);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_ChatAgent_AudioRawFrame other) {
      if (other == null) {
        return;
      }
      if (other.id != 0UL) {
        id = other.id;
      }
      if (other.name.Length != 0) {
        name = other.name;
      }
      if (other.audio.Length != 0) {
        audio = other.audio;
      }
      if (other.sample_rate != 0) {
        sample_rate = other.sample_rate;
      }
      if (other.num_channels != 0) {
        num_channels = other.num_channels;
      }
      if (other.bubble_id.Length != 0) {
        bubble_id = other.bubble_id;
      }
      if (other.is_last_clip != false) {
        is_last_clip = other.is_last_clip;
      }
      if (other.alignment_ != null) {
        if (alignment_ == null) {
          alignment = new global::Msg.explore.PB_ChatAgent_Alignment();
        }
        alignment.MergeFrom(other.alignment);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            id = input.ReadUInt64();
            break;
          }
          case 18: {
            name = input.ReadString();
            break;
          }
          case 26: {
            audio = input.ReadBytes();
            break;
          }
          case 32: {
            sample_rate = input.ReadUInt32();
            break;
          }
          case 40: {
            num_channels = input.ReadUInt32();
            break;
          }
          case 50: {
            bubble_id = input.ReadString();
            break;
          }
          case 56: {
            is_last_clip = input.ReadBool();
            break;
          }
          case 66: {
            if (alignment_ == null) {
              alignment = new global::Msg.explore.PB_ChatAgent_Alignment();
            }
            input.ReadMessage(alignment);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            id = input.ReadUInt64();
            break;
          }
          case 18: {
            name = input.ReadString();
            break;
          }
          case 26: {
            audio = input.ReadBytes();
            break;
          }
          case 32: {
            sample_rate = input.ReadUInt32();
            break;
          }
          case 40: {
            num_channels = input.ReadUInt32();
            break;
          }
          case 50: {
            bubble_id = input.ReadString();
            break;
          }
          case 56: {
            is_last_clip = input.ReadBool();
            break;
          }
          case 66: {
            if (alignment_ == null) {
              alignment = new global::Msg.explore.PB_ChatAgent_Alignment();
            }
            input.ReadMessage(alignment);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 对齐信息
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_ChatAgent_Alignment : pb::IMessage<PB_ChatAgent_Alignment>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_ChatAgent_Alignment> _parser = new pb::MessageParser<PB_ChatAgent_Alignment>(() => new PB_ChatAgent_Alignment());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_ChatAgent_Alignment> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.ChatAgentReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_Alignment() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_Alignment(PB_ChatAgent_Alignment other) : this() {
      words_ = other.words_.Clone();
      word_start_times_seconds_ = other.word_start_times_seconds_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_Alignment Clone() {
      return new PB_ChatAgent_Alignment(this);
    }

    /// <summary>Field number for the "words" field.</summary>
    public const int wordsFieldNumber = 1;
    private static readonly pb::FieldCodec<string> _repeated_words_codec
        = pb::FieldCodec.ForString(10);
    private readonly pbc::RepeatedField<string> words_ = new pbc::RepeatedField<string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> words {
      get { return words_; }
    }

    /// <summary>Field number for the "word_start_times_seconds" field.</summary>
    public const int word_start_times_secondsFieldNumber = 2;
    private static readonly pb::FieldCodec<float> _repeated_word_start_times_seconds_codec
        = pb::FieldCodec.ForFloat(18);
    private readonly pbc::RepeatedField<float> word_start_times_seconds_ = new pbc::RepeatedField<float>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<float> word_start_times_seconds {
      get { return word_start_times_seconds_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_ChatAgent_Alignment);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_ChatAgent_Alignment other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!words_.Equals(other.words_)) return false;
      if(!word_start_times_seconds_.Equals(other.word_start_times_seconds_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= words_.GetHashCode();
      hash ^= word_start_times_seconds_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      words_.WriteTo(output, _repeated_words_codec);
      word_start_times_seconds_.WriteTo(output, _repeated_word_start_times_seconds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      words_.WriteTo(ref output, _repeated_words_codec);
      word_start_times_seconds_.WriteTo(ref output, _repeated_word_start_times_seconds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += words_.CalculateSize(_repeated_words_codec);
      size += word_start_times_seconds_.CalculateSize(_repeated_word_start_times_seconds_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_ChatAgent_Alignment other) {
      if (other == null) {
        return;
      }
      words_.Add(other.words_);
      word_start_times_seconds_.Add(other.word_start_times_seconds_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            words_.AddEntriesFrom(input, _repeated_words_codec);
            break;
          }
          case 18:
          case 21: {
            word_start_times_seconds_.AddEntriesFrom(input, _repeated_word_start_times_seconds_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            words_.AddEntriesFrom(ref input, _repeated_words_codec);
            break;
          }
          case 18:
          case 21: {
            word_start_times_seconds_.AddEntriesFrom(ref input, _repeated_word_start_times_seconds_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 文本框架
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_ChatAgent_TextFrame : pb::IMessage<PB_ChatAgent_TextFrame>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_ChatAgent_TextFrame> _parser = new pb::MessageParser<PB_ChatAgent_TextFrame>(() => new PB_ChatAgent_TextFrame());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_ChatAgent_TextFrame> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.ChatAgentReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_TextFrame() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_TextFrame(PB_ChatAgent_TextFrame other) : this() {
      id_ = other.id_;
      name_ = other.name_;
      text_ = other.text_;
      duration_ = other.duration_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_TextFrame Clone() {
      return new PB_ChatAgent_TextFrame(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int idFieldNumber = 1;
    private ulong id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int nameFieldNumber = 2;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "text" field.</summary>
    public const int textFieldNumber = 3;
    private string text_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string text {
      get { return text_; }
      set {
        text_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "duration" field.</summary>
    public const int durationFieldNumber = 4;
    private int duration_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int duration {
      get { return duration_; }
      set {
        duration_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_ChatAgent_TextFrame);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_ChatAgent_TextFrame other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (id != other.id) return false;
      if (name != other.name) return false;
      if (text != other.text) return false;
      if (duration != other.duration) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (id != 0UL) hash ^= id.GetHashCode();
      if (name.Length != 0) hash ^= name.GetHashCode();
      if (text.Length != 0) hash ^= text.GetHashCode();
      if (duration != 0) hash ^= duration.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(id);
      }
      if (name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(name);
      }
      if (text.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(text);
      }
      if (duration != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(duration);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(id);
      }
      if (name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(name);
      }
      if (text.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(text);
      }
      if (duration != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(duration);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (id != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(id);
      }
      if (name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(name);
      }
      if (text.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(text);
      }
      if (duration != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(duration);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_ChatAgent_TextFrame other) {
      if (other == null) {
        return;
      }
      if (other.id != 0UL) {
        id = other.id;
      }
      if (other.name.Length != 0) {
        name = other.name;
      }
      if (other.text.Length != 0) {
        text = other.text;
      }
      if (other.duration != 0) {
        duration = other.duration;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            id = input.ReadUInt64();
            break;
          }
          case 18: {
            name = input.ReadString();
            break;
          }
          case 26: {
            text = input.ReadString();
            break;
          }
          case 32: {
            duration = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            id = input.ReadUInt64();
            break;
          }
          case 18: {
            name = input.ReadString();
            break;
          }
          case 26: {
            text = input.ReadString();
            break;
          }
          case 32: {
            duration = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 业务事件框架
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_ChatAgent_BizEventFrame : pb::IMessage<PB_ChatAgent_BizEventFrame>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_ChatAgent_BizEventFrame> _parser = new pb::MessageParser<PB_ChatAgent_BizEventFrame>(() => new PB_ChatAgent_BizEventFrame());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_ChatAgent_BizEventFrame> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.ChatAgentReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_BizEventFrame() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_BizEventFrame(PB_ChatAgent_BizEventFrame other) : this() {
      id_ = other.id_;
      name_ = other.name_;
      bizEventName_ = other.bizEventName_;
      switch (other.bizEventDataCase) {
        case bizEventDataOneofCase.userManualSubmitData:
          userManualSubmitData = other.userManualSubmitData.Clone();
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_BizEventFrame Clone() {
      return new PB_ChatAgent_BizEventFrame(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int idFieldNumber = 1;
    private ulong id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "name" field.</summary>
    public const int nameFieldNumber = 2;
    private string name_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string name {
      get { return name_; }
      set {
        name_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "bizEventName" field.</summary>
    public const int bizEventNameFieldNumber = 3;
    private string bizEventName_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string bizEventName {
      get { return bizEventName_; }
      set {
        bizEventName_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "userManualSubmitData" field.</summary>
    public const int userManualSubmitDataFieldNumber = 4;
    /// <summary>
    /// 用户手动提交
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_ChatAgent_BizEventData_UserManualSubmit userManualSubmitData {
      get { return bizEventDataCase_ == bizEventDataOneofCase.userManualSubmitData ? (global::Msg.explore.PB_ChatAgent_BizEventData_UserManualSubmit) bizEventData_ : null; }
      set {
        bizEventData_ = value;
        bizEventDataCase_ = value == null ? bizEventDataOneofCase.None : bizEventDataOneofCase.userManualSubmitData;
      }
    }

    private object bizEventData_;
    /// <summary>Enum of possible cases for the "bizEventData" oneof.</summary>
    public enum bizEventDataOneofCase {
      None = 0,
      userManualSubmitData = 4,
    }
    private bizEventDataOneofCase bizEventDataCase_ = bizEventDataOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bizEventDataOneofCase bizEventDataCase {
      get { return bizEventDataCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearbizEventData() {
      bizEventDataCase_ = bizEventDataOneofCase.None;
      bizEventData_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_ChatAgent_BizEventFrame);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_ChatAgent_BizEventFrame other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (id != other.id) return false;
      if (name != other.name) return false;
      if (bizEventName != other.bizEventName) return false;
      if (!object.Equals(userManualSubmitData, other.userManualSubmitData)) return false;
      if (bizEventDataCase != other.bizEventDataCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (id != 0UL) hash ^= id.GetHashCode();
      if (name.Length != 0) hash ^= name.GetHashCode();
      if (bizEventName.Length != 0) hash ^= bizEventName.GetHashCode();
      if (bizEventDataCase_ == bizEventDataOneofCase.userManualSubmitData) hash ^= userManualSubmitData.GetHashCode();
      hash ^= (int) bizEventDataCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(id);
      }
      if (name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(name);
      }
      if (bizEventName.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(bizEventName);
      }
      if (bizEventDataCase_ == bizEventDataOneofCase.userManualSubmitData) {
        output.WriteRawTag(34);
        output.WriteMessage(userManualSubmitData);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(id);
      }
      if (name.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(name);
      }
      if (bizEventName.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(bizEventName);
      }
      if (bizEventDataCase_ == bizEventDataOneofCase.userManualSubmitData) {
        output.WriteRawTag(34);
        output.WriteMessage(userManualSubmitData);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (id != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(id);
      }
      if (name.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(name);
      }
      if (bizEventName.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(bizEventName);
      }
      if (bizEventDataCase_ == bizEventDataOneofCase.userManualSubmitData) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(userManualSubmitData);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_ChatAgent_BizEventFrame other) {
      if (other == null) {
        return;
      }
      if (other.id != 0UL) {
        id = other.id;
      }
      if (other.name.Length != 0) {
        name = other.name;
      }
      if (other.bizEventName.Length != 0) {
        bizEventName = other.bizEventName;
      }
      switch (other.bizEventDataCase) {
        case bizEventDataOneofCase.userManualSubmitData:
          if (userManualSubmitData == null) {
            userManualSubmitData = new global::Msg.explore.PB_ChatAgent_BizEventData_UserManualSubmit();
          }
          userManualSubmitData.MergeFrom(other.userManualSubmitData);
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            id = input.ReadUInt64();
            break;
          }
          case 18: {
            name = input.ReadString();
            break;
          }
          case 26: {
            bizEventName = input.ReadString();
            break;
          }
          case 34: {
            global::Msg.explore.PB_ChatAgent_BizEventData_UserManualSubmit subBuilder = new global::Msg.explore.PB_ChatAgent_BizEventData_UserManualSubmit();
            if (bizEventDataCase_ == bizEventDataOneofCase.userManualSubmitData) {
              subBuilder.MergeFrom(userManualSubmitData);
            }
            input.ReadMessage(subBuilder);
            userManualSubmitData = subBuilder;
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            id = input.ReadUInt64();
            break;
          }
          case 18: {
            name = input.ReadString();
            break;
          }
          case 26: {
            bizEventName = input.ReadString();
            break;
          }
          case 34: {
            global::Msg.explore.PB_ChatAgent_BizEventData_UserManualSubmit subBuilder = new global::Msg.explore.PB_ChatAgent_BizEventData_UserManualSubmit();
            if (bizEventDataCase_ == bizEventDataOneofCase.userManualSubmitData) {
              subBuilder.MergeFrom(userManualSubmitData);
            }
            input.ReadMessage(subBuilder);
            userManualSubmitData = subBuilder;
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 用户手动提交
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_ChatAgent_BizEventData_UserManualSubmit : pb::IMessage<PB_ChatAgent_BizEventData_UserManualSubmit>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_ChatAgent_BizEventData_UserManualSubmit> _parser = new pb::MessageParser<PB_ChatAgent_BizEventData_UserManualSubmit>(() => new PB_ChatAgent_BizEventData_UserManualSubmit());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_ChatAgent_BizEventData_UserManualSubmit> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.ChatAgentReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_BizEventData_UserManualSubmit() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_BizEventData_UserManualSubmit(PB_ChatAgent_BizEventData_UserManualSubmit other) : this() {
      speakingSpeed_ = other.speakingSpeed_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_BizEventData_UserManualSubmit Clone() {
      return new PB_ChatAgent_BizEventData_UserManualSubmit(this);
    }

    /// <summary>Field number for the "speakingSpeed" field.</summary>
    public const int speakingSpeedFieldNumber = 1;
    private int speakingSpeed_;
    /// <summary>
    /// 语速
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int speakingSpeed {
      get { return speakingSpeed_; }
      set {
        speakingSpeed_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_ChatAgent_BizEventData_UserManualSubmit);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_ChatAgent_BizEventData_UserManualSubmit other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (speakingSpeed != other.speakingSpeed) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (speakingSpeed != 0) hash ^= speakingSpeed.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (speakingSpeed != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(speakingSpeed);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (speakingSpeed != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(speakingSpeed);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (speakingSpeed != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(speakingSpeed);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_ChatAgent_BizEventData_UserManualSubmit other) {
      if (other == null) {
        return;
      }
      if (other.speakingSpeed != 0) {
        speakingSpeed = other.speakingSpeed;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            speakingSpeed = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            speakingSpeed = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 子目标判定结果
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_ChatAgent_SubGoalJudgmentResultFrame : pb::IMessage<PB_ChatAgent_SubGoalJudgmentResultFrame>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_ChatAgent_SubGoalJudgmentResultFrame> _parser = new pb::MessageParser<PB_ChatAgent_SubGoalJudgmentResultFrame>(() => new PB_ChatAgent_SubGoalJudgmentResultFrame());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_ChatAgent_SubGoalJudgmentResultFrame> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.ChatAgentReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_SubGoalJudgmentResultFrame() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_SubGoalJudgmentResultFrame(PB_ChatAgent_SubGoalJudgmentResultFrame other) : this() {
      goalResult_ = other.goalResult_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_SubGoalJudgmentResultFrame Clone() {
      return new PB_ChatAgent_SubGoalJudgmentResultFrame(this);
    }

    /// <summary>Field number for the "goalResult" field.</summary>
    public const int goalResultFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Msg.explore.PB_ChatAgent_GoalResult> _repeated_goalResult_codec
        = pb::FieldCodec.ForMessage(10, global::Msg.explore.PB_ChatAgent_GoalResult.Parser);
    private readonly pbc::RepeatedField<global::Msg.explore.PB_ChatAgent_GoalResult> goalResult_ = new pbc::RepeatedField<global::Msg.explore.PB_ChatAgent_GoalResult>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Msg.explore.PB_ChatAgent_GoalResult> goalResult {
      get { return goalResult_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_ChatAgent_SubGoalJudgmentResultFrame);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_ChatAgent_SubGoalJudgmentResultFrame other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!goalResult_.Equals(other.goalResult_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= goalResult_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      goalResult_.WriteTo(output, _repeated_goalResult_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      goalResult_.WriteTo(ref output, _repeated_goalResult_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += goalResult_.CalculateSize(_repeated_goalResult_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_ChatAgent_SubGoalJudgmentResultFrame other) {
      if (other == null) {
        return;
      }
      goalResult_.Add(other.goalResult_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            goalResult_.AddEntriesFrom(input, _repeated_goalResult_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            goalResult_.AddEntriesFrom(ref input, _repeated_goalResult_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_ChatAgent_GoalResult : pb::IMessage<PB_ChatAgent_GoalResult>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_ChatAgent_GoalResult> _parser = new pb::MessageParser<PB_ChatAgent_GoalResult>(() => new PB_ChatAgent_GoalResult());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_ChatAgent_GoalResult> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.ChatAgentReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_GoalResult() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_GoalResult(PB_ChatAgent_GoalResult other) : this() {
      goalId_ = other.goalId_;
      isComplete_ = other.isComplete_;
      isChange_ = other.isChange_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_GoalResult Clone() {
      return new PB_ChatAgent_GoalResult(this);
    }

    /// <summary>Field number for the "goalId" field.</summary>
    public const int goalIdFieldNumber = 1;
    private string goalId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string goalId {
      get { return goalId_; }
      set {
        goalId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "isComplete" field.</summary>
    public const int isCompleteFieldNumber = 2;
    private bool isComplete_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool isComplete {
      get { return isComplete_; }
      set {
        isComplete_ = value;
      }
    }

    /// <summary>Field number for the "isChange" field.</summary>
    public const int isChangeFieldNumber = 3;
    private bool isChange_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool isChange {
      get { return isChange_; }
      set {
        isChange_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_ChatAgent_GoalResult);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_ChatAgent_GoalResult other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (goalId != other.goalId) return false;
      if (isComplete != other.isComplete) return false;
      if (isChange != other.isChange) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (goalId.Length != 0) hash ^= goalId.GetHashCode();
      if (isComplete != false) hash ^= isComplete.GetHashCode();
      if (isChange != false) hash ^= isChange.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (goalId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(goalId);
      }
      if (isComplete != false) {
        output.WriteRawTag(16);
        output.WriteBool(isComplete);
      }
      if (isChange != false) {
        output.WriteRawTag(24);
        output.WriteBool(isChange);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (goalId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(goalId);
      }
      if (isComplete != false) {
        output.WriteRawTag(16);
        output.WriteBool(isComplete);
      }
      if (isChange != false) {
        output.WriteRawTag(24);
        output.WriteBool(isChange);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (goalId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(goalId);
      }
      if (isComplete != false) {
        size += 1 + 1;
      }
      if (isChange != false) {
        size += 1 + 1;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_ChatAgent_GoalResult other) {
      if (other == null) {
        return;
      }
      if (other.goalId.Length != 0) {
        goalId = other.goalId;
      }
      if (other.isComplete != false) {
        isComplete = other.isComplete;
      }
      if (other.isChange != false) {
        isChange = other.isChange;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            goalId = input.ReadString();
            break;
          }
          case 16: {
            isComplete = input.ReadBool();
            break;
          }
          case 24: {
            isChange = input.ReadBool();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            goalId = input.ReadString();
            break;
          }
          case 16: {
            isComplete = input.ReadBool();
            break;
          }
          case 24: {
            isChange = input.ReadBool();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// Mission剧情进度
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_ChatAgent_MissionStoryProgressFrame : pb::IMessage<PB_ChatAgent_MissionStoryProgressFrame>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_ChatAgent_MissionStoryProgressFrame> _parser = new pb::MessageParser<PB_ChatAgent_MissionStoryProgressFrame>(() => new PB_ChatAgent_MissionStoryProgressFrame());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_ChatAgent_MissionStoryProgressFrame> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.ChatAgentReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_MissionStoryProgressFrame() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_MissionStoryProgressFrame(PB_ChatAgent_MissionStoryProgressFrame other) : this() {
      currentStepNo_ = other.currentStepNo_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_MissionStoryProgressFrame Clone() {
      return new PB_ChatAgent_MissionStoryProgressFrame(this);
    }

    /// <summary>Field number for the "currentStepNo" field.</summary>
    public const int currentStepNoFieldNumber = 1;
    private int currentStepNo_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int currentStepNo {
      get { return currentStepNo_; }
      set {
        currentStepNo_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_ChatAgent_MissionStoryProgressFrame);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_ChatAgent_MissionStoryProgressFrame other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (currentStepNo != other.currentStepNo) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (currentStepNo != 0) hash ^= currentStepNo.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (currentStepNo != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(currentStepNo);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (currentStepNo != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(currentStepNo);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (currentStepNo != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(currentStepNo);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_ChatAgent_MissionStoryProgressFrame other) {
      if (other == null) {
        return;
      }
      if (other.currentStepNo != 0) {
        currentStepNo = other.currentStepNo;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            currentStepNo = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            currentStepNo = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 对话设置V2
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_ChatAgent_DialogSettingReqV2 : pb::IMessage<CS_ChatAgent_DialogSettingReqV2>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_ChatAgent_DialogSettingReqV2> _parser = new pb::MessageParser<CS_ChatAgent_DialogSettingReqV2>(() => new CS_ChatAgent_DialogSettingReqV2());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_ChatAgent_DialogSettingReqV2> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.ChatAgentReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_ChatAgent_DialogSettingReqV2() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_ChatAgent_DialogSettingReqV2(CS_ChatAgent_DialogSettingReqV2 other) : this() {
      taskId_ = other.taskId_;
      userId_ = other.userId_;
      cefrLevel_ = other.cefrLevel_;
      firstLang_ = other.firstLang_;
      avatarInfo_ = other.avatarInfo_;
      clientVersion_ = other.clientVersion_;
      speakingSpeed_ = other.speakingSpeed_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_ChatAgent_DialogSettingReqV2 Clone() {
      return new CS_ChatAgent_DialogSettingReqV2(this);
    }

    /// <summary>Field number for the "taskId" field.</summary>
    public const int taskIdFieldNumber = 1;
    private string taskId_ = "";
    /// <summary>
    /// 任务id（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string taskId {
      get { return taskId_; }
      set {
        taskId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "userId" field.</summary>
    public const int userIdFieldNumber = 2;
    private string userId_ = "";
    /// <summary>
    /// 用户id（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string userId {
      get { return userId_; }
      set {
        userId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "cefrLevel" field.</summary>
    public const int cefrLevelFieldNumber = 3;
    private string cefrLevel_ = "";
    /// <summary>
    /// CEFR等级（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string cefrLevel {
      get { return cefrLevel_; }
      set {
        cefrLevel_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "firstLang" field.</summary>
    public const int firstLangFieldNumber = 4;
    private string firstLang_ = "";
    /// <summary>
    /// 母语（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string firstLang {
      get { return firstLang_; }
      set {
        firstLang_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "avatarInfo" field.</summary>
    public const int avatarInfoFieldNumber = 5;
    private string avatarInfo_ = "";
    /// <summary>
    /// Avatar信息（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string avatarInfo {
      get { return avatarInfo_; }
      set {
        avatarInfo_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "clientVersion" field.</summary>
    public const int clientVersionFieldNumber = 6;
    private string clientVersion_ = "";
    /// <summary>
    /// 客户端版本号（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string clientVersion {
      get { return clientVersion_; }
      set {
        clientVersion_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "speakingSpeed" field.</summary>
    public const int speakingSpeedFieldNumber = 7;
    private int speakingSpeed_;
    /// <summary>
    /// 语速（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int speakingSpeed {
      get { return speakingSpeed_; }
      set {
        speakingSpeed_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_ChatAgent_DialogSettingReqV2);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_ChatAgent_DialogSettingReqV2 other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (taskId != other.taskId) return false;
      if (userId != other.userId) return false;
      if (cefrLevel != other.cefrLevel) return false;
      if (firstLang != other.firstLang) return false;
      if (avatarInfo != other.avatarInfo) return false;
      if (clientVersion != other.clientVersion) return false;
      if (speakingSpeed != other.speakingSpeed) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (taskId.Length != 0) hash ^= taskId.GetHashCode();
      if (userId.Length != 0) hash ^= userId.GetHashCode();
      if (cefrLevel.Length != 0) hash ^= cefrLevel.GetHashCode();
      if (firstLang.Length != 0) hash ^= firstLang.GetHashCode();
      if (avatarInfo.Length != 0) hash ^= avatarInfo.GetHashCode();
      if (clientVersion.Length != 0) hash ^= clientVersion.GetHashCode();
      if (speakingSpeed != 0) hash ^= speakingSpeed.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (taskId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(taskId);
      }
      if (userId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(userId);
      }
      if (cefrLevel.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(cefrLevel);
      }
      if (firstLang.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(firstLang);
      }
      if (avatarInfo.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(avatarInfo);
      }
      if (clientVersion.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(clientVersion);
      }
      if (speakingSpeed != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(speakingSpeed);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (taskId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(taskId);
      }
      if (userId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(userId);
      }
      if (cefrLevel.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(cefrLevel);
      }
      if (firstLang.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(firstLang);
      }
      if (avatarInfo.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(avatarInfo);
      }
      if (clientVersion.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(clientVersion);
      }
      if (speakingSpeed != 0) {
        output.WriteRawTag(56);
        output.WriteInt32(speakingSpeed);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (taskId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(taskId);
      }
      if (userId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(userId);
      }
      if (cefrLevel.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(cefrLevel);
      }
      if (firstLang.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(firstLang);
      }
      if (avatarInfo.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(avatarInfo);
      }
      if (clientVersion.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(clientVersion);
      }
      if (speakingSpeed != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(speakingSpeed);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_ChatAgent_DialogSettingReqV2 other) {
      if (other == null) {
        return;
      }
      if (other.taskId.Length != 0) {
        taskId = other.taskId;
      }
      if (other.userId.Length != 0) {
        userId = other.userId;
      }
      if (other.cefrLevel.Length != 0) {
        cefrLevel = other.cefrLevel;
      }
      if (other.firstLang.Length != 0) {
        firstLang = other.firstLang;
      }
      if (other.avatarInfo.Length != 0) {
        avatarInfo = other.avatarInfo;
      }
      if (other.clientVersion.Length != 0) {
        clientVersion = other.clientVersion;
      }
      if (other.speakingSpeed != 0) {
        speakingSpeed = other.speakingSpeed;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            taskId = input.ReadString();
            break;
          }
          case 18: {
            userId = input.ReadString();
            break;
          }
          case 26: {
            cefrLevel = input.ReadString();
            break;
          }
          case 34: {
            firstLang = input.ReadString();
            break;
          }
          case 42: {
            avatarInfo = input.ReadString();
            break;
          }
          case 50: {
            clientVersion = input.ReadString();
            break;
          }
          case 56: {
            speakingSpeed = input.ReadInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            taskId = input.ReadString();
            break;
          }
          case 18: {
            userId = input.ReadString();
            break;
          }
          case 26: {
            cefrLevel = input.ReadString();
            break;
          }
          case 34: {
            firstLang = input.ReadString();
            break;
          }
          case 42: {
            avatarInfo = input.ReadString();
            break;
          }
          case 50: {
            clientVersion = input.ReadString();
            break;
          }
          case 56: {
            speakingSpeed = input.ReadInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_ChatAgent_DialogSettingRespV2 : pb::IMessage<SC_ChatAgent_DialogSettingRespV2>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_ChatAgent_DialogSettingRespV2> _parser = new pb::MessageParser<SC_ChatAgent_DialogSettingRespV2>(() => new SC_ChatAgent_DialogSettingRespV2());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_ChatAgent_DialogSettingRespV2> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.ChatAgentReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_ChatAgent_DialogSettingRespV2() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_ChatAgent_DialogSettingRespV2(SC_ChatAgent_DialogSettingRespV2 other) : this() {
      code_ = other.code_;
      msg_ = other.msg_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_ChatAgent_DialogSettingRespV2 Clone() {
      return new SC_ChatAgent_DialogSettingRespV2(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_ChatAgent_Code code_ = global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_ChatAgent_Code code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "msg" field.</summary>
    public const int msgFieldNumber = 2;
    private string msg_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string msg {
      get { return msg_; }
      set {
        msg_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 3;
    private global::Msg.explore.PB_ChatAgent_DialogSettingData data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_ChatAgent_DialogSettingData data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_ChatAgent_DialogSettingRespV2);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_ChatAgent_DialogSettingRespV2 other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (msg != other.msg) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) hash ^= code.GetHashCode();
      if (msg.Length != 0) hash ^= msg.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (msg.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(msg);
      }
      if (data_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (msg.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(msg);
      }
      if (data_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (msg.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(msg);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_ChatAgent_DialogSettingRespV2 other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) {
        code = other.code;
      }
      if (other.msg.Length != 0) {
        msg = other.msg;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.explore.PB_ChatAgent_DialogSettingData();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_ChatAgent_Code) input.ReadEnum();
            break;
          }
          case 18: {
            msg = input.ReadString();
            break;
          }
          case 26: {
            if (data_ == null) {
              data = new global::Msg.explore.PB_ChatAgent_DialogSettingData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_ChatAgent_Code) input.ReadEnum();
            break;
          }
          case 18: {
            msg = input.ReadString();
            break;
          }
          case 26: {
            if (data_ == null) {
              data = new global::Msg.explore.PB_ChatAgent_DialogSettingData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 用户聊天设置
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_ChatAgent_UserChatSettingReq : pb::IMessage<CS_ChatAgent_UserChatSettingReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_ChatAgent_UserChatSettingReq> _parser = new pb::MessageParser<CS_ChatAgent_UserChatSettingReq>(() => new CS_ChatAgent_UserChatSettingReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_ChatAgent_UserChatSettingReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.ChatAgentReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_ChatAgent_UserChatSettingReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_ChatAgent_UserChatSettingReq(CS_ChatAgent_UserChatSettingReq other) : this() {
      dialogId_ = other.dialogId_;
      userId_ = other.userId_;
      firstLang_ = other.firstLang_;
      clientVersion_ = other.clientVersion_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_ChatAgent_UserChatSettingReq Clone() {
      return new CS_ChatAgent_UserChatSettingReq(this);
    }

    /// <summary>Field number for the "dialogId" field.</summary>
    public const int dialogIdFieldNumber = 1;
    private string dialogId_ = "";
    /// <summary>
    /// 对话id（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string dialogId {
      get { return dialogId_; }
      set {
        dialogId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "userId" field.</summary>
    public const int userIdFieldNumber = 2;
    private string userId_ = "";
    /// <summary>
    /// 用户id（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string userId {
      get { return userId_; }
      set {
        userId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "firstLang" field.</summary>
    public const int firstLangFieldNumber = 3;
    private string firstLang_ = "";
    /// <summary>
    /// 母语（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string firstLang {
      get { return firstLang_; }
      set {
        firstLang_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "clientVersion" field.</summary>
    public const int clientVersionFieldNumber = 4;
    private string clientVersion_ = "";
    /// <summary>
    /// 客户端版本号（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string clientVersion {
      get { return clientVersion_; }
      set {
        clientVersion_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_ChatAgent_UserChatSettingReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_ChatAgent_UserChatSettingReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (dialogId != other.dialogId) return false;
      if (userId != other.userId) return false;
      if (firstLang != other.firstLang) return false;
      if (clientVersion != other.clientVersion) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (dialogId.Length != 0) hash ^= dialogId.GetHashCode();
      if (userId.Length != 0) hash ^= userId.GetHashCode();
      if (firstLang.Length != 0) hash ^= firstLang.GetHashCode();
      if (clientVersion.Length != 0) hash ^= clientVersion.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (dialogId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(dialogId);
      }
      if (userId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(userId);
      }
      if (firstLang.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(firstLang);
      }
      if (clientVersion.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(clientVersion);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (dialogId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(dialogId);
      }
      if (userId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(userId);
      }
      if (firstLang.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(firstLang);
      }
      if (clientVersion.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(clientVersion);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (dialogId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(dialogId);
      }
      if (userId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(userId);
      }
      if (firstLang.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(firstLang);
      }
      if (clientVersion.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(clientVersion);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_ChatAgent_UserChatSettingReq other) {
      if (other == null) {
        return;
      }
      if (other.dialogId.Length != 0) {
        dialogId = other.dialogId;
      }
      if (other.userId.Length != 0) {
        userId = other.userId;
      }
      if (other.firstLang.Length != 0) {
        firstLang = other.firstLang;
      }
      if (other.clientVersion.Length != 0) {
        clientVersion = other.clientVersion;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            dialogId = input.ReadString();
            break;
          }
          case 18: {
            userId = input.ReadString();
            break;
          }
          case 26: {
            firstLang = input.ReadString();
            break;
          }
          case 34: {
            clientVersion = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            dialogId = input.ReadString();
            break;
          }
          case 18: {
            userId = input.ReadString();
            break;
          }
          case 26: {
            firstLang = input.ReadString();
            break;
          }
          case 34: {
            clientVersion = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_ChatAgent_UserChatSettingResp : pb::IMessage<SC_ChatAgent_UserChatSettingResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_ChatAgent_UserChatSettingResp> _parser = new pb::MessageParser<SC_ChatAgent_UserChatSettingResp>(() => new SC_ChatAgent_UserChatSettingResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_ChatAgent_UserChatSettingResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.ChatAgentReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_ChatAgent_UserChatSettingResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_ChatAgent_UserChatSettingResp(SC_ChatAgent_UserChatSettingResp other) : this() {
      code_ = other.code_;
      msg_ = other.msg_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_ChatAgent_UserChatSettingResp Clone() {
      return new SC_ChatAgent_UserChatSettingResp(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_ChatAgent_Code code_ = global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_ChatAgent_Code code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "msg" field.</summary>
    public const int msgFieldNumber = 2;
    private string msg_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string msg {
      get { return msg_; }
      set {
        msg_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 3;
    private global::Msg.explore.PB_ChatAgent_DialogSettingData data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_ChatAgent_DialogSettingData data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_ChatAgent_UserChatSettingResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_ChatAgent_UserChatSettingResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (msg != other.msg) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) hash ^= code.GetHashCode();
      if (msg.Length != 0) hash ^= msg.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (msg.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(msg);
      }
      if (data_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (msg.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(msg);
      }
      if (data_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (msg.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(msg);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_ChatAgent_UserChatSettingResp other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) {
        code = other.code;
      }
      if (other.msg.Length != 0) {
        msg = other.msg;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.explore.PB_ChatAgent_DialogSettingData();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_ChatAgent_Code) input.ReadEnum();
            break;
          }
          case 18: {
            msg = input.ReadString();
            break;
          }
          case 26: {
            if (data_ == null) {
              data = new global::Msg.explore.PB_ChatAgent_DialogSettingData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_ChatAgent_Code) input.ReadEnum();
            break;
          }
          case 18: {
            msg = input.ReadString();
            break;
          }
          case 26: {
            if (data_ == null) {
              data = new global::Msg.explore.PB_ChatAgent_DialogSettingData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// onboarding对话设置
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_ChatAgent_OnboardingChatSettingReq : pb::IMessage<CS_ChatAgent_OnboardingChatSettingReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_ChatAgent_OnboardingChatSettingReq> _parser = new pb::MessageParser<CS_ChatAgent_OnboardingChatSettingReq>(() => new CS_ChatAgent_OnboardingChatSettingReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_ChatAgent_OnboardingChatSettingReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.ChatAgentReflection.Descriptor.MessageTypes[13]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_ChatAgent_OnboardingChatSettingReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_ChatAgent_OnboardingChatSettingReq(CS_ChatAgent_OnboardingChatSettingReq other) : this() {
      userId_ = other.userId_;
      learningMotivation_ = other.learningMotivation_;
      firstLang_ = other.firstLang_;
      avatarId_ = other.avatarId_;
      avatarInfo_ = other.avatarInfo_;
      clientVersion_ = other.clientVersion_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_ChatAgent_OnboardingChatSettingReq Clone() {
      return new CS_ChatAgent_OnboardingChatSettingReq(this);
    }

    /// <summary>Field number for the "userId" field.</summary>
    public const int userIdFieldNumber = 1;
    private string userId_ = "";
    /// <summary>
    /// 用户id（创建对话不在chat_agent完成必传）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string userId {
      get { return userId_; }
      set {
        userId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "learningMotivation" field.</summary>
    public const int learningMotivationFieldNumber = 2;
    private int learningMotivation_;
    /// <summary>
    /// 学习动机（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int learningMotivation {
      get { return learningMotivation_; }
      set {
        learningMotivation_ = value;
      }
    }

    /// <summary>Field number for the "firstLang" field.</summary>
    public const int firstLangFieldNumber = 3;
    private string firstLang_ = "";
    /// <summary>
    /// 母语（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string firstLang {
      get { return firstLang_; }
      set {
        firstLang_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "avatarId" field.</summary>
    public const int avatarIdFieldNumber = 4;
    private string avatarId_ = "";
    /// <summary>
    /// AvatarId（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string avatarId {
      get { return avatarId_; }
      set {
        avatarId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "avatarInfo" field.</summary>
    public const int avatarInfoFieldNumber = 5;
    private string avatarInfo_ = "";
    /// <summary>
    /// Avatar信息（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string avatarInfo {
      get { return avatarInfo_; }
      set {
        avatarInfo_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "clientVersion" field.</summary>
    public const int clientVersionFieldNumber = 6;
    private string clientVersion_ = "";
    /// <summary>
    /// 客户端版本号
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string clientVersion {
      get { return clientVersion_; }
      set {
        clientVersion_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_ChatAgent_OnboardingChatSettingReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_ChatAgent_OnboardingChatSettingReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (userId != other.userId) return false;
      if (learningMotivation != other.learningMotivation) return false;
      if (firstLang != other.firstLang) return false;
      if (avatarId != other.avatarId) return false;
      if (avatarInfo != other.avatarInfo) return false;
      if (clientVersion != other.clientVersion) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (userId.Length != 0) hash ^= userId.GetHashCode();
      if (learningMotivation != 0) hash ^= learningMotivation.GetHashCode();
      if (firstLang.Length != 0) hash ^= firstLang.GetHashCode();
      if (avatarId.Length != 0) hash ^= avatarId.GetHashCode();
      if (avatarInfo.Length != 0) hash ^= avatarInfo.GetHashCode();
      if (clientVersion.Length != 0) hash ^= clientVersion.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (userId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(userId);
      }
      if (learningMotivation != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(learningMotivation);
      }
      if (firstLang.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(firstLang);
      }
      if (avatarId.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(avatarId);
      }
      if (avatarInfo.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(avatarInfo);
      }
      if (clientVersion.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(clientVersion);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (userId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(userId);
      }
      if (learningMotivation != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(learningMotivation);
      }
      if (firstLang.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(firstLang);
      }
      if (avatarId.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(avatarId);
      }
      if (avatarInfo.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(avatarInfo);
      }
      if (clientVersion.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(clientVersion);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (userId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(userId);
      }
      if (learningMotivation != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(learningMotivation);
      }
      if (firstLang.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(firstLang);
      }
      if (avatarId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(avatarId);
      }
      if (avatarInfo.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(avatarInfo);
      }
      if (clientVersion.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(clientVersion);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_ChatAgent_OnboardingChatSettingReq other) {
      if (other == null) {
        return;
      }
      if (other.userId.Length != 0) {
        userId = other.userId;
      }
      if (other.learningMotivation != 0) {
        learningMotivation = other.learningMotivation;
      }
      if (other.firstLang.Length != 0) {
        firstLang = other.firstLang;
      }
      if (other.avatarId.Length != 0) {
        avatarId = other.avatarId;
      }
      if (other.avatarInfo.Length != 0) {
        avatarInfo = other.avatarInfo;
      }
      if (other.clientVersion.Length != 0) {
        clientVersion = other.clientVersion;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            userId = input.ReadString();
            break;
          }
          case 16: {
            learningMotivation = input.ReadInt32();
            break;
          }
          case 26: {
            firstLang = input.ReadString();
            break;
          }
          case 34: {
            avatarId = input.ReadString();
            break;
          }
          case 42: {
            avatarInfo = input.ReadString();
            break;
          }
          case 50: {
            clientVersion = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            userId = input.ReadString();
            break;
          }
          case 16: {
            learningMotivation = input.ReadInt32();
            break;
          }
          case 26: {
            firstLang = input.ReadString();
            break;
          }
          case 34: {
            avatarId = input.ReadString();
            break;
          }
          case 42: {
            avatarInfo = input.ReadString();
            break;
          }
          case 50: {
            clientVersion = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_ChatAgent_OnboardingChatSettingResp : pb::IMessage<SC_ChatAgent_OnboardingChatSettingResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_ChatAgent_OnboardingChatSettingResp> _parser = new pb::MessageParser<SC_ChatAgent_OnboardingChatSettingResp>(() => new SC_ChatAgent_OnboardingChatSettingResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_ChatAgent_OnboardingChatSettingResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.ChatAgentReflection.Descriptor.MessageTypes[14]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_ChatAgent_OnboardingChatSettingResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_ChatAgent_OnboardingChatSettingResp(SC_ChatAgent_OnboardingChatSettingResp other) : this() {
      code_ = other.code_;
      msg_ = other.msg_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_ChatAgent_OnboardingChatSettingResp Clone() {
      return new SC_ChatAgent_OnboardingChatSettingResp(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_ChatAgent_Code code_ = global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_ChatAgent_Code code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "msg" field.</summary>
    public const int msgFieldNumber = 2;
    private string msg_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string msg {
      get { return msg_; }
      set {
        msg_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 3;
    private global::Msg.explore.PB_ChatAgent_DialogSettingData data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_ChatAgent_DialogSettingData data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_ChatAgent_OnboardingChatSettingResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_ChatAgent_OnboardingChatSettingResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (msg != other.msg) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) hash ^= code.GetHashCode();
      if (msg.Length != 0) hash ^= msg.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (msg.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(msg);
      }
      if (data_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (msg.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(msg);
      }
      if (data_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (msg.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(msg);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_ChatAgent_OnboardingChatSettingResp other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) {
        code = other.code;
      }
      if (other.msg.Length != 0) {
        msg = other.msg;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.explore.PB_ChatAgent_DialogSettingData();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_ChatAgent_Code) input.ReadEnum();
            break;
          }
          case 18: {
            msg = input.ReadString();
            break;
          }
          case 26: {
            if (data_ == null) {
              data = new global::Msg.explore.PB_ChatAgent_DialogSettingData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_ChatAgent_Code) input.ReadEnum();
            break;
          }
          case 18: {
            msg = input.ReadString();
            break;
          }
          case 26: {
            if (data_ == null) {
              data = new global::Msg.explore.PB_ChatAgent_DialogSettingData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// Mission剧情对话设置
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_ChatAgent_MissionStoryChatSettingReq : pb::IMessage<CS_ChatAgent_MissionStoryChatSettingReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_ChatAgent_MissionStoryChatSettingReq> _parser = new pb::MessageParser<CS_ChatAgent_MissionStoryChatSettingReq>(() => new CS_ChatAgent_MissionStoryChatSettingReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_ChatAgent_MissionStoryChatSettingReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.ChatAgentReflection.Descriptor.MessageTypes[15]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_ChatAgent_MissionStoryChatSettingReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_ChatAgent_MissionStoryChatSettingReq(CS_ChatAgent_MissionStoryChatSettingReq other) : this() {
      taskId_ = other.taskId_;
      userId_ = other.userId_;
      cefrLevel_ = other.cefrLevel_;
      firstLang_ = other.firstLang_;
      avatarId_ = other.avatarId_;
      avatarInfo_ = other.avatarInfo_;
      clientVersion_ = other.clientVersion_;
      speakingSpeed_ = other.speakingSpeed_;
      storyId_ = other.storyId_;
      dialogId_ = other.dialogId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_ChatAgent_MissionStoryChatSettingReq Clone() {
      return new CS_ChatAgent_MissionStoryChatSettingReq(this);
    }

    /// <summary>Field number for the "taskId" field.</summary>
    public const int taskIdFieldNumber = 1;
    private string taskId_ = "";
    /// <summary>
    /// 任务id（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string taskId {
      get { return taskId_; }
      set {
        taskId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "userId" field.</summary>
    public const int userIdFieldNumber = 2;
    private string userId_ = "";
    /// <summary>
    /// 用户id（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string userId {
      get { return userId_; }
      set {
        userId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "cefrLevel" field.</summary>
    public const int cefrLevelFieldNumber = 3;
    private string cefrLevel_ = "";
    /// <summary>
    /// CEFR等级（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string cefrLevel {
      get { return cefrLevel_; }
      set {
        cefrLevel_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "firstLang" field.</summary>
    public const int firstLangFieldNumber = 4;
    private string firstLang_ = "";
    /// <summary>
    /// 母语（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string firstLang {
      get { return firstLang_; }
      set {
        firstLang_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "avatarId" field.</summary>
    public const int avatarIdFieldNumber = 5;
    private string avatarId_ = "";
    /// <summary>
    /// AvatarId（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string avatarId {
      get { return avatarId_; }
      set {
        avatarId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "avatarInfo" field.</summary>
    public const int avatarInfoFieldNumber = 6;
    private string avatarInfo_ = "";
    /// <summary>
    /// Avatar信息（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string avatarInfo {
      get { return avatarInfo_; }
      set {
        avatarInfo_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "clientVersion" field.</summary>
    public const int clientVersionFieldNumber = 7;
    private string clientVersion_ = "";
    /// <summary>
    /// 客户端版本号（必选）
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string clientVersion {
      get { return clientVersion_; }
      set {
        clientVersion_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "speakingSpeed" field.</summary>
    public const int speakingSpeedFieldNumber = 8;
    private int speakingSpeed_;
    /// <summary>
    /// 语速
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int speakingSpeed {
      get { return speakingSpeed_; }
      set {
        speakingSpeed_ = value;
      }
    }

    /// <summary>Field number for the "storyId" field.</summary>
    public const int storyIdFieldNumber = 9;
    private string storyId_ = "";
    /// <summary>
    /// 剧情id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string storyId {
      get { return storyId_; }
      set {
        storyId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "dialogId" field.</summary>
    public const int dialogIdFieldNumber = 10;
    private string dialogId_ = "";
    /// <summary>
    /// 对话id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string dialogId {
      get { return dialogId_; }
      set {
        dialogId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_ChatAgent_MissionStoryChatSettingReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_ChatAgent_MissionStoryChatSettingReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (taskId != other.taskId) return false;
      if (userId != other.userId) return false;
      if (cefrLevel != other.cefrLevel) return false;
      if (firstLang != other.firstLang) return false;
      if (avatarId != other.avatarId) return false;
      if (avatarInfo != other.avatarInfo) return false;
      if (clientVersion != other.clientVersion) return false;
      if (speakingSpeed != other.speakingSpeed) return false;
      if (storyId != other.storyId) return false;
      if (dialogId != other.dialogId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (taskId.Length != 0) hash ^= taskId.GetHashCode();
      if (userId.Length != 0) hash ^= userId.GetHashCode();
      if (cefrLevel.Length != 0) hash ^= cefrLevel.GetHashCode();
      if (firstLang.Length != 0) hash ^= firstLang.GetHashCode();
      if (avatarId.Length != 0) hash ^= avatarId.GetHashCode();
      if (avatarInfo.Length != 0) hash ^= avatarInfo.GetHashCode();
      if (clientVersion.Length != 0) hash ^= clientVersion.GetHashCode();
      if (speakingSpeed != 0) hash ^= speakingSpeed.GetHashCode();
      if (storyId.Length != 0) hash ^= storyId.GetHashCode();
      if (dialogId.Length != 0) hash ^= dialogId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (taskId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(taskId);
      }
      if (userId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(userId);
      }
      if (cefrLevel.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(cefrLevel);
      }
      if (firstLang.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(firstLang);
      }
      if (avatarId.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(avatarId);
      }
      if (avatarInfo.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(avatarInfo);
      }
      if (clientVersion.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(clientVersion);
      }
      if (speakingSpeed != 0) {
        output.WriteRawTag(64);
        output.WriteInt32(speakingSpeed);
      }
      if (storyId.Length != 0) {
        output.WriteRawTag(74);
        output.WriteString(storyId);
      }
      if (dialogId.Length != 0) {
        output.WriteRawTag(82);
        output.WriteString(dialogId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (taskId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(taskId);
      }
      if (userId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(userId);
      }
      if (cefrLevel.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(cefrLevel);
      }
      if (firstLang.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(firstLang);
      }
      if (avatarId.Length != 0) {
        output.WriteRawTag(42);
        output.WriteString(avatarId);
      }
      if (avatarInfo.Length != 0) {
        output.WriteRawTag(50);
        output.WriteString(avatarInfo);
      }
      if (clientVersion.Length != 0) {
        output.WriteRawTag(58);
        output.WriteString(clientVersion);
      }
      if (speakingSpeed != 0) {
        output.WriteRawTag(64);
        output.WriteInt32(speakingSpeed);
      }
      if (storyId.Length != 0) {
        output.WriteRawTag(74);
        output.WriteString(storyId);
      }
      if (dialogId.Length != 0) {
        output.WriteRawTag(82);
        output.WriteString(dialogId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (taskId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(taskId);
      }
      if (userId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(userId);
      }
      if (cefrLevel.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(cefrLevel);
      }
      if (firstLang.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(firstLang);
      }
      if (avatarId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(avatarId);
      }
      if (avatarInfo.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(avatarInfo);
      }
      if (clientVersion.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(clientVersion);
      }
      if (speakingSpeed != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(speakingSpeed);
      }
      if (storyId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(storyId);
      }
      if (dialogId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(dialogId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_ChatAgent_MissionStoryChatSettingReq other) {
      if (other == null) {
        return;
      }
      if (other.taskId.Length != 0) {
        taskId = other.taskId;
      }
      if (other.userId.Length != 0) {
        userId = other.userId;
      }
      if (other.cefrLevel.Length != 0) {
        cefrLevel = other.cefrLevel;
      }
      if (other.firstLang.Length != 0) {
        firstLang = other.firstLang;
      }
      if (other.avatarId.Length != 0) {
        avatarId = other.avatarId;
      }
      if (other.avatarInfo.Length != 0) {
        avatarInfo = other.avatarInfo;
      }
      if (other.clientVersion.Length != 0) {
        clientVersion = other.clientVersion;
      }
      if (other.speakingSpeed != 0) {
        speakingSpeed = other.speakingSpeed;
      }
      if (other.storyId.Length != 0) {
        storyId = other.storyId;
      }
      if (other.dialogId.Length != 0) {
        dialogId = other.dialogId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            taskId = input.ReadString();
            break;
          }
          case 18: {
            userId = input.ReadString();
            break;
          }
          case 26: {
            cefrLevel = input.ReadString();
            break;
          }
          case 34: {
            firstLang = input.ReadString();
            break;
          }
          case 42: {
            avatarId = input.ReadString();
            break;
          }
          case 50: {
            avatarInfo = input.ReadString();
            break;
          }
          case 58: {
            clientVersion = input.ReadString();
            break;
          }
          case 64: {
            speakingSpeed = input.ReadInt32();
            break;
          }
          case 74: {
            storyId = input.ReadString();
            break;
          }
          case 82: {
            dialogId = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            taskId = input.ReadString();
            break;
          }
          case 18: {
            userId = input.ReadString();
            break;
          }
          case 26: {
            cefrLevel = input.ReadString();
            break;
          }
          case 34: {
            firstLang = input.ReadString();
            break;
          }
          case 42: {
            avatarId = input.ReadString();
            break;
          }
          case 50: {
            avatarInfo = input.ReadString();
            break;
          }
          case 58: {
            clientVersion = input.ReadString();
            break;
          }
          case 64: {
            speakingSpeed = input.ReadInt32();
            break;
          }
          case 74: {
            storyId = input.ReadString();
            break;
          }
          case 82: {
            dialogId = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_ChatAgent_MissionStoryChatSettingResp : pb::IMessage<SC_ChatAgent_MissionStoryChatSettingResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_ChatAgent_MissionStoryChatSettingResp> _parser = new pb::MessageParser<SC_ChatAgent_MissionStoryChatSettingResp>(() => new SC_ChatAgent_MissionStoryChatSettingResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_ChatAgent_MissionStoryChatSettingResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.ChatAgentReflection.Descriptor.MessageTypes[16]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_ChatAgent_MissionStoryChatSettingResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_ChatAgent_MissionStoryChatSettingResp(SC_ChatAgent_MissionStoryChatSettingResp other) : this() {
      code_ = other.code_;
      msg_ = other.msg_;
      data_ = other.data_ != null ? other.data_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_ChatAgent_MissionStoryChatSettingResp Clone() {
      return new SC_ChatAgent_MissionStoryChatSettingResp(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 1;
    private global::Msg.explore.PB_ChatAgent_Code code_ = global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_ChatAgent_Code code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "msg" field.</summary>
    public const int msgFieldNumber = 2;
    private string msg_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string msg {
      get { return msg_; }
      set {
        msg_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "data" field.</summary>
    public const int dataFieldNumber = 3;
    private global::Msg.explore.PB_ChatAgent_DialogSettingData data_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_ChatAgent_DialogSettingData data {
      get { return data_; }
      set {
        data_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_ChatAgent_MissionStoryChatSettingResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_ChatAgent_MissionStoryChatSettingResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (code != other.code) return false;
      if (msg != other.msg) return false;
      if (!object.Equals(data, other.data)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) hash ^= code.GetHashCode();
      if (msg.Length != 0) hash ^= msg.GetHashCode();
      if (data_ != null) hash ^= data.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (msg.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(msg);
      }
      if (data_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) {
        output.WriteRawTag(8);
        output.WriteEnum((int) code);
      }
      if (msg.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(msg);
      }
      if (data_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(data);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (msg.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(msg);
      }
      if (data_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(data);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_ChatAgent_MissionStoryChatSettingResp other) {
      if (other == null) {
        return;
      }
      if (other.code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) {
        code = other.code;
      }
      if (other.msg.Length != 0) {
        msg = other.msg;
      }
      if (other.data_ != null) {
        if (data_ == null) {
          data = new global::Msg.explore.PB_ChatAgent_DialogSettingData();
        }
        data.MergeFrom(other.data);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_ChatAgent_Code) input.ReadEnum();
            break;
          }
          case 18: {
            msg = input.ReadString();
            break;
          }
          case 26: {
            if (data_ == null) {
              data = new global::Msg.explore.PB_ChatAgent_DialogSettingData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            code = (global::Msg.explore.PB_ChatAgent_Code) input.ReadEnum();
            break;
          }
          case 18: {
            msg = input.ReadString();
            break;
          }
          case 26: {
            if (data_ == null) {
              data = new global::Msg.explore.PB_ChatAgent_DialogSettingData();
            }
            input.ReadMessage(data);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PB_ChatAgent_DialogSettingData : pb::IMessage<PB_ChatAgent_DialogSettingData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PB_ChatAgent_DialogSettingData> _parser = new pb::MessageParser<PB_ChatAgent_DialogSettingData>(() => new PB_ChatAgent_DialogSettingData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PB_ChatAgent_DialogSettingData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.ChatAgentReflection.Descriptor.MessageTypes[17]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_DialogSettingData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_DialogSettingData(PB_ChatAgent_DialogSettingData other) : this() {
      dialogId_ = other.dialogId_;
      sessionId_ = other.sessionId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PB_ChatAgent_DialogSettingData Clone() {
      return new PB_ChatAgent_DialogSettingData(this);
    }

    /// <summary>Field number for the "dialogId" field.</summary>
    public const int dialogIdFieldNumber = 1;
    private string dialogId_ = "";
    /// <summary>
    /// 对话id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string dialogId {
      get { return dialogId_; }
      set {
        dialogId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "sessionId" field.</summary>
    public const int sessionIdFieldNumber = 2;
    private string sessionId_ = "";
    /// <summary>
    /// 对话会话id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string sessionId {
      get { return sessionId_; }
      set {
        sessionId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PB_ChatAgent_DialogSettingData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PB_ChatAgent_DialogSettingData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (dialogId != other.dialogId) return false;
      if (sessionId != other.sessionId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (dialogId.Length != 0) hash ^= dialogId.GetHashCode();
      if (sessionId.Length != 0) hash ^= sessionId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (dialogId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(dialogId);
      }
      if (sessionId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(sessionId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (dialogId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(dialogId);
      }
      if (sessionId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(sessionId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (dialogId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(dialogId);
      }
      if (sessionId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(sessionId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PB_ChatAgent_DialogSettingData other) {
      if (other == null) {
        return;
      }
      if (other.dialogId.Length != 0) {
        dialogId = other.dialogId;
      }
      if (other.sessionId.Length != 0) {
        sessionId = other.sessionId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            dialogId = input.ReadString();
            break;
          }
          case 18: {
            sessionId = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            dialogId = input.ReadString();
            break;
          }
          case 18: {
            sessionId = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 上行数据包 start 
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class CS_ChatAgent_Input : pb::IMessage<CS_ChatAgent_Input>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CS_ChatAgent_Input> _parser = new pb::MessageParser<CS_ChatAgent_Input>(() => new CS_ChatAgent_Input());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CS_ChatAgent_Input> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.ChatAgentReflection.Descriptor.MessageTypes[18]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_ChatAgent_Input() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_ChatAgent_Input(CS_ChatAgent_Input other) : this() {
      dialogId_ = other.dialogId_;
      bubbleId_ = other.bubbleId_;
      switch (other.inputDataCase) {
        case inputDataOneofCase.audio:
          audio = other.audio.Clone();
          break;
        case inputDataOneofCase.text:
          text = other.text.Clone();
          break;
        case inputDataOneofCase.bizEvent:
          bizEvent = other.bizEvent.Clone();
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CS_ChatAgent_Input Clone() {
      return new CS_ChatAgent_Input(this);
    }

    /// <summary>Field number for the "dialogId" field.</summary>
    public const int dialogIdFieldNumber = 1;
    private string dialogId_ = "";
    /// <summary>
    /// 对话id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string dialogId {
      get { return dialogId_; }
      set {
        dialogId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "bubbleId" field.</summary>
    public const int bubbleIdFieldNumber = 2;
    private string bubbleId_ = "";
    /// <summary>
    /// 对话气泡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string bubbleId {
      get { return bubbleId_; }
      set {
        bubbleId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "audio" field.</summary>
    public const int audioFieldNumber = 3;
    /// <summary>
    /// 音频数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_ChatAgent_AudioRawFrame audio {
      get { return inputDataCase_ == inputDataOneofCase.audio ? (global::Msg.explore.PB_ChatAgent_AudioRawFrame) inputData_ : null; }
      set {
        inputData_ = value;
        inputDataCase_ = value == null ? inputDataOneofCase.None : inputDataOneofCase.audio;
      }
    }

    /// <summary>Field number for the "text" field.</summary>
    public const int textFieldNumber = 4;
    /// <summary>
    /// 文本数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_ChatAgent_TextFrame text {
      get { return inputDataCase_ == inputDataOneofCase.text ? (global::Msg.explore.PB_ChatAgent_TextFrame) inputData_ : null; }
      set {
        inputData_ = value;
        inputDataCase_ = value == null ? inputDataOneofCase.None : inputDataOneofCase.text;
      }
    }

    /// <summary>Field number for the "bizEvent" field.</summary>
    public const int bizEventFieldNumber = 5;
    /// <summary>
    /// 业务事件数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_ChatAgent_BizEventFrame bizEvent {
      get { return inputDataCase_ == inputDataOneofCase.bizEvent ? (global::Msg.explore.PB_ChatAgent_BizEventFrame) inputData_ : null; }
      set {
        inputData_ = value;
        inputDataCase_ = value == null ? inputDataOneofCase.None : inputDataOneofCase.bizEvent;
      }
    }

    private object inputData_;
    /// <summary>Enum of possible cases for the "inputData" oneof.</summary>
    public enum inputDataOneofCase {
      None = 0,
      audio = 3,
      text = 4,
      bizEvent = 5,
    }
    private inputDataOneofCase inputDataCase_ = inputDataOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public inputDataOneofCase inputDataCase {
      get { return inputDataCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearinputData() {
      inputDataCase_ = inputDataOneofCase.None;
      inputData_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CS_ChatAgent_Input);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CS_ChatAgent_Input other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (dialogId != other.dialogId) return false;
      if (bubbleId != other.bubbleId) return false;
      if (!object.Equals(audio, other.audio)) return false;
      if (!object.Equals(text, other.text)) return false;
      if (!object.Equals(bizEvent, other.bizEvent)) return false;
      if (inputDataCase != other.inputDataCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (dialogId.Length != 0) hash ^= dialogId.GetHashCode();
      if (bubbleId.Length != 0) hash ^= bubbleId.GetHashCode();
      if (inputDataCase_ == inputDataOneofCase.audio) hash ^= audio.GetHashCode();
      if (inputDataCase_ == inputDataOneofCase.text) hash ^= text.GetHashCode();
      if (inputDataCase_ == inputDataOneofCase.bizEvent) hash ^= bizEvent.GetHashCode();
      hash ^= (int) inputDataCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (dialogId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(dialogId);
      }
      if (bubbleId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(bubbleId);
      }
      if (inputDataCase_ == inputDataOneofCase.audio) {
        output.WriteRawTag(26);
        output.WriteMessage(audio);
      }
      if (inputDataCase_ == inputDataOneofCase.text) {
        output.WriteRawTag(34);
        output.WriteMessage(text);
      }
      if (inputDataCase_ == inputDataOneofCase.bizEvent) {
        output.WriteRawTag(42);
        output.WriteMessage(bizEvent);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (dialogId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(dialogId);
      }
      if (bubbleId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(bubbleId);
      }
      if (inputDataCase_ == inputDataOneofCase.audio) {
        output.WriteRawTag(26);
        output.WriteMessage(audio);
      }
      if (inputDataCase_ == inputDataOneofCase.text) {
        output.WriteRawTag(34);
        output.WriteMessage(text);
      }
      if (inputDataCase_ == inputDataOneofCase.bizEvent) {
        output.WriteRawTag(42);
        output.WriteMessage(bizEvent);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (dialogId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(dialogId);
      }
      if (bubbleId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(bubbleId);
      }
      if (inputDataCase_ == inputDataOneofCase.audio) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(audio);
      }
      if (inputDataCase_ == inputDataOneofCase.text) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(text);
      }
      if (inputDataCase_ == inputDataOneofCase.bizEvent) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(bizEvent);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CS_ChatAgent_Input other) {
      if (other == null) {
        return;
      }
      if (other.dialogId.Length != 0) {
        dialogId = other.dialogId;
      }
      if (other.bubbleId.Length != 0) {
        bubbleId = other.bubbleId;
      }
      switch (other.inputDataCase) {
        case inputDataOneofCase.audio:
          if (audio == null) {
            audio = new global::Msg.explore.PB_ChatAgent_AudioRawFrame();
          }
          audio.MergeFrom(other.audio);
          break;
        case inputDataOneofCase.text:
          if (text == null) {
            text = new global::Msg.explore.PB_ChatAgent_TextFrame();
          }
          text.MergeFrom(other.text);
          break;
        case inputDataOneofCase.bizEvent:
          if (bizEvent == null) {
            bizEvent = new global::Msg.explore.PB_ChatAgent_BizEventFrame();
          }
          bizEvent.MergeFrom(other.bizEvent);
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            dialogId = input.ReadString();
            break;
          }
          case 18: {
            bubbleId = input.ReadString();
            break;
          }
          case 26: {
            global::Msg.explore.PB_ChatAgent_AudioRawFrame subBuilder = new global::Msg.explore.PB_ChatAgent_AudioRawFrame();
            if (inputDataCase_ == inputDataOneofCase.audio) {
              subBuilder.MergeFrom(audio);
            }
            input.ReadMessage(subBuilder);
            audio = subBuilder;
            break;
          }
          case 34: {
            global::Msg.explore.PB_ChatAgent_TextFrame subBuilder = new global::Msg.explore.PB_ChatAgent_TextFrame();
            if (inputDataCase_ == inputDataOneofCase.text) {
              subBuilder.MergeFrom(text);
            }
            input.ReadMessage(subBuilder);
            text = subBuilder;
            break;
          }
          case 42: {
            global::Msg.explore.PB_ChatAgent_BizEventFrame subBuilder = new global::Msg.explore.PB_ChatAgent_BizEventFrame();
            if (inputDataCase_ == inputDataOneofCase.bizEvent) {
              subBuilder.MergeFrom(bizEvent);
            }
            input.ReadMessage(subBuilder);
            bizEvent = subBuilder;
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            dialogId = input.ReadString();
            break;
          }
          case 18: {
            bubbleId = input.ReadString();
            break;
          }
          case 26: {
            global::Msg.explore.PB_ChatAgent_AudioRawFrame subBuilder = new global::Msg.explore.PB_ChatAgent_AudioRawFrame();
            if (inputDataCase_ == inputDataOneofCase.audio) {
              subBuilder.MergeFrom(audio);
            }
            input.ReadMessage(subBuilder);
            audio = subBuilder;
            break;
          }
          case 34: {
            global::Msg.explore.PB_ChatAgent_TextFrame subBuilder = new global::Msg.explore.PB_ChatAgent_TextFrame();
            if (inputDataCase_ == inputDataOneofCase.text) {
              subBuilder.MergeFrom(text);
            }
            input.ReadMessage(subBuilder);
            text = subBuilder;
            break;
          }
          case 42: {
            global::Msg.explore.PB_ChatAgent_BizEventFrame subBuilder = new global::Msg.explore.PB_ChatAgent_BizEventFrame();
            if (inputDataCase_ == inputDataOneofCase.bizEvent) {
              subBuilder.MergeFrom(bizEvent);
            }
            input.ReadMessage(subBuilder);
            bizEvent = subBuilder;
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 下行数据包 start 
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class SC_ChatAgent_Output : pb::IMessage<SC_ChatAgent_Output>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<SC_ChatAgent_Output> _parser = new pb::MessageParser<SC_ChatAgent_Output>(() => new SC_ChatAgent_Output());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<SC_ChatAgent_Output> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Msg.explore.ChatAgentReflection.Descriptor.MessageTypes[19]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_ChatAgent_Output() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_ChatAgent_Output(SC_ChatAgent_Output other) : this() {
      dialogId_ = other.dialogId_;
      sessionId_ = other.sessionId_;
      msgId_ = other.msgId_;
      bubbleId_ = other.bubbleId_;
      msgOwner_ = other.msgOwner_;
      bizDataType_ = other.bizDataType_;
      code_ = other.code_;
      round_ = other.round_;
      taskId_ = other.taskId_;
      taskMode_ = other.taskMode_;
      isFinished_ = other.isFinished_;
      storyId_ = other.storyId_;
      userCefrLevel_ = other.userCefrLevel_;
      switch (other.outputDataCase) {
        case outputDataOneofCase.audio:
          audio = other.audio.Clone();
          break;
        case outputDataOneofCase.text:
          text = other.text.Clone();
          break;
        case outputDataOneofCase.bizEvent:
          bizEvent = other.bizEvent.Clone();
          break;
        case outputDataOneofCase.subGoalJudgmentResult:
          subGoalJudgmentResult = other.subGoalJudgmentResult.Clone();
          break;
        case outputDataOneofCase.missionStoryProgress:
          missionStoryProgress = other.missionStoryProgress.Clone();
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public SC_ChatAgent_Output Clone() {
      return new SC_ChatAgent_Output(this);
    }

    /// <summary>Field number for the "dialogId" field.</summary>
    public const int dialogIdFieldNumber = 1;
    private string dialogId_ = "";
    /// <summary>
    /// 对话id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string dialogId {
      get { return dialogId_; }
      set {
        dialogId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "sessionId" field.</summary>
    public const int sessionIdFieldNumber = 2;
    private string sessionId_ = "";
    /// <summary>
    /// 会话id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string sessionId {
      get { return sessionId_; }
      set {
        sessionId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "msgId" field.</summary>
    public const int msgIdFieldNumber = 3;
    private string msgId_ = "";
    /// <summary>
    /// 消息id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string msgId {
      get { return msgId_; }
      set {
        msgId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "bubbleId" field.</summary>
    public const int bubbleIdFieldNumber = 4;
    private string bubbleId_ = "";
    /// <summary>
    /// 对话气泡id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string bubbleId {
      get { return bubbleId_; }
      set {
        bubbleId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "msgOwner" field.</summary>
    public const int msgOwnerFieldNumber = 5;
    private global::Msg.basic.PB_MsgBelong msgOwner_ = global::Msg.basic.PB_MsgBelong.BNone;
    /// <summary>
    /// 消息归属方
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.basic.PB_MsgBelong msgOwner {
      get { return msgOwner_; }
      set {
        msgOwner_ = value;
      }
    }

    /// <summary>Field number for the "bizDataType" field.</summary>
    public const int bizDataTypeFieldNumber = 6;
    private global::Msg.social.PB_BizDataType bizDataType_ = global::Msg.social.PB_BizDataType.UNKNOWN_BIZ_DATA_TYPE;
    /// <summary>
    /// 业务数据类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.social.PB_BizDataType bizDataType {
      get { return bizDataType_; }
      set {
        bizDataType_ = value;
      }
    }

    /// <summary>Field number for the "audio" field.</summary>
    public const int audioFieldNumber = 7;
    /// <summary>
    /// 音频数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_ChatAgent_AudioRawFrame audio {
      get { return outputDataCase_ == outputDataOneofCase.audio ? (global::Msg.explore.PB_ChatAgent_AudioRawFrame) outputData_ : null; }
      set {
        outputData_ = value;
        outputDataCase_ = value == null ? outputDataOneofCase.None : outputDataOneofCase.audio;
      }
    }

    /// <summary>Field number for the "text" field.</summary>
    public const int textFieldNumber = 8;
    /// <summary>
    /// 文本数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_ChatAgent_TextFrame text {
      get { return outputDataCase_ == outputDataOneofCase.text ? (global::Msg.explore.PB_ChatAgent_TextFrame) outputData_ : null; }
      set {
        outputData_ = value;
        outputDataCase_ = value == null ? outputDataOneofCase.None : outputDataOneofCase.text;
      }
    }

    /// <summary>Field number for the "bizEvent" field.</summary>
    public const int bizEventFieldNumber = 9;
    /// <summary>
    /// 业务事件数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_ChatAgent_BizEventFrame bizEvent {
      get { return outputDataCase_ == outputDataOneofCase.bizEvent ? (global::Msg.explore.PB_ChatAgent_BizEventFrame) outputData_ : null; }
      set {
        outputData_ = value;
        outputDataCase_ = value == null ? outputDataOneofCase.None : outputDataOneofCase.bizEvent;
      }
    }

    /// <summary>Field number for the "subGoalJudgmentResult" field.</summary>
    public const int subGoalJudgmentResultFieldNumber = 13;
    /// <summary>
    /// 子目标判定结果
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_ChatAgent_SubGoalJudgmentResultFrame subGoalJudgmentResult {
      get { return outputDataCase_ == outputDataOneofCase.subGoalJudgmentResult ? (global::Msg.explore.PB_ChatAgent_SubGoalJudgmentResultFrame) outputData_ : null; }
      set {
        outputData_ = value;
        outputDataCase_ = value == null ? outputDataOneofCase.None : outputDataOneofCase.subGoalJudgmentResult;
      }
    }

    /// <summary>Field number for the "missionStoryProgress" field.</summary>
    public const int missionStoryProgressFieldNumber = 16;
    /// <summary>
    /// Mission剧情进度
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_ChatAgent_MissionStoryProgressFrame missionStoryProgress {
      get { return outputDataCase_ == outputDataOneofCase.missionStoryProgress ? (global::Msg.explore.PB_ChatAgent_MissionStoryProgressFrame) outputData_ : null; }
      set {
        outputData_ = value;
        outputDataCase_ = value == null ? outputDataOneofCase.None : outputDataOneofCase.missionStoryProgress;
      }
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int codeFieldNumber = 10;
    private global::Msg.explore.PB_ChatAgent_Code code_ = global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE;
    /// <summary>
    /// 状态码
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Msg.explore.PB_ChatAgent_Code code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "round" field.</summary>
    public const int roundFieldNumber = 11;
    private int round_;
    /// <summary>
    /// 对话轮次
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int round {
      get { return round_; }
      set {
        round_ = value;
      }
    }

    /// <summary>Field number for the "taskId" field.</summary>
    public const int taskIdFieldNumber = 12;
    private string taskId_ = "";
    /// <summary>
    /// 任务id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string taskId {
      get { return taskId_; }
      set {
        taskId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "taskMode" field.</summary>
    public const int taskModeFieldNumber = 14;
    private int taskMode_;
    /// <summary>
    /// 任务模式
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int taskMode {
      get { return taskMode_; }
      set {
        taskMode_ = value;
      }
    }

    /// <summary>Field number for the "isFinished" field.</summary>
    public const int isFinishedFieldNumber = 15;
    private bool isFinished_;
    /// <summary>
    /// 是否结束对话
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool isFinished {
      get { return isFinished_; }
      set {
        isFinished_ = value;
      }
    }

    /// <summary>Field number for the "storyId" field.</summary>
    public const int storyIdFieldNumber = 17;
    private string storyId_ = "";
    /// <summary>
    /// 剧情id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string storyId {
      get { return storyId_; }
      set {
        storyId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "userCefrLevel" field.</summary>
    public const int userCefrLevelFieldNumber = 18;
    private string userCefrLevel_ = "";
    /// <summary>
    /// 用户CEFR等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string userCefrLevel {
      get { return userCefrLevel_; }
      set {
        userCefrLevel_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    private object outputData_;
    /// <summary>Enum of possible cases for the "outputData" oneof.</summary>
    public enum outputDataOneofCase {
      None = 0,
      audio = 7,
      text = 8,
      bizEvent = 9,
      subGoalJudgmentResult = 13,
      missionStoryProgress = 16,
    }
    private outputDataOneofCase outputDataCase_ = outputDataOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public outputDataOneofCase outputDataCase {
      get { return outputDataCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearoutputData() {
      outputDataCase_ = outputDataOneofCase.None;
      outputData_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as SC_ChatAgent_Output);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(SC_ChatAgent_Output other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (dialogId != other.dialogId) return false;
      if (sessionId != other.sessionId) return false;
      if (msgId != other.msgId) return false;
      if (bubbleId != other.bubbleId) return false;
      if (msgOwner != other.msgOwner) return false;
      if (bizDataType != other.bizDataType) return false;
      if (!object.Equals(audio, other.audio)) return false;
      if (!object.Equals(text, other.text)) return false;
      if (!object.Equals(bizEvent, other.bizEvent)) return false;
      if (!object.Equals(subGoalJudgmentResult, other.subGoalJudgmentResult)) return false;
      if (!object.Equals(missionStoryProgress, other.missionStoryProgress)) return false;
      if (code != other.code) return false;
      if (round != other.round) return false;
      if (taskId != other.taskId) return false;
      if (taskMode != other.taskMode) return false;
      if (isFinished != other.isFinished) return false;
      if (storyId != other.storyId) return false;
      if (userCefrLevel != other.userCefrLevel) return false;
      if (outputDataCase != other.outputDataCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (dialogId.Length != 0) hash ^= dialogId.GetHashCode();
      if (sessionId.Length != 0) hash ^= sessionId.GetHashCode();
      if (msgId.Length != 0) hash ^= msgId.GetHashCode();
      if (bubbleId.Length != 0) hash ^= bubbleId.GetHashCode();
      if (msgOwner != global::Msg.basic.PB_MsgBelong.BNone) hash ^= msgOwner.GetHashCode();
      if (bizDataType != global::Msg.social.PB_BizDataType.UNKNOWN_BIZ_DATA_TYPE) hash ^= bizDataType.GetHashCode();
      if (outputDataCase_ == outputDataOneofCase.audio) hash ^= audio.GetHashCode();
      if (outputDataCase_ == outputDataOneofCase.text) hash ^= text.GetHashCode();
      if (outputDataCase_ == outputDataOneofCase.bizEvent) hash ^= bizEvent.GetHashCode();
      if (outputDataCase_ == outputDataOneofCase.subGoalJudgmentResult) hash ^= subGoalJudgmentResult.GetHashCode();
      if (outputDataCase_ == outputDataOneofCase.missionStoryProgress) hash ^= missionStoryProgress.GetHashCode();
      if (code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) hash ^= code.GetHashCode();
      if (round != 0) hash ^= round.GetHashCode();
      if (taskId.Length != 0) hash ^= taskId.GetHashCode();
      if (taskMode != 0) hash ^= taskMode.GetHashCode();
      if (isFinished != false) hash ^= isFinished.GetHashCode();
      if (storyId.Length != 0) hash ^= storyId.GetHashCode();
      if (userCefrLevel.Length != 0) hash ^= userCefrLevel.GetHashCode();
      hash ^= (int) outputDataCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (dialogId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(dialogId);
      }
      if (sessionId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(sessionId);
      }
      if (msgId.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(msgId);
      }
      if (bubbleId.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(bubbleId);
      }
      if (msgOwner != global::Msg.basic.PB_MsgBelong.BNone) {
        output.WriteRawTag(40);
        output.WriteEnum((int) msgOwner);
      }
      if (bizDataType != global::Msg.social.PB_BizDataType.UNKNOWN_BIZ_DATA_TYPE) {
        output.WriteRawTag(48);
        output.WriteEnum((int) bizDataType);
      }
      if (outputDataCase_ == outputDataOneofCase.audio) {
        output.WriteRawTag(58);
        output.WriteMessage(audio);
      }
      if (outputDataCase_ == outputDataOneofCase.text) {
        output.WriteRawTag(66);
        output.WriteMessage(text);
      }
      if (outputDataCase_ == outputDataOneofCase.bizEvent) {
        output.WriteRawTag(74);
        output.WriteMessage(bizEvent);
      }
      if (code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) {
        output.WriteRawTag(80);
        output.WriteEnum((int) code);
      }
      if (round != 0) {
        output.WriteRawTag(88);
        output.WriteInt32(round);
      }
      if (taskId.Length != 0) {
        output.WriteRawTag(98);
        output.WriteString(taskId);
      }
      if (outputDataCase_ == outputDataOneofCase.subGoalJudgmentResult) {
        output.WriteRawTag(106);
        output.WriteMessage(subGoalJudgmentResult);
      }
      if (taskMode != 0) {
        output.WriteRawTag(112);
        output.WriteInt32(taskMode);
      }
      if (isFinished != false) {
        output.WriteRawTag(120);
        output.WriteBool(isFinished);
      }
      if (outputDataCase_ == outputDataOneofCase.missionStoryProgress) {
        output.WriteRawTag(130, 1);
        output.WriteMessage(missionStoryProgress);
      }
      if (storyId.Length != 0) {
        output.WriteRawTag(138, 1);
        output.WriteString(storyId);
      }
      if (userCefrLevel.Length != 0) {
        output.WriteRawTag(146, 1);
        output.WriteString(userCefrLevel);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (dialogId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(dialogId);
      }
      if (sessionId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(sessionId);
      }
      if (msgId.Length != 0) {
        output.WriteRawTag(26);
        output.WriteString(msgId);
      }
      if (bubbleId.Length != 0) {
        output.WriteRawTag(34);
        output.WriteString(bubbleId);
      }
      if (msgOwner != global::Msg.basic.PB_MsgBelong.BNone) {
        output.WriteRawTag(40);
        output.WriteEnum((int) msgOwner);
      }
      if (bizDataType != global::Msg.social.PB_BizDataType.UNKNOWN_BIZ_DATA_TYPE) {
        output.WriteRawTag(48);
        output.WriteEnum((int) bizDataType);
      }
      if (outputDataCase_ == outputDataOneofCase.audio) {
        output.WriteRawTag(58);
        output.WriteMessage(audio);
      }
      if (outputDataCase_ == outputDataOneofCase.text) {
        output.WriteRawTag(66);
        output.WriteMessage(text);
      }
      if (outputDataCase_ == outputDataOneofCase.bizEvent) {
        output.WriteRawTag(74);
        output.WriteMessage(bizEvent);
      }
      if (code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) {
        output.WriteRawTag(80);
        output.WriteEnum((int) code);
      }
      if (round != 0) {
        output.WriteRawTag(88);
        output.WriteInt32(round);
      }
      if (taskId.Length != 0) {
        output.WriteRawTag(98);
        output.WriteString(taskId);
      }
      if (outputDataCase_ == outputDataOneofCase.subGoalJudgmentResult) {
        output.WriteRawTag(106);
        output.WriteMessage(subGoalJudgmentResult);
      }
      if (taskMode != 0) {
        output.WriteRawTag(112);
        output.WriteInt32(taskMode);
      }
      if (isFinished != false) {
        output.WriteRawTag(120);
        output.WriteBool(isFinished);
      }
      if (outputDataCase_ == outputDataOneofCase.missionStoryProgress) {
        output.WriteRawTag(130, 1);
        output.WriteMessage(missionStoryProgress);
      }
      if (storyId.Length != 0) {
        output.WriteRawTag(138, 1);
        output.WriteString(storyId);
      }
      if (userCefrLevel.Length != 0) {
        output.WriteRawTag(146, 1);
        output.WriteString(userCefrLevel);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (dialogId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(dialogId);
      }
      if (sessionId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(sessionId);
      }
      if (msgId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(msgId);
      }
      if (bubbleId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(bubbleId);
      }
      if (msgOwner != global::Msg.basic.PB_MsgBelong.BNone) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) msgOwner);
      }
      if (bizDataType != global::Msg.social.PB_BizDataType.UNKNOWN_BIZ_DATA_TYPE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) bizDataType);
      }
      if (outputDataCase_ == outputDataOneofCase.audio) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(audio);
      }
      if (outputDataCase_ == outputDataOneofCase.text) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(text);
      }
      if (outputDataCase_ == outputDataOneofCase.bizEvent) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(bizEvent);
      }
      if (outputDataCase_ == outputDataOneofCase.subGoalJudgmentResult) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(subGoalJudgmentResult);
      }
      if (outputDataCase_ == outputDataOneofCase.missionStoryProgress) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(missionStoryProgress);
      }
      if (code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) code);
      }
      if (round != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(round);
      }
      if (taskId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(taskId);
      }
      if (taskMode != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(taskMode);
      }
      if (isFinished != false) {
        size += 1 + 1;
      }
      if (storyId.Length != 0) {
        size += 2 + pb::CodedOutputStream.ComputeStringSize(storyId);
      }
      if (userCefrLevel.Length != 0) {
        size += 2 + pb::CodedOutputStream.ComputeStringSize(userCefrLevel);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(SC_ChatAgent_Output other) {
      if (other == null) {
        return;
      }
      if (other.dialogId.Length != 0) {
        dialogId = other.dialogId;
      }
      if (other.sessionId.Length != 0) {
        sessionId = other.sessionId;
      }
      if (other.msgId.Length != 0) {
        msgId = other.msgId;
      }
      if (other.bubbleId.Length != 0) {
        bubbleId = other.bubbleId;
      }
      if (other.msgOwner != global::Msg.basic.PB_MsgBelong.BNone) {
        msgOwner = other.msgOwner;
      }
      if (other.bizDataType != global::Msg.social.PB_BizDataType.UNKNOWN_BIZ_DATA_TYPE) {
        bizDataType = other.bizDataType;
      }
      if (other.code != global::Msg.explore.PB_ChatAgent_Code.UNKNOWN_CODE) {
        code = other.code;
      }
      if (other.round != 0) {
        round = other.round;
      }
      if (other.taskId.Length != 0) {
        taskId = other.taskId;
      }
      if (other.taskMode != 0) {
        taskMode = other.taskMode;
      }
      if (other.isFinished != false) {
        isFinished = other.isFinished;
      }
      if (other.storyId.Length != 0) {
        storyId = other.storyId;
      }
      if (other.userCefrLevel.Length != 0) {
        userCefrLevel = other.userCefrLevel;
      }
      switch (other.outputDataCase) {
        case outputDataOneofCase.audio:
          if (audio == null) {
            audio = new global::Msg.explore.PB_ChatAgent_AudioRawFrame();
          }
          audio.MergeFrom(other.audio);
          break;
        case outputDataOneofCase.text:
          if (text == null) {
            text = new global::Msg.explore.PB_ChatAgent_TextFrame();
          }
          text.MergeFrom(other.text);
          break;
        case outputDataOneofCase.bizEvent:
          if (bizEvent == null) {
            bizEvent = new global::Msg.explore.PB_ChatAgent_BizEventFrame();
          }
          bizEvent.MergeFrom(other.bizEvent);
          break;
        case outputDataOneofCase.subGoalJudgmentResult:
          if (subGoalJudgmentResult == null) {
            subGoalJudgmentResult = new global::Msg.explore.PB_ChatAgent_SubGoalJudgmentResultFrame();
          }
          subGoalJudgmentResult.MergeFrom(other.subGoalJudgmentResult);
          break;
        case outputDataOneofCase.missionStoryProgress:
          if (missionStoryProgress == null) {
            missionStoryProgress = new global::Msg.explore.PB_ChatAgent_MissionStoryProgressFrame();
          }
          missionStoryProgress.MergeFrom(other.missionStoryProgress);
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            dialogId = input.ReadString();
            break;
          }
          case 18: {
            sessionId = input.ReadString();
            break;
          }
          case 26: {
            msgId = input.ReadString();
            break;
          }
          case 34: {
            bubbleId = input.ReadString();
            break;
          }
          case 40: {
            msgOwner = (global::Msg.basic.PB_MsgBelong) input.ReadEnum();
            break;
          }
          case 48: {
            bizDataType = (global::Msg.social.PB_BizDataType) input.ReadEnum();
            break;
          }
          case 58: {
            global::Msg.explore.PB_ChatAgent_AudioRawFrame subBuilder = new global::Msg.explore.PB_ChatAgent_AudioRawFrame();
            if (outputDataCase_ == outputDataOneofCase.audio) {
              subBuilder.MergeFrom(audio);
            }
            input.ReadMessage(subBuilder);
            audio = subBuilder;
            break;
          }
          case 66: {
            global::Msg.explore.PB_ChatAgent_TextFrame subBuilder = new global::Msg.explore.PB_ChatAgent_TextFrame();
            if (outputDataCase_ == outputDataOneofCase.text) {
              subBuilder.MergeFrom(text);
            }
            input.ReadMessage(subBuilder);
            text = subBuilder;
            break;
          }
          case 74: {
            global::Msg.explore.PB_ChatAgent_BizEventFrame subBuilder = new global::Msg.explore.PB_ChatAgent_BizEventFrame();
            if (outputDataCase_ == outputDataOneofCase.bizEvent) {
              subBuilder.MergeFrom(bizEvent);
            }
            input.ReadMessage(subBuilder);
            bizEvent = subBuilder;
            break;
          }
          case 80: {
            code = (global::Msg.explore.PB_ChatAgent_Code) input.ReadEnum();
            break;
          }
          case 88: {
            round = input.ReadInt32();
            break;
          }
          case 98: {
            taskId = input.ReadString();
            break;
          }
          case 106: {
            global::Msg.explore.PB_ChatAgent_SubGoalJudgmentResultFrame subBuilder = new global::Msg.explore.PB_ChatAgent_SubGoalJudgmentResultFrame();
            if (outputDataCase_ == outputDataOneofCase.subGoalJudgmentResult) {
              subBuilder.MergeFrom(subGoalJudgmentResult);
            }
            input.ReadMessage(subBuilder);
            subGoalJudgmentResult = subBuilder;
            break;
          }
          case 112: {
            taskMode = input.ReadInt32();
            break;
          }
          case 120: {
            isFinished = input.ReadBool();
            break;
          }
          case 130: {
            global::Msg.explore.PB_ChatAgent_MissionStoryProgressFrame subBuilder = new global::Msg.explore.PB_ChatAgent_MissionStoryProgressFrame();
            if (outputDataCase_ == outputDataOneofCase.missionStoryProgress) {
              subBuilder.MergeFrom(missionStoryProgress);
            }
            input.ReadMessage(subBuilder);
            missionStoryProgress = subBuilder;
            break;
          }
          case 138: {
            storyId = input.ReadString();
            break;
          }
          case 146: {
            userCefrLevel = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            dialogId = input.ReadString();
            break;
          }
          case 18: {
            sessionId = input.ReadString();
            break;
          }
          case 26: {
            msgId = input.ReadString();
            break;
          }
          case 34: {
            bubbleId = input.ReadString();
            break;
          }
          case 40: {
            msgOwner = (global::Msg.basic.PB_MsgBelong) input.ReadEnum();
            break;
          }
          case 48: {
            bizDataType = (global::Msg.social.PB_BizDataType) input.ReadEnum();
            break;
          }
          case 58: {
            global::Msg.explore.PB_ChatAgent_AudioRawFrame subBuilder = new global::Msg.explore.PB_ChatAgent_AudioRawFrame();
            if (outputDataCase_ == outputDataOneofCase.audio) {
              subBuilder.MergeFrom(audio);
            }
            input.ReadMessage(subBuilder);
            audio = subBuilder;
            break;
          }
          case 66: {
            global::Msg.explore.PB_ChatAgent_TextFrame subBuilder = new global::Msg.explore.PB_ChatAgent_TextFrame();
            if (outputDataCase_ == outputDataOneofCase.text) {
              subBuilder.MergeFrom(text);
            }
            input.ReadMessage(subBuilder);
            text = subBuilder;
            break;
          }
          case 74: {
            global::Msg.explore.PB_ChatAgent_BizEventFrame subBuilder = new global::Msg.explore.PB_ChatAgent_BizEventFrame();
            if (outputDataCase_ == outputDataOneofCase.bizEvent) {
              subBuilder.MergeFrom(bizEvent);
            }
            input.ReadMessage(subBuilder);
            bizEvent = subBuilder;
            break;
          }
          case 80: {
            code = (global::Msg.explore.PB_ChatAgent_Code) input.ReadEnum();
            break;
          }
          case 88: {
            round = input.ReadInt32();
            break;
          }
          case 98: {
            taskId = input.ReadString();
            break;
          }
          case 106: {
            global::Msg.explore.PB_ChatAgent_SubGoalJudgmentResultFrame subBuilder = new global::Msg.explore.PB_ChatAgent_SubGoalJudgmentResultFrame();
            if (outputDataCase_ == outputDataOneofCase.subGoalJudgmentResult) {
              subBuilder.MergeFrom(subGoalJudgmentResult);
            }
            input.ReadMessage(subBuilder);
            subGoalJudgmentResult = subBuilder;
            break;
          }
          case 112: {
            taskMode = input.ReadInt32();
            break;
          }
          case 120: {
            isFinished = input.ReadBool();
            break;
          }
          case 130: {
            global::Msg.explore.PB_ChatAgent_MissionStoryProgressFrame subBuilder = new global::Msg.explore.PB_ChatAgent_MissionStoryProgressFrame();
            if (outputDataCase_ == outputDataOneofCase.missionStoryProgress) {
              subBuilder.MergeFrom(missionStoryProgress);
            }
            input.ReadMessage(subBuilder);
            missionStoryProgress = subBuilder;
            break;
          }
          case 138: {
            storyId = input.ReadString();
            break;
          }
          case 146: {
            userCefrLevel = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
