﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using AnimationSystem;
using Cysharp.Threading.Tasks;
using Game.Modules.Record;
using UIBind.Profile;
using UnityEngine;
using YooAsset;
using Random = UnityEngine.Random;

namespace UIBind.Main
{
    public partial class StarX5Background : ExtendedComponent, IRecordEventListener
    {
        private BackGroundAvatarRoot avatarRoot = new BackGroundAvatarRoot();
        
        private static readonly string avatarSoundPath = "Assets/Build/Sound/StarX5/";
        // private static readonly Dictionary<StarX5Event, List<string>> sound = new()
        // {
        //     { StarX5Event.WinStreak5, new List<string> { "Excellent", "YouAreGreat" } },
        //     { StarX5Event.WinStreak10, new List<string> { "GoodJob", "YouAreAwesome" } },
        //     { StarX5Event.OnStart, new List<string> { "ReadyGo" } },
        //     { StarX5Event.SingleRight, new List<string> { "Wow","Nice","Awesome","Yeah" } },
        //     { StarX5Event.MultiWrong, new List<string> { "TakeItEasy","ThatsOK","DontWorry" } },
        //     { StarX5Event.OnExit, new List<string> { "WellDone","YouDidIt" } },
        //     { StarX5Event.Review, new List<string> { "TryItAgain" } },
        // };
        
        private static readonly Dictionary<StarX5Event, string> sound = new()
        {
            { StarX5Event.WinStreak5, "star5_win5" },
            { StarX5Event.WinStreak10, "star5_win10" },
            { StarX5Event.OnStart, "star5_onStart" },
            { StarX5Event.SingleRight, "star5_right" },
            { StarX5Event.MultiWrong, "star5_wrong" },
            { StarX5Event.OnExit, "star5_onexit" },
            { StarX5Event.Review, "star5_review" },
        };
        
        public bool IsReceivingEvents => true;

        //需要约定参数(struct,并且能够展示)

        public struct DisplayParameters
        {
            public DisplayParameters(long avatarID, string imgBgName)
            {
                this.avatarID = avatarID;
                this.imgBgName = imgBgName;
            }

            public long avatarID;
            public string imgBgName;
        }

        #region demo
        protected override void OnAddedToStage()
        {
            DebugShowAvatar();
            RecordEventManager.Instance.AddListener(this);
        }
        protected override void OnRemovedFromStage()
        {
            RecordEventManager.Instance.RemoveListener(this);
            this.avatarRoot.Dispose();
        }
        
        private void DebugShowAvatar()
        {
            GenerateRTAsync().Forget(); // 使用Forget()来忽略Task，调用者不需要处理异步
        }
        
        private async UniTaskVoid GenerateRTAsync()
        {
            try
            {
                var renderTexture = await this.avatarRoot.GenerateRT(2982168190696505344,"Assets/Build/ExploreBG/StarX5BG").AsUniTask();
                //var renderTexture = await this.avatarRoot.GenerateRT(Random.Range(0f,1f)<= 0.5f ? 17552474204327943 : 2982168190696505344,"Assets/Build/ExploreBG/StarX5BG").AsUniTask();
                this.avatarRoot.SetBackground(null,null,new Vector3(0.17f,0.17f,0.17f));
                this.avatarRoot.SetAvatar(new Vector3(0.0f,0.35f,1.553f),Quaternion.Euler(new Vector3(-10.0f,-180.0f,0.0f)),new Vector3(0.8f,0.8f,0.8f));
                OnGenerateRTCompleted(renderTexture);
            }
            catch (System.Exception ex)
            {
                Debug.LogError(ex.ToString());
                OnGenerateRTFailed();
            }
        }
        
        #endregion

        #region api

        public enum StarX5Event
        {
            Default, //每当用户进行任何操作都要传递Default。我这边要计时。有长时间不操作播动作的需求。
            SingleRight, //单对
            MultiWrong, //连错
            Recording, //当用户点击录音的时候调用这个事件。
            WinStreak5,
            WinStreak10,
            OnStart, //avatar入场时的事件(可以考虑合并到我的组件内自己触发)
            OnExit, //avatar离场时的事件
            DeRecording,
            Review, //错题重练
        }
        
        /// <summary>
        /// 展示指定的Avatar.以后服务器有下发数据了就调用这个。
        /// </summary>
        /// <param name="displayParameters"></param>
        /// <param name="OnComplete"></param>
        public async UniTaskVoid ShowAvatar(DisplayParameters displayParameters, Action<bool> OnComplete = null)
        {
            try
            {
                var renderTexture = await this.avatarRoot.GenerateRT(displayParameters.avatarID,displayParameters.imgBgName).AsUniTask();
                OnGenerateRTCompleted(renderTexture);
            }
            catch (System.Exception ex)
            {
                OnGenerateRTFailed();
            }
            
        }

        // 手动调用释放。
        public void ClearAvatar()
        {
            this.avatarRoot.ClearRT();
        }
        
        
        public void TriggerEvent(StarX5Event e,Action<StarX5PlayAnimationState.AnimCallBack> callback = null)
        {
            Action<StarX5PlayAnimationState.AnimCallBack> newCallBack = (result) =>
            {
                this.StarX5BackgroundCallBack(result);
                if (callback != null) callback.Invoke(result);
            };

           // string url;
            
            switch (e)
            {
                case StarX5Event.Default:
                    this.TriggerRefresh();
                    break;
                case StarX5Event.SingleRight:
                    this.avatarRoot.PlayAnimationByExcel("StarX5Correct",newCallBack);
                    break;
                case StarX5Event.MultiWrong:
                    this.avatarRoot.PlayAnimationByExcel("StarX5Wrong",newCallBack);
                    break;
                case StarX5Event.Recording:
                    this.avatarRoot.PlayAnimationByExcel("StarX5RecordingStart",newCallBack,99999f);
                    break;
                case StarX5Event.WinStreak5:
                   // url = Path.Combine(avatarSoundPath, "ReadyGo-2");
                    this.avatarRoot.PlayAnimationByExcel("StarX5WinStreak5",newCallBack);
                    break;
                case StarX5Event.WinStreak10:
                   // url = Path.Combine(avatarSoundPath, "ReadyGo-2");
                    this.avatarRoot.PlayAnimationByExcel("StarX5WinStreak10",newCallBack);
                    break;
                case StarX5Event.OnStart:
                  //  url = Path.Combine(avatarSoundPath, "ReadyGo-2");
                   // PlaySound(url);
                    this.avatarRoot.PlayAnimationByExcel("StarX5StateBegin",newCallBack);
                    break;
                case StarX5Event.OnExit:
                    this.avatarRoot.PlayAnimationByExcel("StarX5StateEnd",newCallBack);
                    break;
                case StarX5Event.DeRecording:
                    this.avatarRoot.PlayAnimationByExcel("StarX5RecordingEnd",newCallBack);
                    break;
                case StarX5Event.Review:
                    this.avatarRoot.PlayAnimationByExcel("StarX5Review",newCallBack);
                    break;
                default:
                    break;
            }
            
            sound.TryGetValue(e, out var soundStr);
            SoundManger.SoundClip s = SoundManger.instance.GetStarX5SoundPath(soundStr);
            if (s != null)
            {
                avatarRoot.PlayAvatarTTS(s.path, s.volume);
            }
        }


        /// <param name="isRight">当次是对的还是错的</param>
        /// <param name="rightStreak"></param>
        /// <param name="totalRight"></param>
        /// <param name="wrongStreak"></param>
        /// <param name="totalWrong"></param>
        public void OnPlayerAnswer(bool isRight,int rightStreak,int totalRight,int wrongStreak,int totalWrong,Action<StarX5PlayAnimationState.AnimCallBack> cb = null)
        {
            if (isRight)
            {
                // 7.3.2025 Check时现在只触发答对了的动画
                // if (rightStreak == 5)
                // {
                //     TriggerEvent(StarX5Event.WinStreak5,cb);
                // }
                // else if (rightStreak == 10)
                // {
                //     TriggerEvent(StarX5Event.WinStreak10,cb);
                // }
                //else
                {
                    TriggerEvent(StarX5Event.SingleRight,cb);
                }
            }
            else
            {
                // 7.3.2025 Check时现在只触发答对了的动画
                // if (totalWrong % 2 == 0)
                // { 
                //     TriggerEvent(StarX5Event.MultiWrong,cb);
                // }
            }
            TriggerEvent(StarX5Event.Default,cb);
        }
        
        
        public void OnRecordStart(string rawString, int recordId)
        {
            TriggerEvent(StarX5Event.Recording);
        }

        public void OnRecordStop()
        {
            TriggerEvent(StarX5Event.DeRecording);
        }

        public void OnRecordCancel()
        {
            TriggerEvent(StarX5Event.DeRecording);
        }

        public void OnVad()
        {
            TriggerEvent(StarX5Event.DeRecording);
        }

        public void OnCountDown() { }

        public void OnMatchAll()
        {
            TriggerEvent(StarX5Event.DeRecording);
        }

        public void OnTranscription(string transcribedText, int recordId) { }
        #endregion
        
        private void TriggerRefresh()
        {
            if (this.avatarRoot.Manager)
            {
                var state = this.avatarRoot.Manager.currentState;
                if (state is StarX5IdleState idleState)
                {
                    idleState.Reset();
                }
            }
        }

        private void StarX5BackgroundCallBack(StarX5PlayAnimationState.AnimCallBack cb)
        {
            if (cb == StarX5PlayAnimationState.AnimCallBack.Ended)
            {
                if (this.avatarRoot.Manager)
                {
                    var nstate = new StarX5IdleState(); //idle
                    this.avatarRoot.Manager.SetState(nstate); ;
                }
            }
        }
       
        // 完成时的回调处理
        private void OnGenerateRTCompleted(UnityEngine.RenderTexture renderTexture, Action<bool> OnComplete = null)
        {
            if (!renderTexture)
            {
                OnGenerateRTFailed();
                if (OnComplete != null) OnComplete(false);
            }
            
            if (StarX5Loader != null)
            {
                StarX5Loader.texture = new FairyGUI.NTexture(renderTexture);
                if (OnComplete != null) OnComplete(true);
                
                // // 测试用：等待4秒后触发MultiWrong事件
                // DelayTriggerTestEvent().Forget();
            }
            
            
        }
        
        // // 测试用的延迟触发方法
        // private async UniTaskVoid DelayTriggerTestEvent()
        // {
        //     try
        //     {
        //         await UniTask.Delay(4000); // 等待4秒
        //         TriggerEvent(StarX5Event.MultiWrong);
        //         await UniTask.Delay(4000); // 等待4秒
        //         TriggerEvent(StarX5Event.Recording);
        //         await UniTask.Delay(11000); // 等待4秒
        //         TriggerEvent(StarX5Event.DeRecording);
        //         await UniTask.Delay(4000); // 等待4秒
        //         TriggerEvent(StarX5Event.SingleRight);
        //         await UniTask.Delay(4000); // 等待4秒
        //         TriggerEvent(StarX5Event.WinStreak5);
        //         await UniTask.Delay(4000); // 等待4秒
        //         TriggerEvent(StarX5Event.WinStreak10);
        //         await UniTask.Delay(4000); // 等待4秒
        //         TriggerEvent(StarX5Event.OnStart);
        //         await UniTask.Delay(4000); // 等待4秒
        //         TriggerEvent(StarX5Event.OnExit);
        //         await UniTask.Delay(60000); // 等待4秒
        //         TriggerEvent(StarX5Event.Default);
        //         Debug.Log("测试：4秒后触发MultiWrong事件");
        //     }
        //     catch (System.Exception ex)
        //     {
        //         Debug.LogError($"延迟触发测试事件失败: {ex}");
        //     }
        //}
        
        // 失败时的回调处理
        private void OnGenerateRTFailed( Action<bool> OnComplete = null)
        {
            if (OnComplete != null) OnComplete(false);
        }


    }
}

