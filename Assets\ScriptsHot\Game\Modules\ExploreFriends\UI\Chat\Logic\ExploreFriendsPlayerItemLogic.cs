﻿
using UIBind.ExploreFriends.Component;

namespace UIBind.ExploreFriends
{
    public class ExploreFriendsPlayerItemLogic:ItemComponentLogicBase
    {
        private string _txtStr;

        private ExploreFriendsPlayerItem _com;
        public override void UIReady()
        {
            _com = new ExploreFriendsPlayerItem();
            _com.Construct(GetUI());
        }

        public void SetTxt(string value)
        {
            _txtStr = value;
            _com.Txt.text = value;
        }
    }
}