/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.ReviewQuestion
{
    public partial class ReviewQuestionGapFilliingCom : AQuestionCard
    {
        public static string pkgName => "ReviewQuestion";
        public static string comName => "ReviewQuestionGapFilliingCom";
        public static string url => "ui://xlh8p6j0n1h413";

        public GImage imgMask;
        public GTextField tfTitle;
        public GComponent comText;
        public GTextField tfTrans;
        public GButton btnPlayerVoic;
        public GButton btnPlayVoic;
        public GGroup grpVoice;
        public GTextField tfState;
        public GComponent btnRecord;
        public GImage imgCurrent;
        public GRichTextField tfStem;
        public Transition OnShowTT;
        public Transition OnHideTT;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(ReviewQuestionGapFilliingCom));
        }

        public override void ConstructFromXML(XML xml)
        {
            imgMask = GetChildAt(0) as GImage;
            tfTitle = GetChildAt(2) as GTextField;
            comText = GetChildAt(5) as GComponent;
            tfTrans = GetChildAt(6) as GTextField;
            btnPlayerVoic = GetChildAt(7) as GButton;
            btnPlayVoic = GetChildAt(8) as GButton;
            grpVoice = GetChildAt(9) as GGroup;
            tfState = GetChildAt(10) as GTextField;
            btnRecord = GetChildAt(11) as GComponent;
            imgCurrent = GetChildAt(12) as GImage;
            tfStem = GetChildAt(14) as GRichTextField;
            OnShowTT = GetTransitionAt(0);
            OnHideTT = GetTransitionAt(1);
        }
        public override void Dispose()
        {
            imgMask = null;
            tfTitle = null;
            comText = null;
            tfTrans = null;
            btnPlayerVoic = null;
            btnPlayVoic = null;
            grpVoice = null;
            tfState = null;
            btnRecord = null;
            imgCurrent = null;
            tfStem = null;
            OnShowTT = null;
            OnHideTT = null;

            base.Dispose();
        }
    }
}