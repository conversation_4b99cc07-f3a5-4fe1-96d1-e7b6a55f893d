using CommonUI;
using FairyGUI;
using Msg.question;
using ScriptsHot.Game.Modules.Common;
using ScriptsHot.Game.Modules.FragmentPractice;
using ScriptsHot.Game.Modules.ReviewQuestion.Questions.Parser;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using static ScriptsHot.Game.Modules.FragmentPractice.FragmentPracticeUI;
using Game.Modules.FragmentPractice;
using Game.Modules.Record;

namespace UIBind.FragmentPractice
{
    public interface ITextMask
    {
        // 如果答案需要自定义掩码
        string GetMask();
    }

    public partial class TextQuestion : AFragQuestion, IRecordEventListener, IQuestionEventListener
    {
        private int recordId;
        private AFragAnswer answerComp;

        public bool IsReceivingEvents => true;

        public GRichTextFieldPlus TfQuestion => tfQuestion as GRichTextFieldPlus;

        override protected void OnAddedToStage()
        {
            RecordEventManager.Instance.AddListener(this);
            QuestionEventManager.Instance.AddListener(this);
        }

        protected override void OnRemovedFromStage()
        {
            RecordEventManager.Instance.RemoveListener(this);
            QuestionEventManager.Instance.RemoveListener(this);
        }

        #region refresh
        override public void ShowPractice(AFragAnswer answerComp)
        {
            this.answerComp = answerComp;
            RefreshAudio(base.Practice, answerComp);

            FillQuestionText();


            if (Practice is IImageQuestion imageQuestion)
            {
                imgQuestion.ldr.url = imageQuestion.GetImageUrl();
                ctrlImage.selectedIndex = string.IsNullOrEmpty(imgQuestion.ldr.url) ? 0 : 1;
            }
            else
            {
                ctrlImage.selectedIndex = 0;
            }
            grpOutside.EnsureBoundsCorrect();

            height = Mathf.Max(grpOutside.y + grpOutside.height, btnPlayAudio.y + btnPlayAudio.height);
        }

        private void RefreshAudio(APracticeData question, AFragAnswer answerComp)
        {
            var playAudio = question.AudioId != 0;
            audio.selectedPage = playAudio ? "visible" : "invisible";
            btnPlayAudio.AudioId = question.AudioId;

            if (playAudio && IsCurrent)
            {
                btnPlayAudio.Play();
            }
            btnPlayAudio.onClick.Add(OnBtnPlayAudioClick);
        }

        private void OnBtnPlayAudioClick(EventContext context)
        {
            RecordEventManager.Instance.DispatchRecordCancel();
            DotPracticeManager.Instance.Collect(new DataDot_PlayTTS());
        }

        // todo 走事件
        private void FillQuestionText(string filler = "mmmmm")
        {
            if (Practice.QuestionType == PB_QuickPracticeType.Cloze)
            {
                this.TfQuestion.textFormat.lineSpacing = 40;
            }
            var enableTranslate = (Practice.QuestionType != PB_QuickPracticeType.TranslateSenAndTapSen);

            var blocks = GRichTextFieldPlus.SplitWords(Practice.GetStem().Trim(), Practice.GetBlankRanges().Select(
                blank =>new RichTextBlank() { start = blank.start_index, end = blank.end_index, isUnderline = true, isFilled = false}
            ), TfQuestion.color, enableTranslate:enableTranslate);

            // 打个补丁（如果没有这个补丁就不需要分两步做了）
            CheckExplanation(blocks);
            CheckMask(blocks);
            CheckRecord();

            TfQuestion.SetWords(blocks);

            Debug.Log(blocks.Count());
            TimerManager.instance.RegisterNextFrame((c) => LoadClozePositions());
        }

        private void CheckRecord()
        {
            if (answerComp is RecordAnswer)
            {
                TfQuestion.EnableMatchColoring = true;
            }
        }

        private void CheckExplanation(RichTextBlock[] blocks)
        {
            if (Practice.QuestionType != PB_QuickPracticeType.Explanation) return;

            // 解释题要把空格解释成重点词：加粗变色不加点
            foreach (var block in blocks)
            {
                if (block.IsBlank)
                {
                    // 是空格
                    block.isUnderline = false;
                    block.enableTranslate = false;
                    block.fontColor = Purple;
                    block.isBold = true;
                }
            }
        }

        private void CheckMask(RichTextBlock[] blocks)
        {
            ITextMask textMask = answerComp as ITextMask;
            if (textMask == null) return;

            string mask = textMask.GetMask();

            // 把空格加自定义蒙版
            foreach (var block in blocks)
            {
                if (block.IsBlank)
                {
                    // 是空格
                    block.text = mask;
                    block.underlineOffset = -20;  // 多空题的下划线下移15像素
                }
            }
        }

        #endregion

        #region IRecordEventListener
        public void OnRecordStart(string rawString, int recordId)
        {
            this.recordId = recordId;

            // 关闭可能的 TTS
            TTSManager.instance.StopTTS();
        }

        public void OnRecordStop()
        {
            this.recordId = -1;
            RefreshResult(MatchedColor, BlackColor);
        }

        public void OnRecordCancel() { OnRecordStop(); }

        public void OnVad() { OnRecordStop(); }

        public void OnCountDown() { OnRecordStop(); }

        public void OnMatchAll() { OnRecordStop(); }

        public void OnTranscription(string transcribedText, int recordId)
        {
            if (this.recordId != recordId) return;
            if (GMicrophoneManager.instance.IsRecording)
            {
                RefreshResult(MatchedColor, BlackColor);
            }
            else
            {
                RefreshResult(MatchedColor, BlackColor);
            }

            //暂未启用，基本结果应该是一致的
        }

        private void RefreshResult(Color clrMatch, Color clrNotMatch)
        {
            List<MatchResult> results = SpeechToTextManager.instance.MatchWordsInReference(clrMatch, clrNotMatch);

            Dictionary<string, Color> wordColors = new(20);
            foreach (var result in results)
            { 
                wordColors[result.MatchedWord] = result.Color;
            }
            TfQuestion.MatchColoring(wordColors);
        }
        #endregion

        #region IQuestionEventListener
        public void OnAnswered()
        {

        }

        public void OnSubmit()
        {
            if (Practice.QuestionType == PB_QuickPracticeType.listenSenAndChooseWord)
                FillQuestionText();
        }

        public void OnRetry()
        {
            FillQuestionText();
        }

        public void AutoCheck() { }
        public void OnReset() { }
        public void OnJumpListenTask() { }
        public void OnJumpSpeakTask() { }
        #endregion
    }
}