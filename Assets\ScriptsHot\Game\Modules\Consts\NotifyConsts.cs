﻿using System;

public class NotifyConsts
{

    public const string ScenePreLoadEvent_MainPath = "ScenePreLoadEvent_MainPath";
    public const string ScenePreLoadEvent_Explore = "ScenePreLoadEvent_Explore";
    
    public const string PlayerLogin = "PlayerLogin";
    public const string GoldNumChange = "GoldNumChange";
    public const string TaskDetailChange = "TaskDetailChange";
    public const string TaskStatusChange = "TaskStatusChange";
    public const string TaskModeChange = "TaskModeChange";
    public const string TaskProgressChange = "TaskProgressChange";
    public const string FreeLevelChange = "FreeLevelChange";
    public const string StrengthenEvaluateChange = "StrengthenEvaluateChange";
    public const string ShowTaskPanel = "ShowTaskPanel";
    public const string OnBoardClickedRole = "OnBoardClickedRole";

    public const string SceneLoadComplete = "SceneLoadComplete";
    public const string SceneLoadCompletePre = "SceneLoadCompletePre";//早于SceneLoadComplete触发的事件
    public const string SceneUnloadComplete = "SceneUnloadComplete";

    public const string SceneLoadProgress = "SceneLoadProgress";
    public const string SceneChangeComplete = "SceneChangeComplete";
    
    public const string CloseChatScoreUI = "CloseChatScoreUI";
    public const string LearnPathGoalInfo = "LearnPathGoalInfo";
    
    public const string ChatSuggestionInfo = "ChatSuggestionInfo";
    
    public const string DoTopLeftBack = "DoTopLeftBack";//返回类逻辑，含对话中途返回
    public const string DoTopLeftGoOn = "DoTopLeftGoOn";//取消返回
    public const string DoChatBack = "DoChatBack";//退出类逻辑，主要是正常结束的退出

    public const string ShowPathLine = "ShowPathLine";
    public const string RefreshWorldPath = "WorldPath";
    
    public const string LearnPathNodeDataUpdate = "LearnPathNodeDataUpdate";
   

    public const string ChatStartStaminaRefreshEvent = "ChatStartStaminaRefreshEvent";
    
    public const string ChatNewEnter = "ChatNewEnter";
    public const string ChatNewExit = "ChatNewExit";

    public const string ClickAvatarEnterStoryTitle = "ClickAvatarEnterStoryTitle";
    public const string ChangeLanguage = "ChangeLanguage";  // 切换语言，没有区分切的是哪个语言
    
    public const string UserLookAtAvatar = "UserLookAtAvatar";
    public const string UserRunToAvatar = "UserRunToAvatar";
    
    public const string ChangeCameraMode = "ChangeCameraMode";

    public const string EnterDialog = "EnterDialog";//任意常规会话（非3D）
    public const string ExitDialog = "ExitDialog";
    
    public const string SettlementReqDialogResultFinishEvent = "SettlementReqDialogResultFinishEvent";//结算请求数据

    public const string ChangeViewCallbackEvent = "ChangeViewCallbackEvent";

    public const string OnApplicationPaused = "OnApplicationPaused";
    #region 功能模块事件

    //ui
    public const string OpenUI = "OpenUI";
    public const string CloseUI = "CloseUI";
    
    //MainHeaderUI & Tabs
    public const string UpdateHeaderMode = "UpdateHeaderMode";
    public const string AddGrowthExp = "AddGrowthExp";
    public const string ShowGrowthExp = "ShowGrowthExp";
    public const string RefreshAutoHelpEvent = "RefreshAutoHelpEvent";
    public const string OnIncentiveDataRefreshed = "OnIncentiveDataRefreshed";
    public const string MainTabChange = "MainTabChange";
    public const string MainTabIndexBack = "MainTabIndexBack";//tab 移动过小 又回去了
    public const string OnShowStaminaPopupEvent = "OnShowStaminaPopupEvent";

    //Task
    public const string TaskClosePanel = "TaskClosePanel";
    
    //newChat
    public const string PlayComboAni = "PlayComboAni";
    public const string ExitChatNew = "ExitChatNew";//返回类逻辑，含对话中途返回
    public const string StoryTaskInfoUpdate = "StoryTaskInfoUpdate";// 创建世界剧情对话任务 -- 数据保存完毕
    public const string NewChatAddCell = "NewChatAddCell";//界面添加cell
    public const string PlayTweenShowCell = "PlayTweenShowCell";
    public const string DismissWithAnimation = "DismissWithAnimation";
    public const string AddLoadingCell = "AddLoadingCell";
    public const string RemoveLoadingCell = "RemoveLoadingCell";
    public const string ShowTranslateInfo = "ShowTranslateInfo";
    public const string StopStreamAudioTTs = "StopStreamAudioTTs";
    public const string HitKnowledgeUpdate = "HitKnowledgeUpdate"; //命中知识点
    public const string UpdateSuggestionState = "UpdateSuggestionState"; //建议
    public const string ShowAvatarNameAndProfession = "ShowAvatarNameAndProfession"; //显示名称职业
    public const string ShowChatPanelTopic = "ShowChatPanelTopic";//显示自由模式话题
    public const string AddBlankCell = "AddBlankCell";
    public const string RemoveBlankCell = "RemoveBlankCell";
    public const string ChangeAUACompleteStateEvent = "ChangeAUACompleteStateEvent";
    public const string ClickAUAUIContinueEvent = "ClickAUAUIContinueEvent";
    public const string ShowAUATitleEvent = "ShowAUATitleEvent";
    public const string AvatarAudioCompleteEvent = "AvatarAudioCompleteEvent";
    public const string VideoTaskFinished = "VideoTaskFinished";
    
    //mainui
    public const string MainHeaderVisible = "MainHeaderVisible";
    public const string MultiTabShow = "MultiTabShow";
    public const string MultiTabHide = "MultiTabHide";
    public const string InitMultiTabFramework = "InitMultiTabFramework";

    //Story Net
    public const string StreamUserDialogInfoUpdate = "StreamUserDialogInfoUpdate";// 剧情对话user内容更新
    public const string StreamAvatarDialogInfoUpdate = "StreamAvatarDialogInfoUpdate";// 剧情对话avatar内容更新
    public const string StroyStreamAvatarDialogTranslate = "StroyStreamAvatarDialogTranslate";// 剧情对话avatar翻译更新
    public const string StroyStreamAvatarAudioNtf = "StroyStreamAvatarAudioNtf";// 剧情对话avatar语音
    public const string StreamAvatarAdviceNtf = "StreamAvatarAdviceNtf";// 剧情对话avatar advice更新
    public const string StreamAvatarExampleNtf = "StreamAvatarExampleNtf";// 剧情对话avatar example更新
    //Story DO
    public const string StoryChangeState = "StoryChangeState";
    public const string PauseStoryTaskBubble = "PauseStoryTaskBubble";
    public const string ResumeStoryTaskBubble = "ResumeStoryTaskBubble";
    public const string StoryTwoWayGoOn = "StoryTwoWayGoOn"; //继续全双工
    public const string SetIfCanMicrophone = "SetIfCanMicrophone"; //是否开启录音
    public const string StoryAudioStateChange = "StoryAudioStateChange"; //声音播放状态变化
    public const string StoryOutCheck = "StoryOutCheck"; //if can exit chat
    public const string StoryNewFace = "StoryNewFace"; //剧情新首页
    public const string StoryNewFaceOut = "StoryNewFaceOut"; 
    public const string StoryNewFaceEnterChat = "StoryNewFaceEnterChat"; 
    public const string StoryPlayerTextStepStart = "StoryPlayerTextStepStart"; // 玩家文字打字机播放开始
    public const string StoryPlayerTextStepOver = "StoryPlayerTextStepOver"; // 玩家文字打字机播放结束
    public const string StoryEndAni = "StoryEndAni"; // stop effect
    public const string StoryAvatarTalkToAvatarOut = "StoryAvatarTalkToAvatarOut"; 
    public const string StoryTurnToAvatar = "StoryTurnToAvatar";
    public const string ShowQuestionTip = "ShowQuestionTip";
    
    
    //little
    public const string LittleStopRunAni = "LittleStopRunAni";
    public const string LittlePlayRunAni = "LittlePlayRunAni";
    
    //Record
    public const string RecordShowReadly = "RecordShowReadly";
    public const string NewRecordShowReadly = "NewRecordShowReadly";
    public const string NewWorldAudioStart = "StoryAudioStop"; //音频开始
    public const string NewWorldAudioStop = "NewWorldAudioStop"; //音频停止
    public const string EndChatAndEnterProgress = "EndChatAndEnterProgress"; //主动结束对话，且进入结算
    
    //Procedure
    public const string procedure_main_pause = "procedure_main_pause";
    public const string procedure_main_break = "procedure_main_break";
    public const string procedure_main_start = "procedure_main_start";
    public const string procedure_all_over = "procedure_all_over";
    public const string procedure_chatstory_look_avatar_talk_to_avatar = "procedure_chatstory_look_avatar_talk_to_avatar";
    //Procedure chat
    public const string procedure_social = "procedure_social";
    public const string procedure_do_scaffoid_dram = "procedure_do_scaffoid_dram"; //执行队列中的 scaffoid 
    //Procedure rolePlay
    public const string procedure_roleplay_chat_scene_desc = "procedure_roleplay_chat_scene_desc";
    public const string procedure_roleplay_avatar = "procedure_roleplay_avatar";
    public const string procedure_roleplay_audio_play = "procedure_roleplay_audio_play";
    public const string procedure_roleplay_player = "procedure_roleplay_player";
    
    //Procedure autor
    public const string procedure_tutor_chat_image = "procedure_tutor_chat_image";
    public const string procedure_tutor_avatar = "procedure_tutor_avatar";
    public const string procedure_tutor_audio_play = "procedure_tutor_audio_play";
    public const string procedure_tutor_player = "procedure_tutor_player";
    
    //Procedure Explore
    public const string procedure_explore_entity_enter_audio_play = "procedure_explore_entity_enter_audio_play";
    public const string procedure_explore_entity_avatar_first_cell_show = "procedure_explore_entity_avatar_cell_show";
    public const string procedure_explore_entity_avatar_cell_show = "procedure_explore_entity_avatar_show";
    public const string procedure_explore_entity_avatar_audio_play = "procedure_explore_entity_avatar_audio_play";
    public const string procedure_explore_entity_avatar_translate_show = "procedure_explore_entity_avatar_translate_show";
    public const string procedure_explore_entity_player_cell_show = "procedure_explore_entity_player_cell_show";
    public const string procedure_explore_entity_scaffold_show = "procedure_explore_entity_scaffold_show";
    public const string procedure_explore_entity_recordui_show = "procedure_explore_entity_recordui_show";
    public const string procedure_explore_entity_do_recordui_show_out_other = "procedure_explore_entity_do_recordui_show_out_other"; //执行这个 抛弃其他
    public const string procedure_explore_first_avatar_show_out_other = "procedure_explore_first_avatar_show_out_other"; //执行这个 抛弃其他
    public const string procedure_explore_chat_end = "procedure_explore_chat_end"; 
    public const string procedure_explore_entity_intro_audio_play = "procedure_explore_entity_intro_audio_play";
    public const string procedure_explore_entity_intro_over = "procedure_explore_entity_intro_over";
    
    //Procedure Onboarding
    public const string procedure_onboarding_avatar_audio_play = "procedure_onboarding_avatar_audio_play"; 
    public const string procedure_onboarding_chat_end = "procedure_onboarding_chat_end"; 
    
    //LearnPath
    public const string LearnPathReShowEvent = "LearnPathReShowEvent";//中途退出或结算退出重新打开学习路径
    public const string DrillHubReShowEvent = "DrillHubReShowEvent";//中途退出重新打开训练中心

	public const string DrillHubRefreshMistakeEvent = "DrillHubRefreshMistakeEvent";
    public const string DrillHubRefreshLearnEvent = "DrillHubRefreshLearnEvent";
    
    public const string AfterDealResultData = "AfterDealResultData";    
    
    //rolePlay2.0
    public const string ChatRolePlaySceneDescAudioPlayOver = "ChatRolePlaySceneDescAudioPlayOver";
    public const string ChatRolePlayAvatarOrNarrationAudioPlayOver = "ChatRolePlayAvatarOrNarrationAudioPlayOver";
    public const string ChatAudioPlayStart = "ChatAudioPlayStart";
    public const string ShowRecordUI = "ShowRecordUI";
    public const string HideRecordUI = "HideRecordUI";
    public const string BeginStepStateChange = "BeginStepStateChange";
    public const string SetScaffoldShowState = "SetScaffoldShowState";//drama模式脚手架状态
    public const string SetScaffoldCanShow = "SetScaffoldCanShow";//normal脚手架状态 avatar说完没
    public const string CheckAddScaffold = "CheckAddScaffold";//检查添加脚手架
    public const string UpdatePlayerCellState = "UpdatePlayerCellState";//player回复的好不好
    
    //chat public
    //avatar 的任务 结束
    public const string ChatPlayerCellDoOver = "ChatPlayerCellDoOver";
    public const string ChatAvatarCellDoOver = "ChatAvatarCellDoOver";
    
    //流式tts 
    public const string TtsStreamAudioUpdate = "ttsStreamAudioUpdate";
    public const string TtsStreamAudioStart = "ttsStreamAudioStart";
    //mp3 TTs
    public const string TtsMp3AudioLast = "TtsMp3AudioLast";

    // FragmentPractice
    public const string FragmentPracticeClickAnswer = "FragmentPracticeClickAnswer";
    
    //settle
    public const string RefreshSettleBoxEvent = "RefreshSettleBoxEvent";//free box

    //contact
    public const string RefreshContactDataEvent = "RefreshContactDataEvent";
    public const string RefreshContactOfflineEvent = "RefreshContactOfflineEvent";
    
    
    //Explore
    
    public const string ExploreEnter = "ExploreEnter";
    public const string ExploreStepStateChange = "ExploreStepStateChange";
    public const string ExploreChangeOperationType = "ExploreChangeOperationType";
    public const string ExploreInfoAsk = "ExploreInfoAsk";
    public const string RefreshExploreData = "RefreshExploreData";
    public const string RefreshExploreDataAndEnter = "RefreshExploreDataAndEnter";
    public const string ExploreNetConect = "ExploreNetConect";
    public const string RefresUINet = "RefresUINet";
    public const string ExploreNetExit = "ExploreNetExit";
    
    public const string ExploreStreamUserDialogInfoUpdate = "ExploreStreamUserDialogInfoUpdate";// user内容更新
    public const string ExploreStroyStreamAvatarDialogTranslate = "ExploreStroyStreamAvatarDialogTranslate";// 剧情对话avatar翻译更新
    public const string ExploreAvatarTxt= "ExploreAvatarTxt";// avatar文本内容

    public const string ExploreScaffoldTTs = "ExploreScaffoldTTs";// 脚手架示例TTS
    public const string ExploreDialogDownAwardMsg = "ExploreDialogDownAwardMsg";// 对话下行反馈结果
    public const string ExploreAvatarAudioRidey = "ExploreAvatarAudioRidey";// avatar 语音准备好了
    public const string ExplorevatarDialogTxtUpdate = "ExplorevatarDialogTxtUpdate";// 剧情对话avatar内容更新
    public const string ExploreAvatarAudioPlay = "ExploreAvatarAudioPlay";// avatar 语音播放
    
    public const string ExploreEnterAudioPlay = "ExploreChatAudioPlayStart";
    public const string ExploreChatFristAvatarShow = "ExploreChatFristAvatarShow";
    public const string ExploreChatAvatarShow = "ExploreChatAvatarShow";
    public const string ExploreChatFirstAvatarTranslateShow = "ExploreChatFirstAvatarTranslateShow";
    public const string ExploreAvatarAudioPlayOver = "ExploreAvatarAudioPlayOver";
    
    public const string ExploreChatPlayerShow = "ExploreChatPlayerShow";
    public const string ExploreChatPlayerShowOver = "ExploreChatPlayerShowOver";
    public const string ExploreClearScreen = "ExploreClearScreen"; //清屏
    public const string ExploreShowRecordUI = "ExploreShowRecordUI";
    public const string ExploreHideRecordUI = "ExploreHideRecordUI";
    public const string ExploreShowScaffold = "ExploreShowScaffold";
    public const string ExploreShowActive = "ExploreShowActive";
    public const string ExploreClickScaffoldBtn = "ExploreClickScaffoldBtn";
    public const string ExploreScaffoldAudio = "ExploreScaffoldAudio";
    public const string ExploreSoundStop = "ExploreSoundStop";
    public const string ExploreStopCellAudioEffect = "ExploreStopCellAudioEffect";
    public const string ExploreBGMopen = "ExploreBGMopen";
    public const string ExploreNoNetworkTipShow = "ExploreNoNetworkTipShow";
    public const string ExploreDownMsgForServerBasic = "ExploreDownMsgForServerBasic";  //处理内部GRPC 重连
    public const string ExploreProgressChange = "ExploreProgressChange";  //Mission剧情对话下行 - 任务步骤进度变更
    public const string ExploreTaskChange = "ExploreTaskChange";  //Mission剧情对话下行 - 任务变更
    public const string ExploreSettlement = "ExploreSettlement";  //结算数据
    
    
    public const string ExploreGRPCFail = "ExploreGRPCFail";
    
    //Mission
    public const string ExploreJumpMisssion = "ExploreJumpMisssion";
    public const string ExploreLookAtStory = "ExploreLookAtStory";
    public const string ExploreLookAtNextStory = "ExploreLookAtNextStory";
    public const string ExploreEndDataRefresh = "ExploreEndDataRefresh";
    public const string ExploreHideRecordFinishUI = "ExploreHideRecordFinishUI";
    public const string ExploreEnterIntroState = "ExploreEnterIntroState";
    public const string ExploreEnterEntityState = "ExploreEnterEntityState";
    public const string ExploreIntroAudioPlay = "ExploreIntroAudioPlay";// avatar 语音播放
    public const string ExploreTitleTooolVisible = "ExploreTitleTooolVisible";
    

    //跳转商店
    public const string OpenShop = "OpenShop";
    //会员状态刷新
    public const string ShopInfoUpdate = "ShopInfoUpdate";
    
    //homepage
    public const string ShowHomepageBanner = "ShowHomepageBanner";
    public const string HideHomepageBanner = "HideHomepageBanner";
    public const string ShowHomepageTimeBanner = "ShowHomepageTimeBanner";
    public const string HideHomepageTimeBanner = "HideHomepageTimeBanner";
    public const string OnGetIncentiveData = "OnGetIncentiveData";
    
    //guide
    public const string OnFinishCurrentSequence = "OnFinishCurrentSequence";
    //chat
    public const string UpdateScaffoldCellStateEvent = "UpdateScaffoldCellStateEvent";

    //chat start
    public const string UpdateHistoryTransEvent = "UpdateHistoryTransEvent";
    public const string RefreshHistoryListEvent = "RefreshHistoryListEvent";
    
    //videoTask
    public const string DoVideoPauseEvent = "DoVideoPauseEvent";
    public const string DoVideoPlayEvent = "DoVideoPlayEvent";

    //MainPath
    public const string UpdateMainPathEvent = "UpdateMainPathEvent";
    
    //收到公共结算返回
    public const string SC_GetCourseSettlementAck = "SC_GetCourseSettlementAck";

    //warmup类题目
    public const string Start2PlayInteractiveTTS = "Start2PlayInteractiveTTS";//播自己 or 播avatar
    public const string End2PlayInteractiveTTS = "End2PlayInteractiveTTS";//
    
    //P to P
    public const string P2P_UserDialog = "P2P_UserDialog";
    public const string P2P_UserDialog2 = "P2P_UserDialog2";

    public const string P2P_OtherDialog = "P2P_OtherDialog";
    public const string P2P_OtherDialog2 = "P2P_OtherDialog2";
    
    
    public const string P2P_OtherDialogTranslate = "P2P_OtherDialogTranslate";
    public const string P2P_DialogExample = "P2P_DialogExample";
    public const string P2P_DialogExampleTranslate = "P2P_DialogExampleTranslate";

    #endregion
    public const string OnBoardShopUIOver = "OnBoardShopUIOver";
    public const string RecordAnswerRefreshMatchResultEvent = "RecordAnswerRefreshMatchResultEvent";

    
    //
    public const string NSUser_RecvChatMsg = "NSUser_RecvtChatMsg";
    public const string NSUser_AcceptChatMsg = "NSUser_AcceptChatMsg";
    public const string NSUser_CancelChatMsg = "NSUser_CancelChatMsg";//此处的cancel实际是因为对侧超时
    public const string NSUser_RejectChatMsg = "NSUser_RejectChatMsg";//待定未使用
    
    
    //onBoarding
    public const string OnboardFlow_selfTxt = "OnboardFlow_selfTxt";
    public const string OnboardFlow_avatarTxt = "OnboardFlow_avatarTxt";
    public const string OnboardFlow_avatarTranslate = "OnboardFlow_avatarTranslate";
    public const string OnboardFlow_avatarTTs = "OnboardFlow_avatarTTs";
    public const string OnboardFlow_settlement = "OnboardFlow_settlement";
    public const string OnboardFlow_settlement_ui = "OnboardFlow_settlement_ui";
    public const string OnboardFlow_settlement_skip = "OnboardFlow_settlement_skip";
    public const string OnboardFlow_preload = "OnboardFlow_preload";
    public const string Onboard_AskPreLoadData = "Onboard_AskPreLoadData";
    public const string OnboardUI_ShowBack = "OnboardUI_ShowBack";
    
    public const string OnboardFlow_PlayAudio = "OnboardFlow_PlayAudio";
    public const string OnboardFlow_AudioOver = "OnboardFlow_AudioOver";
    
    public const string OnboardFlow_ShowLoading = "OnboardFlow_ShowLoading";
    public const string OnboardFlow_CloseLoading = "OnboardFlow_CloseLoading";
    
    public const string OnboardFlow_bubbleSpine_visible = "OnboardFlow_bubbleSpine_visible";
    public const string OnboardFlow_GrpcFail = "OnboardFlow_GrpcFail";
    
    public const string RefreshMainBtns = "RefreshMainBtns";

    public const string MainCtrlChangeState = "MainCtrlChangeState";

    public const string SettlementFinishEvent = "SettlementFinishEvent";
    
    public const string MainPath3DSceneChange = "MainPath3DSceneChange";
    public const string MainPath3DSceneLightUp = "MainPath3DSceneLightUp";
    public const string MainPath3DLeavePath = "MainPath3DLeavePath";
    public const string MainPath3DBackPath = "MainPath3DBackPath";
    public const string MainPath3DCompleteLevel = "MainPath3DCompleteLevel"; //首次 打卡
    public const string MainPath3DNodeComplete = "MainPath3DNodeComplete";

    public const string MainHeadRefreshEvent = "MainHeadRefreshEvent";
    public const string MainPathHideTipsEvent = "MainPathHideTipsEvent";
    
    #region
    public const string VoiceChat_StartPreMatch = "VoiceChat_StartPreMatch";                    //匹配中
    public const string VoiceChat_StartPreMatchConfirming = "VoiceChat_StartPreMatchConfirming";//最终的连接中
    public const string VoiceChat_CancelPreMatchComplete = "VoiceChat_CancelPreMatchComplete";

    public const string VoiceChat_JoinChatting = "VoiceChat_JoinChatting";

    public const string VoiceChat_RecvOtherEndExitChannel = "VoiceChat_RecvOtherEndExitChannel";
    public const string VoiceChat_ExitChannel = "VoiceChat_ExitChannel";
    #endregion
}

