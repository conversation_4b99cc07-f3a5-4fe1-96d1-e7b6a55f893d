/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class ClozeOptionBtn : GButton
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "ClozeOptionBtn";
        public static string url => "ui://cmoz5osjt0ae2a";

        public Controller ctrlColor;
        public GGraph onScreenBg;
        public GGraph normalBg;
        public GGraph greenBg;
        public GGraph redBg;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(ClozeOptionBtn));
        }

        public override void ConstructFromXML(XML xml)
        {
            ctrlColor = GetControllerAt(0);
            onScreenBg = GetChildAt(0) as GGraph;
            normalBg = GetChildAt(1) as GGraph;
            greenBg = GetChildAt(2) as GGraph;
            redBg = GetChildAt(3) as GGraph;
        }
        public override void Dispose()
        {
            ctrlColor = null;
            onScreenBg = null;
            normalBg = null;
            greenBg = null;
            redBg = null;

            base.Dispose();
        }
    }
}