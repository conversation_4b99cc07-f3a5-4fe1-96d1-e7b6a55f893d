﻿using System.Collections.Generic;
using FairyGUI;
using UnityEngine;
using YooAsset;

namespace UIBind.ExploreFriends
{
    public partial class ExploreFriendsDrawCardsResultPanelUI : BaseUI<ExploreFriendsDrawCardsResultPanel>
    {
        public ExploreFriendsDrawCardsResultPanelUI(string name) : base(name)
        {
        }

        public override string uiLayer => UILayerConsts.Top;

        protected override bool isFullScreen => true;
        private long tmpAvatarId;
        private long tmpRecordId;
        private int tmpSlotIndex;
        private ExploreFriendCfg friendCfg;

        protected override void OnInit(GComponent uiCom)
        {
            base.OnInit(uiCom);

            // 注册按钮点击事件
            AddUIEvent(ui.NextBtn.com.onClick, OnNextBtnClick);
            AddUIEvent(ui.ReDrawBtn.onClick, OnReDrawBtnClick);
        }

        protected override void OnShow()
        {
            base.OnShow();

            tmpAvatarId = GameEntry.ExFriendC.Model.DrawAvatarId;
            tmpRecordId = GameEntry.ExFriendC.Model.DrawRecordId;
            tmpSlotIndex = GameEntry.ExFriendC.Model.DrawSlotIndex;
            if (tmpAvatarId <= 0 || tmpRecordId <= 0 || tmpSlotIndex <= 0)
            {
                Hide();
            }
            
            ui.NextBtn.SetTxt("ui_explore_friends_draw_result_become_friend_button_text");
            ui.ReDrawTxt.text = I18N.inst.MoStr("ui_explore_friends_draw_result_redraw_button_text");
            
            friendCfg = GameEntry.ExFriendC.Model.GetFriendCfgByID(tmpAvatarId);
            if (friendCfg == null)
            {
                Hide();
            }

            RefreshUI();
            RefreshBtn();
        }

        protected override void OnHide()
        {
            base.OnHide();

        }
        
        private void OnNextBtnClick()
        {
            ui.NextBtn.SetLoadingStatus(true);

            GameEntry.ExFriendC.SendBecomeFriendReq(tmpAvatarId,tmpRecordId,tmpSlotIndex);
            
            //todo mock
            //GameEntry.ExFriendC.MockOnBecomeFriendResp();
        }
        
        private void OnReDrawBtnClick()
        {
            ui.NextBtn.SetLoadingStatus(true);

            GameEntry.ExFriendC.SendDrawNewFriendReq(ExploreFriendsController.DrawType.switchDraw, tmpSlotIndex);
            
            //todo mock
            GameEntry.ExFriendC.MockOnDrawNewFriendResp();
        }
        
        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void OnCloseBtnClick()
        {
            Hide();
        }

        /// <summary>
        /// item选中回调
        /// </summary>
        /// <param name="selectedIndex">选中的item索引</param>
        private void RefreshUI()
        {
            ui.Content1Txt.text = string.Format(I18N.inst.MoStr("ui_explore_friends_draw_result_title_text") , friendCfg.NameKey);
            FriendsAvatarLoaderHelper.GenerateRTAsync(friendCfg.AvatarModleId, ui.avatarLoader, delegate(bool b) { }, 0.5f);

            ExploreFriendsUIHelper.SetIntroduceNode(ui.Introduce,friendCfg);
            
            // Color color;
            // if (ColorUtility.TryParseHtmlString(friendCfg.BGColor, out color))
            // {
            //     ui.bgImg.color = color;
            // }
            ExploreFriendsUIHelper.SetBGImg(friendCfg , ui.bgLoader , true);
        }

        private void RefreshBtn()
        {
            ui.NextBtn.SetBtnMode(FriendCommonBtn.BtnMode.DrawResult);
            ui.NextBtn.SetDiamondStatus(true);
            ui.NextBtn.SetLoadingStatus(false);
            ui.NextBtn.SetTxt("todo交友");
        }

        protected override string[] ListNotificationInterests()
        {
            return new[]
            {
                NotifyConsts.ExploreFriendsPlayTimeLineEnd,
                NotifyConsts.ExploreFriendsFriendInfoUpdate
            };
        }
        
        protected override void HandleNotification(string name, object body)
        {
            switch (name)
            {
                case NotifyConsts.ExploreFriendsPlayTimeLineEnd:
                    //todo 播个语音
                    break;
                case NotifyConsts.ExploreFriendsFriendInfoUpdate:
                    Hide();
                    UIManager.instance.GetUI<ExploreFriendsBagPanelUI>(UIConsts.ExploreFriendsBagPanel).Show();
                    break;
            }
        }
        
        
    }
}