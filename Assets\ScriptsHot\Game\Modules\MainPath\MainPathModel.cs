﻿using System;
using System.Collections.Generic;
using System.Linq;
using Msg.basic;
using Msg.course;

namespace ScriptsHot.Game.Modules.MainPath
{
    public class MainPathModel : BaseModel
    {
        public MainPathModel() : base(ModelConsts.MainPath) { }
        
        public long CourseId { get; private set; }
        /// <summary>
        /// 课程信息 --书架页显示
        /// </summary>
        public PB_CourseData BookShelfCourseData { get; private set; }
        
        /// <summary>
        /// 简要数据 用于再路径页显示下一个section的数据
        /// </summary>
        public PB_SectionData NextSectionSimpleData { get; private set; }

        //section id 
        public Dictionary<long, MainPathSectionData> DicSectionDataCache { get; private set; } = new();

        /// <summary>
        /// 当前Running Section
        /// </summary>
        public MainPathSectionData CurSectionData { get; private set; }
        
        /// <summary>
        /// 当前Running Unit
        /// </summary>
        public MainPathUnitData CurUnitData { get; private set; }

        /// <summary>
        /// 当前Running level数据
        /// </summary>
        public MainPathLevelData CurLevelData { get; private set; }
        
        /// <summary>
        /// 当前场景使用的unit data
        /// </summary>
        public PB_UnitData CurSceneUnitData { get; private set; }

        public struct FocusNodeData
        {
            public bool NeedChange;//是否为复习 如果复习不重新focus
            public MainPathSectionData SectionData;//当前所在section
            public MainPathUnitData UnitData;//当前所在Unit
            public MainPathLevelData LevelData;//当前所在Level
        }

        /// <summary>
        /// 进关前保存的数据 用于定位 没有此数据用running定位 有此数据用此数据定位
        /// </summary>
        public FocusNodeData FocusData { get; private set; }
        
        public PB_LevelData RunningLevelData { get; private set; } //正在进行中的level
        public PB_LevelData LastRunningLevelData { get; private set; } //last进行中的level
        
        public long FocusSectionId { get; private set; }
        
        public bool LastIsLight { get; private set; } //上次是否是亮的

        public void SetLastIsLight(bool isLight)
        {
            LastIsLight = isLight;
        }
        
        public void SetCourseId(long courseId)
        {
            CourseId = courseId;
        }
        
        //BookShelfCourseData
        public void SetBookShelfCourseData(PB_CourseData data)
        {
            BookShelfCourseData = data;
        }
        
        public void SetCurSceneUnitData(PB_UnitData data)
        {
            CurSceneUnitData = data;
        }

        public void SetModelData(PB_SectionData sectionData, bool init = false)
        {
            if (DicSectionDataCache.TryGetValue(sectionData.section_id, out var section))
                section.UpdateData(sectionData);
            else
            {
                section = new MainPathSectionData(sectionData);
                DicSectionDataCache[section.ServerSectionData.section_id] = section;
            }

            if (section.ServerSectionData.status == PB_ProgressStatusEnum.PSRunning)
                RefreshRunningData(section);
            if (init)
            {
                LastRunningLevelData = RunningLevelData; //首次登录保证有值
                LastIsLight = GameEntry.SignC.SignModel.signSummary.finish_checkin;
            }
            if (init && CurSectionData == null) //说明初始给的是已完成的section
            {
                CurSectionData = section;
                CurUnitData = CurSectionData.UnitDataList[0];
                CurLevelData = CurUnitData.LevelDataList[0];
                
                RunningLevelData = CurLevelData.ServerLevelData;
                LastRunningLevelData = RunningLevelData;
            }
        }
        
        public void SetNextSectionSimpleData(PB_SectionData sectionData)
        {
            NextSectionSimpleData = sectionData;
        }

        /// <summary>
        /// 进关前在设置一次 结算也要设置一次 是review need change 是false 不是的话就不change
        /// </summary>
        public void SetFocusData(MainPathSectionData sectionData, MainPathUnitData unitData,
            MainPathLevelData levelData, bool needChange = false)
        {
            var focusData = FocusData;
            focusData.SectionData = sectionData;
            focusData.UnitData = unitData;
            focusData.LevelData = levelData;
            focusData.NeedChange = needChange;
            FocusData = focusData;
            SetFocusSectionId(sectionData.ServerSectionData.section_id);
        }

        /// <summary>
        /// 当前section id 结算用 获取数据 区分跳关 进关前赋值
        /// </summary>
        /// <param name="id"></param>
        public void SetFocusSectionId(long id)
        {
            FocusSectionId = id;
        }

        public void UpdateLastRunning()
        {
            LastRunningLevelData = RunningLevelData;
        }

        private void RefreshRunningData(MainPathSectionData section)
        {
            CurSectionData = section;
            MainPathUnitData runUnit = section.UnitDataList.FirstOrDefault(u =>
                u.ServerUnitData.status == PB_ProgressStatusEnum.PSRunning);
            MainPathLevelData runLevel = runUnit?.LevelDataList.FirstOrDefault(l =>
                l.ServerLevelData.status == PB_ProgressStatusEnum.PSRunning);
            CurUnitData = runUnit;
            CurLevelData = runLevel ?? section.UnitDataList[0].LevelDataList[0];
            
            LastRunningLevelData = RunningLevelData;
            RunningLevelData = CurLevelData.ServerLevelData;
        }

        public void ClearMainPathData()
        {
            foreach (var sectionData in DicSectionDataCache.Values)
                sectionData.Dispose();
            
            DicSectionDataCache.Clear();
        }
    }
}