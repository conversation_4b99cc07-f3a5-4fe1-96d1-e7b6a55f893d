﻿using System;
using FairyGUI;
using Game.Modules.FragmentPractice;
using Modules.DataDot;
using Msg.basic;
using Msg.incentive;
using ScriptsHot.Game.Modules.ExplorePush;
using ScriptsHot.Game.Modules.FragmentPractice;
using UnityEngine;

namespace UIBind.FragmentPractice
{
    public partial class CompHead
    {

        private static FragmentPracticeModel FragModel =>
            ModelManager.instance.GetModel<FragmentPracticeModel>(ModelConsts.FragmentPractice);
        private static FragmentPracticeController FragController =>
            ControllerManager.instance.GetController<FragmentPracticeController>(ModelConsts.FragmentPractice);
        
        public event Action OnExit;
        private FragmentPracticeUI.FragmentPracticeUIArgs _openArgs;
        private FragmentPracticeUI _panel;
        
        private TimerManager.Handler disappear;
        private int _lastQuestionNum;
        
        public void Init(FragmentPracticeUI panel)
        {
            tfCombo.SetKeyArgs("ui_fragment_page_5row", 5);
            _panel = panel;

            shieldList.itemRenderer = OnRendererShield;
            panel.AddUIEvent(BtnClose.onClick, OnClickClose);
            
            BarPractice.Init(100);
        }

        public void OnHide()
        {
            shieldList.numItems = 0;
            shieldList.ResizeToFit();
        }
        
        public void FirstShowBar()
        {
            _lastQuestionNum = 0;
            if (FragModel.JumpMode)
            {
                DotAppearSkipBar dot = new DotAppearSkipBar();
                dot.shield_count = FragModel.JumpTaskShieldTotalNum;
                DataDotMgr.Collect(dot);
                
                state.selectedPage = "jump";
                shieldList.numItems = FragModel.JumpTaskShieldTotalNum;
                shieldList.ResizeToFit();
            }
            else
            {
                state.selectedPage = "normal";
            }
            RefreshProgress();
        }

        public void RefreshProgress()
        {
            float final;
            if (FragModel.JumpMode)
                final = FragModel.InitProgress +
                        1.0f * FragModel.CurQuestionNum / FragModel.CurQuestions.Count *
                        (100 - FragModel.InitProgress);
            else
                final = FragModel.InitProgress +
                        (1f * FragModel.CorrectNum / FragModel.CurQuestions.Count +
                         FragModel.ErrorQuestionCount / (2f * FragModel.CurQuestions.Count)) *
                        (100 - FragModel.InitProgress);

            var showSpine = FragModel.CurQuestionNum != 0 && _lastQuestionNum != FragModel.CurQuestionNum &&
                          QuestionEventManager.Instance.IsRightAnswer == true;
            BarPractice.SetProgress(final, showSpine, ()=>_lastQuestionNum = FragModel.CurQuestionNum);
        }

        public void UpdateShield()
        {
            if (!FragModel.JumpMode) return;
            
            int brokenNum = 0;
            for (int i = FragModel.JumpTaskShieldTotalNum - 1; i >= 0; i--)
            {
                GObject obj = shieldList.GetChildAt(i);
                if (brokenNum < FragModel.JumpTaskShieldTotalNum - FragModel.JumpTaskShieldCurNum)
                    obj.asCom.GetController("state").selectedIndex = 1;
                brokenNum++;
            }
        }


        public void InRowState(int count)
        {
            tfCombo.visible = count >= 2;
            tfCombo.SetKeyArgs("ui_fragment_page_5row", count);
            combo.Play();

            disappear?.Clear();
            disappear = new ((c) =>
            {
                tfCombo.visible = false;
                disappear?.Clear();
            }, 2000);
            
            
        }
        
        public void OnClickClose()
        {            
            DotPracticeManager.Instance.Collect(new DataDot_Exit());
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Warning);
            
            if (_openArgs.pageState == FragmentPracticeUI.PageState.resign)
            {
                var fragmentPracticeConfirmUI = _panel.GetUI<FragmentPracticeConfirmUI>(UIConsts.FragmentPracticeConfirmUI);
                fragmentPracticeConfirmUI.SetAction(Stay, Exit);
                fragmentPracticeConfirmUI.Show();
            }
            else
            {                
                _panel.GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N("ui_common_retention_tips", Stay,
                    Exit, 3, "common_keep_learning", "common_quit", false, 1);
            }
        }
        
        public void SetOpenArgs(FragmentPracticeUI.FragmentPracticeUIArgs openArgs)
        {
            _openArgs = openArgs;
        }

        private void OnRendererShield(int index, GObject obj)
        {
            GComponent comp = obj as GComponent;
            CompShieldItem item = new CompShieldItem();
            item.Construct(comp);
            item.state.selectedIndex = 0;
            item.reset.Play();
        }
        
        #region 离开弹框，是不是还应该移回panel？
        private void Stay()
        {
            if (FragModel.CurQuestion == null)
            {
                return;
            }
            // 留下来！
            DataDotClick_FragmentPractice_quit_cancel dot0 = new DataDotClick_FragmentPractice_quit_cancel();
            dot0.List_id = FragController.UnitId;
            dot0.Question_id = FragModel.CurQuestion.QuestionId;
            dot0.Question_index = FragController.GetCurQuestionIndex();
            dot0.Question_num = FragController.GetQuestionNum();
            dot0.Exercises_type = FragModel.Round == 0 ? "Planned" : "Corrected";
            DataDotMgr.Collect(dot0);
        }

        //中途退出
        public async void Exit()
        {
            if (FragModel.CurQuestion == null)
            {
                return;
            }
            // 确认退出
            DataDotClick_FragmentPractice_quit_confirm dot1 = new DataDotClick_FragmentPractice_quit_confirm();
            dot1.List_id = FragController.UnitId;
            dot1.Question_id = FragModel.CurQuestion.QuestionId;
            dot1.Question_index = FragController.GetCurQuestionIndex();
            dot1.Question_num = FragController.GetQuestionNum();
            dot1.Exercises_type = FragModel.Round == 0 ? "Planned" : "Corrected";
            DataDotMgr.Collect(dot1);

            if (_openArgs.pageState == FragmentPracticeUI.PageState.resign)
            {
                await MsgManager.instance.SendAsyncMsg<SC_RefuseRecheckinAck>(new CS_RefuseRecheckinReq());
                await MsgManager.instance.SendAsyncMsg<SC_GetCheckinSummaryAck>(new CS_GetCheckinSummaryReq());
                NetMsgProxy.instance.SetCacheDataInvalid<SC_GetUserCheckinPortalDataAck>();
                Notifier.instance.SendNotification(NotifyConsts.MainHeadRefreshEvent);
            }

            if (FragModel.DialogSource == PB_DialogSourceEnum.DialogSourceLearnPath)
            {
                // ModelManager.instance.GetModel<LearnPathModel>(ModelConsts.LearnPath).NeedAutoOpen = true;
                Notifier.instance.SendNotification(NotifyConsts.LearnPathReShowEvent);
            }
            
            GameEntry.LoginC.GetController<ExplorePushController>(ModelConsts.ExplorePush)
                .ReqGetHomepageGuideItem(ExplorePushModel.ExplorePushEntrance.un_finish_course_back_main_path);
            
            OnExit?.Invoke();
        }
        #endregion
    }
}