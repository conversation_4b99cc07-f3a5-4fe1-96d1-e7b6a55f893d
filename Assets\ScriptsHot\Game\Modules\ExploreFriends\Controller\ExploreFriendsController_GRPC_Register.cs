﻿
using System;
using Google.Protobuf.Collections;
using Modules.DataDot;
using Msg.explore;
using ScriptsHot.Game.Modules.Explore;
using UnityEngine;

public partial class ExploreFriendsController
{

    private void InitRegister()
    {
        //用户自己说话数据
        MsgManager.instance.RegisterCallBack<SC_FriendChatDownMsgForASR>(OnSelfTxt);
        MsgManager.instance.RegisterCallBack<SC_FriendChatDownMsgForAvatarReply>(OnAvatarTxt);
        MsgManager.instance.RegisterCallBack<SC_FriendChatDownMsgForAvatarReplyTranslate>(OnAvatarTranslate);
        MsgManager.instance.RegisterCallBack<SC_FriendChatDownMsgForAvatarReplyTTS>(OnAvatarTTS);
        
        MsgManager.instance.RegisterCallBack<SC_UserChatDownMsgForBizEvent>(OnDownMsg);
        MsgManager.instance.RegisterCallBack<SC_FriendChatDownMsgForBizEvent>(this.OnDownMsgForBizEvent);
        MsgManager.instance.RegisterCallBack<SC_GetFriendChatPreloadDataResp>(OnPreLoad);
        MsgManager.instance.RegisterCallBack<SC_FriendChatDownMsgForClosenessProgressChange>(OnClosenessChange);
        
        MsgManager.instance.RegisterCallBack<SC_DrawNewFriendResp>(OnDrawNewFriendResp);
        MsgManager.instance.RegisterCallBack<SC_BecomeFriendResp>(OnBecomeFriendResp);
        MsgManager.instance.RegisterCallBack<SC_GetClosenessLevelListResp>(OnGetClosenessLevelListResp);
        MsgManager.instance.RegisterCallBack<SC_GetFriendSlotListResp>(OnGetFriendSlotListResp);    
        
        Notifier.instance.RegisterNotification(NotifyConsts.ExploreFriendsInfoAsk,OnExploreFriendsInfoAsk);
    }

    private void UnInitRegister()
    {
        MsgManager.instance.UnRegisterCallBack<SC_FriendChatDownMsgForASR>(OnSelfTxt);
        MsgManager.instance.UnRegisterCallBack<SC_FriendChatDownMsgForAvatarReply>(OnAvatarTxt);
        MsgManager.instance.UnRegisterCallBack<SC_FriendChatDownMsgForAvatarReplyTranslate>(OnAvatarTranslate);
        MsgManager.instance.UnRegisterCallBack<SC_FriendChatDownMsgForAvatarReplyTTS>(OnAvatarTTS);
        
        MsgManager.instance.UnRegisterCallBack<SC_UserChatDownMsgForBizEvent>(OnDownMsg);
        MsgManager.instance.UnRegisterCallBack<SC_FriendChatDownMsgForBizEvent>(this.OnDownMsgForBizEvent);
        MsgManager.instance.UnRegisterCallBack<SC_GetFriendChatPreloadDataResp>(OnPreLoad);
        MsgManager.instance.UnRegisterCallBack<SC_FriendChatDownMsgForClosenessProgressChange>(OnClosenessChange);
        
        MsgManager.instance.UnRegisterCallBack<SC_DrawNewFriendResp>(OnDrawNewFriendResp);
        MsgManager.instance.UnRegisterCallBack<SC_BecomeFriendResp>(OnBecomeFriendResp);
        MsgManager.instance.UnRegisterCallBack<SC_GetClosenessLevelListResp>(OnGetClosenessLevelListResp);
        MsgManager.instance.UnRegisterCallBack<SC_GetFriendSlotListResp>(OnGetFriendSlotListResp);   
        
        Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreFriendsInfoAsk,OnExploreFriendsInfoAsk);
                
    }

    #region 界面
    private void OnGetFriendSlotListResp(SC_GetFriendSlotListResp msg)
    {
        if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
        {
            Model.UpdateFriendInfo(msg);
        }
    }

    private void OnGetClosenessLevelListResp(SC_GetClosenessLevelListResp msg)
    {
        if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
        {
            //todo @tanglei自己设计 
            if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
            {
                Model.SetClosenessLevelList(msg);
            }
            
        }
    }
    
    private void OnBecomeFriendResp(SC_BecomeFriendResp msg)
    {
        if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
        {
            var data = msg.data;
            long avatarId = data.avatarId;
            long slotId = data.slotId;
            SendUpdateFriendDataReq();
        }
    }

    private void OnDrawNewFriendResp(SC_DrawNewFriendResp msg)
    {
        if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
        {
            Model.SetDrawNewFriendResp(msg);
        }
    }
    #endregion

    #region chat

    /// <summary>
    /// 获取Friend对话预加载数据请求
    /// </summary>
    /// <param name="s"></param>
    /// <param name="body"></param>
    private void OnExploreFriendsInfoAsk(string s, object body)
    {
        CS_GetFriendChatPreloadDataReq msg = new CS_GetFriendChatPreloadDataReq();
        MsgManager.instance.SendMsg(msg);
    }
    
    /// <summary>
    ///  获取Friend对话预加载数据响应
    /// </summary>
    /// <param name="msg"></param>
    private void OnPreLoad(SC_GetFriendChatPreloadDataResp msg)
    {
        VFDebug.LogError("/获取Friend对话预加载数据响应 msg.code::" + msg.code);
        if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
        {
            Model.SetPreLoadInfo(msg);
            RepeatedField<global::Msg.explore.PB_FriendChatPreloadData> firstDataArr = msg.preloadData;
            foreach (var firstData in firstDataArr)
            {
                RepeatedField<global::Msg.explore.PB_Friend_DialogRoundData> dialogItemArr = firstData.dialogRound;
                foreach (var dialogItem in dialogItemArr)
                {
                    if (dialogItem.avatarExtRoundData != null && dialogItem.avatarExtRoundData.audio.audio.Length > 0)
                    {
                        Model.AddStreamAudio(true, dialogItem.avatarExtRoundData.audio.audio.ToByteArray(), dialogItem.avatarExtRoundData.audio.bubble_id, dialogItem.avatarExtRoundData.audio.id, dialogItem.avatarExtRoundData.audio.is_last_clip, dialogItem.avatarExtRoundData.audio,false);
                    }
                }
            }
        }
        else
        {
            VFDebug.LogError("获取Friend对话预加载数据请求失败！");
        }
    }
    
    /// <summary>
    /// 亲密度等级进度变更
    /// </summary>
    /// <param name="msg"></param>
    private void OnClosenessChange(SC_FriendChatDownMsgForClosenessProgressChange msg)
    {
        Debug.LogError("亲密度等级进度变更afterValue:" + msg.closenessProgressInfo.afterValue);
        Model.SetClosenessChange(msg);
    }

    private string _bubbleFlag = String.Empty;
    /// <summary>
    ///  对话下行 - 用户语音识别最终结果
    /// </summary>
    /// <param name="msg"></param>
    private void OnSelfTxt(SC_FriendChatDownMsgForASR msg)
    {
        VFDebug.LogError("/用户自己说话数据--返回:msg.code::" + msg.code);
    
        if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
        {
            // 清除超时定时器
            ClearResponseTimeoutTimer();
            Model.SetSelfTxtInfo(msg);
        }
        else
        {
            AlertDesc("common_toast");
            OnCancelRecored();
            return;
        }
        HideRecordButton();
        
        _bubbleFlag = TimerManager.instance.RegisterTimer((a) =>
        {
            Notifier.instance.SendNotification(NotifyConsts.ExploreFriend_bubbleSpine_visible,true);
        }, 1500);
        
    }
    
    private string _endTimeFlag = String.Empty;
    /// <summary>
    /// 对话下行 - Avatar回复
    /// </summary>
    /// <param name="msg"></param>
    private void OnAvatarTxt(SC_FriendChatDownMsgForAvatarReply msg)
    {
        TimerManager.instance.UnRegisterTimer(_bubbleFlag);
        Debug.LogError("/friend对话下行 - Avatar回复:msgId::" + msg.commonData.msgId);
        if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
        {
            // 清除超时定时器
            ClearResponseTimeoutTimer();
            Model.SetAvatarTxtInfo(msg);
          
        }
        if(!_exit)
            OnCancelRecored();
        
        Notifier.instance.SendNotification(NotifyConsts.ExploreFriend_bubbleSpine_visible,false);
    }
    /// <summary>
    /// 对话下行 - Avatar回复翻译
    /// </summary>
    /// <param name="msg"></param>
    private void OnAvatarTranslate(SC_FriendChatDownMsgForAvatarReplyTranslate msg)
    {
        Debug.Log("/对话下行 - Avatar  翻译");
        if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
        {
            Model.SetAvatarTranslate(msg);
        }
    }
    
    /// <summary>
    ///对话下行 - Avatar回复TTS
    /// </summary>
    /// <param name="msg"></param>
    private void OnAvatarTTS(SC_FriendChatDownMsgForAvatarReplyTTS msg)
    {
        Debug.Log("/对话下行 - Avatar音频:msgId::" + msg.commonData.msgId);
        if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
        {
            if (_endTimeFlag != String.Empty)
            {
                TimerManager.instance.UnRegisterTimer(_endTimeFlag);
                _endTimeFlag = String.Empty;
            }

            Model.SetAvatarTTS(msg);
            
            Model.AddStreamAudio(true, msg.audio.audio.ToByteArray(), msg.audio.bubble_id, msg.audio.id, msg.audio.is_last_clip, msg.audio,true);
        }
    }

    
    /// <summary>
    /// 用户聊天下行 - 业务事件
    /// </summary>
    /// <param name="msg"></param>
    private void OnDownMsg(SC_UserChatDownMsgForBizEvent msg)
    {
    }
    #endregion
}