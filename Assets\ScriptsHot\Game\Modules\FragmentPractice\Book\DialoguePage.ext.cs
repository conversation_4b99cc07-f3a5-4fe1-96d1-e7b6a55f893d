using System;
using System.Linq;
using UnityEngine;
using FairyGUI;
using ScriptsHot.Game.Modules.FragmentPractice;
using Game.Modules.FragmentPractice;
using System.Threading.Tasks;
using YooAsset;
using UIBind.common;


namespace UIBind.FragmentPractice
{
    public enum Mode
    {
        Normal,
        Auto,  // 自动往下进行
        Fast,  // 快速到下一道题
    }

    public interface IDialogueAction
    {
        void SetAction(DialogueAction action);
    }
    public partial class DialoguePage : ExtendedComponent, IQuestionEventListener
    {
        private static FragmentPracticeModel FragModel => ModelManager.instance.GetModel<FragmentPracticeModel>(ModelConsts.FragmentPractice);
        private LocalImageLoader BgLoader => bgLoader as LocalImageLoader;

        private Mode mode = Mode.Normal; // 模式
        private DialogueAction[] actionList;
        private int currentActionIndex = 0;
        private AvatarHead[] avatars; // Array to store avatar components
        private Vector2[] stagePositions; // positions on the stage
        private Vector2[] outsidePositions; // positions outside the stage
        // private AssetHandle _handle;
        public event Action<int> OnStepChanged;

        private int steps = 1;  // 有一个在标题页

        override protected void OnConstructed()
        {
            btnContinue.com.SetKey("common_continue");
            btnContinue.com.onClick.Add(OnClickBtnContinue);

            chatList.onTouchBegin.Add(OnChatListTouchBegin);
            chatList.onTouchEnd.Add(OnChatListTouchEnd);

            QuestionEventManager.Instance.AddListener(this);
        }

        protected override void OnAddedToStage()
        {
            HideAnswerPanelFirst();
        }

        private void InitAvatars(BookModel bookData)
        {
            if (bookData.InitAvatarCount == 2)
            {
                avatars = new AvatarHead[] { avatar1, avatar2 };
                avatar1.visible = true;
                avatar2.visible = true;
            }
            else if (bookData.InitAvatarCount == 1)
            {
                avatars = new AvatarHead[] { avatar1 };
                avatar1.visible = true;
                avatar2.visible = false;
            }
            else
            {
                avatars = new AvatarHead[0];
                avatar1.visible = false;
                avatar2.visible = false;
            }
            stagePositions = new Vector2[avatars.Length];
            outsidePositions = new Vector2[avatars.Length];
            for (int i = 0; i < avatars.Length; i++)
            {
                stagePositions[i] = avatars[i].position;

                float offsetX = this.width * 1.5f * (i == 0 ? -1 : 1);
                avatars[i].position = new Vector2(stagePositions[i].x + offsetX, stagePositions[i].y);
                outsidePositions[i] = avatars[i].position;
                avatars[i].SetAvatar(null);
            }
        }

        override protected void OnWillDispose()
        {
            QuestionEventManager.Instance.RemoveListener(this);

            btnContinue.com.onClick.Remove(OnClickBtnContinue);
            OnStepChanged = null;
        }

        public void ShowBook(BookModel bookData)
        {
            if (AppConst.IsDebug)
            {
                tfDebugAudio.visible = true;
                tfDebugAudio.text = $"bookId {bookData.BookData.session_id.ToString("#### #### #### #### ###0")}";
            }
            else
            {
                tfDebugAudio.visible = false;
            }

            InitAvatars(bookData);

            actionList = bookData.actionList;
            _ = LoadBackground(bookData);
            DoNextAction();
        }

        public async Task LoadBackground(BookModel bookData)
        {
            var tag = bookData.GetBgTag();
            if (string.IsNullOrEmpty(tag))
            {
                VFDebug.LogError("没有背景图 tag");
                return;
            }

            Debug.Log($"书本关tag {tag} #book");
            await BgLoader.LoadExploreBg(tag);
        }

        private void OnClickBtnContinue()
        {
            DotPracticeManager.Instance.Collect(new DataDot_Continue());
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Pop);
            DoNextAction();
        }

        private void DoNextAction()
        {
            if (currentActionIndex >= actionList.Length)
            {
                // 关板结算
                var ctrl = ControllerManager.instance.GetController<FragmentPracticeController>(ModelConsts.FragmentPractice);
                if (ctrl.CourseId > 0)
                {
                    ctrl.ReqSettlement();
                }
                UIManager.instance.GetUI(UIConsts.FragBookPanelUI).Hide();
            }
            else
            {
                DialogueAction action = actionList[currentActionIndex];
                currentActionIndex++;

                switch (action.Type)
                {
                    case ActionType.Narrator:
                        // 旁白
                        NearestAction = action;
                        AddTextToList(action, TryNextAction);
                        break;
                    case ActionType.Practice:
                        NearestAction = action;
                        ShowPractice(action);
                        return;
                    case ActionType.Speak:
                        NearestAction = action;
                        // 显示说话的头像和文本
                        var speakingAvatar = FindAvatarById(action.Avatar.avatar_id);
                        if (speakingAvatar != null)
                        {
                            // Find non-speaking avatar
                            var nonSpeakingAvatar = avatars.FirstOrDefault(a => a != speakingAvatar && !a.IsEmpty);

                            var scaleTime = 0.2f;
                            if (mode == Mode.Fast)
                            {
                                scaleTime = 0.01f;
                            }
                            // Animate speaking avatar to grow
                            speakingAvatar.TweenScale(new Vector2(1.2f, 1.2f), scaleTime).SetEase(EaseType.QuadOut);
                            nonSpeakingAvatar?.TweenScale(new Vector2(0.9f, 0.9f), scaleTime).SetEase(EaseType.QuadOut);


                            AddTextToList(action, () =>
                            {
                                nonSpeakingAvatar?.TweenScale(new Vector2(1f, 1f), scaleTime).SetEase(EaseType.QuadIn);
                                speakingAvatar.TweenScale(new Vector2(1f, 1f), scaleTime).SetEase(EaseType.QuadIn).OnComplete(() =>
                                {
                                    // 说话动画完成后，继续执行下一个动作 
                                    TryNextAction();
                                });
                            });
                        }
                        else
                        {
                            VFDebug.LogError($"曰 头像 {action.Avatar.avatar_name} 不在当前屏幕上，无法说话。");
                            TryNextAction();
                        }
                        break;
                    case ActionType.Enter:
                        // 显示头像
                        var emptyAvatar = FindEmptyAvatar();
                        var moveInTime = 0.1f;
                        if (mode == Mode.Fast)
                        {
                            moveInTime = 0.01f;
                        }
                        if (emptyAvatar != null)
                        {
                            emptyAvatar.SetAvatar(action.Avatar);
                            emptyAvatar.TweenMove(stagePositions[Array.IndexOf(avatars, emptyAvatar)], moveInTime)
                                .SetEase(EaseType.QuadIn)
                                .OnComplete(() =>
                                {
                                    // 头像上场动画完成后，继续执行下一个动作
                                    DoNextAction();
                                });
                        }
                        else
                        {
                            VFDebug.LogError("曰 两个头像都已占用，无法进入新头像。");
                            DoNextAction();
                        }
                        break;
                    case ActionType.Exit:
                        // 隐藏头像
                        var moveOutTime = 0.1f;
                        if (mode == Mode.Fast)
                        {
                            moveOutTime = 0.01f;
                        }
                        var exitingAvatar = FindAvatarById(action.Avatar.avatar_id);
                        if (exitingAvatar != null)
                        {
                            exitingAvatar.TweenMove(outsidePositions[Array.IndexOf(avatars, exitingAvatar)], moveOutTime)
                                .SetEase(EaseType.QuadOut)
                                .OnComplete(() =>
                                {
                                    exitingAvatar.SetAvatar(null);
                                    // 头像下场动画完成后，继续执行下一个动作
                                    DoNextAction();
                                });
                        }
                        else
                        {
                            VFDebug.LogError($"曰 头像 {action.Avatar.avatar_name} 不在当前屏幕上，无法退出。");
                            DoNextAction();
                        }
                        break;
                }
            }
        }

        private void TryNextAction()
        {
            // 继续
            switch (mode)
            {
                case Mode.Normal:
                    // 正常模式，点按钮继续执行下一个动作
                    ShowContinueButton();
                    break;
                case Mode.Auto:
                case Mode.Fast:
                    DoNextAction();
                    break;
            }
        }

        private void ShowPractice(DialogueAction action)
        {
            Debug.Assert(action.Practice != null, "没有练习题");
            var practice = action.Practice;
            FragModel.CurQuestion = practice;
            AddQuestionToList(practice, action);
            practice.SetStartServerTime();

            steps++;
            OnStepChanged(steps);
        }

        private void AddQuestionToList(APracticeData practice, DialogueAction action)
        {
            var questionUrl = practice.GetQuestionComponentUrl();
            AFragQuestion question = null;

            if (!string.IsNullOrEmpty(questionUrl))
            {
                // 问题组件
                question = UIPackage.CreateObjectFromURL(questionUrl).asCom as AFragQuestion;
                question.Init(true, practice);
                (question as IDialogueAction)?.SetAction(action);
                chatList.AddChild(question);
                chatList.scrollPane.ScrollBottom();

                question.onSizeChanged.Add(OnItemSizeChanged);
            }

            // 回答组件
            AFragAnswer answer = answerPanel.LoadPractice(practice, question);

            (answer as IDialogueAction)?.SetAction(action);
            question?.ShowPractice(answer);

            ShowAnswerPanel();
        }

        private void OnItemSizeChanged(EventContext context)
        {
            chatList.scrollPane.ScrollBottom();
        }

        private void AddTextToList(DialogueAction action, Action onComplete)
        {
            ChatText listItem = UIPackage.CreateObjectFromURL(ChatText.url).asCom as ChatText;
            listItem.SetSilent(mode == Mode.Fast);
            listItem.SetContent(action, onComplete);
            chatList.AddChild(listItem);
            chatList.scrollPane.ScrollBottom(true);

            HideContinueButton();
        }

        private void HideContinueButton()
        {
            btnContinue.state.selectedIndex = 3;
            btnContinue.com.touchable = false;
        }
        private void ShowContinueButton()
        {
            btnContinue.state.selectedIndex = 2;
            btnContinue.com.touchable = true;
        }

        private void ShowAnswerPanel()
        {
            answerPanel.EnsureBoundsCorrect();


            // 从底部上来
            float finalPosition = checker.y - answerPanel.height;
            // Debug.Log($"AnswerPopup b4anim finalPos_answerPanel:{finalPosition},c:{checker.y},apH:{answerPanel.height}");

            answerPanel.visible = true;
            answerPanel.displayObject.cacheAsBitmap = true;
            answerPanel.TweenFade(1, 0.4f).
                SetEase(EaseType.QuadIn);
            answerPanel.TweenMoveY(finalPosition, 0.3f)
                .SetEase(EaseType.QuadOut)
                .OnUpdate(() =>
                {
                    chatList.height = GetListTop() - chatList.y;
                    answerPanel.displayObject.cacheAsBitmap = true;
                })
                .OnComplete(() =>
                {
                    answerPanel.displayObject.cacheAsBitmap = false;
                    chatList.height = GetListTop() - chatList.y;
                    chatList.scrollPane.ScrollBottom();
                    checker.visible = true;
                });
            QuestionEventManager.Instance.ClearAnswer();
            checker.OnNewQuestion();
        }

        private void HideAnswerPanelFirst()
        {
            // 下去
            float finalPosition = height + 300;
            answerPanel.alpha = 0;
            answerPanel.y = finalPosition;
            chatList.height = btnContinue.com.y - chatList.y - 50;
            chatList.scrollPane.ScrollBottom();
            answerPanel.Clear();
            checker.visible = false;
        }

        private void HideAnswerPanel(Action onComplete = null)
        {
            // 下去
            float finalPosition = height + 300;
            
            // 列表直接下去
            chatList.height = btnContinue.com.y - chatList.y - 50;
            chatList.scrollPane.ScrollBottom();
            
            answerPanel.visible = true;
            answerPanel.TweenFade(0, 0.1f).
                SetEase(EaseType.QuadIn);
            answerPanel.TweenMoveY(finalPosition, 0.3f)
                .SetEase(EaseType.QuadOut)
                .OnComplete(() =>
                {                    
                    // chatList.height = GetListTop() - chatList.y;
                    // chatList.scrollPane.ScrollBottom();
                    answerPanel.Clear();
                    checker.visible = false;
                    onComplete?.Invoke();
                });
        }

        private float GetListTop()
        {
            return Mathf.Min(answerPanel.y, checker.y, btnContinue.com.y) - 50;
        }

        private AvatarHead FindAvatarById(long id)
        {
            return avatars.FirstOrDefault(a => a.AvatarId == id);
        }

        private AvatarHead FindEmptyAvatar()
        {
            return avatars.FirstOrDefault(a => a.IsEmpty);
        }

        public void OnAnswered()
        {
        }

        public void OnSubmit()
        {
            SoundManger.instance.PlayUI(QuestionEventManager.Instance.IsRightAnswer != true
                ? "question_error"
                : "question_right");
        }

        public void OnRetry() { }

        public void AutoCheck() { }
        public void OnReset()
        {
            HideAnswerPanel(DoNextAction);
        }
        public void OnJumpListenTask() { }
        public void OnJumpSpeakTask() { }

        #region 打点
        private float startY = 0f;
        public DialogueAction NearestAction {get; private set; } = null;  // 最近的实义动作，不包括上下场

        private void OnChatListTouchBegin(EventContext context)
        {
            // 获取当前滚动位置
            startY = chatList.scrollPane.posY;
        }

        private void OnChatListTouchEnd(EventContext context)
        {
            var currentY = chatList.scrollPane.posY;
            if (currentY > startY + 10f)
            {
                DotPracticeManager.Instance.Collect(new DataDot_ScrollDialogue(true));
            }
            else if (currentY < startY - 10f)
            {
                DotPracticeManager.Instance.Collect(new DataDot_ScrollDialogue(false));
            }
        }
        #endregion
    }
}