/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.Review
{
    public partial class DragCard : AGestureCard
    {
        public static string pkgName => "Review";
        public static string comName => "DragCard";
        public static string url => "ui://l7zo233dl0uy1o";

        public CardRotContent content;
        public Transition discard;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(DragCard));
        }

        public override void ConstructFromXML(XML xml)
        {
            content = GetChildAt(1) as CardRotContent;
            discard = GetTransitionAt(0);
        }
        public override void Dispose()
        {
            content = null;
            discard = null;

            base.Dispose();
        }
    }
}