/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class RecordAnswer : AFragAnswer
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "RecordAnswer";
        public static string url => "ui://cmoz5osjz7rm2i";

        public Controller ctrlRecording;
        public GComponent btnSpeak;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(RecordAnswer));
        }

        public override void ConstructFromXML(XML xml)
        {
            ctrlRecording = GetControllerAt(0);
            btnSpeak = GetChildAt(0) as GComponent;
        }
        public override void Dispose()
        {
            ctrlRecording = null;
            btnSpeak = null;

            base.Dispose();
        }
    }
}