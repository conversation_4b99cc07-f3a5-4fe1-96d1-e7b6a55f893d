﻿using System;
using System.Collections.Generic;
using System.Timers;
using FairyGUI;
using Msg.basic;
using Msg.explore;
using PlasticGui.WorkspaceWindow.NotificationBar;
using PlasticGui.WorkspaceWindow.Topbar;
using ScriptsHot.Game.Modules.Explore;
using ScriptsHot.Game.Modules.Procedure;
using UIBind.ExploreFriends.Component;
using UnityEngine;
using YooAsset;

namespace UIBind.ExploreFriends
{
    public partial class ExploreFriendsChatPanelUI: BaseUI<ExploreFriendsChatPanel>
    {
        public ExploreFriendsChatPanelUI(string name) : base(name)
        {
        }
        public override string uiLayer => UILayerConsts.Top;
        
        protected override bool isFullScreen => true;

        private ExploreFriendCfg _friendCfg;
        
        private Dictionary<ExploreFriendsCellType, List<GComponent>> _pools = new Dictionary<ExploreFriendsCellType, List<GComponent>>();

        private Dictionary<string, ItemComponentLogicBase> _allCell = new Dictionary<string, ItemComponentLogicBase>();
        
        private UIPackage _uiPackage;

        protected override void OnInit(GComponent uiCom)
        {
            base.OnInit(uiCom);

            _uiPackage = FairyGUI.UIPackage.GetByName("ExploreFriends");
            
            AddUIEvent(ui.btnBack.onClick, OnNextBtnClick);
            AddUIEvent(ui.btnSetting.onClick, OnSettingBtnClick);
            AddUIEvent(ui.btnDown.onClick, OnBtnDown);
            InitBar();
        }

        private GameObject _fxEffectObject;
        private void InitBar()
        {
            GameObject prefab = GResManager.instance.LoadPrefab("BarFinishedFX");
            _fxEffectObject = GameObject.Instantiate(prefab);
            if (_fxEffectObject == null)
            {
                VFDebug.LogError("BarFinishedFX == null");
            }
            GoWrapper wrapper = new GoWrapper(_fxEffectObject);
            ui.comBar.holder.SetNativeObject(wrapper);
            ui.comBar.holder.visible = true;
            ui.comBar.SetValue(0,100);
            
            _fxEffectObject.transform.localPosition = Vector3.zero;
            _fxEffectObject.transform.localScale = new Vector3(50,50,50);
        }

        private void AddEvent()
        {
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreFriend_closeness_update, OnUpdateCloseness);
            
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreFriend_selfTxt, OnShowSelfCell);
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreFriend_avatarTxt, OnShowAvatarCell);
            
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreFriend_AvatarAudioPlay, OnAvatarAudioPlay);
            
        }
        private void RemoveEvent()
        {
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreFriend_closeness_update, OnUpdateCloseness);
            
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreFriend_selfTxt, OnShowSelfCell);
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreFriend_avatarTxt, OnShowAvatarCell);
            
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreFriend_AvatarAudioPlay, OnAvatarAudioPlay);
        }
        protected override void OnShow()
        {
            base.OnShow();
            AddEvent();
            
            _friendCfg = Cfg.T.TBExploreFriend.Get(1001);
            
            ShowAvatar();
            ShowImg2d();
            ShowBar();
            ShowHistoryCells();
            GameEntry.ExFriendC.ShowRecordUI();
            PlayLastAudio();
        }

        protected override void OnHide()
        {
            base.OnHide();
            RemoveEvent();
            RemoveAllItem();
        }

        #region 界面逻辑

        private void ShowBar()
        {
            return;
            var info = GameEntry.ExFriendC.Model.GetFriendSlotDataByAvatarId(GameEntry.ExFriendC.EnterChatAvatarId);
            int cur = info.ClosenessValue;
            var curLevelData = GameEntry.ExFriendC.Model.GetClosenessLevelData(info.LevelId);
            int max = curLevelData.maxClosenessValue;
            ui.comBar.SetValue(cur,max);

            ui.comBar.ctrl.selectedIndex = curLevelData.levelIndex - 1;
            ui.comBar.ctrlIcon.selectedIndex = curLevelData.levelIndex - 1;
        }
        
        private void UpdateBar(bool uplevel,Action barFull)
        {
            // SC_FriendChatDownMsgForClosenessProgressChange changeMsg = GameEntry.ExFriendC.Model.ClosenessInfo;
            // if (uplevel)
            // {
            //     int changeValue = changeMsg.closenessProgressInfo.afterValue - changeMsg.closenessProgressInfo.beforeValue;
            //     ui.comBar.PlayOverflowEffect(changeValue,changeMsg.closenessProgressInfo.closenessUpgradeDetail.maxValue,barFull);
            // }
            // else
            // {
            //     ui.comBar.SetEffectValue(changeMsg.closenessProgressInfo.afterValue,changeMsg.closenessProgressInfo.maxValue);
            // }
        }
        private async void ShowAvatar()
        {
            FriendsAvatarLoaderHelper.GenerateRTAsync(GameEntry.ExFriendC.EnterChatAvatarId, ui.comLoader3D, delegate(bool b)
            {
                UpdateImgCenter(ui.comLoader3D);
            }, 0.7f);
        }
        public async void ShowImg2d()
        {
            ui.comLoaderImg.fill = FillType.ScaleMatchHeight;
            float screenWidth = GRoot.inst.width;
            float screenHeight = GRoot.inst.height + ExploreConst.ScreenOffsetHeight;
            
            string url = ResUtils.GetExploreBgImgPath(_friendCfg.BGImgUrl);
            var handle =  YooAsset.YooAssets.LoadAssetAsync<Texture2D>(url);
            await handle.Task;
            if (handle != null && handle.Status == EOperationStatus.Succeed)
            {
                Texture2D texture = handle.AssetObject as Texture2D;
                if (texture != null)
                {
                    NTexture t = new NTexture(texture);
                    float imageRatio = (float)t.width / t.height;
                    float screenRatio = screenWidth / screenHeight;
                    if (imageRatio > screenRatio)
                    {
                        // 图片更宽，以高度为基准
                        ui.comLoaderImg.fill = FillType.ScaleMatchHeight;
                        float targetWidth = screenHeight * imageRatio;
                        ui.comLoaderImg.SetSize(targetWidth, screenHeight);
                    }
                    else
                    {
                        // 图片更高，以宽度为基准
                        ui.comLoaderImg.fill = FillType.ScaleMatchWidth;
                        float targetHeight = screenWidth / imageRatio;
                        ui.comLoaderImg.SetSize(screenWidth, targetHeight);
                    }
                
                    ui.comLoaderImg.texture = t;
                    UpdateImgCenter( ui.comLoaderImg);
                }
            }
        }
        private void UpdateImgCenter(GLoader imageLoader)
        {
            float screenHeight = GRoot.inst.height + ExploreConst.ScreenOffsetHeight;
            imageLoader.SetXY((ui.com.width - imageLoader.width * imageLoader.scaleX) / 2, (screenHeight - imageLoader.height * imageLoader.scaleY) / 2);
        }
        
        private void ShowHistoryCells()
        {
            SC_GetFriendChatPreloadDataResp ack = GameEntry.ExFriendC.Model.AllInfo;
            if (ack.preloadData.Count <= 0) return;
            foreach (var item in ack.preloadData)
            {
                if (item.avatarId == GameEntry.ExFriendC.EnterChatAvatarId)
                {
                    //话题信息
                    ui.comTitle.txtDesc.text = item.topic.title;
                    for (int i = 0; i < item.dialogRound.Count; i++)
                    {
                        PB_Friend_DialogRoundData roundData = item.dialogRound[i];
                        if (roundData.msgBelong == PB_MsgBelong.Avatar)
                        {
                            ExploreFriendsAvatarItemLogic avatarUI = new ExploreFriendsAvatarItemLogic();
                            avatarUI.Init();
                            AddChatCellCom(avatarUI, ExploreFriendsCellType.avatar);
                            avatarUI.SetTxt(roundData.text);
                            _allCell[roundData.bubbleId] = avatarUI;
                        }
                        else if (roundData.msgBelong == PB_MsgBelong.User)
                        {
                            ExploreFriendsPlayerItemLogic selfUI = new ExploreFriendsPlayerItemLogic();
                            AddChatCellCom(selfUI, ExploreFriendsCellType.player);
                            selfUI.SetTxt(roundData.text);
                            _allCell[roundData.bubbleId] = selfUI;
                        }
                    }
                    break;
                }
            }
            
        }
        
        /// <summary>
        /// 播放最后一条音频
        /// </summary>
        private void PlayLastAudio()
        {
            PB_FriendChatExtRoundData_Avatar audioInfo = GameEntry.ExFriendC.Model.GetLastAudioIdByAvatarId(GameEntry.ExFriendC.EnterChatAvatarId);
            ProcedureParams p = new ProcedureParams();
            p.type = EProcedureType.OnExploreFriendAudioPlay;
            p.param = audioInfo.audio.id;
            Notifier.instance.SendNotification(NotifyConsts.procedure_explore_friend_avatar_audio_play,p);
        }

        #endregion

        #region 点击

        private void OnNextBtnClick()
        {
            GameEntry.ExFriendC.Back();
            //test
            ui.comBar.SetEffectValue(50, 100);
        }
        
        private void OnSettingBtnClick()
        {
            Notifier.instance.SendNotification(NotifyConsts.OpenUI,UIConsts.ExploreSettingPanelUI);
        }
        
        private void OnBtnDown()
        {
            //test
            ui.comBar.PlayOverflowEffect(30, 150, null);
            Notifier.instance.SendNotification(NotifyConsts.OpenUI,UIConsts.ExploreFriendsClosenessAlert);
        }

        #endregion
     

        #region 服务器数据

        private void OnShowAvatarCell(string s, object body)
        {
            ExploreFriendsAvatarItemLogic item = new ExploreFriendsAvatarItemLogic();
            item.Init();
            AddChatCellCom(item, ExploreFriendsCellType.avatar);
            item.SetTxt(GameEntry.ExFriendC.Model.AvatarTxtInfo.replyText);
            _allCell[GameEntry.ExFriendC.Model.AvatarTxtInfo.commonData.bubbleId] = item;
        }

        private void OnShowSelfCell(string s, object body)
        {
            ExploreFriendsPlayerItemLogic item = new ExploreFriendsPlayerItemLogic();
            AddChatCellCom(item, ExploreFriendsCellType.player);
            item.SetTxt(GameEntry.ExFriendC.Model.SelfTxtInfo.text);
            _allCell[GameEntry.ExFriendC.Model.SelfTxtInfo.commonData.bubbleId] = item;
        }

        public void AddChatCellCom(ItemComponentLogicBase baseCell, ExploreFriendsCellType cellType)
        {
            GComponent gCom;
            string resName = GetCellResName(cellType);
            if (resName == String.Empty) return;
            
            if (_pools.ContainsKey(cellType) && _pools[cellType].Count > 0)
            {
                gCom = _pools[cellType][0];
                _pools[cellType].RemoveAt(0);
            }
            else
                gCom = _uiPackage.CreateObject(resName).asCom;

            gCom.name = String.Empty;

            baseCell.SetUI(gCom);

            this.ui.comCellContainer.com.AddChild(gCom);
            gCom.group = this.ui.comCellContainer.Content;
            TimerManager.instance.RegisterNextFrame((a) =>
            {
                this.ui.comCellContainer.Content.EnsureBoundsCorrect();
                ui.comCellContainer.com.EnsureBoundsCorrect();
            });
        }

        private string GetCellResName(ExploreFriendsCellType cellType)
        {
            if(cellType == ExploreFriendsCellType.avatar)
                return "ExploreFriendsAvatarItem";
            else if(cellType == ExploreFriendsCellType.player)
                return "ExploreFriendsPlayerItem";
            return String.Empty;
        }
        
        public void RemoveAllItem()
        {
            foreach (var kv in _allCell)
            {
                GComponent gCom = kv.Value.GetUI();
                if (gCom.parent != null)
                    gCom.parent.RemoveChild(gCom);

                ExploreFriendsCellType cellType = ExploreFriendsCellType.avatar;
                if (kv.Value is ExploreFriendsAvatarItemLogic)
                {
                    cellType = ExploreFriendsCellType.avatar;
                }
                else if (kv.Value is ExploreFriendsPlayerItemLogic)
                {
                    cellType = ExploreFriendsCellType.player;
                }

                if (!_pools.ContainsKey(cellType))
                    _pools[cellType] = new List<GComponent>();
                bool isRecycle = _pools[cellType].Count < 100;

                _allCell.Remove(kv.Key);
                if (!isRecycle)
                {
                    gCom.Dispose(); 
                }
                else
                    _pools[cellType].Add(gCom);
            }
        }

        private void OnUpdateCloseness(string s, object body)
        {
            Debug.LogError("OnUpdateCloseness   isUpgrade:::" + GameEntry.ExFriendC.Model.ClosenessInfo.closenessProgressInfo.isUpgrade);
            if(GameEntry.ExFriendC.Model.ClosenessInfo.closenessProgressInfo.isUpgrade)
            {
                UpdateBar(true,() =>
                {
                    Notifier.instance.SendNotification(NotifyConsts.OpenUI,UIConsts.ExploreFriendsClosenessAlert);
                });
            }
            else
            {
                UpdateBar(false, null);
            }
        }
        
        private async void OnAvatarAudioPlay(string s, object body)
        {
            ExploreAudioPlayEffect audioPlayEffect = body as ExploreAudioPlayEffect;
            GameEntry.ExFriendC.StopStreamAudioTTs();
            try
            {
                AudioClip clip = await GameEntry.ExFriendC.Model.GetAudioByRecordId(audioPlayEffect.AudioId);
                
                if (clip == null)
                {
                    Notifier.instance.SendNotification(NotifyConsts.ExploreFriend_AvatarAudioPlayOver);
                    VFDebug.LogError("avatar播放的音频加载失败：：：audioId：" + audioPlayEffect.AudioId);
                    return;
                }
                GSoundManager.instance.PlayAvatarTTS(clip, 1);
            }
            catch (Exception ex)
            {
                Notifier.instance.SendNotification(NotifyConsts.ExploreFriend_AvatarAudioPlayOver);
                VFDebug.LogError($"播放音频失败: {ex.Message}");
            }
        }

        #endregion
    }
}