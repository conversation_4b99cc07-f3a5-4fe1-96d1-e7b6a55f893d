%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b340139c9e4d054f904d8b452798652, type: 3}
  m_Name: ES3Defaults
  m_EditorClassIdentifier: 
  settings:
    _location: 0
    path: SaveFile.es3
    encryptionType: 0
    compressionType: 0
    encryptionPassword: password
    directory: 0
    format: 0
    prettyPrint: 1
    bufferSize: 2048
    saveChildren: 1
    postprocessRawCachedData: 0
    typeChecking: 1
    safeReflection: 1
    memberReferenceMode: 0
    referenceMode: 2
    serializationDepthLimit: 64
    assemblyNames:
    - AgoraRtcSDK
    - AndroidAdapter
    - AppleAuth
    - AppsFlyer
    - Assembly-CSharp
    - Autodesk.Fbx
    - AvatarData
    - AVProVideo.Demos
    - AVProVideo.Extensions.Timeline
    - AVProVideo.Extensions.UnityUI
    - AVProVideo.Extensions.VisualEffectGraph
    - AVProVideo.Runtime
    - babilinski.vosk.unity
    - Bunny83.simpleJson
    - Carrier
    - Cinemachine
    - CW.Common
    - Cysharp.Net.Http.YetAnotherHttpHandler
    - DOTween.Modules
    - EasySave3
    - FairyGUI
    - FbxBuildTestAssets
    - Game
    - Google.Play.AssetDelivery
    - Google.Play.AssetDelivery.Samples.AssetDeliveryDemo
    - Google.Play.AssetDelivery.Samples.TextureTargetingDemo
    - Google.Play.Common
    - Google.Play.Core
    - GoogleSignIn
    - HybridCLR.Runtime
    - Ios
    - LeanCommon
    - LeanTouch
    - LitJson
    - Luban.Runtime
    - Main
    - MTPush
    - Oculus.LipSync
    - Purchasing.Common
    - PushNotification
    - ShareSDK
    - SpeechToText
    - spine-csharp
    - spine-unity
    - StompyRobot.SRDebugger
    - StompyRobot.SRF
    - UniTask
    - UniTask.Addressables
    - UniTask.DOTween
    - UniTask.Linq
    - UniTask.TextMeshPro
    - Unity.Advertisement.IosSupport
    - Unity.Advertisement.IosSupport.Tests
    - UniWebView
    - VFEditor
    - Vibration
    - YooAsset
    showAdvancedSettings: 0
  addMgrToSceneAutomatically: 0
  autoUpdateReferences: 1
  addAllPrefabsToManager: 1
  collectDependenciesDepth: 4
  collectDependenciesTimeout: 10
  updateReferencesWhenSceneChanges: 1
  updateReferencesWhenSceneIsSaved: 1
  updateReferencesWhenSceneIsOpened: 1
  referenceFolders: []
  logDebugInfo: 0
  logWarnings: 1
  logErrors: 1
