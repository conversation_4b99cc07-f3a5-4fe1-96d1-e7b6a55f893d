/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.Main
{
    public partial class BottomTabRegion : UIBindT
    {
        public override string pkgName => "Main";
        public override string comName => "BottomTabRegion";

        public Controller SelectedTabBgState;
        public GGraph BottomTabBg;
        public GGraph BottomTabSelected;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            SelectedTabBgState = com.GetControllerAt(0);
            BottomTabBg = (GGraph)com.GetChildAt(0);
            BottomTabSelected = (GGraph)com.GetChildAt(1);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            SelectedTabBgState = null;
            BottomTabBg = null;
            BottomTabSelected = null;
        }
    }
}