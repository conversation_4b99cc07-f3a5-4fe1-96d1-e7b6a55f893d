using UIBind.FragmentPractice;
using FairyGUI;
using Msg.question_process;
using Game.Modules.FragmentPractice;
using ScriptsHot.Game.Modules.MainPath;
using UnityEngine;
using UIBind.Debugger;
using ScriptsHot.Game.Modules.FragmentPractice;
using System.Linq;
using System.Collections.Generic;
using System;

namespace ScriptsHot.Game.Modules.Debugger
{
    public class DebugPracticePanelUI : BaseUI<DebugPracticePanel>//, IQuestionEventListener
    {
        public override string uiLayer => UILayerConsts.Top;

        public object KeyboardType { get; private set; }

        private CompPractice Practice => ui.practice as CompPractice;

        public DebugPracticePanelUI(string name) : base(name)
        {
        }

        protected override void OnInit(GComponent uiCom)
        {
            base.OnInit(uiCom);

            AddUIEvent(ui.btnClose.onClick, OnClose);
            AddUIEvent(ui.btnBook.onClick, OnBookLevelClicked);
            AddUIEvent(ui.btnFrag.onClick, OnFragTestClicked);
            AddUIEvent(ui.btnLevel.onClick, OnStarLevelClicked);

            Practice.AddEventListener("onInputFocusIn", OnInputFocusIn);
            Practice.AddEventListener("onInputFocusOut", OnInputFocusOut);
            Practice.Init(true);

            SetupEditableComboBox();
            LoadComboBoxHistory(); // 载入历史记录
        }

        private void OnClose()
        {
            Hide();
        }

        private void LoadComboBoxHistory()
        {
            var history = PlayerPrefs.GetString("DebugPractice_ComboBoxHistory", "");
            if (!string.IsNullOrEmpty(history))
            {
                var items = history.Split(',');
                ui.cbId.items = items;
            }
        }

        private void SaveComboBoxHistory()
        {
            if (ui.cbId.items != null && ui.cbId.items.Length > 0)
            {
                var history = string.Join(",", ui.cbId.items);
                PlayerPrefs.SetString("DebugPractice_ComboBoxHistory", history);
                PlayerPrefs.Save();
            }
        }

        private void AddToComboBox(string item)
        {
            if (string.IsNullOrEmpty(item)) return;
            
            var itemsList = ui.cbId.items?.ToList() ?? new List<string>();
            
            // 如果已存在，先移除旧的
            if (itemsList.Contains(item))
            {
                itemsList.Remove(item);
            }
            
            // 添加到最前面
            itemsList.Insert(0, item);
            
            // 限制最多10项
            if (itemsList.Count > 10)
            {
                itemsList = itemsList.Take(10).ToList();
            }
            
            ui.cbId.items = itemsList.ToArray();
            SaveComboBoxHistory(); // 保存历史记录
        }
        private void SetupEditableComboBox()
        {
            // 假设你的UI中有一个输入框和下拉框
            var textInput = ui.tfQid; // 输入框
            var comboBox = ui.cbId;   // 下拉框
            
            // 当下拉框选择改变时，更新输入框
            comboBox.onChanged.Add(() => {
                if (comboBox.selectedIndex >= 0)
                {
                    textInput.text = comboBox.items[comboBox.selectedIndex];
                }
            });
        }
        private void OnBookLevelClicked()
        {
            var trimmed = ui.tfQid.text.Replace(" ", "");
            if (long.TryParse(trimmed, out long qID))
            {
                UIManager.instance.GetUI<FragBookPanelUI>(UIConsts.FragBookPanelUI).Show(qID, "ui://lno6dlsnlkfp1t");
                AddToComboBox(trimmed);
            }
        }

        private void OnFragTestClicked()
        {
            var trimmed = ui.tfQid.text.Replace(" ", "");
            if (long.TryParse(trimmed, out long qID))
            {
                LoadQuestionData(qID);
                AddToComboBox(trimmed);
            }
        }

        private void OnStarLevelClicked()
        {
            ui.starEditor.visible = true;
        }

        private void OnInputFocusIn()
        {
            Practice.y = 655;
        }

        private void OnInputFocusOut()
        {
            Practice.y = 1133;
        }

        private async void LoadQuestionData(long qID)
        {
            var response = await MsgManager.instance.SendAsyncMsg<SC_GetQuickPracticeListAck>( new CS_GetQuickPracticeListReq()
            {
                session_id=1,
                question_id=qID
            });

            if (response.code != 0) return;

            if (response.data.data.Count == 0)
            {
                UnityEngine.Debug.LogError($"获取不到问题数据");
                return;
            }
            var practice = APracticeData.TryCreate(0, response.data.data[0]);
            if (practice == null) return;

            var m = ModelManager.instance.GetModel<FragmentPracticeModel>(ModelConsts.FragmentPractice);
            m.CurQuestion = practice;
            ShowPractice(practice);
            QuestionEventManager.Instance.ClearAnswer();
            (ui.checker as CompCheck).OnNewQuestion();
        }
        public void ShowPractice(APracticeData practice)
        {
            ui.tfQuestionInfo.text = $"type:{practice.QuestionType}\naudioId:{practice.AudioId}\navatar:{practice.AvatarId}";
            Practice.ShowPractice(practice);
        }

        // public void OnAnswered()
        // {
        //     // throw new NotImplementedException();
        // }

        // void IQuestionEventListener.OnSubmit()
        // {
        //     // OnSubmit();
        // }

        // public void OnRetry()
        // {
        //     // throw new NotImplementedException();
        // }

        // public void AutoCheck()
        // {
        //     // throw new NotImplementedException();
        // }

        // public void OnReset()
        // {
        //     // throw new NotImplementedException();
        // }

        // public void OnJumpListenTask()
        // {
        //     // throw new NotImplementedException();
        // }

        // public void OnJumpSpeakTask()
        // {
        //     // throw new NotImplementedException();
        // }
    }
}