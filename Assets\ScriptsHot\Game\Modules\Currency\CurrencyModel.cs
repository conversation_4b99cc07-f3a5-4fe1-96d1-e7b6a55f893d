﻿/*
 ****************************************************
 * 作者：ZhangXiaoWu
 * 创建时间：2024/05/10 21:11:46 星期五
 * 功能：Nothing
 ****************************************************
 */

using System;
using System.Collections.Generic;
using Msg.economic;

public enum GameEventName
{
    GameEnter, //进入游戏
    GameChatPre, //对话进入
    GameChatPost //对话退出
}

public enum EconomicType
{
    Gold, //金币
    Diamond, //永久钻石
    TemporaryDiamond //临时钻石
}

public class EconomicCallEvent
{
    public const string OnRefreshEconomicInfo = "OnRefreshEconomicInfo";//刷新
}

public class EconomicInfo
{
    public long CurNum { private set; get; } //当前余额
    public long PreNum { private set; get; } //原余额
    public long ChangeNum { private set; get; } //改变数量
    public long ValidTime { private set; get; } //有效期时间戳

    public void SetInfo(long curr, long pre, long change, long validTime)
    {
        CurNum = curr;
        PreNum = pre;
        ChangeNum = change;
        if (change == 0)
        {
            ChangeNum = curr - pre;
        }
        ValidTime = validTime;
    }
}

public class CurrencyModel : BaseModel
{
    public CurrencyModel() : base(ModelConsts.CurrencyController) { }
    private readonly Dictionary<EconomicType, EconomicInfo>
        _curEconomicDic = new Dictionary<EconomicType, EconomicInfo>(); //当前所有资源数量
    private readonly Dictionary<EconomicType, EconomicInfo>
        _preEconomicDic = new Dictionary<EconomicType, EconomicInfo>(); //之前所有资源数量
    private readonly Dictionary<EconomicType, EconomicInfo>
        _postEconomicDic = new Dictionary<EconomicType, EconomicInfo>(); //之后所有资源数量
    
    // 10091 设置资源
    public void SetEconomicInfo(PB_EconomicInfoResp data,GameEventName gameEventName = GameEventName.GameEnter)
    {
        // int curr, int pre, int change, int validTime
        SetEconomicInfoVo(EconomicType.Gold,gameEventName).SetInfo(data.coin_info.cur_balance, data.coin_info.pre_balance,
            data.coin_info.change, 0);
        
        SetEconomicInfoVo(EconomicType.Diamond,gameEventName).SetInfo(data.diamond_info.permanent_info.cur_balance,
            data.diamond_info.permanent_info.pre_balance, 0, 0);
        
        SetEconomicInfoVo(EconomicType.TemporaryDiamond,gameEventName).SetInfo(data.diamond_info.temporary_info.cur_balance,
            data.diamond_info.temporary_info.pre_balance, 0, data.diamond_info.temporary_info.valid_time);
        SendNotification(EconomicCallEvent.OnRefreshEconomicInfo);
    }

    public void SetEconomicInfo(EconomicType type, long value)
    {
        if (_curEconomicDic.ContainsKey(type))
        {
            _curEconomicDic[type].SetInfo(value, _curEconomicDic[type].CurNum, 0, _curEconomicDic[type].ValidTime);
        }
    }

    private EconomicInfo SetEconomicInfoVo(EconomicType economicType,GameEventName gameEventName)
    {
        switch (gameEventName)
        {
            case GameEventName.GameChatPre:
                return _preEconomicDic.TryGetValue(economicType, out var value) ? value
                    : _preEconomicDic[economicType] = new EconomicInfo();
            case GameEventName.GameChatPost:
                return _postEconomicDic.TryGetValue(economicType, out var value1) ? value1
                    : _postEconomicDic[economicType] = new EconomicInfo();
            default:
                return _curEconomicDic.TryGetValue(economicType, out var value2) ? value2
                    : _curEconomicDic[economicType] = new EconomicInfo();
        }
    }

    //获取单个资源接口
    public EconomicInfo GetEconomicInfo(EconomicType economicType,GameEventName gameEventName = GameEventName.GameEnter)
    {
        switch (gameEventName)
        {
            case GameEventName.GameChatPre:
                if (_preEconomicDic.ContainsKey(economicType))
                    return _preEconomicDic[economicType];
                else
                {
                    var info = new EconomicInfo();
                    info.SetInfo(0, 0, 0, -1);
                    return info;  
                }
            case GameEventName.GameChatPost:
                if (_postEconomicDic.ContainsKey(economicType))
                    return _postEconomicDic[economicType];
                else
                {
                    var info = new EconomicInfo();
                    info.SetInfo(0, 0, 0, -1);
                    return info;  
                }
            default:
                if (_curEconomicDic.ContainsKey(economicType))
                    return _curEconomicDic[economicType];
                else
                {
                    var info = new EconomicInfo();
                    info.SetInfo(0, 0, 0, -1);
                    return info;
                }
            
        }
    }

    public long SetTaskChangeGoldNum()
    {
        var postCurNum = GetEconomicInfo(EconomicType.Gold,GameEventName.GameChatPost).CurNum;
        var preCurNum = GetEconomicInfo(EconomicType.Gold,GameEventName.GameChatPre).CurNum;
        return postCurNum - preCurNum;
    }


}