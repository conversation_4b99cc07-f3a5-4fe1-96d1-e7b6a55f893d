/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class ImageMaskComp : ExtendedComponent
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "ImageMaskComp";
        public static string url => "ui://cmoz5osjd50u3n";

        public GLoader ldr;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(ImageMaskComp));
        }

        public override void ConstructFromXML(XML xml)
        {
            ldr = GetChildAt(1) as GLoader;

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();

            ldr = null;

            base.Dispose();
        }
    }
}