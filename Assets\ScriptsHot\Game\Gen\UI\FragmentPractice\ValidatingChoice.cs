/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class ValidatingChoice : GComponent
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "ValidatingChoice";
        public static string url => "ui://cmoz5osjp3vduvptce";

        public Controller state;
        public GTextField tfAnswer;
        public GGraph hotArea;
        public GGraph holder;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(ValidatingChoice));
        }

        public override void ConstructFromXML(XML xml)
        {
            state = GetControllerAt(0);
            tfAnswer = GetChildAt(0) as GTextField;
            hotArea = GetChildAt(5) as GGraph;
            holder = GetChildAt(7) as GGraph;
        }
        public override void Dispose()
        {
            state = null;
            tfAnswer = null;
            hotArea = null;
            holder = null;

            base.Dispose();
        }
    }
}