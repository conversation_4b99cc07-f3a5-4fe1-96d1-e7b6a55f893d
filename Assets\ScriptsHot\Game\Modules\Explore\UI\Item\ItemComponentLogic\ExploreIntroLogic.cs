﻿
using FairyGUI;
using Google.Protobuf.Collections;
using Msg.explore;
using ScriptsHot.Game.Modules.Common;
using ScriptsHot.Game.Modules.Explore;
using ScriptsHot.Game.Modules.Explore.ExploreType.Base;
using ScriptsHot.Game.Modules.Procedure;
using UnityEngine;

namespace UIBind.Explore.Item.ItemComponentLogic
{
    /// <summary>
    /// Intro
    /// </summary>
    public class ExploreIntroLogic:ItemComponentLogicBase
    {
        public ExploreIntro Com;
        
        private ExploreCellBase _item;
        
        private ExploreItemUI _cellItem => _item as ExploreItemUI;

        private PB_StoryPreloadData _curItemInfo;
        private PB_Task_Intro _curIntroInfo;
        private int _curIndex;
        private GTweener _scaleTweener;

        public override void Init()
        {
            base.Init();
            Com.btnSkip.onClick.Add(() =>
            {
                StopIntroScaleAnimation();
                SetVisible(false);
                //停止声音
                Notifier.instance.SendNotification(NotifyConsts.ExploreSoundStop);
                //清空队列
                Notifier.instance.SendNotification(NotifyConsts.procedure_main_break);
                //进入实体状态
                Notifier.instance.SendNotification(NotifyConsts.ExploreEnterEntityState);
                
                CLICK_EXPLORE_INTRO_PAGE_SKIP_BUTTON intro = new CLICK_EXPLORE_INTRO_PAGE_SKIP_BUTTON();
                intro.story_id = _curItemInfo.storyId;
                intro.task_id = _curItemInfo.taskId;
                DataDotMgr.Collect(intro);
            });
        }
        
        public void SetParentItem(ExploreCellBase item)
        {
            _item = item;
        }

        public void UpdateInfo(PB_Task_Intro value,bool effect)
        {
            _curIntroInfo = value;
            _curIndex = GetIndex(value.audio.id);
            ShowTxt(value.introText,value.firstLangText);
            ShowBack(effect);
        }

        private void ShowTxt(string str,string translate)
        {
            Com.txtDesc.text = str;
            Com.txtTranslate.text = translate;
        }

        private async void ShowBack(bool effect)
        {
            ExploreIntroCfg cfg = Cfg.T.TBExploreIntro.GetOrDefault(_curItemInfo.taskId.ToString());
            //test
            // if(cfg == null)
                // cfg = Cfg.T.TBExploreIntro.GetOrDefault("1944663105032294400");
            //test end
            
            if(_curIndex == 0)
               await _cellItem.LoadIntroImage(cfg.introBack1);
            else 
                await _cellItem.LoadIntroImage(cfg.introBack2);

            // if (effect)
            // {
            //     StopIntroScaleAnimation();
            //     // 重置到 100% 再执行到 120%
            //     if (Com != null && Com.comLoaderImg != null)
            //     {
            //         Com.comLoaderImg.SetScale(1f, 1f);
            //         _scaleTweener = Com.comLoaderImg
            //             .TweenScale(new Vector2(1.1f, 1.1f), 5f)
            //             .SetEase(EaseType.QuadOut);
            //     }
            // }
        }

        public void SetData(PB_StoryPreloadData itemData)
        {
            _curItemInfo = itemData;

            RepeatedField<PB_Task_Intro>  IntroData = _cellItem.Data.taskIntro;
            if (IntroData.Count > 0)
            {
                UpdateInfo(IntroData[0],false);
            }
            
            SetVisible(IntroData.Count > 0);
        }
        
        public void Show(PB_StoryPreloadData itemData)
        {
            // _curItemInfo = itemData;
            SetVisible(true);
            RepeatedField<PB_Task_Intro>  IntroData = _cellItem.Data.taskIntro;
            
            ExploreIntroCfg cfg = Cfg.T.TBExploreIntro.GetOrDefault(_cellItem.Data.taskId.ToString());
            if (IntroData.Count > 0 && cfg != null)
            {
                UpdateInfo(IntroData[0],false);
              
                for (int i = 0; i < IntroData.Count; i++)
                {
                    PB_Task_Intro taskIntro = IntroData[i];
                    ExploreIntroParam introP = new ExploreIntroParam()
                    {
                        info = taskIntro,
                        storyId = _cellItem.Data.storyId,
                        taskId = _cellItem.Data.taskId
                    };
                    ProcedureParams p = new ProcedureParams()
                    {
                        param = introP
                    };
                    // Debug.Log("Show Intro Audio!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! ::" + i + "    IntroData.Count::" + IntroData.Count);
                    Notifier.instance.SendNotification(NotifyConsts.procedure_explore_entity_intro_audio_play,p);
                }
            }
            Notifier.instance.SendNotification(NotifyConsts.procedure_explore_entity_intro_over);
            
        }

        public void SetVisible(bool value)
        {
            Com.com.visible = value;
        }

        private int GetIndex(ulong audioId)
        {
            RepeatedField<PB_Task_Intro>  IntroData = _cellItem.Data.taskIntro;
            for (int i = 0; i < IntroData.Count; i++)
            {
                PB_Task_Intro taskIntro = IntroData[i];
                if (taskIntro.audio.id == audioId)
                    return i;
            }

            return 0;
        }

        public void StopIntroScaleAnimation()
        {
            if (_scaleTweener != null)
            {
                _scaleTweener.Kill();
                _scaleTweener = null;
            }

            if (Com != null && Com.comLoaderImg != null)
            {
                Com.comLoaderImg.SetScale(1f, 1f);
            }
        }
    }
}