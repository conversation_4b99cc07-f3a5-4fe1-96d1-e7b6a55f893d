/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class PhoneQuestion : AFragQuestion
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "PhoneQuestion";
        public static string url => "ui://cmoz5osju0ckuvptc6";

        public Controller stateCtrl;
        public GTextField textCenter;
        public OldBtnNormalPlay btnFast;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(PhoneQuestion));
        }

        public override void ConstructFromXML(XML xml)
        {
            stateCtrl = GetControllerAt(0);
            textCenter = GetChildAt(0) as GTextField;
            btnFast = GetChildAt(1) as OldBtnNormalPlay;
        }
        public override void Dispose()
        {
            stateCtrl = null;
            textCenter = null;
            btnFast = null;

            base.Dispose();
        }
    }
}