/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class CompCheck : ExtendedComponent
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "CompCheck";
        public static string url => "ui://cmoz5osjsnlj3p";

        public Controller state;
        public Controller jump;
        public CompCheckContent compCheckContent;
        public BtnJump btnJump;
        public BtnBottom btnCheck;
        public GGroup grp;
        public Transition show;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(CompCheck));
        }

        public override void ConstructFromXML(XML xml)
        {
            state = GetControllerAt(0);
            jump = GetControllerAt(1);
            compCheckContent = new CompCheckContent();
            compCheckContent.Construct(GetChildAt(0).asCom);
            btnJump = new BtnJump();
            btnJump.Construct(GetChildAt(1).asCom);
            btnCheck = new BtnBottom();
            btnCheck.Construct(GetChildAt(2).asCom);
            grp = GetChildAt(3) as GGroup;
            show = GetTransitionAt(0);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();

            state = null;
            jump = null;
            compCheckContent.Dispose();
            compCheckContent = null;
            btnJump.Dispose();
            btnJump = null;
            btnCheck.Dispose();
            btnCheck = null;
            grp = null;
            show = null;

            base.Dispose();
        }
    }
}