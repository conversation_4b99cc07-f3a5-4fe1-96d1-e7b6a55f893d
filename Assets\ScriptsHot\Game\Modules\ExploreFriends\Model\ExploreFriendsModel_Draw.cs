using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public partial class ExploreFriendsModel
{
    private FriendSlotData _redrawFriendSlotData;
    
    public void SetRedrawData(FriendSlotData friendSlotData)
    {
        _redrawFriendSlotData = friendSlotData;
        drawSlotIndex = friendSlotData.SlotIndex;


    }

    public FriendSlotData GetDrawData()
    {
        if (_redrawFriendSlotData == null)
        {
            _redrawFriendSlotData = GetEmptySlotIndex();
        }

        return _redrawFriendSlotData;
    }






}
