[{"MainKey": 1001, "clipKey": "Idle", "clipName": "Idle", "animType": "Boy", "canBlend": true, "blendIn": 1, "blendOut": 1, "canLoop": true, "loopMax": 1666666600.0, "loopBlendTime": 0.2, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 1003, "clipKey": "State1-2", "clipName": "State1-2", "animType": "Boy", "canBlend": true, "blendIn": 0.1, "blendOut": 0.05, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.88, "speedRangeMax": 1.12, "footIK": false, "animAdditionTag": "explore_sentence_end", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 1004, "clipKey": "State1-3", "clipName": "State1-3", "animType": "Boy", "canBlend": true, "blendIn": 0.4, "blendOut": 0.4, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.88, "speedRangeMax": 1.12, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 1005, "clipKey": "State1-4", "clipName": "State1-4", "animType": "Boy", "canBlend": true, "blendIn": 0.1, "blendOut": 0.4, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.88, "speedRangeMax": 1.12, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 1006, "clipKey": "State1-5", "clipName": "State1-5", "animType": "Boy", "canBlend": true, "blendIn": 0, "blendOut": 0.4, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.83, "speedRangeMax": 1, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [1], "animBlendIn": [0.07, 0.1], "blendOutType": [], "animBlendOut": []}, {"MainKey": 1007, "clipKey": "State1-6", "clipName": "State1-6", "animType": "Boy", "canBlend": true, "blendIn": 0, "blendOut": 0.4, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.88, "speedRangeMax": 1.12, "footIK": false, "animAdditionTag": "explore_sentence_not_end", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 1009, "clipKey": "State1-8", "clipName": "State1-8", "animType": "Boy", "canBlend": true, "blendIn": 0.1, "blendOut": 0.4, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.88, "speedRangeMax": 1.12, "footIK": false, "animAdditionTag": "explore_sentence_not_end", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 1010, "clipKey": "State2-2", "clipName": "State2-2", "animType": "Boy", "canBlend": true, "blendIn": 0.05, "blendOut": 0.55, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.88, "speedRangeMax": 1.12, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 1011, "clipKey": "State2-3", "clipName": "State2-3", "animType": "Boy", "canBlend": true, "blendIn": 0.05, "blendOut": 0.55, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.88, "speedRangeMax": 1.12, "footIK": false, "animAdditionTag": "explore_sentence_end", "animBlendType": 1, "blendInType": [1], "animBlendIn": [0, 0.08], "blendOutType": [], "animBlendOut": []}, {"MainKey": 1012, "clipKey": "State2-4", "clipName": "State2-4", "animType": "Boy", "canBlend": true, "blendIn": 0.1, "blendOut": 0.22, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.88, "speedRangeMax": 1.12, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [1], "animBlendIn": [0.1, 0.12], "blendOutType": [], "animBlendOut": []}, {"MainKey": 1013, "clipKey": "State2-5", "clipName": "State2-5", "animType": "Boy", "canBlend": true, "blendIn": 0.1, "blendOut": 0.3, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.88, "speedRangeMax": 1.12, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 1014, "clipKey": "State2-6", "clipName": "State2-6", "animType": "Boy", "canBlend": true, "blendIn": 0.1, "blendOut": 0.28, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.88, "speedRangeMax": 1.12, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 1016, "clipKey": "PointSelf1", "clipName": "PointSelf1", "animType": "Boy", "canBlend": true, "blendIn": 0.2, "blendOut": 0.25, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.8, "speedRangeMax": 1.2, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [1], "animBlendIn": [0.05, 0.2], "blendOutType": [], "animBlendOut": []}, {"MainKey": 1018, "clipKey": "PointPlayer1", "clipName": "PointPlayer1", "animType": "Boy", "canBlend": true, "blendIn": 0.2, "blendOut": 0.25, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.8, "speedRangeMax": 1.2, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 1020, "clipKey": "TalkingIdle", "clipName": "TalkingIdle", "animType": "Boy", "canBlend": true, "blendIn": 1, "blendOut": 1, "canLoop": true, "loopMax": 1666666600.0, "loopBlendTime": 0.2, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 1021, "clipKey": "DeepIdle1-1", "clipName": "DeepIdle1-1", "animType": "Boy", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 1022, "clipKey": "DeepIdle1-2", "clipName": "DeepIdle1-2", "animType": "Boy", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": true, "loopMax": 88, "loopBlendTime": 0.2, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 1023, "clipKey": "DeepIdle1-3", "clipName": "DeepIdle1-3", "animType": "Boy", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 1024, "clipKey": "DeepIdle2-1", "clipName": "DeepIdle2-1", "animType": "Boy", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 1025, "clipKey": "DeepIdle2-2", "clipName": "DeepIdle2-2", "animType": "Boy", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": true, "loopMax": 88, "loopBlendTime": 0.2, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 1026, "clipKey": "DeepIdle2-3", "clipName": "DeepIdle2-3", "animType": "Boy", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 1027, "clipKey": "ShallowIdle1", "clipName": "ShallowIdle1", "animType": "Boy", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.9, "speedRangeMax": 1.11, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 1028, "clipKey": "ShallowIdle2", "clipName": "ShallowIdle2", "animType": "Boy", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": true, "loopMax": 7, "loopBlendTime": 0.2, "canChangeSpeed": true, "speedRangeMin": 0.9, "speedRangeMax": 1.11, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 1029, "clipKey": "ShallowIdle3", "clipName": "ShallowIdle3", "animType": "Boy", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.9, "speedRangeMax": 1.11, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 1032, "clipKey": "Greeting", "clipName": "Greeting", "animType": "Boy", "canBlend": true, "blendIn": 0.1, "blendOut": 0.5, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.88, "speedRangeMax": 1.12, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 2001, "clipKey": "Idle", "clipName": "Idle", "animType": "Girl", "canBlend": true, "blendIn": 1, "blendOut": 1, "canLoop": true, "loopMax": 1666666600.0, "loopBlendTime": 0.2, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 2002, "clipKey": "TalkingIdle", "clipName": "TalkingIdle", "animType": "Girl", "canBlend": true, "blendIn": 1, "blendOut": 1, "canLoop": true, "loopMax": 1666666600.0, "loopBlendTime": 0.2, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 2003, "clipKey": "Greeting", "clipName": "Greeting", "animType": "Girl", "canBlend": true, "blendIn": 0.1, "blendOut": 0.5, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.82, "speedRangeMax": 1.05, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 2004, "clipKey": "State1-1", "clipName": "State1-1", "animType": "Girl", "canBlend": true, "blendIn": 0.1, "blendOut": 0.4, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.82, "speedRangeMax": 1.05, "footIK": false, "animAdditionTag": "explore_sentence_not_end", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 2005, "clipKey": "State1-5", "clipName": "State1-5", "animType": "Girl", "canBlend": true, "blendIn": 0.15, "blendOut": 0.3, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.82, "speedRangeMax": 1.05, "footIK": false, "animAdditionTag": "explore_sentence_not_end", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 2006, "clipKey": "State1-6", "clipName": "State1-6", "animType": "Girl", "canBlend": true, "blendIn": 0.1, "blendOut": 0.3, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.82, "speedRangeMax": 1.05, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 2007, "clipKey": "State1-3", "clipName": "State1-3", "animType": "Girl", "canBlend": true, "blendIn": 0.1, "blendOut": 0.15, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.82, "speedRangeMax": 1.05, "footIK": false, "animAdditionTag": "explore_sentence_not_end", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 2008, "clipKey": "State1-4", "clipName": "State1-4", "animType": "Girl", "canBlend": true, "blendIn": 0.1, "blendOut": 0.15, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.82, "speedRangeMax": 1.05, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 2009, "clipKey": "State1-7", "clipName": "State1-7", "animType": "Girl", "canBlend": true, "blendIn": 0.1, "blendOut": 0.15, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.82, "speedRangeMax": 1.05, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 2010, "clipKey": "State1-8", "clipName": "State1-8", "animType": "Girl", "canBlend": true, "blendIn": 0.1, "blendOut": 0.15, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.82, "speedRangeMax": 1.05, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 2013, "clipKey": "State2-3", "clipName": "State2-3", "animType": "Girl", "canBlend": true, "blendIn": 0.1, "blendOut": 0.3, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.82, "speedRangeMax": 1.05, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [1], "animBlendIn": [0.03, 0.1], "blendOutType": [1], "animBlendOut": [0, 0.18]}, {"MainKey": 2014, "clipKey": "State2-4", "clipName": "State2-4", "animType": "Girl", "canBlend": true, "blendIn": 0.1, "blendOut": 0.17, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.82, "speedRangeMax": 1.05, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 2015, "clipKey": "State2-5", "clipName": "State2-5", "animType": "Girl", "canBlend": true, "blendIn": 0.1, "blendOut": 0.2, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.82, "speedRangeMax": 1.05, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 2016, "clipKey": "State2-2", "clipName": "State2-2", "animType": "Girl", "canBlend": true, "blendIn": 0.1, "blendOut": 0.38, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.82, "speedRangeMax": 1.05, "footIK": false, "animAdditionTag": "explore_sentence_not_end", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 2018, "clipKey": "PointSelf1", "clipName": "PointSelf1", "animType": "Girl", "canBlend": true, "blendIn": 0.2, "blendOut": 0.38, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.8, "speedRangeMax": 1.2, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 2020, "clipKey": "PointPlayer1", "clipName": "PointPlayer1", "animType": "Girl", "canBlend": true, "blendIn": 0.08, "blendOut": 0.25, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.8, "speedRangeMax": 1.2, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 2023, "clipKey": "DeepIdle1-1", "clipName": "DeepIdle1-1", "animType": "Girl", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 2024, "clipKey": "DeepIdle1-2", "clipName": "DeepIdle1-2", "animType": "Girl", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": true, "loopMax": 88, "loopBlendTime": 0.2, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 2025, "clipKey": "DeepIdle1-3", "clipName": "DeepIdle1-3", "animType": "Girl", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 2026, "clipKey": "DeepIdle2-1", "clipName": "DeepIdle2-1", "animType": "Girl", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 2027, "clipKey": "DeepIdle2-2", "clipName": "DeepIdle2-2", "animType": "Girl", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": true, "loopMax": 88, "loopBlendTime": 0.2, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 2028, "clipKey": "DeepIdle2-3", "clipName": "DeepIdle2-3", "animType": "Girl", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 2029, "clipKey": "ShallowIdle1", "clipName": "ShallowIdle1", "animType": "Girl", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.9, "speedRangeMax": 1.11, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 2030, "clipKey": "ShallowIdle2", "clipName": "ShallowIdle2", "animType": "Girl", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": true, "loopMax": 7, "loopBlendTime": 0.2, "canChangeSpeed": true, "speedRangeMin": 0.9, "speedRangeMax": 1.11, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 2031, "clipKey": "ShallowIdle3", "clipName": "ShallowIdle3", "animType": "Girl", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.9, "speedRangeMax": 1.11, "footIK": false, "animAdditionTag": "", "animBlendType": 1, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3001, "clipKey": "Idle", "clipName": "Idle", "animType": "all", "canBlend": true, "blendIn": 1, "blendOut": 1, "canLoop": true, "loopMax": 1666666600.0, "loopBlendTime": 0.2, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3002, "clipKey": "State1-1", "clipName": "State1-1", "animType": "all", "canBlend": true, "blendIn": 0, "blendOut": 0.25, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.8, "speedRangeMax": 1.25, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3003, "clipKey": "State1-2", "clipName": "State1-2", "animType": "all", "canBlend": true, "blendIn": 0, "blendOut": 0.25, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.8, "speedRangeMax": 1.25, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3004, "clipKey": "State1-3", "clipName": "State1-3", "animType": "all", "canBlend": true, "blendIn": 0, "blendOut": 0.25, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.8, "speedRangeMax": 1.25, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3005, "clipKey": "State1-4", "clipName": "State1-4", "animType": "all", "canBlend": true, "blendIn": 0, "blendOut": 0.25, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.8, "speedRangeMax": 1.25, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3006, "clipKey": "State1-5", "clipName": "State1-5", "animType": "all", "canBlend": true, "blendIn": 0, "blendOut": 0.25, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.8, "speedRangeMax": 1.25, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3007, "clipKey": "State1-6", "clipName": "State1-6", "animType": "all", "canBlend": true, "blendIn": 0, "blendOut": 0.25, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.8, "speedRangeMax": 1.25, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3008, "clipKey": "State2-1", "clipName": "State2-1", "animType": "all", "canBlend": true, "blendIn": 0, "blendOut": 0.2, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.8, "speedRangeMax": 1.25, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3009, "clipKey": "State2-2", "clipName": "State2-2", "animType": "all", "canBlend": true, "blendIn": 0, "blendOut": 0.2, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.8, "speedRangeMax": 1.25, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3010, "clipKey": "State2-3", "clipName": "State2-3", "animType": "all", "canBlend": true, "blendIn": 0, "blendOut": 0.2, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.8, "speedRangeMax": 1.25, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3011, "clipKey": "State2-4", "clipName": "State2-4", "animType": "all", "canBlend": true, "blendIn": 0, "blendOut": 0.2, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.8, "speedRangeMax": 1.25, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3012, "clipKey": "State2-5", "clipName": "State2-5", "animType": "all", "canBlend": true, "blendIn": 0, "blendOut": 0.2, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.8, "speedRangeMax": 1.25, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3013, "clipKey": "State2-6", "clipName": "State2-6", "animType": "all", "canBlend": true, "blendIn": 0, "blendOut": 0.2, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.8, "speedRangeMax": 1.25, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3014, "clipKey": "State3-1", "clipName": "State3-1", "animType": "all", "canBlend": true, "blendIn": 0, "blendOut": 0.15, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.8, "speedRangeMax": 1.25, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3015, "clipKey": "State3-2", "clipName": "State3-2", "animType": "all", "canBlend": true, "blendIn": 0, "blendOut": 0.15, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.8, "speedRangeMax": 1.25, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3016, "clipKey": "PointSelf1", "clipName": "PointSelf1", "animType": "all", "canBlend": true, "blendIn": 0, "blendOut": 0.33, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.66, "speedRangeMax": 1.66, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3017, "clipKey": "PointSelf2", "clipName": "PointSelf2", "animType": "all", "canBlend": true, "blendIn": 0, "blendOut": 0.33, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.66, "speedRangeMax": 1.66, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3018, "clipKey": "PointPlayer1", "clipName": "PointPlayer1", "animType": "all", "canBlend": true, "blendIn": 0, "blendOut": 0.33, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.66, "speedRangeMax": 1.66, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3019, "clipKey": "PointPlayer2", "clipName": "PointPlayer2", "animType": "all", "canBlend": true, "blendIn": 0, "blendOut": 0.33, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.66, "speedRangeMax": 1.66, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3020, "clipKey": "TalkingIdle", "clipName": "TalkingIdle", "animType": "all", "canBlend": true, "blendIn": 1, "blendOut": 1, "canLoop": true, "loopMax": 1666666600.0, "loopBlendTime": 0.2, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3021, "clipKey": "DeepIdle1-1", "clipName": "DeepIdle1-1", "animType": "all", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3022, "clipKey": "DeepIdle1-2", "clipName": "DeepIdle1-2", "animType": "all", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": true, "loopMax": 88, "loopBlendTime": 0.2, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3023, "clipKey": "DeepIdle1-3", "clipName": "DeepIdle1-3", "animType": "all", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3024, "clipKey": "DeepIdle2-1", "clipName": "DeepIdle2-1", "animType": "all", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3025, "clipKey": "DeepIdle2-2", "clipName": "DeepIdle2-2", "animType": "all", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": true, "loopMax": 88, "loopBlendTime": 0.2, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3026, "clipKey": "DeepIdle2-3", "clipName": "DeepIdle2-3", "animType": "all", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3027, "clipKey": "ShallowIdle1", "clipName": "ShallowIdle1", "animType": "all", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.9, "speedRangeMax": 1.11, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3028, "clipKey": "ShallowIdle2", "clipName": "ShallowIdle2", "animType": "all", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": true, "loopMax": 7, "loopBlendTime": 0.2, "canChangeSpeed": true, "speedRangeMin": 0.9, "speedRangeMax": 1.11, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3029, "clipKey": "ShallowIdle3", "clipName": "ShallowIdle3", "animType": "all", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.9, "speedRangeMax": 1.11, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3030, "clipKey": "PointPlayer1-1", "clipName": "PointPlayer1-1", "animType": "all", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.9, "speedRangeMax": 1.11, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 3031, "clipKey": "PointPlayer1-2", "clipName": "PointPlayer1-2", "animType": "all", "canBlend": true, "blendIn": 0, "blendOut": 0.8, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": true, "speedRangeMin": 0.9, "speedRangeMax": 1.11, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 100001, "clipKey": "Correct1", "clipName": "Correct1", "animType": "all", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 100002, "clipKey": "Correct2", "clipName": "Correct2", "animType": "all", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 100003, "clipKey": "Correct3", "clipName": "Correct3", "animType": "all", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 100004, "clipKey": "Recording1", "clipName": "Recording1", "animType": "all", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 100005, "clipKey": "Recording2", "clipName": "Recording2", "animType": "all", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": true, "loopMax": 2147483600.0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 100006, "clipKey": "Recording3", "clipName": "Recording3", "animType": "all", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 100007, "clipKey": "StageBegin", "clipName": "StageBegin", "animType": "all", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 100008, "clipKey": "StageEnd", "clipName": "StageEnd", "animType": "all", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 100009, "clipKey": "Win5", "clipName": "Win5", "animType": "all", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 100010, "clipKey": "Win10-1", "clipName": "Win10-1", "animType": "all", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 100011, "clipKey": "Wrong1", "clipName": "Wrong1", "animType": "all", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 100012, "clipKey": "Win10-2", "clipName": "Win10-2", "animType": "all", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}, {"MainKey": 200001, "clipKey": "Path3DLightUp", "clipName": "Path3DLightUp", "animType": "all", "canBlend": false, "blendIn": 0, "blendOut": 0, "canLoop": false, "loopMax": 0, "loopBlendTime": 0, "canChangeSpeed": false, "speedRangeMin": 0, "speedRangeMax": 0, "footIK": false, "animAdditionTag": "", "animBlendType": 0, "blendInType": [], "animBlendIn": [], "blendOutType": [], "animBlendOut": []}]