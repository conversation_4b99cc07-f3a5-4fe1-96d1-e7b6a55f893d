﻿using ScriptsHot.Game.Modules.Procedure.Drama;
using ScriptsHot.Game.Modules.Procedure.Drama.Intro;
using ScriptsHot.Game.Modules.Procedure.Drama.OnBoarding;
using ScriptsHot.Game.Modules.Procedure.Drama.StoryChat;

namespace ScriptsHot.Game.Modules.Procedure
{
    public partial class ProcedureManager
    {
       
        private void PackDramaPlayerToPoint(object p)
        {
            Insert<DramaPlayerToPoint>(p);
        }
        
        private void PackDramaLookAvatarTalkToAvatar(object p)
        {
            Insert<DramaLookAvatarTalkToAvatar>(p);
        }

        #region rolePlay
        /// <summary>
        /// 聊天场景描述
        /// </summary>
        /// <param name="p"></param>
        private void PackDramaRolePlayChatSceneDesc(object p)
        {
            Insert<DramaRolePlayChatSceneDesc>(p);
        }
        /// <summary>
        /// 聊天avatar内容
        /// </summary>
        /// <param name="p"></param>
        private void PackDramaRolePlayChatAvatar(object p)
        {
            VFDebug.Log("开始显示  avatar drama");
            Insert<DramaRolePlayChatAvatar>(p);
        }
        
        /// <summary>
        /// 播放rolePlay 音频
        /// </summary>
        /// <param name="p"></param>
        private void PackDramaRolePlayAudioPlay(object p)
        {
            VFDebug.Log("开始显示  avatar audio drama");
            Insert<DramaRolePlayAudioPlay>(p);
        }
        
        /// <summary>
        /// 聊天player内容
        /// </summary>
        /// <param name="p"></param>
        private void PackDramaRolePlayChatPlayer(object p)
        {
            VFDebug.Log("开始显示  player drama");
            Insert<DramaRolePlayChatPlayer>(p);
        }
        #endregion

        #region tutor

        /// <summary>
        /// 聊天avatar内容
        /// </summary>
        /// <param name="p"></param>
        private void PackDramaTutorChatAvatar(object p)
        {
            VFDebug.Log("开始显示 tutor  avatar drama");
            Insert<DramaTutorChatAvatar>(p);
        }
        
        /// <summary>
        /// 播放tutor 音频
        /// </summary>
        /// <param name="p"></param>
        private void PackDramaTutorAudioPlay(object p)
        {
            VFDebug.Log("开始显示  tutuor avatar audio drama");
            Insert<DramaTutorAudioPlay>(p);
        }
        
        /// <summary>
        /// 聊天player内容
        /// </summary>
        /// <param name="p"></param>
        private void PackDramaTutorChatPlayer(object p)
        {
            VFDebug.Log("开始显示 tutor  player drama");
            Insert<DramaTutorChatPlayer>(p);
        }
        
        /// <summary>
        /// 聊天image内容
        /// </summary>
        /// <param name="p"></param>
        private void PackDramaTutorChatImage(object p)
        {
            VFDebug.Log("开始显示 tutor  image drama");
            Insert<DramaTutorChatImage>(p);
        }
        
        #endregion

        #region chat  public 
        /// <summary>
        /// 聊天 脚手架内容
        /// </summary>
        /// <param name="p"></param>
        private void PackDramaChatSocial(object p)
        {
            VFDebug.Log("开始显示 Social drama");
            Insert<DramaChatSocial>(p);
        }
        #endregion

        #region Explore

               
        /// <summary>
        /// Explore Entity 进入时候播放音频
        /// </summary>
        /// <param name="p"></param>
        private void PackDramaExploreEntityEnterAudioPlay(object p)
        {
            Insert<DramaExploreEntityEnterAudioPlay>(p);
        }
        
        /// <summary>
        /// Explore Entity 进入时候显示avatar cell
        /// </summary>
        /// <param name="p"></param>
        private void PackDramaExploreEntityEnterAvatarCellShow(object p)
        {
            Insert<DramaExploreEntityEnterAvatarCellShow>(p);
        }
        
        /// <summary>
        /// Explore Entity 进入时候显示avatar translate
        /// </summary>
        /// <param name="p"></param>
        private void PackDramaExploreEntityEnterAvatarTranslateShow(object p)
        {
            Insert<DramaExploreEntityEnterAvatarTranslateShow>(p);
        }
        
        /// <summary>
        /// Explor  Entity 进入时候显示avatar
        /// </summary>
        /// <param name="p"></param>
        private void PackDramaExploreEntityAvatarShow(object p)
        {
            Insert<DramaExploreEntityAvatarShow>(p);
        }
        
        /// <summary>
        /// Explore Entity 显示player cell
        /// </summary>
        /// <param name="p"></param>
        private void PackDramaExploreEntityEnterPlayerCellShow(object p)
        {
            Insert<DramaExploreEntityPlayerCellShow>(p);
        }
        
        /// <summary>
        /// Explore Entity 播放avatar audio
        /// </summary>
        /// <param name="p"></param>
        private void PackDramaExploreEntityAvatarAudioPlay(object p)
        {
            InsertFirst<DramaExploreEntityAvatarAudioPlay>(p);
        }

        /// <summary>
        /// Explore Entity 显示脚手架
        /// </summary>
        /// <param name="p"></param>
        private void PackDramaExploreEntityScaffoldShow(object p)
        {
            Insert<DramaExploreEntityScaffoldShow>(p);
        }

        /// <summary>
        /// Explore Entity 显示记录UI
        /// </summary>
        /// <param name="p"></param>
        private void PackDramaExploreEntityRecordUIShow(object p)
        {
            Insert<DramaExploreEntityRecordUIShow>(p);
        }
        
        private void PackDramaExploreChatEnd(object p)
        {
            Insert<DramaExploreChatEnd>(p);
        }
        

        #endregion

        #region OnBoarding

        private void PackDramaOnBoardingAvatarAudioPlay(object p)
        {
            InsertFirst<DramaOnboardingAudioPlay>(p);
        }
        
        private void PackDramaOnboardingChatEnd(object p)
        {
            Insert<DramaOnboardingChatEnd>(p);
        }
        
        #endregion

        #region intro

        private void PackDramaIntroAudioPlay(object p)
        {
            Insert<DramaExploreIntroAudioPlay>(p);
        }
        
        private void PackDramaIntroOver(object p)
        {
            Insert<DramaExploreIntroOver>(p);
        }
        
        #endregion
    }
}