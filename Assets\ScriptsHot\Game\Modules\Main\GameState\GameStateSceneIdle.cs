﻿using Lean.Touch;
using System.Collections.Generic;
//using Game.Modules.Flow;
using Msg.incentive;
using ScriptsHot.Game.Modules.Main;
using ScriptsHot.Game.Modules.Scene.Level;
using ScriptsHot.Game.Modules.Scene.Level.Component;
using ScriptsHot.Game.Modules.Scene.Level.Homepage;
using UnityEngine;

/// <summary>
/// 目前SceneIdle 与SceneLoading 的GameState 依旧在最顶层处理 几类问题
/// 1 切换前|中（SceneLoading），切换后（SceneIdle）自动根据场景类别控制 各自默认UI的显+隐+刷新（主要是MainHeader）
/// 2 自动更新信息or状态 （触发网络请求）
/// 3 自动清理 map unlock unit
/// 4 判定是否进 onboard状态
/// </summary>
public class GameStateSceneIdle : GameStateBase
{
    private SceneController _sceneController;
    private Level _gameScene;
    private bool _isLockInputEvents = false;
    private bool _isLoadedGuide = false;
    private string initTimerKey0 = null;
    public GameStateSceneIdle() : base(GameState.SceneIdle) { }

    public override void OnEnter(params object[] args)
    {
        base.OnEnter(args);
        this._sceneController = this.owner.GetController<SceneController>(ModelConsts.Scene);
        this._gameScene = this._sceneController.scene;

        var onboardCtrl = GameEntry.LoginC.GetModel<LoginOnBoardingModel>(ModelConsts.Login);

        if (GameEntry.LoginC.GetController<LoginController>(ModelConsts.Login).isOnRequestFirstTask)
        {
            GameEntry.LoginC.GetController<LoginController>(ModelConsts.Login).TryReconnectGuide();
        }
        
        if (this._gameScene.sceneType == ESceneType.World)
        {
            PerformanceSaver.instance.UseLowFPS();
            this.owner.GetUI(UIConsts.SceneLocate).Show();
            // this.owner.GetUI<MainHeaderUI>(UIConsts.MainHeader).SetHeader(MainHeaderMode.Outdoor);
            // this.owner.GetUI(UIConsts.MainHeader).Show();
            this.owner.GetUI(UIConsts.WorldHomepageUI).Show().onCompleted += () =>
            {
                this.owner.GetUI<WorldHomepageUI>(UIConsts.WorldHomepageUI).SetStatus(WorldHomepageMode.outdoor);
            };
        }
        else if(this._gameScene.sceneType == ESceneType.HomepageRoom)
        {
            Debug.Log("ENTER homepage");
            PerformanceSaver.instance.UseNormalFPS();
            //ControllerManager.instance.GetController<HomepageController>(ModelConsts.Homepage).ShowLivingRoomBgTabView();
            ControllerManager.instance.GetController<HomepageController>(ModelConsts.Homepage).ShowLessonView();
            this.owner.GetUI(UIConsts.WorldHomepageUI).Hide();
        }
        else 
        {
            //indoor+home+??

            // this.owner.GetUI<MainHeaderUI>(UIConsts.MainHeader).SetHeader(MainHeaderMode.Indoor);
            // this.owner.GetUI(UIConsts.MainHeader).Show();
            this.owner.GetUI(UIConsts.WorldHomepageUI).Show().onCompleted += () =>
            {
                this.owner.GetUI<WorldHomepageUI>(UIConsts.WorldHomepageUI).SetStatus(WorldHomepageMode.indoor);
            };
        }

        //todo0 大概率是废弃的功能 owner.SendNotification(RecommendCardCallEvent.OnResetRecommentState);
        this._gameScene.GetComponent<UnitComponent>().UnLockAllUnit();//退回到自由状态时清理所有Unit的锁定

        owner.GetController<CurrencyController>(ModelConsts.CurrencyController).SendGetEconomicInfoReq(GameEventName.GameEnter);//更新金币
        // 刷新大地图货币信息，打卡信息
        // if (owner.GetUI<MainHeaderUI>(UIConsts.MainHeader).isShow)
        // {
        //     owner.GetUI<MainHeaderUI>(UIConsts.MainHeader).Refresh();
        // }
    }

    public override void OnReEnter(params object[] args)
    {
        OnEnter();
    }

    public void SetInputEventActive(bool active)
    {
        _isLockInputEvents = !active;
        VFDebug.Log("InputEventLock:" + _isLockInputEvents);
    }

    public override void OnExit()
    {
        base.OnExit();
        //暂时不隐藏头像
        //this.owner.GetUI(UIConsts.SceneLocate).Hide();
        GameEntry.LoginC.GetController<MainController>(ModelConsts.Main).ClearActionCache(GameState.SceneIdle);
    }

    public override void OnTopLeftBack()
    {
        this.owner.GetController<SceneController>(ModelConsts.Scene).ReqChangeNextScene();
    }
}