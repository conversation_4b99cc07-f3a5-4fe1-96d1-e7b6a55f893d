﻿using System.Collections.Generic;
using Game.Modules.FragmentPractice;
using Msg.basic;
using Msg.course;
using Msg.question_process;
using ScriptsHot.Game.Modules.FragmentPractice;
using UIBind.FragmentPractice;
using UnityEngine;

public class PhoneQuestionController : BaseController
{
    public PhoneQuestionController() : base(ModelConsts.PhoneQuestion) { }
    private PhoneQuestionModel phoneQuestionModel => GetModel<PhoneQuestionModel>(ModelConsts.PhoneQuestion);
    private FragmentPracticeController fragmentPracticeController => GetController<FragmentPracticeController>(ModelConsts.FragmentPractice);
    public override void OnInit()
    {
        RegisterModel(new PhoneQuestionModel());
        RegisterUI(new PhoneQuestionUI(UIConsts.PhoneQuestion));

        BindComponents();
        
        MsgManager.instance.RegisterCallBack<SC_GetRadioDataAck>(OnGetRadioDataAck);
    }

    private void BindComponents()
    {
        BtnAudio.Bind();
        BtnBaseAnswer.Bind();
        BiSelectAnswer.Bind();
        MatchAnswer.Bind();
        PhoneQuestion.Bind();
        // PhoneInstruction.Bind();
        AudioSelectAnswer.Bind();
        MultipleChoiceAnswer.Bind();
    }

    private void OnGetRadioDataAck(SC_GetRadioDataAck resp)
    {
        if (resp.code != 0) return;
        if (resp.data == null)
        {
            GetUI<CommConfirmUI>(UIConsts.CommConfirm).Open("服务器返回数据为空", () =>
            {
                GetUI(UIConsts.PhoneQuestion).Hide();
            }, null, 1, null, I18N.inst.MoStr("common_check"));
            return;
        }
        DotPracticeManager.Instance.recordId = resp.data.session_record_id;

        PhoneQuestionGrp questionGrp = new PhoneQuestionGrp(resp.data);
        phoneQuestionModel.QuestionGrp = questionGrp;
        phoneQuestionModel.CurQuestion = questionGrp.CurQuestions[0];
        phoneQuestionModel.StartTimeStamp = Time.realtimeSinceStartup;
        phoneQuestionModel.CurQuestions.AddRange(questionGrp.CurQuestions);
        phoneQuestionModel.sessionTitle = resp.data.title;
        
        phoneQuestionModel.AvatarInfos.AddRange(resp.data.avatar_info_list);        
        phoneQuestionModel.AvatarBgInfos.AddRange(resp.data.bg_info_list);
        phoneQuestionModel.Reset();
        fragmentPracticeController.SessionRecordId = resp.data.session_record_id;
        
        for (int i = 0; i < resp.data.element_data_list.Count; i++)
        {
            var data = resp.data.element_data_list[i];
            if (data.element_type == PB_ElementTypeEnum.EleTAvatarText)
            {
                if (phoneQuestionModel.curAvatar == 0)
                {
                    phoneQuestionModel.curAvatar = data.avatar_text_data.avatar_id;

                }
                else if (phoneQuestionModel.otherAvatar == 0 && phoneQuestionModel.curAvatar != data.avatar_text_data.avatar_id)
                {
                    phoneQuestionModel.otherAvatar = data.avatar_text_data.avatar_id;
                }
            }
            else if (data.element_type == PB_ElementTypeEnum.EleTProse)
            {
                //skip 
            }
            else
            {
                phoneQuestionModel.allQuestionNum++;
            }
        }
        
        PrepareTotalTts();
        
        if (!GetUI<PhoneQuestionUI>(UIConsts.PhoneQuestion).isShow)
        {
            // Debug.LogError("PhoneQuestionUI is not show");
            GetUI<PhoneQuestionUI>(UIConsts.PhoneQuestion).Show().onCompleted += () =>
            {
                GetUI<PhoneQuestionUI>(UIConsts.PhoneQuestion).FinalShow();
            };
        }
        else
        {
            GetUI<PhoneQuestionUI>(UIConsts.PhoneQuestion).FinalShow();
        }
    }
    
    private void FailedEnterPractice(GRPCManager.ErrorType et,Google.Protobuf.IMessage msg)
    {
        GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N("ui_socialchat_network_timeout", () =>
        {
            GetUI<PhoneQuestionUI>(UIConsts.PhoneQuestion).Hide();
        }, null, 1, null, "common_check");
    }

    public void GetRadioDataReq(long taskId)
    {
        CS_GetRadioDataReq req = new CS_GetRadioDataReq();
        req.session_id = taskId;
        MsgManager.instance.SendMsg(req, FailedEnterPractice);

        GetUI<PhoneQuestionUI>(UIConsts.PhoneQuestion).Show();
    }

    public void ReqSettlement()
    {
        GetModel<FragmentPracticeModel>(ModelConsts.FragmentPractice).FirstAnswerDecisions =
            phoneQuestionModel.FirstAnswerDecisions;
        fragmentPracticeController.ReqSettlement();
    }
    
    /// <summary>
    /// 中途退出上报服务器 不等回复
    /// </summary>
    public void SendExitQuickPracticeReq()
    {
        PB_SubmitUnit answer = new PB_SubmitUnit();
        foreach (var val in phoneQuestionModel.FirstAnswerDecisions.Values)
        {
            answer.answer_list.Add(new PB_SubmitAnswer()
            {
                question_id = val.Id,
                decision = val.IsRight
                    ? PB_PracticeAnswerDecision.PracticeAnswerDecision_Right
                    : PB_PracticeAnswerDecision.PracticeAnswerDecision_Wrong,
            });
        }

        for (int i = phoneQuestionModel.FirstAnswerDecisions.Count; i < phoneQuestionModel.CurQuestions.Count; i++)
        {
            answer.answer_list.Add(new PB_SubmitAnswer()
            {
                question_id = phoneQuestionModel.CurQuestions[i].QuestionId,
                decision = PB_PracticeAnswerDecision.PracticeAnswerDecision_Noanswer,
            });
        }

        answer.unit_id = phoneQuestionModel.QuestionGrp.GroupId;
        answer.cost_seconds = (long)(Time.realtimeSinceStartup - phoneQuestionModel.StartTimeStamp);
            
        var msg = new CS_ExitQuickPracticeReq();
        msg.user_id = GetModel<MainModel>(ModelConsts.Main).userID;
        msg.unit_list.Add(answer);
        MsgManager.instance.SendMsg(msg);
    }
    
    public void DoAnswerError()
    {
        phoneQuestionModel.ComboNum = 0;
        // phoneQuestionModel.CurQuestionNum++;
        phoneQuestionModel.ErrorQuestionCount++;
        phoneQuestionModel.FirstAnswerDecisions[phoneQuestionModel.CurQuestion.QuestionId] = 
            new FragmentPracticeModel.QuestionAnswerData()
            {
                Duration = 1000 * (Time.realtimeSinceStartup - phoneQuestionModel.CurQuestion.StartTimeSecond),
                Id = phoneQuestionModel.CurQuestion.QuestionId,
                QuestionType = phoneQuestionModel.CurQuestion.QuestionType,
                IsRight = false,
                RecommendInfo = phoneQuestionModel.CurQuestion.ServerRecommend,
            };
    }

    public void DoAnswerCorrect()
    {
        var isCompletelyRight = !QuestionEventManager.Instance.HasHinted;
        phoneQuestionModel.ComboNum++;
        if (!isCompletelyRight) phoneQuestionModel.CorrectNum++;
        phoneQuestionModel.CurQuestionNum++;

        phoneQuestionModel.FirstAnswerDecisions[phoneQuestionModel.CurQuestion.QuestionId] =
            new FragmentPracticeModel.QuestionAnswerData()
            {
                Duration = 1000 * (Time.realtimeSinceStartup - phoneQuestionModel.CurQuestion.StartTimeSecond),
                Id = phoneQuestionModel.CurQuestion.QuestionId,
                QuestionType = phoneQuestionModel.CurQuestion.QuestionType,
                IsRight = isCompletelyRight,
                RecommendInfo = phoneQuestionModel.CurQuestion.ServerRecommend,
            };
    }
    
    
    public bool NextQuestion()
    {
        phoneQuestionModel.CurQuestionIdx++;
        int curIndex = phoneQuestionModel.CurQuestionIdx;
        if (curIndex == phoneQuestionModel.CurQuestions.Count)
        {
            phoneQuestionModel.CurQuestionIdx--;
            return false;
        }

        if (phoneQuestionModel.CurQuestions.Count == 0)
        {
            phoneQuestionModel.CurQuestionIdx--;
            return false;
        }

        
        VFDebug.Log($"NextQuestion： {phoneQuestionModel.CurQuestionIdx}");
        phoneQuestionModel.CurQuestion = phoneQuestionModel.CurQuestions[phoneQuestionModel.CurQuestionIdx];
        return true;
    }
    
    public bool PreQuestion()
    {
        int curIndex = phoneQuestionModel.CurQuestionIdx;
        if (curIndex - 1 < 0)
        {
            return false; 
        }
        phoneQuestionModel.CurQuestionIdx--;
        VFDebug.Log($"PreQuestion： {phoneQuestionModel.CurQuestionIdx}");
        phoneQuestionModel.CurQuestion = phoneQuestionModel.CurQuestions[phoneQuestionModel.CurQuestionIdx];
        return true;
    }

    public APracticeData GetNextQuestion()
    {
        int curIndex = phoneQuestionModel.CurQuestionIdx;

        if (curIndex + 1 < phoneQuestionModel.CurQuestions.Count)
        {
            return phoneQuestionModel.CurQuestions[curIndex + 1];
        }

        return phoneQuestionModel.NextQuestions.Count > 0
            ? phoneQuestionModel.NextQuestions[0]
            : phoneQuestionModel.CurQuestions[^1];
    }

    public APracticeData GetPrevQuestion()
    {
        int curIndex = phoneQuestionModel.CurQuestionIdx;
        if (curIndex - 1 >= 0)
        {
            return phoneQuestionModel.CurQuestions[curIndex - 1];
        }

        return null;
    }

    public void QuitPractice()
    {
        phoneQuestionModel.QuestionGrp = null;
        phoneQuestionModel.CurQuestion = null;
        phoneQuestionModel.ComboNum = 0;
        phoneQuestionModel.CorrectNum = 0;
        phoneQuestionModel.InitProgress = 7f;
        phoneQuestionModel.CurQuestionIdx = 0;
        phoneQuestionModel.CurQuestionNum = 0;
        phoneQuestionModel.ErrorQuestionCount = 0;
        phoneQuestionModel.CurQuestions.Clear();
        phoneQuestionModel.NextQuestions.Clear();
        phoneQuestionModel.FirstAnswerDecisions.Clear();
    }

    //整理全部题目和题干的TTS
    private void PrepareTotalTts()
    {
        List<long> totalTTS = new();
        foreach (var question in phoneQuestionModel.QuestionGrp.CurQuestions)
        {
            long[] ttsOptions = question.GetAnswerOptionTts();
            if (question.AudioId > 0)
                totalTTS.Add(question.AudioId);
            foreach (var tts in ttsOptions)
                if (tts > 0)
                    totalTTS.Add(tts);
        }
            
        Preload(totalTTS);
    }

    private async void Preload(List<long> totalTts)
    {
        TTSPreloader.Instance.Clear();
        await TTSPreloader.Instance.PreloadTTS(totalTts,
            () =>
            {
                //有需要就干点其他的
            });
    }
}
