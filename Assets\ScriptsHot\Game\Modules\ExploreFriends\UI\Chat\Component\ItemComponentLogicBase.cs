﻿using FairyGUI;

namespace UIBind.ExploreFriends.Component
{
    public class ItemComponentLogicBase:ComponentObject,IComponetOwner
    {

        protected ExploreTranslWordComponent _translateWordComponent;

        protected GComponent _ui;
        public virtual void Init()
        {
            _translateWordComponent = this.AddComponent<ExploreTranslWordComponent>() as ExploreTranslWordComponent;
            InitComponents();
        }

        public void SetUI(GComponent ui)
        {
            _ui = ui;
            UIReady();
        }
        
        public virtual void UIReady()
        {

        }
        public GComponent GetUI()
        {
            return _ui;
        }

        public void AddEvent()
        {
        }

        public void UnEvent()
        {
        }
    }
}