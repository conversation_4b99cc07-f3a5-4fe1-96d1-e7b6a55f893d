/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class AvatarHead : GComponent
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "AvatarHead";
        public static string url => "ui://cmoz5osjw1ks4d";

        public GComponent img;
        public GLoader nameBg;
        public GTextField tfName;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(AvatarHead));
        }

        public override void ConstructFromXML(XML xml)
        {
            img = GetChildAt(0) as GComponent;
            nameBg = GetChildAt(1) as GLoader;
            tfName = GetChildAt(2) as GTextField;
        }
        public override void Dispose()
        {
            img = null;
            nameBg = null;
            tfName = null;

            base.Dispose();
        }
    }
}