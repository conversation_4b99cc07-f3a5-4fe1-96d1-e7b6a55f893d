/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class HideShowQuestion : AFragQuestion
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "HideShowQuestion";
        public static string url => "ui://cmoz5osjz7rm2m";

        public Controller showStem;
        public GRichTextField tfSentence;
        public BtnAudio btnPlay;
        public GButton btnDisplay;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(HideShowQuestion));
        }

        public override void ConstructFromXML(XML xml)
        {
            showStem = GetControllerAt(0);
            tfSentence = GetChildAt(0) as GRichTextField;
            btnPlay = GetChildAt(1) as BtnAudio;
            btnDisplay = GetChildAt(3) as GButton;

            SetMultiLanguageInChildren();
        }
        public override void Dispose()
        {
            showStem = null;
            tfSentence = null;
            btnPlay = null;
            btnDisplay = null;

            base.Dispose();
        }
        public void SetMultiLanguageInChildren()
        {
            this.btnDisplay.SetKey("ui_fragment_display");  // ""
        }
    }
}