using System;
using FairyGUI;
using UnityEngine;
using System.Collections.Generic;
using Codice.CM.WorkspaceServer.DataStore.Merge;
using Cysharp.Threading.Tasks;

namespace UIBind.ExploreFriends
{
    public partial class ExploreFriendsIntroducePanelUI : BaseUI<ExploreFriendsIntroducePanel>
    {
        // 弧形列表配置
        private readonly float[] _itemScales = { 0.55f, 0.7f, 1f};
        private readonly float[] _itemYOffsets = { -60f, -20f, 0f };
        private readonly int _centerIndex = 2; // 中心位置索引
        private readonly int _visibleItemCount = 5; // 可见item数量

        private bool _isDragging = false;
        private bool _isAnimating = false;
        private int _currentCenterItemIndex = -1; // 当前中心item的数据索引
        private List<ExploreFriendCfg> _listData = new List<ExploreFriendCfg>(); // 列表数据
        private List<ExploreFriendCfg> _originalData = new List<ExploreFriendCfg>(); // 原始数据（不包含占位项）
        private const int PLACEHOLDER_COUNT = 2; // 左右各添加的占位项数量

        /// <summary>
        /// 将实际数据索引转换为显示索引（包含占位项）
        /// </summary>
        /// <param name="dataIndex">实际数据索引</param>
        /// <returns>显示索引</returns>
        private int DataIndexToDisplayIndex(int dataIndex)
        {
            return dataIndex + PLACEHOLDER_COUNT;
        }

        /// <summary>
        /// 将显示索引转换为实际数据索引
        /// </summary>
        /// <param name="displayIndex">显示索引</param>
        /// <returns>实际数据索引，如果是占位项则返回-1</returns>
        private int DisplayIndexToDataIndex(int displayIndex)
        {
            if (displayIndex < PLACEHOLDER_COUNT || displayIndex >= _listData.Count - PLACEHOLDER_COUNT)
            {
                return -1; // 占位项
            }
            return displayIndex - PLACEHOLDER_COUNT;
        }

        /// <summary>
        /// 检查显示索引是否为占位项
        /// </summary>
        /// <param name="displayIndex">显示索引</param>
        /// <returns>是否为占位项</returns>
        private bool IsPlaceholderIndex(int displayIndex)
        {
            return DisplayIndexToDataIndex(displayIndex) == -1;
        }
        
        private List<string> introduceSoundList = new List<string>();
        private List<string> introduceContentKeyList = new List<string>();
        private int soundIndex = 0;
        public Action OpenPreUI;

        private string soundTimerKey;
        private FriendSlotData friendSlotData;
        
        public ExploreFriendsIntroducePanelUI(string name) : base(name)
        {
        }
        
        

        public override string uiLayer => UILayerConsts.Top;

        protected override bool isFullScreen => true;

        protected override void OnInit(GComponent uiCom)
        {
            base.OnInit(uiCom);

            // 注册按钮点击事件
            AddUIEvent(ui.NextBtn.com.onClick, OnNextBtnClick);
            AddUIEvent(ui.selectList.onClickItem, OnSelectListClick);
            AddUIEvent(ui.CloseBtn.onClick, OnCloseBtnClick);
            // 注册滚动相关事件 - 使用ScrollPane的事件
            if (ui.selectList.scrollPane != null)
            {
                AddUIEvent(ui.selectList.scrollPane.onScroll, OnSelectListScroll);
                AddUIEvent(ui.selectList.scrollPane.onScrollEnd, OnSelectListScrollEnd);
            }
            InitializeList();
        }

        protected override void OnShow()
        {
            base.OnShow();

            ui.TitleTxt.text = I18N.inst.MoStr("ui_explore_friends_preview_title_text");


            friendSlotData = GameEntry.ExFriendC.Model.GetDrawData();
            // 初始化列表数据和显示
            SetupListData();

            // 移动到第一个实际数据项（跳过占位项）
            MoveToCenter(DataIndexToDisplayIndex(0));
            RefreshBtn();

            CheckUIData();
            UpdateItemsDisplay();
        }

        private float listCenterX;
        private float listStartX;
        private float listEndX;
        private float listHalfWidth;
        
        private void CheckUIData()
        {
            if (ui.selectList.numItems < 2) return;

            // 计算中心位置
            listCenterX = ui.selectList.width * 0.5f;
            Debug.LogError($"ui.selectList 中心位置 = {listCenterX}");
            
            GObject item0 = ui.selectList.GetChildren()[0];
            float item0CenterX = item0.x + item0.width * 0.5f;
            GObject item1 = ui.selectList.GetChildren()[1];
            float item1CenterX = item1.x + item1.width * 0.5f;
            float itemDistance = Math.Abs(item0CenterX - item1CenterX);

            listStartX = listCenterX - itemDistance - itemDistance;
            listEndX = listCenterX + itemDistance + itemDistance;
            listHalfWidth = itemDistance * 2f;

            Debug.LogError($"ui.selectList 最小X = {listStartX}");
            Debug.LogError($"ui.selectList 最大X = {listEndX}");
        }

        protected override void OnHide()
        {
            if (!string.IsNullOrEmpty(soundTimerKey))
            {
                TimerManager.instance.UnRegisterTimer(soundTimerKey);
                soundTimerKey = string.Empty;
            }
            SoundManger.instance.StopMusic();
            base.OnHide();
        }

        /// <summary>
        /// 初始化列表设置
        /// </summary>
        private void InitializeList()
        {
            ui.selectList.ClearSelection();
            // 设置列表为横向布局
            ui.selectList.layout = ListLayoutType.SingleRow;
            ui.selectList.scrollItemToViewOnClick = false; // 禁用默认的滚动到视图功能

            // 设置列表渲染回调
            ui.selectList.itemRenderer = RenderListItem;
        }

        /// <summary>
        /// 设置列表数据
        /// </summary>
        private void SetupListData()
        {
            // 保存原始数据
            _originalData.Clear();
            foreach (var cfg in GameEntry.ExFriendC.Model.FriendList)
            {
                _originalData.Add(cfg);
            }

            // 创建包含占位项的完整列表数据
            _listData.Clear();

            // 添加左侧占位项（使用空数据）
            for (int i = 0; i < PLACEHOLDER_COUNT; i++)
            {
                _listData.Add(null);
            }

            // 添加实际数据
            _listData.AddRange(_originalData);

            // 添加右侧占位项（使用空数据）
            for (int i = 0; i < PLACEHOLDER_COUNT; i++)
            {
                _listData.Add(null);
            }

            // 设置列表item数量
            ui.selectList.numItems = _listData.Count;
        }

        /// <summary>
        /// 列表item渲染回调
        /// </summary>
        /// <param name="index">item索引</param>
        /// <param name="item">item对象</param>
        private void RenderListItem(int index, GObject item)
        {
            if (index >= _listData.Count) return;

            ExploreFriendCfg data = _listData[index];

            // 重要：无论item是什么类型，都要设置data用于点击事件
            item.data = index;

            // 如果是占位项，隐藏item并返回
            if (data == null)
            {
                item.visible = false;
                return;
            }

            // 显示实际数据的item
            item.visible = true;

            // 获取IntroduceListItem的具体组件
            // 由于item是GComponent，我们需要通过子组件来访问IntroduceListItem的属性
            GComponent itemComp = item.asCom;
            if (itemComp != null)
            {
                // 根据IntroduceListItem的结构来设置数据
                // IsSelect = Controller at index 0
                Controller isSelectController = itemComp.GetControllerAt(0);

                // selectedImg = GGraph at index 0
                GGraph selectedImg = itemComp.GetChildAt(0) as GGraph;

                // img = GGraph at index 1
                GGraph img = itemComp.GetChildAt(1) as GGraph;

                // headLoader = GLoader at index 2
                GLoader headLoader = itemComp.GetChildAt(2) as GLoader;

                // 设置头像
                if (headLoader != null && !string.IsNullOrEmpty(data.HeadIcon))
                {
                    headLoader.url = data.HeadIcon;
                }

                // 根据解锁状态设置显示
                if (isSelectController != null)
                {
                    isSelectController.selectedIndex = 1;
                }
                itemComp.scale = new Vector3(0.5f,0.5f,1);
            }
        }

        private void OnNextBtnClick()
        {
            ExploreFriendsController.DrawType drawType = ExploreFriendsController.DrawType.consumeDiamondDraw;
            if (GameEntry.ExFriendC.Model.IsFree)
            {
                drawType = ExploreFriendsController.DrawType.freeDraw;
            }
            GameEntry.ExFriendC.SendDrawNewFriendReq(drawType , GameEntry.ExFriendC.Model.DrawSlotIndex);
            
            ui.NextBtn.SetLoadingStatus(true);

            //todo mock
            //GameEntry.ExFriendC.MockOnDrawNewFriendResp();
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void OnCloseBtnClick()
        {
            OpenPreUI?.Invoke();
            Hide();
        }

        /// <summary>
        /// 列表item点击事件
        /// </summary>
        private void OnSelectListClick(EventContext context)
        {
            if (_isAnimating) return;

            // 从事件上下文获取被点击的item
            // onClickItem事件的data参数就是被点击的item对象
            GObject clickedItem = context.data as GObject;
            if (clickedItem?.data != null)
            {
                int clickedIndex = (int)clickedItem.data;
                // 只允许点击实际数据项，不允许点击占位项
                if (!IsPlaceholderIndex(clickedIndex))
                {
                    MoveToCenter(clickedIndex);
                }
            }
        }

        /// <summary>
        /// 列表滚动事件 - 滚动过程中持续触发
        /// </summary>
        private void OnSelectListScroll()
        {
            Debug.Log("OnSelectListScroll");
            if (!_isDragging)
            {
                _isDragging = true;
            }

            // 在滚动过程中实时更新item显示效果
            UpdateItemsDisplay();
        }

        /// <summary>
        /// 列表滚动结束事件 - 滚动停止时触发
        /// </summary>
        private void OnSelectListScrollEnd()
        {
            _isDragging = false;
            Debug.Log("OnSelectListScrollEnd");
            // 滚动结束后，吸附到最近的中心位置
            //SnapToCenter();
        }

        /// <summary>
        /// 将指定索引的item移动到中心位置
        /// </summary>
        /// <param name="targetIndex">目标item的数据索引</param>
        private void MoveToCenter(int targetIndex)
        {
            if (_isAnimating || targetIndex == _currentCenterItemIndex) return;

            _isAnimating = true;
            
            UnselectItem(_currentCenterItemIndex);
            
            _currentCenterItemIndex = targetIndex;
            SelectItem(_currentCenterItemIndex);
            
            // 滚动到目标位置
            ui.selectList.ScrollToView(targetIndex, true, false);

            // 延迟一帧后更新显示，确保滚动完成
            GTween.DelayedCall(0.1f).OnComplete(() =>
            {
                // UpdateItemsDisplay();
                _isAnimating = false;
            });
        }

        /// <summary>
        /// 吸附到最近的中心位置
        /// </summary>
        private void SnapToCenter()
        {
            if (_isAnimating) return;

            // 计算当前最接近中心的item
            int nearestCenterIndex = CalculateNearestCenterIndex();

            if (nearestCenterIndex != _currentCenterItemIndex)
            {
                MoveToCenter(nearestCenterIndex);
            }
            else
            {
                // 如果已经在中心，只需要更新显示
                UpdateItemsDisplay();
            }
        }

        /// <summary>
        /// 计算最接近中心的item索引
        /// </summary>
        /// <returns>最接近中心的item索引</returns>
        private int CalculateNearestCenterIndex()
        {
            // 获取列表的滚动位置
            float scrollPos = ui.selectList.scrollPane.posX;
            float itemWidth = ui.selectList.defaultItemSize.x + ui.selectList.columnGap;

            // 计算最接近中心的item索引
            int nearestIndex = Mathf.RoundToInt(scrollPos / itemWidth);

            // 确保索引在有效范围内
            nearestIndex = Mathf.Clamp(nearestIndex, 0, ui.selectList.numItems - 1);

            return nearestIndex;
        }

        /// <summary>
        /// 更新items的显示效果（缩放和位置）
        /// </summary>
        private void UpdateItemsDisplay()
        {
            if (ui.selectList.numItems == 0) return;

            float scrollPositionX = ui.selectList.scrollPane.posX;
            
            Debug.LogError($"scrollPositionX = {scrollPositionX}");
            // 为每个可见item设置弧形效果
            for (int i = 0; i < ui.selectList.numChildren; i++)
            {
                GObject item = ui.selectList.GetChildren()[i];
                if (item.data == null) continue;
                int itemDataIndex = (int)item.data;
                // 计算item相对于中心的距离
                float itemCenterX = item.x + item.width * 0.5f;
                float distanceFromCenter = Mathf.Abs(itemCenterX - listCenterX);
                
                Debug.LogError($"item 索引 = {itemDataIndex} item 中心位置 = {distanceFromCenter}");
                // 应用缩放和Y偏移
                ApplyArcEffect(item , itemCenterX - scrollPositionX);

                // 如果是中心位置，标记为选中；否则取消选中
                // if (positionIndex == _centerIndex)
                // {
                //     SelectItem(item, itemDataIndex);
                // }
                // else
                // {
                //     UnselectItem(item);
                // }
            }
        }

        /// <summary>
        /// 计算item在5个位置中的索引
        /// </summary>
        /// <param name="itemCenterX">item中心X坐标</param>
        /// <param name="listCenterX">列表中心X坐标</param>
        /// <returns>位置索引(0-4)</returns>
        private int CalculatePositionIndex(float itemCenterX, float listCenterX)
        {
            float distance = itemCenterX - listCenterX;
            float itemWidth = ui.selectList.defaultItemSize.x + ui.selectList.columnGap;

            // 计算相对位置
            int relativePosition = Mathf.RoundToInt(distance / itemWidth);

            // 映射到0-4的范围，中心为2
            int positionIndex = _centerIndex + relativePosition;

            // 确保在有效范围内
            positionIndex = Mathf.Clamp(positionIndex, 0, _visibleItemCount - 1);

            return positionIndex;
        }

        /// <summary>
        /// 应用弧形效果（缩放和Y偏移）
        /// </summary>
        /// <param name="item">要应用效果的item</param>
        /// <param name="itemCenterX">item中心X坐标</param>
        private void ApplyArcEffect(GObject item , float itemCenterX)
        {
            float scale;
            float offsetY;
            if (itemCenterX <= listStartX || itemCenterX >= listEndX)
            {
                scale = _itemScales[0];
                offsetY = _itemYOffsets[0];
            }
            else
            {
                float distance = Math.Abs(itemCenterX - listCenterX);
                float normalizedDistance = distance / listHalfWidth; // 0到1之间的距离比例

                // 使用三段式插值：中心(index=2) -> 中间(index=1) -> 边缘(index=0)
                if (normalizedDistance <= 0.5f)
                {
                    // 在中心和中间位置之间插值
                    float t = normalizedDistance * 2f; // 将0-0.5映射到0-1
                    scale = Mathf.Lerp(_itemScales[2], _itemScales[1], t);
                    offsetY = Mathf.Lerp(_itemYOffsets[2], _itemYOffsets[1], t);
                }
                else
                {
                    // 在中间位置和边缘之间插值
                    float t = (normalizedDistance - 0.5f) * 2f; // 将0.5-1映射到0-1
                    scale = Mathf.Lerp(_itemScales[1], _itemScales[0], t);
                    offsetY = Mathf.Lerp(_itemYOffsets[1], _itemYOffsets[0], t);
                }
            }


            item.scale = new Vector2(scale, scale);
            item.y = offsetY * -1;
        }

        /// <summary>
        /// 选中指定item
        /// </summary>
        /// <param name="item">要选中的item</param>
        /// <param name="itemIndex">item的数据索引</param>
        private void SelectItem(int itemIndex)
        {
            var item = ui.selectList.GetChildAt(itemIndex);
            // 设置IntroduceListItem的选中状态
            GComponent itemComp = item.asCom;
            if (itemComp != null)
            {
                // 获取IsSelect Controller (index 0)
                Controller isSelectController = itemComp.GetControllerAt(0);
                if (isSelectController != null)
                {
                    // 设置为选中状态 (假设1为选中状态)
                    isSelectController.selectedIndex = 0;
                }
                itemComp.scale = new Vector3(1f,1f,1);
            }

            // 触发选中事件回调
            OnItemSelected(itemIndex);
        }

        /// <summary>
        /// 取消选中指定item
        /// </summary>
        /// <param name="item">要取消选中的item</param>
        private void UnselectItem(int index)
        {
            if (index < 0 || index >= ui.selectList.numChildren) return;
            var item = ui.selectList.GetChildAt(index);
            GComponent itemComp = item.asCom;
            if (itemComp != null)
            {
                // 获取IsSelect Controller (index 0)
                Controller isSelectController = itemComp.GetControllerAt(0);
                if (isSelectController != null)
                {
                    // 设置为非选中状态 (假设0为非选中状态)
                    isSelectController.selectedIndex = 1;
                }
                itemComp.scale = new Vector3(0.5f,0.5f,1);
            }
        }

        /// <summary>
        /// item选中回调
        /// </summary>
        /// <param name="selectedIndex">选中的item索引（显示索引）</param>
        private void OnItemSelected(int selectedIndex)
        {
            if (selectedIndex >= 0 && selectedIndex < _listData.Count)
            {
                ExploreFriendCfg selectedData = _listData[selectedIndex];

                // 如果是占位项，不执行任何操作
                if (selectedData == null) return;

                Debug.Log($"Item selected: {selectedData.NameKey} (Index: {selectedIndex})");

                ui.SelectItemName.text = selectedData.NameKey;
                FriendsAvatarLoaderHelper.GenerateRTAsync(selectedData.AvatarModleId, ui.avatarLoader, delegate(bool b) { }, 0.5f);

                ExploreFriendsUIHelper.SetIntroduceNode(ui.Introduce,selectedData);
                ExploreFriendsUIHelper.SetBGImg(selectedData , ui.bgLoader);
                // Color color;
                // if (ColorUtility.TryParseHtmlString(selectedData.BGColor, out color))
                // {
                //     ui.bgImg.color = color;
                // }

                introduceSoundList.Clear();
                introduceContentKeyList.Clear();
                if (!string.IsNullOrEmpty(selectedData.introduceSoundId1) && !string.IsNullOrEmpty(selectedData.introduceTxt1))
                {
                    introduceSoundList.Add(selectedData.introduceSoundId1);
                    introduceContentKeyList.Add(selectedData.introduceTxt1);
                }
                if (!string.IsNullOrEmpty(selectedData.introduceSoundId2) && !string.IsNullOrEmpty(selectedData.introduceTxt2))
                {
                    introduceSoundList.Add(selectedData.introduceSoundId2);
                    introduceContentKeyList.Add(selectedData.introduceTxt2);
                }
                soundIndex = 0;
                if (!string.IsNullOrEmpty(soundTimerKey))
                {
                    TimerManager.instance.UnRegisterTimer(soundTimerKey);
                    soundTimerKey = string.Empty;
                }
                RefreshIntroducePop();
            }
        }

        private void RefreshIntroducePop()
        {
            if (introduceContentKeyList.Count <= soundIndex)
            {
                soundIndex = 0;
            }

            if (introduceContentKeyList.Count > soundIndex)
            {
                ui.ChatContent.text = I18N.inst.FoStr(introduceContentKeyList[soundIndex]) + "\n" + I18N.inst.MoStr(introduceContentKeyList[soundIndex]);
                SoundManger.instance.PlayMusicByNameAndReturnLength(introduceSoundList[soundIndex] ,
                    delegate(float length)
                    {
                        soundTimerKey = TimerManager.instance.RegisterTimer(OnSoundPlayComplate, (int)(length + 1f) * 1000, 1);
                    });
                soundIndex++;
                if (soundIndex >= introduceSoundList.Count) soundIndex = 0;
            }
        }

        private void OnSoundPlayComplate(int count)
        {
            RefreshIntroducePop();
        }

        private void RefreshBtn()
        {
            ui.NextBtn.SetBtnMode(FriendCommonBtn.BtnMode.Introduce);
            ui.NextBtn.SetDiamondStatus(GameEntry.ExFriendC.Model.IsFree , GameEntry.ExFriendC.Model.GetConsumeDiamondBySlot(friendSlotData));
            ui.NextBtn.SetLoadingStatus(false);
            ui.NextBtn.SetTxt("ui_explore_friends_preview_draw_button_text");
        }
        
        #region 公共接口方法

        /// <summary>
        /// 设置列表数据（外部调用）
        /// </summary>
        /// <param name="dataList">数据列表</param>
        public void SetListData(List<ExploreFriendCfg> dataList)
        {
            // 保存原始数据
            _originalData.Clear();
            _originalData.AddRange(dataList);

            // 创建包含占位项的完整列表数据
            _listData.Clear();

            // 添加左侧占位项
            for (int i = 0; i < PLACEHOLDER_COUNT; i++)
            {
                _listData.Add(null);
            }

            // 添加实际数据
            _listData.AddRange(_originalData);

            // 添加右侧占位项
            for (int i = 0; i < PLACEHOLDER_COUNT; i++)
            {
                _listData.Add(null);
            }

            ui.selectList.numItems = _listData.Count;
            _currentCenterItemIndex = DataIndexToDisplayIndex(0); // 设置为第一个实际数据项

            // 刷新显示
            UpdateItemsDisplay();
        }

        /// <summary>
        /// 获取当前选中的item数据
        /// </summary>
        /// <returns>当前选中的item数据</returns>
        public ExploreFriendCfg GetCurrentSelectedData()
        {
            if (_currentCenterItemIndex >= 0 && _currentCenterItemIndex < _listData.Count)
            {
                return _listData[_currentCenterItemIndex];
            }
            return null;
        }

        /// <summary>
        /// 获取当前选中的item索引
        /// </summary>
        /// <returns>当前选中的item索引</returns>
        public int GetCurrentSelectedIndex()
        {
            return _currentCenterItemIndex;
        }

        /// <summary>
        /// 程序化选中指定索引的item
        /// </summary>
        /// <param name="index">要选中的item索引</param>
        public void SelectItemByIndex(int index)
        {
            if (index >= 0 && index < _listData.Count)
            {
                MoveToCenter(index);
            }
        }

        #endregion
        
        protected override string[] ListNotificationInterests()
        {
            return new[]
            {
                NotifyConsts.ExploreFriendsTimeLineLoaded
            };
        }

        protected override void HandleNotification(string name, object body)
        {
            switch (name)
            {
                case NotifyConsts.ExploreFriendsTimeLineLoaded:
                    UIManager.instance
                        .GetUI<ExploreFriendsDrawCardsResultPanelUI>(UIConsts.ExploreFriendsDrawCardsResultPanel)
                        .Show();
                    Hide();
                    break;
                
            }
        }


    }
}