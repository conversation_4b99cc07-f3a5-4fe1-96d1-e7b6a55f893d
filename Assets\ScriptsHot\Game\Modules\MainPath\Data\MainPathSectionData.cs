﻿using System.Collections.Generic;
using Msg.basic;
using Msg.course;

namespace ScriptsHot.Game.Modules.MainPath
{
    public class MainPathSectionData
    {
        public enum NodeType
        {
            None,
            AUAItem,
            BookItem,
            CupItem,
            CutItem,
            DumbBellItem,
            HeadEmptyItem,
            NextItem,
            RadioItem,
            StarItem,
            WarmUpItem,
            GrammarItem,
            ReviewItem,
            SentenceItem,
            WordItem,
        }

        public struct MainPathNodesStruct
        {
            public NodeType NodeType;
            public MainPathLevelData NodeData;
            public MainPathUnitData UnitData;
            public int Position;// 0-5
        }
        
        /// <summary>
        /// index
        /// </summary>
        public int ID { get; private set; }
        
        /// <summary>
        /// 服务器Section数据
        /// </summary>
        public PB_SectionData ServerSectionData { get; private set; }

        /// <summary>
        /// Unit List
        /// </summary>
        public List<MainPathUnitData> UnitDataList { get; private set; } = new();
        
        public List<MainPathNodesStruct> NodeList { get; private set; } = new();
        
        public MainPathSectionData(PB_SectionData data)
        {
            ServerSectionData = data;
            ID = data.section_index;
            foreach (var unitData in data.unit_data_list)
            {
                MainPathUnitData unit = new MainPathUnitData(unitData);
                UnitDataList.Add(unit);
            }
            CreateNodeListCache();
        }

        public void UpdateData(PB_SectionData data)
        {
            ServerSectionData = data;
            ID = data.section_index;
            foreach (var unitData in data.unit_data_list)
                UnitDataList[unitData.unit_index].UpdateData(unitData);
            UpdateNodeList();
        }

        private void CreateNodeListCache()
        {
            int pos = 0;
            for (int i = 0; i < UnitDataList.Count; i++)
            {
                if (i == 0)
                    NodeList.Add(new MainPathNodesStruct
                    {
                        NodeType= NodeType.HeadEmptyItem,
                        UnitData = UnitDataList[i]
                    });
                else
                    NodeList.Add(new MainPathNodesStruct
                    {
                        NodeType= NodeType.CutItem,
                        UnitData = UnitDataList[i],
                    });
                
                for (int j = 0; j < UnitDataList[i].LevelDataList.Count; j++)
                {
                    pos %= 6;
                    MainPathLevelData levelData = UnitDataList[i].LevelDataList[j];
                    NodeType nodeType = NodeType.None;
                    switch (levelData.ServerLevelData.level_type)
                    {
                        case PB_LevelTypeEnum.LTStar:
                            nodeType = NodeType.StarItem;
                            break;
                        case PB_LevelTypeEnum.LTBook:
                            nodeType = NodeType.BookItem;
                            break;
                        case PB_LevelTypeEnum.LTRadio:
                            nodeType = NodeType.RadioItem;
                            break;
                        case PB_LevelTypeEnum.LTDumbBell:
                            nodeType = NodeType.DumbBellItem;
                            break;
                        case PB_LevelTypeEnum.LTAua:
                            nodeType = NodeType.AUAItem;
                            break;
                        case PB_LevelTypeEnum.LTWarmUp:
                            nodeType = NodeType.WarmUpItem;
                            break;
                        case PB_LevelTypeEnum.LTCup:
                            nodeType = NodeType.CupItem;
                            break;
                        // case PB_LevelTypeEnum.LTGrammar:
                        //     nodeType = NodeType.GrammarItem;
                        //     break;
                        // case PB_LevelTypeEnum.LTReview:
                        //     nodeType = NodeType.ReviewItem;
                        //     break;
                        // case PB_LevelTypeEnum.LTSentence:
                        //     nodeType = NodeType.SentenceItem;
                        //     break;
                        // case PB_LevelTypeEnum.LTWord:
                        //     nodeType = NodeType.WordItem;
                        //     break;
                        default:
                            VFDebug.Log("有节点没加上！！ " + levelData.ServerLevelData.level_type);
                            break;
                    }
                    
                    NodeList.Add(new MainPathNodesStruct()
                    {
                        NodeType = nodeType,
                        NodeData = levelData,
                        Position = pos,
                        UnitData = UnitDataList[i],
                    });
                    pos++;
                }
            }
            
            NodeList.Add(new MainPathNodesStruct()
            {
                NodeType = NodeType.NextItem
            });
        }

        private void UpdateNodeList()
        {
            int nodeIndex = 0;
            for (int i = 0; i < UnitDataList.Count; i++)
            {
                var curNode = NodeList[nodeIndex];
                curNode.UnitData = UnitDataList[i];
                NodeList[nodeIndex] = curNode;
                nodeIndex++;
                
                for (int j = 0; j < UnitDataList[i].LevelDataList.Count; j++)
                {
                    if (nodeIndex >= NodeList.Count)
                    {
                        VFDebug.Log("Error");
                        return;
                    }
                    var pathNode = NodeList[nodeIndex];
                    MainPathLevelData levelData = UnitDataList[i].LevelDataList[j];
                    pathNode.NodeData = levelData;
                    NodeList[nodeIndex] = pathNode;
                    nodeIndex++;
                }
            }
        }

        public void Dispose()
        {
            foreach (var unitData in UnitDataList)
            {
                unitData.Dispose();
            }
            UnitDataList.Clear();
        }
    }
}