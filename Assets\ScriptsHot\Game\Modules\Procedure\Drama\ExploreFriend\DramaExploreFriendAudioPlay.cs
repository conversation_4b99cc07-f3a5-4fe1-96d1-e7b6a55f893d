﻿
using Msg.explore;
using ScriptsHot.Game.Modules.Explore;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Procedure.Drama.ExploreFriend
{
    /// <summary>
    /// avatar 音频播放
    /// </summary>
    public class DramaExploreFriendAudioPlay : BaseDrama
    {
        private ulong _audioId;
        public override void OnEvent()
        {
            base.OnEvent();
            Notifier.instance.RegisterNotification(NotifyConsts.ExploreFriend_AvatarAudioPlayOver,OnAudioOver);
        }
        
        private void OnAudioOver(string name, object body)
        {
            if (body == null)
            {
                Finish();
                return;
            }
            
            ulong tts_id = (ulong)body;
          

            VFDebug.LogError($"收到 audio  dram 音频播放完毕 ---------tts_id:{tts_id} + _audioId:{_audioId}");
            if (_audioId == tts_id)
            {
                Finish();
            }
        }

        public override void Do(bool canFinish = true)
        {
            base.Do();
            ProcedureParams p = (ProcedureParams)Params;
            _audioId = (ulong)p.param;
            VFDebug.LogError($"开始播放 audio  dram 音频 ---------_audioId:{_audioId}");
            
            ExploreAudioPlayEffect playEffect = new ExploreAudioPlayEffect()
            {
                AudioId = _audioId,
                Effect = true
            };
            
            Notifier.instance.SendNotification(NotifyConsts.ExploreFriend_AvatarAudioPlay,playEffect);
        }

        public override void OnFinish(bool isBreak)
        {
            base.OnFinish(isBreak);
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreFriend_AvatarAudioPlayOver,OnAudioOver);
        }
        
        public override void Dispose()
        {
            base.Dispose();
            Notifier.instance.UnRegisterNotification(NotifyConsts.ExploreFriend_AvatarAudioPlayOver,OnAudioOver);
        }
    }
} 