﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Assets.ScriptsHot.Game.Modules.Common;
using CommonUI;
using FairyGUI;
using Msg.basic;
using Msg.dialog_task;
using Msg.incentive;
using Msg.login;
using ScriptsHot.Game.Modules.Common;
using UIBind.Main;
using UnityEngine;
#if UNITY_IOS
using Unity.Advertisement.IosSupport;
#endif

public class CommonController : BaseController
{
    public CommonController() : base(ModelConsts.Common) { }

    public override void OnInit()
    {
        MsgManager.instance.RegisterCallBack<SC_GetBoxRewardAck>(RespBoxRewardAck);
        MsgManager.instance.RegisterCallBack<SC_UpdateAppsflyerCallbackDataResp>(RespAppsflyerCallback);        
    }

    public override void OnUIInit()
    {
        UIManager.instance.RegisterItemExtension("common", "TabGListComponent", typeof(TabGList));//todo TabGListComponent待修订
        UIManager.instance.RegisterItemExtension("common","TextExt",typeof(TextFieldExtension));
        UIManager.instance.RegisterItemExtension("common","TextExtEasy", typeof(SocialChatText));
        UIManager.instance.RegisterItemExtension("common","NewWordCom",typeof(NewWordComponent));
        
        UIManager.instance.RegisterItemExtension("common","NewWordUnderlineCom",typeof(NewWordUnderlineComponent));
        UIManager.instance.RegisterItemExtension("common","QuestionAnswerCom",typeof(QuestionAnswerComponent));
        UIManager.instance.RegisterItemExtension("Main","VoiceChatInviteMsgBox",typeof(VoiceChatInviteMsgBox));
        //
        this.RegisterUI(new CommConfirmUI(UIConsts.CommConfirm));
        this.RegisterUI(new CommonToastUI(UIConsts.CommonToast));
        this.RegisterUI(new CommBusyUI(UIConsts.CommBusy));
        this.RegisterUI(new CommonGiftUI(UIConsts.CommonGift));
        this.RegisterUI(new CommonGetGoldTipsUI(UIConsts.CommonGetGold));
        this.RegisterUI(new CommonGetDiamondUI(UIConsts.CommonGetDiamond));
        this.RegisterUI(new CommonBackDiamondUI(UIConsts.CommonBackDiamond));
        this.RegisterUI(new CommonBottomUseItemUI(UIConsts.CommonBottomUseItem));
        this.RegisterUI(new CommonPushUI(UIConsts.CommonPush));
        this.RegisterUI(new MaskOperationUI(UIConsts.MaskOperation));
        this.RegisterUI(new CommonNewItemUI(UIConsts.CommonNewItemUI));
        this.RegisterUI(new HalfScreenMsgUI(UIConsts.HalfScreenMsgUI));
        this.RegisterUI(new FullScreenMsgUI(UIConsts.FullScreenMsgUI));       
        this.RegisterUI(new NoNetworkTipUI(UIConsts.NoNetworkTipUI));   
        this.RegisterUI(new NoNetworkPanelUI(UIConsts.NoNetworkPanelUI));   
        this.RegisterUI(new AppRatingUI(UIConsts.AppRatingUI));   
    }

    private void ClickRoot(EventContext context)
    {
        this.GetUI<TipsUI>(UIConsts.Tips).Open("aaaaasdfgsdfgsdfffffffffffffffffffffffffffffghsdfhgssssssssssshsdfghsdf", context.inputEvent.position);
       
    }


    public override void OnUpdate(int interval)
    {
        // if (Input.GetKeyDown(KeyCode.S))
        // {
        //     GRoot.inst.onClick.Add(ClickRoot);
        // }
        // if (Input.GetKeyDown(KeyCode.D))
        // {
        //     GRoot.inst.onClick.Remove(ClickRoot);
        // }

    }

    /// <summary>
    /// 结算领宝箱触发 通讯录领宝箱触发
    /// </summary>
    /// <param name="msg"></param>
    private void RespBoxRewardAck(SC_GetBoxRewardAck msg)
    {
        if (msg != null && msg.code == PB_Code.Normal)
        {
            GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
            GetUI<CommonGiftUI>(UIConsts.CommonGift).ShowGiftDiamondUI(msg.data.economic_info.diamond_info.permanent_info);
            SendNotification(NotifyConsts.RefreshSettleBoxEvent, msg);
            GetController<CurrencyController>(ModelConsts.CurrencyController)
                .SendGetEconomicInfoReq(GameEventName.GameEnter);//刷新钻石数
        }
        else
        {
            FailGetRewardCallback(GRPCManager.ErrorType.None, null, msg?.code.ToString());
        }
    }

    private void FailGetRewardCallback(GRPCManager.ErrorType e = GRPCManager.ErrorType.None,
        Google.Protobuf.IMessage m = null, string severCode = "")
    {
        GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
        VFDebug.LogError("领取亲密度宝箱失败 errorType:" + e + " msg:" + m + " serverCode:" + severCode);
    }

    public override void OnEnterGame()
    {
        AFHelper.CheckATT();
  
        BIAttStatus();
        SendAppsflyerDataToServer();
        AFHelper.SetAppsflyerDataCb(SendAppsflyerDataToServer);
    }

    #region af归因 发送给server
    private string afTimer = String.Empty;
    public void SendAppsflyerDataToServer()
    {
        var afId = AFHelper.GetAppFlyerId();
        if (string.IsNullOrEmpty(afId))
        {
            return;
        }

        var data1 = AFHelper.Ins.GetConversionData();
        var data2 = AFHelper.Ins.GetAttributionData();
        if (string.IsNullOrEmpty(data2) && string.IsNullOrEmpty(data1))
        {
            return;
        }

        if (!string.IsNullOrEmpty(afTimer))
        {
            TimerManager.instance.UnRegisterTimer(afTimer);
            afTimer = string.Empty;
        }
        
        afTimer = TimerManager.instance.RegisterTimer((k) =>
        {
            SendAppsflyerDataToServer(afId, data1, data2);
            afTimer = string.Empty;
        } , 10000 , 1);


    }
    
    private void SendAppsflyerDataToServer(string afId, string data1, string data2)
    {
        MsgManager.instance.SendMsg(new CS_UpdateAppsflyerCallbackDataReq()
        {
            appsflyer_id = afId,
            conversion_data = data1,
            app_open_attribution = data2
        });
    }
    
    private void RespAppsflyerCallback(SC_UpdateAppsflyerCallbackDataResp msg){ }
    #endregion

    #region BIAttStatus
    private void BIAttStatus()
    {
#if UNITY_IOS
        var dic = BI.ApplyBiDic();
        var status = ATTrackingStatusBinding.GetAuthorizationTrackingStatus();
        string statusStr = string.Empty;
        switch (status)
        {
            case ATTrackingStatusBinding.AuthorizationTrackingStatus.NOT_DETERMINED:
                statusStr = "NOT_DETERMINED";
                break;
            case ATTrackingStatusBinding.AuthorizationTrackingStatus.RESTRICTED:
                statusStr = "RESTRICTED";
                break;
            case ATTrackingStatusBinding.AuthorizationTrackingStatus.DENIED:
                statusStr = "DENIED";
                break;
            case ATTrackingStatusBinding.AuthorizationTrackingStatus.AUTHORIZED:
                statusStr = "AUTHORIZED";
                break;
            default:
                statusStr = "UNKNOWN";
                break;
        }

        dic.TryAdd(BIkey.status, statusStr);
        BI.Collect(BIEvent.att_status, dic);
#endif
    }
    #endregion
}