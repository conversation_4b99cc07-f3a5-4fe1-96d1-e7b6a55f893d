/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.MainPath3D
{
    public partial class NextItem : UIBindT
    {
        public override string pkgName => "MainPath3D";
        public override string comName => "NextItem";

        public GTextField title1;
        public GTextField title2;
        public GButton BtnSkipToThis;
        public GGraph dynamicHeight;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            title1 = (GTextField)com.GetChildAt(1);
            title2 = (GTextField)com.GetChildAt(2);
            BtnSkipToThis = (GButton)com.GetChildAt(3);
            dynamicHeight = (GGraph)com.GetChildAt(4);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            title1 = null;
            title2 = null;
            BtnSkipToThis = null;
            dynamicHeight = null;
        }
    }
}