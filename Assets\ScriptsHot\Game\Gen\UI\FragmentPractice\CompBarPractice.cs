/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class CompBarPractice : ExtendedComponent
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "CompBarPractice";
        public static string url => "ui://cmoz5osjnkfk1n";

        public GGraph bgBlur;
        public GProgressBar compBar;
        public GLoader3D SpineProgress;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(CompBarPractice));
        }

        public override void ConstructFromXML(XML xml)
        {
            bgBlur = GetChildAt(0) as GGraph;
            compBar = GetChildAt(1) as GProgressBar;
            SpineProgress = GetChildAt(2) as GLoader3D;

            OnConstructed();

            SetMultiLanguageInChildren();
        }
        public override void Dispose()
        {
            OnWillDispose();

            bgBlur = null;
            compBar = null;
            SpineProgress = null;

            base.Dispose();
        }
        public void SetMultiLanguageInChildren()
        {
            this.bgBlur.SetKey("BLUR");  // ""
        }
    }
}