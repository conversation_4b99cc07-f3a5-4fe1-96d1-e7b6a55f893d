﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using CommonUI;
using LitJson;
using Msg;
using ScriptsHot.Game.Modules.Chat.ChatScore;
using ScriptsHot.Game.Modules.Task;
using Msg.dialog_task;
using Msg.learn_assist;
using pb = global::Google.Protobuf;
using Msg.basic;
using ScriptsHot.Game.Modules.Chat.ChatSuggestion;
using ScriptsHot.Game.Modules.Shop;

using Debug = UnityEngine.Debug;
using Msg.learnPath;
using ScriptsHot.Game.Modules.Chat.ChatCell;
using ScriptsHot.Game.Modules.ChatStart;
using ScriptsHot.Game.Modules.Common;
using ScriptsHot.Game.Modules.Consts;
using ScriptsHot.Game.Modules.Contacts;
using ScriptsHot.Game.Modules.ReviewQuestion;
using UIBind.Chat;
using ScriptsHot.Game.Modules.FragmentPractice;
using ScriptsHot.Game.Modules.Settlement;

public class MsgData
{
    public short msgId;
    public pb::IMessage data;
}

public class ChatController : BaseController
{
    public ChatController() : base(ModelConsts.Chat)
    {
    }

    /// <summary>
    /// player 自动说话
    /// </summary>
    public static bool AutoPlayer = false;
    
    private ChatStateMachine _stateMachine = null;
    private List<MsgData> _handleMsgDatas = new List<MsgData>();
    private Dictionary<ChatCellType, string> _chatCellResDic = new();
    private ChatModel _chatModel => GetModel<ChatModel>(ModelConsts.Chat);
    private int _waitSequeneId = 1;
    private int _endSequeneId = 1;
    private Dictionary<int, MsgData> _waitDialogTaskMsgRespToSeqId = new Dictionary<int, MsgData>();
    private Queue<object> _audioQueue = new();
    private int _curTalkingId = 0;

    private long _flowHelpOnboardingTTSId = 0;
    public long curAvatarId {get; private set;}
    public long curTaskId {get; private set;}
    public PB_DialogSourceEnum curSourceEnum {get; private set;}
    public bool _isSendASREnd { get; private set; } = false;
    public long TestCreateDialogTime = 0;
    private RecommendCardModel _recommendCardModel => GetModel<RecommendCardModel>(ModelConsts.RecommendCard);

    // private LearnPathModel _learnPathModel => GetModel<LearnPathModel>(ModelConsts.LearnPath);
    private int loadingBubbleId = -1;
    private int emptyBubbleId = -1;
    public bool isPlayNewWordAble { get; private set; } = true;
    
    public Action<SC_ClickWordAck> NewWordResp;
    public readonly int GuideOnboardingRoundIdone = 1; //第1轮出引导
    public readonly int GuideOnboardingRoundIdtwo = 2;

    private PB_DialogMode _createDialogMode = PB_DialogMode.MNone;
    public override void OnUIInit()
    {
        // this.RegisterUI(new ChatModeUI(UIConsts.ChatMode));
        this.RegisterUI(new ChatUI(UIConsts.Chat));
        this.RegisterUI(new RecordUI(UIConsts.RecordUI));
        this.RegisterUI(new ChatScoreUI(UIConsts.ChatScore));
        this.RegisterUI(new ChatJumpReviewUI(UIConsts.ChatJumpReviewUI));
        this.RegisterUI(new ChatSuggestionUI(UIConsts.ChatSuggestion));
    }

    public override void OnInit()
    {
        this.RegisterModel(new ChatModel());
        this._stateMachine = new ChatStateMachine(this);
        RegisterSerCallBack();
        BindChatCellComponent();
        
        Notifier.instance.RegisterNotification(NotifyConsts.UpdateScaffoldCellStateEvent,UpdateScaffoldCell);
    }

    private void BindChatCellComponent()
    {
        // avatar普通气泡 + mask功能
        _chatCellResDic.Add(ChatCellType.AvatarNormal, "AvatarNormalCellCom");

        // avatar等待spine
        _chatCellResDic.Add(ChatCellType.PreTalk, "PreTalkCellCom");

        _chatCellResDic.Add(ChatCellType.PlayerNormal, "PlayerNormalCellCom");

        // player跟读气泡 + 状态
        _chatCellResDic.Add(ChatCellType.PlayerFollow, "PlayerFollowCellCom");
        // toast气泡
        _chatCellResDic.Add(ChatCellType.Toast, "ToastCellCom");

        // 脚手架
        _chatCellResDic.Add(ChatCellType.Scaffold, "ScaffoldCellCom");

        // suggestion气泡
        _chatCellResDic.Add(ChatCellType.Suggest, "SuggestCellCom");
        // Advice
        _chatCellResDic.Add(ChatCellType.Advice, "AdviceCellCom");
        // empty气泡
        _chatCellResDic.Add(ChatCellType.Empty, "EmptyCellCom");
    }

    // 生词查询
    public void ClickWordAck(SC_ClickWordAck msg)
    {
        if (NewWordResp != null)
        {
            NewWordResp.Invoke(msg);
        }
    }

    // public void ResisterNewWordReq(Action<>)
    // {
    //     NewWordResp += 
    // }

    private void RegisterSerCallBack()
    {
        //10003 获取任务对话模式回复 获取模式列表
        MsgManager.instance.RegisterCallBack<SC_GetDialogTaskModeListAck>(this.GetDialogTaskModeListAck);
        // //10005 获取模式详情回复 获取当前mode具体的模式数据
        MsgManager.instance.RegisterCallBack<SC_GetModeInfoAck>(this.GetModeInfoAck);
        //10008 任务对话回复 创建对话
        MsgManager.instance.RegisterCallBack<SC_CreateDialogTaskAck>(this.CreateDialogTaskAck);
        // //10009  任务对话回复
        MsgManager.instance.RegisterCallBack<SC_DialogTaskMsgHandleAck>(this.DialogTaskMsgHandleAck);

        //10009  任务对话回复
        MsgManager.instance.RegisterCallBack<SC_GetDialogTaskNextRoundMsgAck>(this.DialogTaskCacheMsgAck);
        
        // 获取职业对话模式详情回复
        MsgManager.instance.RegisterCallBack<SC_GetModeInfoForCareerAck>(GetModeInfoForCareer);
        // MsgManager.instance.RegisterCallBack<SC_TranslateAck>(this.ShowNewWordTrans);

        //生词
        MsgManager.instance.RegisterCallBack<SC_ClickWordAck>(ClickWordAck);
        
        //脚手架回复
        MsgManager.instance.RegisterCallBack<SC_GetDialogScaffoldAck>(GetDialogScaffoldAck);
        //获取进行中对话信息回复
        //MsgManager.instance.RegisterCallBack<SC_GetInProgressDialogAck>(GetInProgressDialogAck);

        // 修改跟读模式引导标记
        MsgManager.instance.RegisterCallBack<SC_ChangeMarkInfoAck>(OnSCChangeMarkInfoAck);
        
        // 事后建议
        MsgManager.instance.RegisterCallBack<SC_DialogSuggestionAck>(OnSCDialogSuggestionAck);
        // 事后建议
        MsgManager.instance.RegisterCallBack<SC_DialogTranslateAck>(OnSCDialogTranslateAck);
        //援助等级
        MsgManager.instance.RegisterCallBack<SC_SetUserAssistLevelAck>(GetUserAssistLevelAck);
       
    }

    #region 处理服务器回调数据

    //收到对话选择模式列表
    public void GetDialogTaskModeListAck(SC_GetDialogTaskModeListAck msg)
    {
        if (msg.code != PB_Code.Normal)
        {
            DealErrorData(msg.code.ToString());
            return;
        }
        if (_stateMachine.currState.name == ChatState.Select)
        {
            MsgData msgData = new MsgData();
            short msgId = MsgMap.msgT2ID[typeof(SC_GetDialogTaskModeListAck)];
            msgData.msgId = msgId;
            msgData.data = msg;
            bool isAccept = _stateMachine.currState.OnHandleMsg(msgData);
            if (!isAccept)
                Debug.LogError("ChatSelectState deal data is error ");
        }
        else
            Debug.LogError("curChatState is not Select " + _stateMachine.currState.name);
    }

    //收到服务器当前具体模式数据  是否监听可以考虑 应该是客户端存储就行 不需要服务器确认
    private void GetModeInfoAck(SC_GetModeInfoAck msg)
    {
        if (msg.code != PB_Code.Normal)
        {
            DealErrorData(msg.code.ToString());
            return;
        }
        if (_stateMachine.currState.name == ChatState.Select)
        {
            MsgData msgData = new MsgData();
            short msgId = MsgMap.msgT2ID[typeof(SC_GetModeInfoAck)];
            msgData.msgId = msgId;
            msgData.data = msg;
            bool isAccept = _stateMachine.currState.OnHandleMsg(msgData);
            if (!isAccept)
                Debug.LogError("ChatSelectState deal data is error ");
        }
        else
            Debug.LogError("curChatState is not Select " + _stateMachine.currState.name);
    }

    //成功创建对话的回调
    public void CreateDialogTaskAck(SC_CreateDialogTaskAck msg)
    {

        // if (msg.code == PB_Code.NotEnoughAsset)
        // {
        //     NotDiamondEnough();
        //     return;
        // }
        if (IsNewDialogMode(_chatModel.chatMode))
        {
            return;
        }
        if (msg.code != PB_Code.Normal || msg.data == null)
        {
            DealErrorData(msg.code.ToString());
            return;
        }
        
        PB_CreateDialogTaskResp item = msg.data;

        _createDialogMode = item.task_mode;

        MsgData msgData = new MsgData();
        short msgId = MsgMap.msgT2ID[typeof(SC_CreateDialogTaskAck)];
        msgData.msgId = msgId;
        msgData.data = item;

        if (_chatModel.curRoundId > 0)
        {
            VFDebug.LogWarning("CreateDialogTaskAck 已走过创建对话的逻辑 此条不处理 curRoundId = " + _chatModel.curRoundId);
            return;
        }

        bool isAccept = _stateMachine.currState.OnHandleMsg(msgData);
        if (!isAccept)
        {
            VFDebug.Log("<color=#FF0000>create dialogId is error: </color>" + GetChatStateName());
            DealErrorData("isAccept error");
        }
        else
        {
            
            this.GetController<TaskController>(ModelConsts.Task).CreateDialogSuccess();
        }
            
    }

    public bool IsNewDialogMode(PB_DialogMode mode)
    {
        if (mode == PB_DialogMode.RolePlay || mode == PB_DialogMode.WorldStory || mode == PB_DialogMode.Tutor ||
            mode == PB_DialogMode.Career || mode == PB_DialogMode.Flash || mode == PB_DialogMode.Video|| mode == PB_DialogMode.MNone)
        {
            return true;
        }

        return false;
    }

    //任务回复
    public void DialogTaskMsgHandleAck(SC_DialogTaskMsgHandleAck msg)
    {
        if (IsNewDialogMode(_createDialogMode))
        {
            return;
        }
        //chatgpt封控
        if (msg.code == PB_Code.ChatGptRiskControl)
        {
            if (msg.data != null && msg.data.msg_items.Count >= 1)
            {
                if (msg.data.msg_items[0].dialog_id == _chatModel.dialogId &&
                    msg.data.msg_items[0].round_id == _chatModel.curRoundId)
                {
                    bool isOk = GetStateMachine().currState.ChatGptLock(msg.data.msg_items[0].bubble_id);
                    DataDotCutApiCase dataDotCutApiCase = new DataDotCutApiCase();
                    dataDotCutApiCase.Dialogue_id = _chatModel.dialogId;
                    dataDotCutApiCase.Task_id = curTaskId;
                    dataDotCutApiCase.Dialogue_step = _chatModel.GetStepIdByRoundId(_chatModel.curRoundId);
                    dataDotCutApiCase.Task_mode = (int)_chatModel.chatMode;
                    dataDotCutApiCase.Api_address = "SC_DialogTaskMsgHandleAck";
                    dataDotCutApiCase.Cut_mode = 3;
                    DataDotMgr.Collect(dataDotCutApiCase);
                    
                    if(isOk)
                        return;
                }
            }
        }

        if (msg.code != PB_Code.Normal || msg.data == null || (msg.data.msg_items.Count == 0 && msg.data.ext_items.Count ==0 && _chatModel.chatMode != PB_DialogMode.Tutor))
        {
            DealErrorData(msg.code.ToString());
            return;
        }

        if (_chatModel.GetDialogTaskMsgHandleDealState(_chatModel.curRoundId))
        {
            VFDebug.LogWarning("当前轮次已走过MsgHandle 此条不处理");
            return;
        }

        _chatModel.SetDialogTaskMsgHandleDeal(_chatModel.curRoundId);//表示当前轮次已处理过
        short msgId = MsgMap.msgT2ID[typeof(SC_DialogTaskMsgHandleAck)];
        DealDialogTaskData(msg.data, msgId);
        HandleSuggestion(_chatModel.curRoundId - 1);
    }
    
    private void HandleSuggestion(int roundId)//收到MSG Handle在请求
    {
        if (_chatModel.chatMode != PB_DialogMode.Tutor && _chatModel.chatMode != PB_DialogMode.Challenge &&
            _chatModel.chatMode != PB_DialogMode.Career)
            return;
        
        if (roundId <= 0)
            return;

        ChatModel.SuggestionMsgData msgData = _chatModel.GetDicSuggestionMsgData(roundId);
        if (msgData.RoundId == 0)
            return;
        
        PB_TutorDialogSuggestionResp data = _chatModel.GetAITutorSuggestionByBubbleId(msgData.BubbleId);
        if (data != null)
            return;
        
        PlayerNormalCell cell = _chatModel.GetChatCellByBubbleId(msgData.BubbleId) as PlayerNormalCell;
        if (cell != null)
            cell.ChangeCtrlBtnState(PlayerNormalCellCtrlBtnState.Loading);
        CS_DialogSuggestionReq msg = new CS_DialogSuggestionReq();
        if (_chatModel.chatMode == PB_DialogMode.Challenge)
        {
            msg.dialog_mode = _chatModel.chatMode;
            msg.challenge_suggestion_req = new PB_ChallengeDialogSuggestionReq();
            msg.challenge_suggestion_req.dialog_id = _chatModel.dialogId;
            msg.challenge_suggestion_req.step_id = _chatModel.GetStepIdByRoundId(msgData.RoundId);
            msg.challenge_suggestion_req.bubble_id = msgData.BubbleId;
            MsgManager.instance.SendMsg(msg); 
        }

        if (_chatModel.chatMode == PB_DialogMode.Tutor)
        {
            msg.dialog_mode = _chatModel.chatMode;
            msg.tutor_suggestion_req = new PB_TutorDialogSuggestionReq();
            msg.tutor_suggestion_req.dialog_id = _chatModel.dialogId;
            msg.tutor_suggestion_req.task_id = curTaskId;
            msg.tutor_suggestion_req.bubble_id = msgData.BubbleId;
            msg.tutor_suggestion_req.user_say_content = msgData.Content;
            msg.tutor_suggestion_req.msg_id = _chatModel.GetMsgIdByBubbleId(msgData.BubbleId);
            msg.tutor_suggestion_req.round_id = roundId;
            MsgManager.instance.SendMsg(msg);
        }

        if (_chatModel.chatMode == PB_DialogMode.Career)
        {
            msg.dialog_mode = _chatModel.chatMode;
            msg.career_suggestion_req = new PB_TutorDialogSuggestionReq();
            msg.career_suggestion_req.dialog_id = _chatModel.dialogId;
            msg.career_suggestion_req.task_id = curTaskId;
            msg.career_suggestion_req.bubble_id = msgData.BubbleId;
            msg.career_suggestion_req.user_say_content = msgData.Content;
            msg.career_suggestion_req.msg_id = _chatModel.GetMsgIdByBubbleId(msgData.BubbleId);
            msg.career_suggestion_req.round_id = roundId;
            MsgManager.instance.SendMsg(msg);
        }
    }

    //获取下一轮任务对话消息回复
    public void DialogTaskCacheMsgAck(SC_GetDialogTaskNextRoundMsgAck msg)
    {
        if (IsNewDialogMode(_createDialogMode))
        {
            return;
        }
        
        CommonUtils.Instance.TimeEnd();
        if (msg.code != PB_Code.Normal|| (msg.data.msg_items.Count == 0 && msg.data.ext_items.Count ==0))
        {
            DealErrorData(msg.code.ToString());
            return;
        }

        if (_chatModel.GetNextRoundMsgAckDealState(_chatModel.curRoundId))
        {
            VFDebug.LogWarning("当前轮次已走过NextRound 此条不处理");
            return;
        }

        _chatModel.SetNextRoundMsgAckDeal(_chatModel.curRoundId);//表示当前轮次已处理过
        for (int i = 0; i < msg.data.msg_items.Count; i++)
        {
            PB_DialogTaskMsgItem item = msg.data.msg_items[i];
            if(item.msg_biz_type == PB_DownMsgBizType.FreetalkAdvice)
                _chatModel.SetFreeTalkAdviceByRoundId(item.freetalk_advice_data,item.round_id);
        }
        short msgId = MsgMap.msgT2ID[typeof(SC_GetDialogTaskNextRoundMsgAck)];
        float time = TimeExt.currTime - TestCreateDialogTime;
        VFDebug.Log($"DialogueSlow: dialogid:{_chatModel.dialogId}, time:{time}, timestamp:{TimeExt.currTime}, type: 收到avatar信息, roundId:{_chatModel.curRoundId}, taskId:{curTaskId}");
        DealDialogTaskData(msg.data, msgId);

    }

    //获取职业对话模式详情回复
    private void GetModeInfoForCareer(SC_GetModeInfoForCareerAck msg)
    {
        if (msg.code != PB_Code.Normal)
        {
            DealErrorData(msg.code.ToString());
            return;
        }
        
        if (_stateMachine.currState.name == ChatState.Select)
        {
            MsgData msgData = new MsgData();
            short msgId = MsgMap.msgT2ID[typeof(SC_GetModeInfoForCareerAck)];
            msgData.msgId = msgId;
            msgData.data = msg;
            bool isAccept = _stateMachine.currState.OnHandleMsg(msgData);
            if (!isAccept)
            {
                Debug.LogError("ChatSelectState deal data is error ");
                DealErrorData("isAccept error");
            }
        }
        else
        {
            DealErrorData("chatstate is error");
            Debug.LogError("curChatState is not Select " + _stateMachine.currState.name);
        }

      
        
    }

    private void GetDialogScaffoldAck(SC_GetDialogScaffoldAck msg)
    {
        if (IsNewDialogMode(_createDialogMode))
        {
            return;
        }
        if (msg.code != PB_Code.Normal)
        {
            VFDebug.LogError("SC_GetDialogScaffoldAck is error: "+msg.code);
            return;
        }

        if (_chatModel.dialogId != 0 && msg.data.dialog_id != _chatModel.dialogId)
        {
            VFDebug.LogError("SC_GetDialogScaffoldAck is error: "+msg.code);
            return;
        }
        if(msg.data.round_id != _chatModel.curRoundId)
            return;
        
        MsgData msgData = new MsgData();
        short msgId = MsgMap.msgT2ID[typeof(SC_GetDialogScaffoldAck)];
        msgData.msgId = msgId;
        msgData.data = msg;

        if (_chatModel.chatMode == PB_DialogMode.Career)
            _chatModel.SetRoundIdToScaffoldData(msg.data.round_id, msg.data);
        
        bool isAccept = _stateMachine.currState.OnHandleMsg(msgData);
        if (!isAccept)
        {
            Debug.LogError("GetDialogScaffoldAck deal data is error ");
        }
        
    }

    public void GetInProgressDialogAck(PB_InProgressDialogPop msg)
    {
        this.GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N("common_reconnect_tips", () =>
        {
            ChatReconnection(msg);
        }, null, 1, null, "chat_btn_cancel");
    }

    private void ChatReconnection(PB_InProgressDialogPop msg)
    {
        long avatarId = msg.avatar_id;
        long taskId = msg.task_id;
        _chatModel.goal_record_id = msg.goal_record_id;
        _chatModel.chapter_record_id = msg.chapter_record_id;
        _recommendCardModel.SetCurTaskID(taskId);
       
        _chatModel.SetChatMode(msg.task_mode);
        if (msg.task_mode == PB_DialogMode.Challenge || msg.task_mode == PB_DialogMode.Tutor)
        {
            NotifyInfo info = new NotifyInfo();
            info.key = SceneState.ChatRecoonect;
            info.param = new object[]{ msg.task_mode,avatarId,taskId,msg.dialog_source};
            Notifier.instance.SendNotification(SceneState.ChatRecoonect,info);
        }
        else
        {
            NotifyInfo info = new NotifyInfo();
            info.key = SceneState.ChatRecoonect;
            info.param = new object[]{ msg.task_mode,avatarId,taskId,msg.dialog_source};
            Notifier.instance.SendNotification(SceneState.ChatRecoonect,info);
        }
        CS_CreateDialogTaskCacheReq cache = new CS_CreateDialogTaskCacheReq()
        {
            avatar_id = avatarId,
            task_id = taskId,
            task_mode = msg.task_mode,
            topic_id = _chatModel.topicId,
        };
        MsgManager.instance.SendMsg(cache);
        VFDebug.Log($"DialogueSlow:  timestamp:{TimeExt.currTime}, type: 点击按钮进入对话");
    }

    //处理服务器对话数据
    private void DealDialogTaskData(PB_DialogTaskMsgResp msg, short msgId)
    {
        if (msg == null)
        {
            Debug.LogError("DealDialogTaskData data is null");
            DealErrorData("DealDialogTaskData data is null");
            return;
        }

        for (int i = 0; i < msg.msg_items.Count; i++)
        {
            PB_DialogTaskMsgItem item = msg.msg_items[i];
            _chatModel.SetTaskMsgItemByMsgId(item);
            if (DealPastMsgData(item.round_id, item.dialog_id))
            {
                MsgData msgData = new MsgData();
                msgData.msgId = msgId;
                msgData.data = item;
                DealWaitsSequenceLogic(item.sequence_id, msgData);
            }
        }
        
        for (int i = 0; i < msg.ext_items.Count; i++)
        {
            PB_DialogTaskExtItem item = msg.ext_items[i];
            if (DealPastMsgData(item.round_id, item.dialog_id))
            {
                MsgData msgData = new MsgData();
                msgData.msgId = msgId;
                msgData.data = item;
                bool isAccept = _stateMachine.currState.OnHandleMsg(msgData);
                if (!isAccept)
                    _handleMsgDatas.Add(msgData);
            }
        }

        for (int i = 0; i < msg.next_items.Count; i++)
        {
            PB_DialogTaskNextItem item = msg.next_items[i];
            if (DealPastMsgData(item.round_id, item.dialog_id))
            {
                MsgData msgData = new MsgData();
                msgData.msgId = msgId;
                msgData.data = item;
                bool isAccept = _stateMachine.currState.OnHandleMsg(msgData);
                if (!isAccept)
                    _handleMsgDatas.Add(msgData);
            }
        }

        // //新埋点：任务开启
        // DataDotCutDialogueStart dot = new DataDotCutDialogueStart();
        // dot.Dialogue_id = DataDotMgr.GetDialogId();
        // PB_DialogMode chatMode = this.GetModel<ChatModel>(ModelConsts.Chat).chatMode;
        //
        // dot.Task_type = "Task";
        // if (chatMode == PB_DialogMode.WarmupPractice)
        // {
        //     dot.Task_type = "Warmup";
        // }
        // else if (chatMode == PB_DialogMode.Career)
        // {
        //     dot.Task_type = "Free_talk";
        // }
        // dot.Task_id = curTaskId;
        // DataDotMgr.Collect(dot);
    }
    
    public void DealErrorData(string address, string errcode = "")
    {
        if(GetChatStateName() == ChatState.None)
            return;

        DataDotCutApiCase dataDotCutApiCase = new DataDotCutApiCase();
        dataDotCutApiCase.Dialogue_id = _chatModel.dialogId;
        dataDotCutApiCase.Task_id = curTaskId;
        dataDotCutApiCase.Dialogue_step = _chatModel.GetStepIdByRoundId(_chatModel.curRoundId);
        dataDotCutApiCase.Task_mode = (int)_chatModel.chatMode;
        dataDotCutApiCase.Api_address = address;
        dataDotCutApiCase.Cut_mode = 1;
        DataDotMgr.Collect(dataDotCutApiCase);
        Debug.LogError("server error "+address);
        //点击返回出弹窗
        this.GetUI<CommConfirmUI>(UIConsts.CommConfirm).Open(I18N.inst.MoStr("common_chat_error") + "\n" + address, () =>
        {
            if (!GetModel<LoginOnBoardingModel>(ModelConsts.Login).IsOnBoarding)
                SettlementExitChat(SettlementClickType.AbnormalExitChat);
            else
                OnboardingExit();
        }, null, 1, null, "chat_btn_cancel");
    }

    //钻石不够
    private void NotDiamondEnough()
    {
        if(GetChatStateName() == ChatState.None)
            return;
        //点击返回出弹窗
        this.GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N("ui_common_lack_diamond", () =>
        {
            SettlementExitChat(SettlementClickType.AbnormalExitChat);
            GetController<ShopController>(ModelConsts.Shop).EnterShop();
            
        }, null, 1, null, "chat_btn_cancel");
    }

    //处理死等逻辑
    private void DealWaitsSequenceLogic(int msgSequenceId, MsgData msgData)
    {
        if (msgSequenceId < _waitSequeneId)
            Debug.LogError($"SequenceId is error: msgSequenceId:{msgSequenceId}  _waitSequeneId:{_waitSequeneId}");
        _waitDialogTaskMsgRespToSeqId[msgSequenceId] = msgData;
        int length = _waitDialogTaskMsgRespToSeqId.Count;
        for (int i = 0; i < length; i++)
        {
            if (_waitDialogTaskMsgRespToSeqId.ContainsKey(_waitSequeneId))
            {
                bool isAccept = _stateMachine.currState.OnHandleMsg(_waitDialogTaskMsgRespToSeqId[_waitSequeneId]);
                if (!isAccept)
                    _handleMsgDatas.Add(msgData);
                _waitDialogTaskMsgRespToSeqId.Remove(_waitSequeneId);
                _waitSequeneId++;
            }
            else
                break;
        }
    }

    private void ScaffoldPush()
    {

    }

    private bool DealPastMsgData(int roundId, long dialogId)
    {
        //判断roundId 是否是之前的 并且dialogId需要一致 
        if (_chatModel.curRoundId != roundId || _chatModel.dialogId != dialogId)
        {
            Debug.Log(" DealPastMsgData data is error:cur roundId " + _chatModel.curRoundId +" msg roundid "+roundId+ " cur dialogId " +
                           _chatModel.dialogId+" msg  dialogId "+dialogId);
            if(_chatModel.curRoundId != 0)
                DealErrorData("_chatModel.curRoundId is error");
            return false;
        }
        else
            return true;
    }

    #endregion

    #region 改变状态接口

    public void EnterChat(long avatarId,long taskId,PB_DialogSourceEnum sourceEnum,PB_DialogMode chatMode)
    {
         curSourceEnum = sourceEnum;
         VFDebug.Log("curSourceEnum  "+curSourceEnum);
         // GetModel<LearnPathModel>(ModelConsts.LearnPath).learnPathTaskState = false;
         // GetModel<LearnPathModel>(ModelConsts.LearnPath).NeedAutoOpen = curSourceEnum == PB_DialogSourceEnum.DialogSourceLearnPath;
         SetTaskId(taskId);
         SetAvatarId(avatarId);
         _chatModel.SetChatMode(chatMode);
         ChangeState(ChatState.Select);
    }
    
    // public void EnterCareerChat(long avatarId,PB_DialogSourceEnum sourceEnum)
    // {
    //     curSourceEnum = sourceEnum;
    //     SetAvatarId(avatarId);
    //     SetTaskId(0);
    //     _chatModel.SetChatMode(PB_DialogMode.Career);
    //     ChangeState(ChatState.Select);
    //
    // }
    
    public void EnterOnBoardingChat(long avatarId,long taskId)
    {

        SetAvatarId(avatarId);
        SetTaskId(taskId);
        _chatModel.SetChatMode(PB_DialogMode.OnBoarding);
        ChangeState(ChatState.Select);
        
         _chatModel.SetChatMode(PB_DialogMode.OnBoarding);
         (GetChatState() as ChatStateSelect).OnUIModelSelect();
         CreateDialog();
    }

    private void SetTaskId(long taskId)
    {
        this.curTaskId = taskId;
        // curTaskId = 1;
    }
    private void SetAvatarId(long avatarId)
    {
        this.curAvatarId = avatarId;
        // curAvatarId = 17186613838304477;
    }

    //点击返回
    public void ExitChat()
    {
        if (curSourceEnum == PB_DialogSourceEnum.DialogSourceMap && _chatModel.chatMode == PB_DialogMode.Career)
        {
            var msg = new CS_GetAvatarTaskInfoReq()
            {
                avatar_id = curAvatarId
            };
            MsgManager.instance.SendMsg(msg);
        }
        // else if (curSourceEnum == PB_DialogSourceEnum.DialogSourceUserContacts && _chatModel.chatMode == PB_DialogMode.Career)
        // {
        //     GetController<ContactsController>(ModelConsts.ContactsController).EnterContacts();
        // }
        ChangeState(ChatState.Exit, SettlementClickType.ExitChat);
    }

    //更改聊天状态
    public void ChangeState(string name, params object[] args)
    {
        VFDebug.Log("ChatController  ChangeState  " + name);
        _stateMachine.ChangeState(name, args);
        if (name != ChatState.None)
        {
            List<int> removeIndex = new List<int>();
            for (int i = 0; i < _handleMsgDatas.Count; i++)
            {
                bool isAccept = _stateMachine.currState.OnHandleMsg(_handleMsgDatas[i]);
                if (isAccept)
                    removeIndex.Add(i);
            }

            for (int i = removeIndex.Count - 1; i >= 0; i--)
            {
                _handleMsgDatas.RemoveAt(removeIndex[i]);
            }
        }
        // Debug.LogError("cur state :"+name);
    }

    #endregion


    #region 语音相关接口

    public void PlayTalking(object data)
    {
        if (_curTalkingId == 0)
        {

        }
        else
        {
            _audioQueue.Enqueue(data);
        }
    }

    public void StopTalking(object data)
    {

    }

    //打断之前所有语音
    public void InterruptTalking(object data)
    {
        StopAllAudio();
        if (data == null)
            PlayTalking(data);
    }

    //语音播放完成
    public void TalkingOver(object data)
    {
        if (_audioQueue.Count > 0)
        {
            object aaa = _audioQueue.Dequeue();
            _curTalkingId = (int)aaa;
        }
        else
        {
            _curTalkingId = 0;
        }
    }

    public void StopAllAudio()
    {
        _audioQueue.Clear();
    }

    public void ClickListenAduio()
    {

    }

    //显示avatarName
    public void ShowAvatarName(string avatarName ,string job)
    {
        if (string.IsNullOrEmpty(avatarName) && _chatModel.chatMode != PB_DialogMode.OnBoarding)
            Debug.LogError("avatarName is null");
        else if (!string.IsNullOrEmpty(avatarName))
        {

            string[] strArr= avatarName.Split(" ");
            if (strArr.Length > 1) {
                avatarName = strArr[0];
            }

            if (!this.GetUI(UIConsts.Chat).isShow)
            {
                this.GetUI(UIConsts.Chat).Show().onCompleted += () =>
                {
                    this.GetUI<ChatUI>(UIConsts.Chat).ShowAvatarName(avatarName,job);
                };
            }
            else
                this.GetUI<ChatUI>(UIConsts.Chat).ShowAvatarName(avatarName,job);
        }
        
    }

    public void ShowTopic(PB_TopicInfo info)
    {
        if (null == info)
            return;
        if (_chatModel.chatMode != PB_DialogMode.Career)
            return;
        if (!GetUI(UIConsts.Chat).isShow)
            GetUI(UIConsts.Chat).Show().onCompleted += () => { GetUI<ChatUI>(UIConsts.Chat).ShowTopic(info); };
        else
            GetUI<ChatUI>(UIConsts.Chat).ShowTopic(info);
    }

    //隐藏avatarName
    public void HideAvatarName()
    {
        if(this.GetUI<ChatUI>(UIConsts.Chat).isShow)
            this.GetUI<ChatUI>(UIConsts.Chat).HideAvatarName();
    }

    public void OpenChatSuggestionUI(int bubbleId,int roundId)
    {
        this.GetUI<ChatSuggestionUI>(UIConsts.ChatSuggestion).Show(bubbleId,roundId,_chatModel.chatMode);
    }

    #endregion

    #region 外部调用

    //获取聊天状态
    public string GetChatStateName()
    {
        return GetStateMachine().currState.name;
    }

    //获取当前状态
    public BaseChatMachineState GetChatState()
    {
        return GetStateMachine().currState;
    }

    //获取状态机
    public ChatStateMachine GetStateMachine()
    {
        return _stateMachine;
    }

    //获取上一个状态名字
    public string GetPreStateMachine()
    {
        return _stateMachine.preState.name;
    }

    public string GetChatCellResName(ChatCellType type)
    {
        return _chatCellResDic[type];
    }

    public void ClearAllStateData()
    {
        if (GetChatStateName() == ChatState.Exit)
            _stateMachine.ClearAllData();
        else
        {
            Debug.LogError("other state cannot use: " + GetChatStateName());
        }
    }


    #endregion

    #region 处理ui显示接口

    //清除当前所有cell
    public void ClearChatUIData()
    {
        this.GetUI<ChatUI>(UIConsts.Chat).ClearData();
    }


    //开始下一步
    public void StartNextRound()
    {
        SendDialogTaskCacheMsg();
        ChangeState(ChatState.Avatar);
        isPlayNewWordAble = true;
        // if (_chatModel.chatMode == PB_DialogMode.Exercise)
        //     ChangeState(ChatState.Avatar);
        // else
        //     ChangeState(ChatState.PreTalk);
    }

    public void SendGetDialogScaffoldReq(int bubbleId = 0)
    {
        CS_GetDialogScaffoldReq msg = new CS_GetDialogScaffoldReq();
        msg.dialog_id = _chatModel.dialogId;
        msg.round_id = _chatModel.curRoundId;
        msg.task_id = curTaskId;
        msg.step_id = _chatModel.GetStepIdByRoundId(_chatModel.curRoundId);
        msg.dialog_mode = _chatModel.chatMode;
        msg.bubble_id = bubbleId;
        MsgManager.instance.SendMsg(msg,(a,b)=>GetDialogScaffoldFail(msg));
    }

    private void GetDialogScaffoldFail(CS_GetDialogScaffoldReq msg)
    {
        if(msg.dialog_id == _chatModel.dialogId)
            this.GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("help-error-toast",true);
        if (msg.dialog_id == _chatModel.dialogId && msg.round_id == _chatModel.curRoundId)
        {
            GetChatState().ClickBulbLightFail();
        }
    }

    public void SendDialogTaskCacheMsg()
    {
        _chatModel.AddRoundId();
        CS_GetDialogTaskNextRoundMsgReq sendMsg = new CS_GetDialogTaskNextRoundMsgReq();
        sendMsg.dialog_id = _chatModel.dialogId;
        sendMsg.round_id = _chatModel.curRoundId;
        MsgManager.instance.SendMsg(sendMsg,(a,n)=>DealErrorData("CS_GetDialogTaskNextRoundMsgReq"));
        TestCreateDialogTime = TimeExt.currTime;
    }

    #endregion

    public override void OnUpdate(int interval)
    {
        _stateMachine.Update(interval);
    }
    
    public void SetIsSendASREnd(bool active)
    {
        _isSendASREnd = active;
    }

    public void CreateDialog()
    {
        
        CS_CreateDialogTaskReq info = new CS_CreateDialogTaskReq();
       
        if (_chatModel.chatMode == PB_DialogMode.Challenge || _chatModel.chatMode == PB_DialogMode.Exercise || _chatModel.chatMode == PB_DialogMode.OnBoarding|| _chatModel.chatMode == PB_DialogMode.Tutor || _chatModel.chatMode == PB_DialogMode.Career)
            info.task_id = curTaskId;
        info.avatar_id = curAvatarId;
        info.round_id = _chatModel.curRoundId;
        info.task_mode = _chatModel.chatMode;
        info.dialog_source = curSourceEnum;
        info.topic_id = _chatModel.topicId;

        //_learnPathModel  get参数前，有前置要求
        // info.learn_path_params = _learnPathModel.GetPathParams();


        MsgManager.instance.SendMsg(info,CreateDialogFail);
        TestCreateDialogTime = TimeExt.currTime;
    }

    private void CreateDialogFail(GRPCManager.ErrorType et,Google.Protobuf.IMessage msg)
    {
        float deltaTime = TimeExt.currTime - TestCreateDialogTime;
        VFDebug.Log($"DialogueSlow: time:{deltaTime},timestamp:{TimeExt.currTime}, type: 创建对话服务器报错");
        DealErrorData("CS_CreateDialogTaskReq");
    }

    //结算界面点击再次挑战（3.14版本需求：再次进入当前模式)
    public void SettlementChallengeAgain()
    {
        PB_DialogMode curChatMode = _chatModel.chatMode;
        Debug.Log("curChatMode  "+curChatMode);
        ChangeState(ChatState.Exit, SettlementClickType.ChallengeAgain);
        _chatModel.SetChatMode(curChatMode);
        // ChangeState(ChatState.PreTalk, curChatMode);
        ChangeState(ChatState.Select);
    }

    /// <summary>
    /// 通过taskID和avatarID开启一个新对话
    /// </summary>
    /// <param name="taskID"></param>
    /// <param name="avatarId"></param>
    public void SettlementStartTask(PB_DialogMode chatMode, long taskID, long avatarId,PB_DialogSourceEnum sourceEnum = PB_DialogSourceEnum.DialogSourceLearnPath)
    {
        PB_DialogMode curChatMode = chatMode;
        Debug.Log("SettlementStartTask mode : " + curChatMode+ " taskID="+ taskID);
        ChangeState(ChatState.Exit, SettlementClickType.ChallengeAgain);
        EnterChat(avatarId,taskID,sourceEnum,curChatMode);
    }

    //结算界面点击返回进入模式选择
    public void SettlementReturnMode()
    {
        // ChangeState(ChatState.Exit, SettlementClickType.ReturnMode);
        // ChangeState(ChatState.Select);

        SettlementExitChat();
    }
    
    // onboarding出错了 重新开启第一轮
    private void OnboardingExit()
    {
        long avatarId = curAvatarId;
        long taskId = curTaskId;
        ChangeState(ChatState.Exit, SettlementClickType.ChallengeAgain);
        EnterOnBoardingChat(avatarId,taskId);
    }

    // 结算界面点击返回 退出聊天
    public void SettlementExitChat(SettlementClickType param = SettlementClickType.ExitChat)
    {
        ChangeState(ChatState.Exit, param);
        Notifier.instance.SendNotification(NotifyConsts.DoChatBack);
        ChangeState(ChatState.None);
        GetController<SettlementController>(ModelConsts.Settlement).ClearData();
        Notifier.instance.SendNotification(NotifyConsts.LearnPathReShowEvent);
        GetModel<MainModel>(ModelConsts.Main).SetChatEnterPos();
        Notifier.instance.SendNotification(NotifyConsts.ShowGrowthExp);
      
    }

    /// <summary>
    /// 结算点继续
    /// </summary>
    /// <param name="param"></param>
    public void SettlementExitAndContinueChat(SettlementClickType param = SettlementClickType.ExitChat)
    {
        ChangeState(ChatState.Exit, param);
        ChangeState(ChatState.None);
        //ChangeState(ChatState.Select); //EnterChat中已设置 此处重复
        GetController<SettlementController>(ModelConsts.Settlement).ClearData();
        Notifier.instance.SendNotification(NotifyConsts.LearnPathReShowEvent);
    }

    // 结算界面点击进入挑战模式 （3.14版本需求：直接进入英雄模式对话页面)
    public void SettlementEnterChallenge()
    {
        ChangeState(ChatState.Exit, SettlementClickType.EnterChallenge);
        ChangeState(ChatState.Select);
    }

    public void SettlementEnterAITutor()
    {
        ChangeState(ChatState.Exit, SettlementClickType.EnterChallenge);
        ChangeState(ChatState.Select);
    }

    public void SettlementEnterFreeTalk()
    {
        ChangeState(ChatState.Exit, SettlementClickType.EnterChallenge);
        ChangeState(ChatState.Select);
    }

    // 结算界面点击开启强化 
    public void SettlementEnterIntensify()
    {
        // if (_chatModel.chatMode == PB_DialogMode.Exercise)
        // {
        //     GetUI<MainHeaderUI>(UIConsts.MainHeader).SetHeader(MainHeaderMode.Talk);
        // }
        
        ChangeState(ChatState.Exit, SettlementClickType.EnterIntensify);
        ChangeState(ChatState.None);//Todo:None ?? 怎么跳转的？
        GetController<SettlementController>(ModelConsts.Settlement).ClearData();
        _chatModel.SetChatMode(PB_DialogMode.Intensify);
        GetUI(UIConsts.ChatModeRetry).Show(curTaskId, curAvatarId);
        // GetUI<ChatModeRetryUI>(UIConsts.ChatModeRetry).BindData();

        //新埋点：结算页进入强化练习
        DataDotClickDialogueResultPractice dot = new DataDotClickDialogueResultPractice();
        dot.Dialogue_id = _chatModel.dialogId;
        DataDotMgr.Collect(dot);

    }

    //todo:rm 无调用点
    // 主场景/结算时进入每日强化 
    public void EnterDailyIntensify()
    {
        // if (_chatModel.chatMode == PB_DialogMode.DailyIntensify || _chatModel.chatMode == PB_DialogMode.MNone)
        // {
        //     GetUI<MainHeaderUI>(UIConsts.MainHeader).SetHeader(MainHeaderMode.Talk);
        // }
        
        ChangeState(ChatState.None);//Todo:None 待移除
        _chatModel.SetChatMode(PB_DialogMode.DailyIntensify);
        // GetUI(UIConsts.ChatModeRetry).Show(curTaskId, curAvatarId);
        // GetUI<ChatModeRetryUI>(UIConsts.ChatModeRetry).BindData();
        
    }

   

    //主路径/ 结算时进入WarmUp_practice
    public void EnterWarmUp_practice(long taskId,PB_CourseLearnPathParams mainPathParams,PB_DialogSourceEnum sourceEnum = PB_DialogSourceEnum.DialogSourceLearnPath)
    {
        //  GetUI<MainHeaderUI>(UIConsts.MainHeader).SetHeader(MainHeaderMode.Talk);
        ChangeState(ChatState.None);//Todo:None  RQCtrl
        //_chatModel.SetChatMode(PB_DialogMode.WarmupPractice); //在之前设置好 这样我训练中心的错题和知识点也可以走这部分
        // GetUI(UIConsts.ChatModeRetry).Show(taskId,(long)0);
        curSourceEnum = sourceEnum;


        //强行模仿 状态机
        GetController<ReviewQuestionController>(ModelConsts.ReviewQuestionModel)
            .OnEnter(_chatModel.chatMode, taskId, 0,mainPathParams);
        VFDebug.Log("curSourceEnum  "+curSourceEnum);

    }
    
    //训练中心复习的warmUp
    // public void EnterDrillHubWarmUp(long taskId, PB_DialogMode dialogMode, PB_DialogSourceEnum sourceEnum)
    // {
    //     GetUI<MainHeaderUI>(UIConsts.MainHeader).SetHeader(MainHeaderMode.Talk);
    //     ChangeState(ChatState.None);//Todo:None  RQCtrl
    //     _chatModel.SetChatMode(dialogMode);
    //     curSourceEnum = sourceEnum;
    //     
    //     //强行模仿 状态机
    //     GetController<ReviewQuestionController>(ModelConsts.ReviewQuestionModel)
    //         .OnEnter(_chatModel.chatMode, taskId, 0);
    //     VFDebug.Log("curSourceEnum  "+curSourceEnum);
    // }
    
    // onboarding结算界面
    public void SettlementOnboarding()
    {
        
        ChangeState(ChatState.Exit, SettlementClickType.EnterOnboarding);
        ChangeState(ChatState.None);
    }
    
    public void OpenChatSocreUI(PB_DialogTaskQuestionResult data)
    {
        //新埋点:对话气泡中点击分数展示评分
        DataDotClickDialogueScoreShow dot = new DataDotClickDialogueScoreShow();
        dot.Dialogue_id = DataDotMgr.GetDialogId();
        DataDotMgr.Collect(dot);
        
        
        this.GetUI(UIConsts.ChatScore).Show(data);
    }

    public void CloseChatSocreUI()
    {
       
        
        
        this.GetUI(UIConsts.ChatScore).Hide();
    }

    //最大是1
    public void ForceRefershScroll(float progress)
    {
        if (progress > 1)
        {
            Debug.LogError("最大值是1");
            return;
        }

        this.GetUI<ChatUI>(UIConsts.Chat).ForceRefershScroll(progress);
    }

    //获取模式是否是第一次点击离开
    public bool GetModeIsFirstExit(PB_DialogMode dialogMode)
    {
        bool isFirst = true;
        if (dialogMode == PB_DialogMode.OnBoarding)
            return isFirst;
        string key = GetChatModeExitKey(dialogMode);
        JsonData data = LocalCfgMgr.instance.GetRole("chatModeExitState", new JsonData());
        if (data.ContainsKey(key))
        {
            isFirst = (bool)data[key];
        }
        return isFirst;
    }
    //设置本地存储数据
    public void SetModeIsFirstExit(PB_DialogMode dialogMode, bool value)
    {
        JsonData data = LocalCfgMgr.instance.GetRole("chatModeExitState", new JsonData());
        string key = GetChatModeExitKey(dialogMode);
        data[key] = value;
        LocalCfgMgr.instance.SetRole("chatModeExitState", data);
    }

    //获取存储数据的ke
    private string GetChatModeExitKey(PB_DialogMode dialogMode)
    {
        string key = "";
        switch (dialogMode)
        {
            case PB_DialogMode.Open:
                key = "IsFirstExitOpen";
                break;
            case PB_DialogMode.Exercise:
                key = "IsFirstExitExercise";
                break;
            case PB_DialogMode.Challenge:
                key = "IsFirstExitChallenge";
                break;
            case PB_DialogMode.Intensify:
                key = "IsFirstExitIntensify";
                break;
            case PB_DialogMode.Career:
                key = "IsFirstExitCareer";
                break;
            default:
                Debug.LogError("GetModeIsFirstExit dialogMode is error " + dialogMode);
                break;
        }

        return key;
    }
    /// <summary>
    /// 点击avatar遮罩后 模式是否变成普通模式
    /// </summary>
    /// <param name="isNewest">是否是最新轮次的气泡</param>
    public void JudgeChangeChallengeNormal(bool isNewest)
    {
        if (isNewest && _chatModel.chatMode == PB_DialogMode.Challenge)
        {
            if (GetChatStateName() == ChatState.Avatar)
            {
                _chatModel.ChangeChallengeNormal();
            }
            else if (GetChatStateName() == ChatState.PlayerChallenge)
            {
                bool isSend = (GetChatState() as ChatStatePlayerChallenge).isSend;
                if (!isSend)
                    _chatModel.ChangeChallengeNormal();
            }

        }
    }

    public void ChangeChatContainHeight(float taskPosY)
    {
        this.GetUI<ChatUI>(UIConsts.Chat).ChangeChatContainHeight(taskPosY);
    }
    
    //onboarding 提示
    public void ONSCGetAudioForFlowAck(long ttsId,PB_Code code)
    {
        if (code != PB_Code.Normal)
        {
            DataDotCutApiCase dataDotCutApiCase = new DataDotCutApiCase();
            dataDotCutApiCase.Dialogue_id = _chatModel.dialogId;
            dataDotCutApiCase.Task_id = curTaskId;
            dataDotCutApiCase.Dialogue_step = _chatModel.GetStepIdByRoundId(_chatModel.curRoundId);
            dataDotCutApiCase.Task_mode = (int)_chatModel.chatMode;
            dataDotCutApiCase.Api_address = "SC_GetAudioForFlowAck";
            dataDotCutApiCase.Cut_mode = 3;
            DataDotMgr.Collect(dataDotCutApiCase);
        }

        if (GetChatStateName() != ChatState.PlayerOnboarding)
        {
            Debug.LogError("ONSCGetAudioForFlowAck state is error  "+GetChatStateName());
            return;
        }
        
        (GetChatState() as ChatStatePlayerOnboarding).WaitFlowHelpTTSId(ttsId);
    }
    //onboarding 生词点击提示
    public void ONSCNewWordTipsAudio(long ttsId,PB_Code code)
    {
        if (code != PB_Code.Normal)
        {
            DataDotCutApiCase dataDotCutApiCase = new DataDotCutApiCase();
            dataDotCutApiCase.Dialogue_id = _chatModel.dialogId;
            dataDotCutApiCase.Task_id = curTaskId;
            dataDotCutApiCase.Dialogue_step = _chatModel.GetStepIdByRoundId(_chatModel.curRoundId);
            dataDotCutApiCase.Task_mode = (int)_chatModel.chatMode;
            dataDotCutApiCase.Api_address = "SC_GetAudioForFlowAck";
            dataDotCutApiCase.Cut_mode = 3;
            DataDotMgr.Collect(dataDotCutApiCase);
        }
        if (GetChatStateName() != ChatState.Avatar || _chatModel.chatMode !=PB_DialogMode.OnBoarding)
        {
            Debug.LogError("ONSCGetAudioForFlowAck state is error  "+GetChatStateName());
            return;
        }

        (GetChatState() as ChatStateAvatar).GuideAudioCallBack(ttsId);
    } 
    
    //发送数据变更请求
    public void SendChangeMarkInfoReq(PB_MarkOperationTypeEnum option)
    {
        CS_ChangeMarkInfoReq changeMarkInfoReq = new CS_ChangeMarkInfoReq();
        changeMarkInfoReq.option = option;
        MsgManager.instance.SendMsg(changeMarkInfoReq);
    } 


    private void OnSCChangeMarkInfoAck(SC_ChangeMarkInfoAck msg)
    {
        if (msg.data.mark_info != null)
        {
            _chatModel.SetFirsrTaskState(msg.data.mark_info.is_notice);
        }
    }
    
    private void OnSCDialogSuggestionAck(SC_DialogSuggestionAck msg)
    {
        if (IsNewDialogMode(_createDialogMode))
        {
            return;
        }
        
        if (msg.code != PB_Code.Normal || msg.data == null)
        {
            VFDebug.LogError("获取建议报错: "+msg.code);
            return;
        } 
        if(msg.data.dialog_mode == PB_DialogMode.Tutor)
            _chatModel.SetAITutorSuggestionByBubbleId(msg.data.tutor_suggestion_resp);
        else if(msg.data.dialog_mode == PB_DialogMode.Career)
            _chatModel.SetAITutorSuggestionByBubbleId(msg.data.career_suggestion_resp);
        else if(msg.data.dialog_mode == PB_DialogMode.Challenge)
            _chatModel.SetAITutorSuggestionByBubbleId(msg.data.challenge_suggestion_resp);
        MsgData msgData = new MsgData();
        msgData.msgId = MsgMap.msgT2ID[typeof(SC_DialogSuggestionAck)];
        msgData.data = msg;
        bool isAccept = _stateMachine.currState.OnHandleMsg(msgData);
        if (!isAccept) //有可能是状态机到了endProgress 不算bug
            Debug.Log("<color=#FF0000>SC_DialogSuggestionAck deal data is error <color/>");
    }
    
    private void OnSCDialogTranslateAck(SC_DialogTranslateAck msg)
    {
        if (IsNewDialogMode(_createDialogMode))
        {
            return;
        }

        if (msg.code != PB_Code.Normal || msg.data == null)
        {
            VFDebug.LogError("SC_DialogTranslateAck iserror: "+msg);
            return;
        }
        
        if (msg.data.dialog_id != _chatModel.dialogId)
            return;
        
        BaseChatCell baseChatCell =  _chatModel.GetChatCellByBubbleId(msg.data.bubble_id);
        RichContent richContent = new RichContent();
        richContent.Content = msg.data.trans_content;
        richContent.Type = RichContentType.Text;
        richContent.IsLast = true;
        if (baseChatCell != null)
        {
            if (baseChatCell is AvatarNormalCell)
            {
                AvatarNormalCell avatarNormalCell = baseChatCell as AvatarNormalCell;
                avatarNormalCell.AppendTransRichContent(richContent);
            }
            else if (baseChatCell is AdviceCell)
            {
                AdviceCell adviceCell = baseChatCell as AdviceCell;
                adviceCell.AppendTransRichContent(msg.data.content_list.ToList());
            }
            else if (baseChatCell is PlayerFollowCell)
            {
                PlayerFollowCell followCell = baseChatCell as PlayerFollowCell;
                followCell.AppendTransRichContent( msg.data.trans_content);
            }
            else if (baseChatCell is ScaffoldCell)
            {
                ScaffoldCell scaffoldCell = baseChatCell as ScaffoldCell;
                scaffoldCell.AppendTransRichContent(msg.data.trans_content);
            }
        }
        
    }

    private void GetUserAssistLevelAck(SC_SetUserAssistLevelAck msg)
    {
        if (msg.code == PB_Code.Normal)
            SendNotification(NotifyConsts.RefreshAutoHelpEvent);
    }
    
    public void CancelRecordUI()
    {
        this.GetUI<RecordUI>(UIConsts.RecordUI).CancelRecording();
    }
    
    public void AddLoadingBubble()
    {
        if (loadingBubbleId == -1)
        {
            BaseChatCell baseChatCell = new PreTalkCell();
            loadingBubbleId =  this.GetUI<ChatUI>(UIConsts.Chat).AddChatCellCom(baseChatCell, ChatCellType.PreTalk, null);
        }
    }

    public void RemoveLoadingBubble()
    {
        if (loadingBubbleId != -1)
        {
            this.GetUI<ChatUI>(UIConsts.Chat).ReturnChatCellItem(loadingBubbleId,ChatCellType.PreTalk);
            loadingBubbleId = -1;
        }
    }
    
    public void AddEmptyBubble()
    {
        if (emptyBubbleId == -1)
        {
            BaseChatCell baseChatCell = new EmptyCell();
            emptyBubbleId =  this.GetUI<ChatUI>(UIConsts.Chat).AddChatCellCom(baseChatCell, ChatCellType.Empty, null);
        }
    }

    public void RemoveEmptyBubble()
    {
        if (emptyBubbleId != -1)
        {
            this.GetUI<ChatUI>(UIConsts.Chat).ReturnChatCellItem(emptyBubbleId, ChatCellType.Empty);
            emptyBubbleId = -1;
        }
    }

    private void UpdateScaffoldCell(string name, object roundId)
    {
        _chatModel.SetRoundScaffoldIsShow((int)roundId, true);
        _chatModel.SetScaffoldShowState((int)roundId, true);
    }

    public void ClearData(bool isEnterChat = false)
    {
        _handleMsgDatas.Clear();
        _waitSequeneId = 1;
        _endSequeneId = 1;
        _waitDialogTaskMsgRespToSeqId.Clear();
        _audioQueue.Clear();
        _curTalkingId = 0;
        RemoveLoadingBubble();
        RemoveEmptyBubble();
        ClearChatUIData();
        _chatModel.ClearData();
        Debug.Log("chat ClearData");
        ASRManager.instance.StopASRWithCancel();
        TTSManager.instance.StopTTS();
        TimelineManager.instance.StopTimeLine();
        this.GetUI<RecordUI>(UIConsts.RecordUI).ClearStatusAndTimer();
        this.GetUI(UIConsts.RecordUI).Hide();
        if(!isEnterChat)
            this.GetUI(UIConsts.Chat).Hide();
        Debug.Log("hide");
        this.GetModel<TaskModel>(ModelConsts.Task).ClearAllData();
    }
    
    
    public void SetPlayNewWordAble(bool able)
    {
        isPlayNewWordAble = able;
    }

}