using UIBind.FragmentPractice;
using UnityEngine;
using FairyGUI;
using System;
using ScriptsHot.Game.Modules.ChatStart;
using ScriptsHot.Game.Modules.MainPath;
using Msg.basic;
using Game.Modules.FragmentPractice;
using Msg.question;

namespace ScriptsHot.Game.Modules.FragmentPractice
{
    public class FragBookPanelUI : BaseUI<FragBookPanel>, IQuestionEventListener
    {
        public override void OnBackBtnClick()
        {
            OnBtnCloseClicked();
        }

        private FragmentPracticeController FragController =>
            GetController<FragmentPracticeController>(ModelConsts.FragmentPractice);
            
        private BookModel bookData;
        private int step = 0;

        public override string uiLayer => UILayerConsts.Top;
        protected override bool isFullScreen => true;

        public FragBookPanelUI(string name) : base(name)
        {
        }

        protected override void OnInit(GComponent uiCom)
        {
            AddUIEvent(ui.btnClose.onClick, OnBtnCloseClicked);
        }
        
        protected override void OnShow()
        {
            string bookIDStr = this.args[0].ToString();
            Debug.Log("bookIDStr="+ bookIDStr);
            long bookId = (long)this.args[0];
            LoadData(bookId);

            QuestionEventManager.Instance.AddListener(this);
        }

        protected override void OnHide()
        {            
            QuestionEventManager.Instance.RemoveListener(this);
            ui.contentLoader.url = null;
            FragController.QuitPractice();
        }

        private async void LoadData(long bookId)
        {

            UIManager.instance.GetUI<WarmupLoadingCutInUI>(UIConsts.WarmupLoadingCutInUI)?.Show();
            bookData = new BookModel(); 
            await bookData.LoadBookData(bookId);
            UIManager.instance.GetUI<WarmupLoadingCutInUI>(UIConsts.WarmupLoadingCutInUI)?.TryHide(0.5f);
            
            ui.progressBar.Init(bookData.TotalSteps + 1, 0);  // +1是因为有封面页

            ui.contentLoader.url = null;
            ui.contentLoader.url = TitlePage.url;
            var titlePage = ui.contentLoader.component as TitlePage;
            titlePage.ShowBook(bookData, OnTitlePageFinish);
        }

        private void OnTitlePageFinish()
        {
            ui.progressBar.SetProgress(1f);
            ui.contentLoader.url = DialoguePage.url;

            step = 1;
            ui.progressBar.SetProgress(step);
            var dialogue = ui.contentLoader.component as DialoguePage;
            dialogue.OnStepChanged += OnStepChanged;
            dialogue.ShowBook(bookData);
        }

        private void OnStepChanged(int step)
        {
            this.step = step;
        }

        private void OnBtnCloseClicked()
        {
            DotPracticeManager.Instance.Collect(new DataDot_Exit());
            UIManager.instance.GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N("ui_common_retention_tips", null,
                    Close, 3, "common_keep_learning", "common_quit", false, 1);
        }

        private void Close()
        {
            var dialogue = ui.contentLoader.component as DialoguePage;
            if (dialogue != null)
            {
                var lastAction = dialogue.NearestAction;
                if (lastAction != null)
                {
                    var content = lastAction.Text;
                    var avatarId = lastAction.Avatar==null?0:lastAction.Avatar.avatar_id;
                    var questionId = lastAction.Practice==null?0:lastAction.Practice.QuestionId;

                    DotPracticeManager.Instance.Collect(new DataDot_ExitBook(avatarId, questionId, content));
                }
            }
            TTSManager.instance.StopTTS();            
            Notifier.instance.SendNotification(NotifyConsts.MainPath3DBackPath);
            FragController.SendExitQuickPracticeReq();
            Hide();
        }

        public static void BindUI()
        {
            TitlePage.Bind();
            DialoguePage.Bind();
            AvatarHead.Bind();
            ChatText.Bind();
            AnswerPopup.Bind();
            BtnDialogueAudio.Bind();
            ClozeOptionBtn.Bind();
            TapChoiceAnswer.Bind();
        }

        public void OnAnswered(){}

        public void OnSubmit()
        {
            ui.progressBar.SetProgress(step);
        }

        public void OnRetry(){}

        public void AutoCheck(){}

        public void OnReset(){}

        public void OnJumpListenTask(){}

        public void OnJumpSpeakTask(){}
    }

}