/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreFriends
{
    public partial class ExploreFriendsBagPanel : UIBindT
    {
        public override string pkgName => "ExploreFriends";
        public override string comName => "ExploreFriendsBagPanel";

        public GGraph imgBG;
        public GLoader bgLoader;
        public GLoader avatarLoader;
        public GImage EmptyAvatarImg;
        public GTextField TitleTxt;
        public FriendCommonBtn NextBtn;
        public GTextField SelectItemName;
        public GGroup NameNode;
        public GList selectList;
        public GTextField ReDrawTxt;
        public GGraph ReDrawBtn;
        public GGraph BackBtn;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            imgBG = (GGraph)com.GetChildAt(0);
            bgLoader = (GLoader)com.GetChildAt(1);
            avatarLoader = (GLoader)com.GetChildAt(2);
            EmptyAvatarImg = (GImage)com.GetChildAt(3);
            TitleTxt = (GTextField)com.GetChildAt(4);
            NextBtn = new FriendCommonBtn();
            NextBtn.Construct(com.GetChildAt(5).asCom);
            SelectItemName = (GTextField)com.GetChildAt(6);
            NameNode = (GGroup)com.GetChildAt(8);
            selectList = (GList)com.GetChildAt(9);
            ReDrawTxt = (GTextField)com.GetChildAt(11);
            ReDrawBtn = (GGraph)com.GetChildAt(12);
            BackBtn = (GGraph)com.GetChildAt(15);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            imgBG = null;
            bgLoader = null;
            avatarLoader = null;
            EmptyAvatarImg = null;
            TitleTxt = null;
            NextBtn.Dispose();
            NextBtn = null;
            SelectItemName = null;
            NameNode = null;
            selectList = null;
            ReDrawTxt = null;
            ReDrawBtn = null;
            BackBtn = null;
        }
    }
}