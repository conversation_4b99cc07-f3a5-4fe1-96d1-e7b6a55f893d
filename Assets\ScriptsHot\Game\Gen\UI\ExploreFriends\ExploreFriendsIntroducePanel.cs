/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreFriends
{
    public partial class ExploreFriendsIntroducePanel : UIBindT
    {
        public override string pkgName => "ExploreFriends";
        public override string comName => "ExploreFriendsIntroducePanel";

        public Controller IsSelect;
        public GGraph imgBG;
        public GGraph bgImg;
        public GLoader bgLoader;
        public GLoader avatarLoader;
        public GTextField TitleTxt;
        public GGroup Introduce;
        public FriendCommonBtn NextBtn;
        public GTextField SelectItemName;
        public GGroup NameNode;
        public GImage bg;
        public GTextField ChatContent;
        public GGroup chatNode;
        public GList selectList;
        public GGraph CloseBtn;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            IsSelect = com.GetControllerAt(0);
            imgBG = (GGraph)com.GetChildAt(0);
            bgImg = (GGraph)com.GetChildAt(1);
            bgLoader = (GLoader)com.GetChildAt(2);
            avatarLoader = (GLoader)com.GetChildAt(3);
            TitleTxt = (GTextField)com.GetChildAt(4);
            Introduce = (GGroup)com.GetChildAt(9);
            NextBtn = new FriendCommonBtn();
            NextBtn.Construct(com.GetChildAt(10).asCom);
            SelectItemName = (GTextField)com.GetChildAt(11);
            NameNode = (GGroup)com.GetChildAt(13);
            bg = (GImage)com.GetChildAt(14);
            ChatContent = (GTextField)com.GetChildAt(15);
            chatNode = (GGroup)com.GetChildAt(16);
            selectList = (GList)com.GetChildAt(17);
            CloseBtn = (GGraph)com.GetChildAt(20);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            IsSelect = null;
            imgBG = null;
            bgImg = null;
            bgLoader = null;
            avatarLoader = null;
            TitleTxt = null;
            Introduce = null;
            NextBtn.Dispose();
            NextBtn = null;
            SelectItemName = null;
            NameNode = null;
            bg = null;
            ChatContent = null;
            chatNode = null;
            selectList = null;
            CloseBtn = null;
        }
    }
}