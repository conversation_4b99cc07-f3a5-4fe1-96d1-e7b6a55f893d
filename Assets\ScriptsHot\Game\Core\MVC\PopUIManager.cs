using System.Collections.Generic;
using UnityEngine;

public class PopUIManager
{
    private static PopUIManager _instance = null;
    public static PopUIManager instance
    {
        get
        {
            if (_instance == null)
                _instance = new PopUIManager();
            return _instance;
        }
    }
    
    /// <summary>
    /// pop优先级 ， 越高越优先, low级x秒才能弹一次
    /// </summary>
    public enum Priority
    {
        //低优 - 60s弹一次
        Low = 1,
        
        //以下全算高优
        Middle = 2,
        
        ExplorePush = 3,
        //连胜结束
        WinEnd,
        
        //好友功能
        Friend,
        
        //Push
        Push,

        //补签功能
        Sign,

        //商业化功能
        Business,
        
        High
    }

    private struct PopUIStruct
    {
        public IBaseUI ui;
        public Priority priority;
        public object[] args;
    }

    /// <summary>
    /// 储存list
    /// </summary>
    private List<PopUIStruct> popUIList = new List<PopUIStruct>();
    /// <summary>
    /// 上一次展示的pop界面
    /// </summary>
    private IBaseUI lastPopUI;
    
    //低优UI展示间隔
    private const int LOW_UI_SHOW_INTERVAL = 60;
    private int curLowUIShowInterval;
    
    /// <summary>
    /// 下一帧检测标记
    /// </summary>
    private bool nextFrameCheckPop = false;

    /// <summary>
    /// 当帧是否检测过，每帧只检测一次
    /// </summary>
    private bool curFrameChecked = false;

    private int canPopDelayFrame = 0; // 新增：用于2帧延迟判断

    /// <summary>
    /// 获取最高优的pop界面
    /// </summary>
    /// <returns></returns>
    private PopUIStruct GetPopUIStruct()
    {
        if (popUIList.Count <= 0)
        {
            return default;
        }
        PopUIStruct popUIStruct = popUIList[0];
        for (int i = 0; i < popUIList.Count; i++)
        {
            if (popUIList[i].priority > popUIStruct.priority)
            {
                popUIStruct = popUIList[i];
            }
        }
        return popUIStruct;
    }

    private int GetPopUIListLength()
    {
        return popUIList.Count;
    }

    /// <summary>
    /// layer层黑名单，总有几个界面不按layer展示
    /// </summary>
    public List<string> LayerExcept = new List<string>()
    {
        //主界面三爹，layer瞎写的
        // UIConsts.MainHeader,
        UIConsts.RankUI,
        UIConsts.ExploreUI
        
        
    };
    
    /// <summary>
    /// 上层界面是否有UI正在展示
    /// </summary>
    private bool IsLayerShowing
    {
        get
        {
            
            if(UIManager.instance.HasLayerShow(UILayerConsts.Loading,LayerExcept))return true;
            if(UIManager.instance.HasLayerShow(UILayerConsts.Guide,LayerExcept))return true;
            if(UIManager.instance.HasLayerShow(UILayerConsts.Float,LayerExcept))return true;
            if(UIManager.instance.HasLayerShow(UILayerConsts.Top,LayerExcept))return true;
            if(UIManager.instance.HasLayerShow(UILayerConsts.Module,LayerExcept))return true;
            if(UIManager.instance.HasLayerShow(UILayerConsts.Home,LayerExcept))return true;
            if(UIManager.instance.HasLayerShow(UILayerConsts.FeaturePage,LayerExcept))return true;
            return false;
        }
    }

    private bool CanPop
    {
        get
        {
            //每帧最多一次
            if (curFrameChecked)
            {
                canPopDelayFrame = 0;
                return false;
            }
            if (GetPopUIListLength() <= 0)
            {
                canPopDelayFrame = 0;
                return false;
            }
            //只在路径页才会弹
            if (!GameEntry.HomepageC.IsHomeTab())
            {
                canPopDelayFrame = 0;
                return false;
            }
            if (IsLayerShowing)
            {
                canPopDelayFrame = 0;
                return false;
            }
            if (lastPopUI != null && lastPopUI.isShow)
            {
                canPopDelayFrame = 0;
                return false;
            }
            if (lastPopUI != null && lastPopUI.isLoadingPackage)
            {
                canPopDelayFrame = 0;
                return false;
            }
            //onBoard引导优先
            if (GameEntry.MainPathC.IsOnBoarding)
            {
                canPopDelayFrame = 0;
                return false;
            }

            // 满足所有条件，开始计数
            canPopDelayFrame++;
            if (canPopDelayFrame >= 2)
            {
                return true;
            }
            return false;
        }
    }

    /// <summary>
    /// 容错用，判断lastPopUI不为空的情况下，当lastPopUI.isShow为false时，强制lastPopUI = null
    /// </summary>
    private void CheckLastPopUI()
    {
        if (lastPopUI == null)
        {
            return;
        }
        
        if (!lastPopUI.isShow && !lastPopUI.isLoadingPackage)
        {
            lastPopUI = null;
        }
    }

    #region 触发点
    /// <summary>
    /// 只做下一帧检测用，避免重复检测
    /// </summary>
    public void Update()
    {
        if (curFrameChecked)
        {
            curFrameChecked = false;
        }
        if (nextFrameCheckPop)
        {
            nextFrameCheckPop = false;
            CheckPop();
        }
    }
    //每秒检测一次
    public void UpdateOneSecend()
    {
        curLowUIShowInterval--;
        CheckPop();
    }

    //当有界面关闭时候
    public void OnUIHide(IBaseUI ui)
    {
        if (lastPopUI == ui)
        {
            lastPopUI = null;
        }

        //方案1，如果pop界面有跳转 先关闭自己 后打开其他界面会有问题
        //CheckPop();
        
        //方案2,下一帧检查
        nextFrameCheckPop = true;
    }
    #endregion

    private void CheckPop()
    {
        //卡死容错用
        CheckLastPopUI();

        if (!CanPop)
        {
            return;
        }

        PopUIStruct popUIStruct = GetPopUIStruct();
        if  (popUIStruct.ui == null)
        {
            return;
        }

        if (popUIStruct.priority == Priority.Low && curLowUIShowInterval < 0)
        {
            return;
        }
        popUIList.Remove(popUIStruct);
        
        lastPopUI = popUIStruct.ui.Show(popUIStruct.args);
        curFrameChecked = true;
        curLowUIShowInterval = LOW_UI_SHOW_INTERVAL;
    }
    
    public void AddPopUI(IBaseUI ui, Priority  priority = Priority.Middle , params object[] args)
    {
        //去重
        for (int i = 0; i < popUIList.Count; i++)
        {
            if (popUIList[i].ui == ui)
            {
                popUIList[i] = new PopUIStruct() //替换为最新的arg
                {
                    ui = ui,
                    priority = priority,
                    args = args
                };
                return;
            }
        }
        if (lastPopUI != null && lastPopUI.isShow && lastPopUI == ui)
        {
            return;
        }
        //
        
        Debug.Log($"==pop队列进新：name = {ui.name} , priority = {priority}");
        
        //添加到一个大列表popUIList里,注意是否重复
        popUIList.Add(new PopUIStruct()
        {
            ui = ui,
            priority = priority,
            args = args
        });
    }

    public void TryRemovePopUI(IBaseUI ui)
    {
        for (int i = 0; i < popUIList.Count; i++)
        {
            if (popUIList[i].ui == ui)
            {
                popUIList.RemoveAt(i);
                return;
            }
        }
    }
}

