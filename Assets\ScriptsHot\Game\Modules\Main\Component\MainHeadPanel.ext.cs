﻿using System;
using FairyGUI;
using Modules.DataDot;
using Msg.basic;
using Msg.economic;
using ScriptsHot.Game.Modules.FragmentPractice;
using ScriptsHot.Game.Modules.MainPath;
using ScriptsHot.Game.Modules.Profile;
using UnityEngine;

namespace UIBind.MainHead
{
    public partial class MainHeadPanel
    {
        private CenterHomeUI _homePanel;
        private bool _isShowPopup;
        
        //埋点用
        private DotRefillBtnEnum _refillEnum;
        private DotPracticeBtnEnum _practiceEnum;
        
        private const int FreeFragmentPracticeTimesMax = 1;
        
        private StaminaModel StaminaModel => ModelManager.instance.GetModel<StaminaModel>(ModelConsts.Stamina);
        private CurrencyModel CurrencyModel => ModelManager.instance.GetModel<CurrencyModel>(ModelConsts.CurrencyController);
        public void OnInit(CenterHomeUI panel)
        {
            _homePanel = panel;

            panel.AddUIEvent(compHead.com.onClick, OnClickProfile);
            panel.AddUIEvent(btnHeart.com.onClick, OnClickHeart);
            panel.AddUIEvent(btnPaywall.com.onClick, OnClickPaywall);
            panel.AddUIEvent(btnStreak.com.onClick, EnterFire);
            panel.AddUIEvent(btnGem.com.onClick, EnterShop);
            panel.AddUIEvent(btnVip.onClick, OnClickVip);
            panel.AddUIEvent(imgBG.onClick, ResetPopup);
            panel.AddUIEvent(com.onClick, OnClickHead);

            panel.AddUIEvent(compStamina.compRefill.com.onClick, OnClickRefill);
            panel.AddUIEvent(compStamina.compPractice.com.onClick, OnClickPractice);
            panel.AddUIEvent(compStamina.compUnlimited.com.onClick, OnClickUnlimited);

            compStamina.heartList.itemRenderer = OnRendererHeart;

            Notifier.instance.RegisterNotification(NotifyConsts.ChangeLanguage, OnChangeLanguage);
            Notifier.instance.RegisterNotification(NotifyConsts.MainHeadRefreshEvent, DoRefreshEvent);
            Notifier.instance.RegisterNotification(NotifyConsts.OnShowStaminaPopupEvent, OnShowStaminaPopup);
            
            compStamina.compRefill.tfTitle.SetKey("ui_stamina_common_btn_refill");
            compStamina.compPractice.tfTitle.SetKey("ui_stamina_common_btn_practice");
            compStamina.compUnlimited.tfTitle.SetKey("ui_stamina_purchase_btn_subscribe");
            compStamina.compUnlimited.tfCost.SetKey("ui_stamina_purchase_btn_subscribe_desc");
        }

        public void OnShow()
        {
            _isShowPopup = false;
            
            OnRefreshHead();
        }

        private void OnRefreshHead()
        {
            RefreshHead();
            RefreshFire();
            RefreshGem();
            RefreshHeart();

            var mainModel = GameEntry.MainC.MainModel;
            btnPaywall.state.selectedIndex = 0;
            if (mainModel.IsMember() )
                btnPaywall.state.selectedIndex = 1;
            else if(mainModel.incentiveData.homepage_economic_info?.member_info?.SubscribeStatus == SubscribeStatus.Canceled ||
                    mainModel.incentiveData.homepage_economic_info?.member_info?.SubscribeStatus == SubscribeStatus.Suspend ||
                    mainModel.incentiveData.homepage_economic_info?.member_info?.SubscribeStatus == SubscribeStatus.FreeTrialCanceled)
                btnPaywall.state.selectedIndex = 2;

            RefreshGap();
            
            popup.selectedIndex = 0;
            if (_isShowPopup)
                popup.selectedIndex = GameEntry.MainC.MainModel.IsUnlimitedStamina() ? 2 : 1;
        }

        private void RefreshPopup()
        {
            if (GameEntry.MainC.MainModel.IsUnlimitedStamina())
                return;

            var mainModel = GameEntry.MainC.MainModel;
            compStamina.heartList.numItems = StaminaModel.maxStamina / 20;
            compStamina.compRefill.state.selectedIndex = 1;
            compStamina.compPractice.state.selectedIndex = 1;
            compStamina.compRefill.com.touchable = true;
            compStamina.compPractice.com.touchable = true;
            
            int completeFreeTimes = FreeFragmentPracticeTimesMax -
                                    (int)mainModel.incentiveData.stamina_data.remain_stamina_task_cnt;
            completeFreeTimes = Math.Min(1,completeFreeTimes);
            completeFreeTimes = Math.Max(0,completeFreeTimes);
            compStamina.compPractice.tfCost.SetVar("cur", completeFreeTimes.ToString())
                .SetVar("total", FreeFragmentPracticeTimesMax.ToString()).FlushVars();
            if (StaminaModel.staminaData != null)
            {
                if (StaminaModel.staminaData.stamina >= StaminaModel.maxStamina)
                {
                    compStamina.tfTime.SetKey("ui_stamina_purchase_unlimited_heart_full_desc");
                    compStamina.compRefill.state.selectedIndex = 0;
                    compStamina.compPractice.state.selectedIndex = 0;
                    compStamina.compRefill.com.touchable = false;
                    compStamina.compPractice.com.touchable = false;
                }
                else
                {
                    var timeSpan = DateTimeOffset.FromUnixTimeMilliseconds(mainModel.incentiveData.stamina_data.next_recover_timestamp).DateTime -
                                   TimeExt.serverTime;
                    int showMin = Math.Max(timeSpan.Minutes + 1, 1);
                    compStamina.tfTime.SetKeyArgs("ui_stamina_purchase_unlimited_heart_desc", timeSpan.Hours,showMin);
                }

                compStamina.compRefill.tfCost.text = StaminaModel.staminaData.cost_per_purchase.ToString();
            }

            RefreshStamina();
        }

        private void RefreshHeart()
        {
            var mainModel = GameEntry.MainC.MainModel;
            vip.selectedIndex = 0;
            if (GameEntry.MainC.MainModel.incentiveData?.stamina_data != null)
            {
                var stamina = (int)(mainModel.incentiveData.stamina_data.stamina / 20);
                if (!mainModel.IsUnlimitedStamina())
                    btnHeart.com.text = stamina.ToString();
                else
                    vip.selectedIndex = 1;
            }
            else
            {
                btnHeart.com.text = "-";
            }
        }

        private void RefreshGap()
        {
            var headOffset = compHead.com.x + compHead.com.width;
            var heartOffset = GameEntry.MainC.MainModel.IsUnlimitedStamina() ? btnVip.width : btnHeart.com.width;
            var gap = (com.width - 32 - headOffset - btnStreak.com.width - btnGem.com.width - heartOffset -
                       btnPaywall.com.width) / 4;
            grpGap.columnGap = (int)gap;
            grpGap.EnsureBoundsCorrect();
            if (GameEntry.MainC.MainModel.IsUnlimitedStamina())
            {
                compVip.arrow.x = compVip.com.GlobalToLocal(btnVip.LocalToGlobal(Vector2.zero)).x;
            }
            else
            {
                compStamina.arrow.x = compStamina.com.GlobalToLocal(btnHeart.com.LocalToGlobal(Vector2.zero)).x;
            }
        }

        public async void RefreshFire()
        {
            await GameEntry.SignC.TryRefreshSignAndStreak();
            btnStreak.icon.selectedPage = GameEntry.SignC.SignModel.signSummary.finish_checkin ? "fire" : "noFire";
            btnStreak.com.text = GameEntry.SignC.SignModel.GetContinueDays().ToString();
        }

        public void RefreshGem()
        {
            if (CurrencyModel.GetEconomicInfo(EconomicType.Diamond).ValidTime == -1)
                btnGem.com.text = "-";
            else
                btnGem.com.text = CurrencyModel.GetEconomicInfo(EconomicType.Diamond).CurNum.ToString();
        }
        
        private void RefreshHead()
        {
            var mainModel = GameEntry.MainC.MainModel;
            if (string.IsNullOrEmpty(mainModel.headerURL))
                ControllerManager.instance.GetController<LoginController>(ModelConsts.Login)
                    .SendGetSelfHeaderURL(mainModel.userID);
            else
            {
                long level = 0;
                if (mainModel.incentiveData != null && mainModel.incentiveData.growth_data != null)
                    level = mainModel.incentiveData.growth_data.growth_level;
                
                compHead.compLevel.level.text = level.ToString();
                
                if (mainModel.avatarHeadType == PB_HeadItemType.HEAD_ITEM_TYPE_MATERIA)
                {
                    if (Cfg.T.TBItemTable.DataMap.ContainsKey(mainModel.avatarHeadId))
                    {
                        var cfg = Cfg.T.TBItemTable.Get(mainModel.avatarHeadId);
                        compHead.compInner.ldr.url = cfg.iconNormalizePath;
                    }
                }
                else if (mainModel.avatarHeadType == PB_HeadItemType.HEAD_ITEM_TYPE_AVATAR)
                {
                    if (Cfg.T.TBRoleInfo.DataMap.ContainsKey(mainModel.avatarHeadId.ToString()))
                    {
                        var cfg = Cfg.T.TBRoleInfo.Get(mainModel.avatarHeadId.ToString());
                        compHead.compInner.ldr.url = cfg.headUrl;
                    }
                }
                else
                {
                    VFDebug.Log("【首页头像】 出现未知类型");
                    compHead.compInner.ldr.url = mainModel.headerURL;
                }
            }
        }
        
        private void RefreshStamina()
        {
            if (StaminaModel.staminaData != null)
            {
                var stamina = (int)Math.Floor((decimal)(GameEntry.MainC.MainModel.incentiveData?.stamina_data.stamina / 20));

                DotAppearHeartPage appearDot = new DotAppearHeartPage();
                appearDot.heart_count = stamina;
                DataDotMgr.Collect(appearDot);
            }

            compStamina.discount.selectedIndex = 0;
            if (GameEntry.ShopC.ShopModel.IsShowDiscountNode())
            {
                compStamina.discount.selectedIndex = 1;
                ShopUIUtil.UpdateDiscountNode(compStamina.discountNode , GameEntry.ShopC.ShopModel);
            }
        }

        private void OnRendererHeart(int index, GObject obj)
        {
            int cnt = (int)Math.Floor((decimal)(StaminaModel.staminaData.stamina / 20));
            if (index == 0)
                obj.asCom.GetController("state").selectedPage = cnt > 0 ? "full" : "half";
            else
                obj.asCom.GetController("state").selectedPage =
                    cnt > index ? "full" : cnt > index - 1 ? "half" : "empty";
        }

        private void ResetPopup()
        {
            popup.selectedIndex = 0;
            _isShowPopup = false;
        }
        
        private void OnClickProfile()
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
            ResetPopup();
            var profileCtrl = ControllerManager.instance.GetController<ProfileController>(ModelConsts.Profile);
            profileCtrl.OnClickSelf();
        }
        
        private void EnterFire()
        {
            DotClickFireIcon dot = new DotClickFireIcon();
            dot.fire_count = GameEntry.SignC.SignModel.signInfo?.continue_days ?? 0;
            DataDotMgr.Collect(dot);
            
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
            ResetPopup();
            GameEntry.SignC.EnterSign();
        }
        
        private void EnterShop()
        {
            var dot = new DotClickDiamondIcon();
            DataDotMgr.Collect(dot);

            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
            ResetPopup();
            GameEntry.ShopC.EnterShop();
        }

        private void OnClickHead()
        {
            Notifier.instance.SendNotification(NotifyConsts.MainPathHideTipsEvent);
        }
        
        private void OnClickVip()
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
            DotClickHeart();
            
            popup.selectedIndex = !_isShowPopup ? 2 : 0;
            _isShowPopup = !_isShowPopup;
        }

        private void OnClickPaywall()
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
            ResetPopup();
            
            PayWallDotHelper.LastSourcePage = PaywallEntrySourceType.membership_icon;

            string status = DotMemberStatusEnum.non_member.ToString();
            if (GameEntry.MainC.MainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus ==
                SubscribeStatus.Canceled ||
                GameEntry.MainC.MainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus ==
                SubscribeStatus.Suspend ||
                GameEntry.MainC.MainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus ==
                SubscribeStatus.FreeTrialCanceled)
                status = DotMemberStatusEnum.unrenewed_member.ToString();
            else if (GameEntry.MainC.MainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus ==
                     SubscribeStatus.Expired ||
                     GameEntry.MainC.MainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus ==
                     SubscribeStatus.Subscribing ||
                     GameEntry.MainC.MainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus ==
                     SubscribeStatus.Refund ||
                     GameEntry.MainC.MainModel.incentiveData.homepage_economic_info.member_info.SubscribeStatus ==
                     SubscribeStatus.FreeTrial)
                status = DotMemberStatusEnum.member.ToString();

            var dot = new DotClickMembershipIcon();
            dot.member_status = status;
            DataDotMgr.Collect(dot);
            
            GameEntry.LoginC.GetUI(UIConsts.SpeakPlanPromotionStep1UI).Show();
        }
        
        private void OnClickHeart()
        {
            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
            DotClickHeart();
            if (!_isShowPopup)
            {
                RefreshPopup();
                popup.selectedIndex = 1;
            }
            else
                popup.selectedIndex = 0;
            _isShowPopup = !_isShowPopup;
        }

        private void OnClickRefill()
        {
            var dot = new DotClickRefillHearts();
            dot.practice_to_earn_hearts_button_status = _practiceEnum.ToString();
            DataDotMgr.Collect(dot);

            var staminaModel = _homePanel.GetModel<StaminaModel>(ModelConsts.Stamina);
            var currencyController = _homePanel.GetController<CurrencyController>(ModelConsts.CurrencyController);
            if (!currencyController.IsEnough((int)staminaModel.staminaData.cost_per_purchase))
            {
                _homePanel.GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N("common_lack_diamond",
                    GameEntry.ShopC.EnterShop, null, 2, "ui_profile_confirm", "ui_profile_cancel", false, 1);
                return;
            }

            _homePanel.GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N("ui_stamina_confirm_desc",
                _homePanel.GetController<StaminaController>(ModelConsts.Stamina).PurchaseStamina, null, 2,
                "ui_profile_confirm", "ui_profile_cancel", false, 2,
                contentArgs: new string[] { staminaModel.staminaData.cost_per_purchase.ToString(), "1" }
            );
        }

        private void OnClickPractice()
        {
            var dot = new DotClickPracticeToEarnHearts();
            dot.refill_hearts_button_status = _refillEnum.ToString();
            DataDotMgr.Collect(dot);

            ResetPopup();
            Notifier.instance.SendNotification(NotifyConsts.MainPath3DLeavePath);
            long courseId = _homePanel.GetController<MainPathController>(ModelConsts.MainPath).Model.CourseId;
            _homePanel.GetController<FragmentPracticeController>(ModelConsts.FragmentPractice)
                .EnterPractice(courseId, PB_CourseTypeEnum.CTNone, 0, 0, 0, 0, 0, PB_LevelTypeEnum.LTNone, 0, 0, false,
                    PB_DialogSourceEnum.DialogSourceMenuBarForStamina);
        }

        private void OnClickUnlimited()
        {
            var dot = new DotClickUnlimitedHearts();
            dot.refill_hearts_button_status = _refillEnum.ToString();
            dot.practice_to_earn_hearts_button_status = _practiceEnum.ToString();
            DataDotMgr.Collect(dot);
            
            PayWallDotHelper.LastSourcePage = PaywallEntrySourceType.heart_icon;
            
            ResetPopup();
            GameEntry.LoginC.GetUI(UIConsts.SpeakPlanPromotionStep1UI).Show();
        }

        private void DotClickHeart()
        {
            var mainModel = GameEntry.MainC.MainModel;
            var currencyController = _homePanel.GetController<CurrencyController>(ModelConsts.CurrencyController);
            var dot = new DotClickHeartIcon();
            if (mainModel.IsMember())
            {
                dot.heart_status = DotHeartStatusEnum.unlimited_heart.ToString("");
                DataDotMgr.Collect(dot);
                if (!_isShowPopup)
                {
                    var dot2 = new DotAppearUnlimitedHeartPage();
                    dot2.heart_status = DotHeartStatusEnum.unlimited_heart.ToString("");
                }
            }
            else if (mainModel.IsDuringUnlimitedStaminaItem())
            {
                dot.heart_status = DotHeartStatusEnum.unlimited_hearts_tool.ToString("");
                DataDotMgr.Collect(dot);
                if (!_isShowPopup)
                {
                    var dot2 = new DotAppearUnlimitedHeartToolPage();
                    var timeSpan = DateTimeOffset
                            .FromUnixTimeSeconds(mainModel.incentiveData.stamina_data.infinite_stamina_end_time / 1000)
                            .DateTime - TimeExt.serverTime;
                    dot2.time_left = timeSpan.TotalMinutes;
                    DataDotMgr.Collect(dot2);
                }
            }
            else if (mainModel.incentiveData.stamina_data.stamina >= 100)
            {
                dot.heart_status = DotHeartStatusEnum.full_hearts.ToString("");
                DataDotMgr.Collect(dot);
                if (!_isShowPopup)
                {
                    var dot2 = new DotAppearFullHeartsPage();
                    DataDotMgr.Collect(dot2);
                }  
            }
            else
            {
                dot.heart_status = DotHeartStatusEnum.next_heart.ToString("");
                DataDotMgr.Collect(dot);
                if (!_isShowPopup)
                {
                    var staminaModel = GameEntry.LoginC.GetModel<StaminaModel>(ModelConsts.Stamina);
                    var isEnough = currencyController.IsEnough((int)staminaModel.staminaData.cost_per_purchase);
                    _refillEnum = compStamina.compRefill.state.selectedIndex == 0
                        ? isEnough ? DotRefillBtnEnum.diamond_enough : DotRefillBtnEnum.diamond_not_enough
                        : DotRefillBtnEnum.button_unavailable;
                    _practiceEnum = compStamina.compPractice.state.selectedIndex == 0
                        ? mainModel.incentiveData.stamina_data.remain_stamina_task_cnt > 0
                            ? DotPracticeBtnEnum.pracitce_chance_1
                            : DotPracticeBtnEnum.pracitce_chance_0
                        : DotPracticeBtnEnum.button_unavailable;

                    var dot2 = new DotAppearNextHeartPage();
                    dot2.heart_left = (int)(mainModel.incentiveData.stamina_data.stamina / 20);
                    var nextRecoverTimestamp = mainModel.incentiveData.stamina_data.next_recover_timestamp;
                    var timeSpan = TimeSpan.FromMilliseconds(nextRecoverTimestamp - TimeExt.serverTimestamp);
                    dot2.next_heart_time_left = timeSpan.TotalMinutes;
                    dot2.refill_hearts_button_status = _refillEnum.ToString();
                    dot2.practice_to_earn_hearts_button_status = _practiceEnum.ToString();
                    DataDotMgr.Collect(dot2);
                }  
            }
        }

        //参数 show popup
        private void DoRefreshEvent(string str, object obj)
        {
            if (com == null)
                return;
            if (obj != null)
                _isShowPopup = (bool)obj;
            OnRefreshHead();
        }

        private void OnChangeLanguage(string name, object body)
        {
            if (com == null)
                return;
            OnRefreshHead();
        }

        private void OnShowStaminaPopup(string name, object body)
        {
            if (com == null)
                return;
            RefreshPopup();
            popup.selectedIndex = GameEntry.MainC.MainModel.IsUnlimitedStamina() ? 2 : 1;
            _isShowPopup = true;
        }
    }
}