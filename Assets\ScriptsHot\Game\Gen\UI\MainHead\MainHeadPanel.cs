/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.MainHead
{
    public partial class MainHeadPanel : UIBindT
    {
        public override string pkgName => "MainHead";
        public override string comName => "MainHeadPanel";

        public Controller popup;
        public Controller vip;
        public GGraph imgBG;
        public CompHead compHead;
        public CompPopupNormal compStamina;
        public CompPopupVIP compVip;
        public CompItem btnStreak;
        public CompItem btnGem;
        public GLoader btnVip;
        public CompItem btnHeart;
        public CompPaywall btnPaywall;
        public GGroup grpGap;
        public Transition staminaShow;
        public Transition vipShow;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            popup = com.GetControllerAt(0);
            vip = com.GetControllerAt(1);
            imgBG = (GGraph)com.GetChildAt(0);
            compHead = new CompHead();
            compHead.Construct(com.GetChildAt(1).asCom);
            compStamina = new CompPopupNormal();
            compStamina.Construct(com.GetChildAt(2).asCom);
            compVip = new CompPopupVIP();
            compVip.Construct(com.GetChildAt(3).asCom);
            btnStreak = new CompItem();
            btnStreak.Construct(com.GetChildAt(4).asCom);
            btnGem = new CompItem();
            btnGem.Construct(com.GetChildAt(5).asCom);
            btnVip = (GLoader)com.GetChildAt(6);
            btnHeart = new CompItem();
            btnHeart.Construct(com.GetChildAt(7).asCom);
            btnPaywall = new CompPaywall();
            btnPaywall.Construct(com.GetChildAt(8).asCom);
            grpGap = (GGroup)com.GetChildAt(9);
            staminaShow = com.GetTransitionAt(0);
            vipShow = com.GetTransitionAt(1);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            popup = null;
            vip = null;
            imgBG = null;
            compHead.Dispose();
            compHead = null;
            compStamina.Dispose();
            compStamina = null;
            compVip.Dispose();
            compVip = null;
            btnStreak.Dispose();
            btnStreak = null;
            btnGem.Dispose();
            btnGem = null;
            btnVip = null;
            btnHeart.Dispose();
            btnHeart = null;
            btnPaywall.Dispose();
            btnPaywall = null;
            grpGap = null;
            staminaShow = null;
            vipShow = null;
        }
    }
}