/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreFriends
{
    public partial class BagListItem : UIBindT
    {
        public override string pkgName => "ExploreFriends";
        public override string comName => "BagListItem";

        public Controller IsSelect;
        public Controller IsEmpty;
        public MaskImg selectImgFlag;
        public GLoader headLoader;
        public GGraph bg;
        public GGraph bg_2;
        public MaskImg selectImgFlag_2;
        public GGraph bg_3;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            IsSelect = com.GetControllerAt(0);
            IsEmpty = com.GetControllerAt(1);
            selectImgFlag = new MaskImg();
            selectImgFlag.Construct(com.GetChildAt(0).asCom);
            headLoader = (GLoader)com.GetChildAt(1);
            bg = (GGraph)com.GetChildAt(2);
            bg_2 = (GGraph)com.GetChildAt(5);
            selectImgFlag_2 = new MaskImg();
            selectImgFlag_2.Construct(com.GetChildAt(8).asCom);
            bg_3 = (GGraph)com.GetChildAt(9);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            IsSelect = null;
            IsEmpty = null;
            selectImgFlag.Dispose();
            selectImgFlag = null;
            headLoader = null;
            bg = null;
            bg_2 = null;
            selectImgFlag_2.Dispose();
            selectImgFlag_2 = null;
            bg_3 = null;
        }
    }
}