﻿
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Serialization;


public class SceneRenderingManager : MonoSingleton<SceneRenderingManager>
{
    public AmbientMode ambientMode; 
    public Color ambientLight;

    private static readonly AmbientMode roomSceneMode = AmbientMode.Skybox;
    
    public void Update()
    {
        if (RenderSettings.ambientMode != ambientMode)
        {
            RenderSettings.ambientMode = ambientMode;
        }
        
        if (ambientMode == AmbientMode.Flat && RenderSettings.ambientLight != ambientLight)
        {
            RenderSettings.ambientLight = ambientLight;
        }
    }

    public void SetToRoomScene()
    {
        this.ambientMode = roomSceneMode;
    }
}
