using System;
using System.Collections.Generic;
using Msg;
using pb = global::Google.Protobuf;
using Google.Protobuf;
using Cysharp.Threading.Tasks;
using System.Linq;
using System.Text;

public delegate void IMessageCallback<T>(T msg) where T : pb::IMessage;

public class MsgManager
{
    private Dictionary<short, bool> _excludeLogMsg = new Dictionary<short, bool>
    {
        { (short)GameMSGID.CS_HeartBeatReq_ID, true}, { (short)GameMSGID.SC_HeartBeatAck_ID, true },
        { (short)GameMSGID.CS_RoleMoveReq_ID, true}, { (short)GameMSGID.SC_RoleMoveAck_ID, true},
        { (short)GameMSGID.SC_ObjMoveNtf_ID, true},{ (short)GameMSGID.SC_BubblingInfoNtf_ID, true},
        { (short)GameMSGID.SC_ActionInfoNtf_ID, true},
        { (short)GameMSGID.SC_AvatarAppearInfoNtf_ID, true},//20240823 目前Avatar Appear = Append Avatar info，启动时高频触发
        { (short)GameMSGID.CS_GetTalkitPushReq_ID, true}, { (short)GameMSGID.SC_GetTalkitPushAck_ID, true},
    };

    private Dictionary<short, List<MessageCallbackVO>> _listenMap = new Dictionary<short, List<MessageCallbackVO>>();
    StringBuilder stringBuilder = new StringBuilder();
    
    private static MsgManager _instance = null;

    public static MsgManager instance
    {
        get
        {
            if (_instance == null)
                _instance = new MsgManager();
            return _instance;
        }
    }

    /**
     * 发送消息
     */
    public void SendMsg(pb::IMessage msg, Action<GRPCManager.ErrorType,pb::IMessage> exceptionCallback = null, bool popUI = false , NetErrorUI.Data uiData = null, bool updateCacheData = false)
    {
        // if (msg.GetType() == typeof(Msg.dialog_task.CS_GetUserInfoReq))
        // {
        //     exceptionCallback?.Invoke();
        //     PopErrorUI(popUI, uiData);
        //     return;
        // }
        
        
        Type msgT = msg.GetType();
        if (!MsgMap.msgT2ID.ContainsKey(msgT))
        {
            VFDebug.LogError($"Cannot find msgId.MsgType:{msgT}");
            exceptionCallback?.Invoke(GRPCManager.ErrorType.NoRegister , msg );
            PopErrorUI(popUI, uiData);
            return;
        }
        short msgId = MsgMap.msgT2ID[msgT];
        // 缓存类协议禁止直接发送，需要通过代理发送
        if (AppConst.CacheMsgEnable && NetMsgProxy.instance.IsCacheMsg(msgId) && !updateCacheData)
        {
            VFDebug.LogError($"缓存类协议禁止直接发送，需要通过代理CacheMsgProxy发送, msgId = {msgId}");
            return;
        }
        
        //
        if (IsTcpMsg(msgId))
        {
            this.SendWithTCP(msgId, msg);
        }
        else
        {
            this.SendWithGRPC(msgId, msg, exceptionCallback,popUI,uiData, updateCacheData);
        }
        //
        

        if (AppConst.IsLogMsg)
        {
            if (!this._excludeLogMsg.ContainsKey(msgId))
            {
                stringBuilder.Clear();
                stringBuilder.Append("SendMsg ");
                stringBuilder.Append(GetMsgChannel(msgId));
                stringBuilder.Append(", MsgId:");
                stringBuilder.Append(msgId);
                stringBuilder.Append(" msgType:");
                stringBuilder.Append(msgT.Name);
                //不要打开 耗时严重
                // Stopwatch sw = Stopwatch.StartNew();
                // sb.AppendLine(msg.ToString());
                // VFDebug.Log($"msgManager log cost:{sw.ElapsedMilliseconds}ms");
                VFDebug.Log(stringBuilder.ToString());
            }
        }

    }

    private void SendWithTCP(short msgId, pb::IMessage msg)
    {
        byte[] bytes = msg.ToByteArray();
        NetManager.instance.Send(msgId, bytes);
    }

    private void SendWithGRPC(short msgId, pb::IMessage msg, Action<GRPCManager.ErrorType,pb::IMessage> exceptionCallback = null, bool popUI = false , NetErrorUI.Data uiData = null, bool updateCacheData = false)
    {
        UniTask.Void(async () =>
        {
            // try
            // {
                Type msgT = msg.GetType();
                var respMsg = await GRPCManager.instance.AsyncSend(msgId, msg);
                //
                Type respMsgT = respMsg.GetType();
                if (!MsgMap.msgT2ID.ContainsKey(respMsgT))
                {
                    VFDebug.LogError($"Cannot find msgId.MsgType:{respMsgT}");
                    exceptionCallback?.Invoke(GRPCManager.ErrorType.NoRegister , msg);
                    PopErrorUI(popUI, uiData);
                    return;
                }
                short respMsgId = MsgMap.msgT2ID[respMsgT];
                if (!MsgMap.map.ContainsKey(respMsgId))
                {
                    if (AppConst.IsLogMsg)
                    {
                        VFDebug.Log($"Cannot find msg Class.MsgId:{respMsgId}");
                    }
                    exceptionCallback?.Invoke(GRPCManager.ErrorType.NoRegister, msg);
                    PopErrorUI(popUI, uiData);
                    return;
                }
                var msgClass = MsgMap.map[respMsgId];
                this.HandleMsg(respMsgId, respMsg, msgClass);
                if (updateCacheData)
                    NetMsgProxy.instance.SetCacheData(respMsg);
            // }
            // catch(Exception e)
            // {
            //     
            //     var flag = Enum.TryParse(e.Message, out GRPCManager.ErrorType errorType);
            //     if (flag)
            //     {
            //         exceptionCallback?.Invoke(errorType, msg);
            //     }
            //     else
            //     {
            //         exceptionCallback?.Invoke(GRPCManager.ErrorType.UnKnown, msg);
            //     }
            //     PopErrorUI(popUI, uiData);
            // }
        });
    }

    public async UniTask<T> SendAsyncMsg<T>(pb::IMessage msg , Action<GRPCManager.ErrorType,pb::IMessage> failCallback = null , bool popUI = false , NetErrorUI.Data uiData = null, bool updateCacheData = false) where T : pb::IMessage
    {
        // if (typeof(T) == typeof(Msg.incentive.SC_GetMyProfileResponse))
        // {
        //     failCallback?.Invoke();
        //     PopErrorUI(popUI, uiData);
        //     return default;
        // }

        Type msgT = msg.GetType();
        if (!MsgMap.msgT2ID.ContainsKey(msgT))
        {
            VFDebug.LogError($"Cannot find msgId.MsgType:{msgT}");
            failCallback?.Invoke(GRPCManager.ErrorType.NoRegister, msg);
            PopErrorUI(popUI, uiData);
            return default;
            // throw new GRpcException($"Cannot find msgId.MsgType:{msgT}");
        }
        short msgId = MsgMap.msgT2ID[msgT];

        // 缓存类协议禁止直接发送，需要通过代理发送
        if (AppConst.CacheMsgEnable && NetMsgProxy.instance.IsCacheMsg(msgId) && !updateCacheData)
        {
            VFDebug.LogError($"缓存类协议禁止直接发送，需要通过代理CacheMsgProxy发送, msgId = {msgId}");
            return default;
        }
        
        //
        if (IsTcpMsg(msgId))
        {
            VFDebug.LogError($"Cannot send gamesrv msg by SendAsyncMsg. MsgType:{msgT}");
            failCallback?.Invoke(GRPCManager.ErrorType.NoRegister, msg);
            PopErrorUI(popUI, uiData);
            return default;
            // throw new GRpcException($"Cannot send gamesrv msg by SendAsyncMsg. MsgType:{msgT}");
        }

        try
        {
            var respMsg = await GRPCManager.instance.AsyncSend(msgId, msg);
            if (AppConst.IsLogMsg)
            {
                short respMsgId = MsgMap.msgT2ID[respMsg.GetType()];
                if (!this._excludeLogMsg.ContainsKey(respMsgId))
                {
                    if (AppConst.IsLogMsg)
                    {
#if UNITY_EDITOR
                        VFDebug.Log($"HandleAsyncMsg.MsgId:{respMsgId}, msgType:{respMsg.GetType().Name}, \n {respMsg.ToString()}");           
#else
                        VFDebug.Log($"HandleAsyncMsg.MsgId:{respMsgId}, msgType:{respMsg.GetType().Name}");//, \n {respMsg.ToString()}");
#endif

                    }
                }
                    
            }
            if (updateCacheData)
                NetMsgProxy.instance.SetCacheData(respMsg);
            return (T)respMsg;

        }
        catch (GRpcException e)
        {
            var flag = Enum.TryParse(e.Message, out GRPCManager.ErrorType errorType);
            if (flag)
            {
                failCallback?.Invoke(errorType, msg);
            }
            else
            {
                failCallback?.Invoke(GRPCManager.ErrorType.UnKnown, msg);
            }

            PopErrorUI(popUI, uiData);
            return default;
            // throw new GRpcException($"Not handled in design. MsgType:{msgT}");
        }
         
    }

    private void PopErrorUI(bool popUI, NetErrorUI.Data uiData)
    {
        if (popUI)
        {
            NetErrorUI.ShowUI(uiData);
        }
    }

    /**
 * 注册消息回调
 */
    public void RegisterCallBack<T>(IMessageCallback<T> callback) where T : pb::IMessage
    {
        Type msgT = typeof(T);
        if (!MsgMap.msgT2ID.ContainsKey(msgT))
        {
            VFDebug.LogError($"Cannot find msgId.MsgType:{msgT}");
            return;
        }
        short msgId = MsgMap.msgT2ID[msgT];

        if (!this._listenMap.ContainsKey(msgId))
            this._listenMap[msgId] = new List<MessageCallbackVO>();

        List<MessageCallbackVO> list = this._listenMap[msgId];

        // 避免重复注册
        if (list.Any(vo => vo.originalCallback.Equals(callback)))
        {
            return;
        }

        // 注册回调
        list.Add(new MessageCallbackVO
        {
            originalCallback = callback,
            callback = msg => { callback((T)msg); } // 包装回调
        });
    }

    /**
     * 取消注册消息回调
     */
    public void UnRegisterCallBack<T>(IMessageCallback<T> callback) where T : pb::IMessage
    {
        Type msgT = typeof(T);
        if (!MsgMap.msgT2ID.ContainsKey(msgT))
        {
            // VFDebug.LogError($"Cannot find msgId.MsgType:{msgT}");
            return;
        }
        short msgId = MsgMap.msgT2ID[msgT];

        if (!this._listenMap.ContainsKey(msgId))
        {
            // VFDebug.LogError($"Cannot find callback.MsgId:{msgId}");
            return;
        }

        var list = this._listenMap[msgId];

        // 根据 originalCallback 进行匹配并移除
        list.RemoveAll(vo => vo.originalCallback.Equals(callback));

        // 如果列表为空，移除 msgId
        if (list.Count == 0)
        {
            this._listenMap.Remove(msgId);
        }
    }


    /**
     * 接受消息
     */
    public void HandleMsg(short msgId, byte[] data)
    {
        if (!MsgMap.map.ContainsKey(msgId))
        {
            if (AppConst.IsLogMsg)
            {
                VFDebug.Log($"Cannot find msg Class.MsgId:{msgId}");
            }
            return;
        }
        Type msgClass = MsgMap.map[msgId];
        var msg = Activator.CreateInstance(msgClass);
        msg = (msg as pb::IMessage).Descriptor.Parser.ParseFrom(data);
        //
        this.HandleMsg(msgId, msg as pb::IMessage, msgClass);
    }

    private bool IsTcpMsg(short msgId)
    {
        return msgId < 10000;
    }
    private string GetMsgChannel(short msgId)
    {
        return IsTcpMsg(msgId)?"TCP Channel":"GRPC Channel";
    }

    public void HandleMsg(short msgId, pb::IMessage msg, Type msgClass)
    {

        if (!this._listenMap.ContainsKey(msgId))
        {
            if (AppConst.IsLogMsg)
            {
                VFDebug.Log($"Receive no listener Msg. MsgId:{msgId}, MsgType:{msgClass.Name}");
            }
            return;
        }
        
        if (AppConst.IsLogMsg)
        {
            if (!this._excludeLogMsg.ContainsKey(msgId))
            {
                // VFDebug.Log($"HandleMsg.MsgId:{msgId}, msgType:{msgClass.Name}");//, \n {msg.ToString()}");
                VFDebug.Log($"HandleMsg.MsgId:{msgId}, msgType:{msgClass.Name}, msg-len {msg.ToByteArray().Length}");//, \n {msg.ToString()}");
            }
        }
        // 避免再 执行的时候 list有变动 报错
        var list = this._listenMap[msgId];
        var copyList = new List<MessageCallbackVO>(list);
        foreach (var vo in copyList)
        {
            vo.callback(msg as pb::IMessage);
        }
    }

    internal class MessageCallbackVO
    {
        public object originalCallback;
        public IMessageCallback<pb::IMessage> callback;
    }
}

