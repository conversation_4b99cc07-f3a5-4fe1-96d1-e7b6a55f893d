/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreFriends
{
    public partial class IntroduceListItem : UIBindT
    {
        public override string pkgName => "ExploreFriends";
        public override string comName => "IntroduceListItem";

        public Controller IsSelect;
        public GGraph selectedImg;
        public GGraph img;
        public GLoader headLoader;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            IsSelect = com.GetControllerAt(0);
            selectedImg = (GGraph)com.GetChildAt(0);
            img = (GGraph)com.GetChildAt(1);
            headLoader = (GLoader)com.GetChildAt(2);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            IsSelect = null;
            selectedImg = null;
            img = null;
            headLoader = null;
        }
    }
}