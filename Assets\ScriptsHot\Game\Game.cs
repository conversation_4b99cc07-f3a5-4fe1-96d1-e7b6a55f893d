﻿using HybridCLR;

using ScriptsHot.Game.Modules;
using ScriptsHot.Game.Modules.GM;


using ScriptsHot.Game.Modules.Profile;
using ScriptsHot.Game.Modules.Setting;
using ScriptsHot.Game.Modules.Settlement;
using ScriptsHot.Game.Modules.Shop;
using ScriptsHot.Game.Modules.Sign;
using ScriptsHot.Game.Modules.Task;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;
using ScriptsHot.Game.Modules.FragmentPractice;
using ScriptsHot.Game.Modules.IncentiveTask;
using ScriptsHot.Game.Modules.WebView;
using FairyGUI;
using ScriptsHot.Game.Modules.ChatLogicNew;
using ScriptsHot.Game.Modules.ChatStart;
using ScriptsHot.Game.Modules.Contacts;
using ScriptsHot.Game.Modules.Procedure;
using ScriptsHot.Game.Modules.DrillHub;
using SRF;
using UnityEngine;
using Debug = UnityEngine.Debug;
using Talkit.Framework.DesignControl;
using ScriptsHot.Game.Modules.Guide;
using Game.Core.RedDot;
using ScriptsHot.Game.Modules.ExplorePush;
using ScriptsHot.Game.Modules.MainPath;

using ScriptsHot.Game.Modules.VideoLessonTask;
using ScriptsHot.Game.Modules.AgoraRtc;
using ScriptsHot.Game.Modules.ExploreSettlement;
using UIBind.Explore;
using UnityEngine.Rendering;
using UIBind.common;

/**
* 热更域启动逻辑
*/
public class GameStart : IGame
{
    private static List<string> AOTMetaAssemblyFiles { get; } = new List<string>()
    {
        "DOTween", "Grpc.Core.Api", "Grpc.Net.Common", "LitJson", "Luban.Runtime",
        "Main", "Newtonsoft.Json", "StompyRobot.SRF", "System.Core", "System", "UniTask",
        "UnityEngine.CoreModule", "mscorlib","UnityEngine.AnimationModule"
    };

    private static bool _inited = false;

    /**
     * 初始化
     */
    public void InitGame()
    {
        _InitGame();

    }

    private async void _InitGame()
    {
        TimerLogHelper.Ins.AddLog("GInit0");//Game.InitGame start"
        LoadMetadataForAOTAssemblies();
        
        FGUIInit();   
        VibrationManager.Ins.Init();

        AddPrefab();
        
        await IconManager.instance.Startup();
        await GResManager.instance.Startup();
        await GSenManager.instance.Startup();
        await GSoundManager.instance.Startup();
        await GMicrophoneManager.instance.Startup();
        await GNetManager.instance.Startup();
        await GDeviceManager.instance.Startup();
        await PerformanceSaver.instance.Startup();
        await LittleMapCamera.instance.Startup();
#if UNITY_IOS && !UNITY_EDITOR
        await ShareManager.instance.Startup();
#endif

        await SpeechToTextManager.instance.Startup();
        await MediaManager.instance.Startup();
        await VoiceChatManager.instance.Startup();

        ProcedureManager.instance.OnInit();

        this.InitOVRLip();
        Cfg.instance.LoadAllCfg();
        //
       
        UIManager.instance.Init();
        NetManager.instance.Init();
        RegisterControllers();
        //
    
        ControllerManager.instance.OnInit();
        ControllerManager.instance.OnUIInit();
        GameStart._inited = true;
        TimerLogHelper.Ins.AddLog("GInit1");

        RedDotManager.Instance.Init();
        AFHelper.CheckATT();

        TranslateManager.inst.Init();
        StartGame();
    }

    private void AddPrefab()
    {
        GameObject gameMgrNode = GameObject.Find("GameMgr");
        if (gameMgrNode == null)
        {
            Debug.LogError("no gameMgrNode");
        }
        GameObject prefab = GResManager.instance.LoadPrefab("GSoundManager");
        GameObject soundManagerNode = GameObject.Instantiate(prefab , gameMgrNode.transform);
        soundManagerNode.GetComponentOrAdd<GSoundManager>();
        
#if UNITY_IOS && !UNITY_EDITOR
        prefab = GResManager.instance.LoadPrefab("ShareManager");
        GameObject shareManagerNode = GameObject.Instantiate(prefab , gameMgrNode.transform);
        shareManagerNode.GetComponentOrAdd<ShareManager>();
#endif
    }
    
    /**
     * update
     * @param interval 帧间隔,ms
     */
    public void Update(int interval)
    {
        if (!GameStart._inited)
            return;
        TimeExt.Update(Time.deltaTime, Time.unscaledDeltaTime);
        AmazonTimeUtil.Update(Time.deltaTime, Time.unscaledDeltaTime);
        ControllerManager.instance.Update(interval);
        UIManager.instance.Update(interval);
        PopUIManager.instance.Update();
        TimerManager.instance.Update(interval);
        NetManager.instance.Update(interval);
        PushManager.instance.Update(interval);
        ProcedureManager.instance.Update(interval);
        timer += Time.deltaTime;
        if (timer >= 1f)
        {
            timer = 0f;
            UpdateOneSecend();
        }
    }
    
    private float timer = 0f;
    public void UpdateOneSecend()
    {
        PopUIManager.instance.UpdateOneSecend();
    }
    

    /**
     * 开始游戏
     */
    public void StartGame()
    {
        TimerLogHelper.Ins.AddLog("GStart0");//Game.StartGame start
        UIManager.instance.Preload(ExecuteEnterGame);
       
        // SimcardCheck simcardCheck = new SimcardCheck();
        // TimerLogHelper.Ins.SaveTimeStart(TimerLogHelper.TimeType.Simcard);
        // simcardCheck.IgnoreCompetitor(() =>
        // {
        //
        // });        
    }

    private void ExecuteEnterGame()
    {
        LoginController loginController = ControllerManager.instance.GetController(ModelConsts.Login) as LoginController;
        loginController.ExecuteEnterGame();
    }

    /**
     * 注册Controller
     */
    public static void RegisterControllers()
    {
        ControllerManager.instance.Register(new MainPathController());
        ControllerManager.instance.Register(new LoginController());
        ControllerManager.instance.Register(new MainController());
        ControllerManager.instance.Register(new HomepageController());
        ControllerManager.instance.Register(new NoticeController());        
        ControllerManager.instance.Register(new TaskController());
        ControllerManager.instance.Register(new ChatController());
        ControllerManager.instance.Register(new ChatLogicController());
        ControllerManager.instance.Register(new SceneController());
        ControllerManager.instance.Register(new GmController());
		ControllerManager.instance.Register(new CommonController());
        ControllerManager.instance.Register(new SettlementController());
        ControllerManager.instance.Register(new SettingController());
        ControllerManager.instance.Register(new QuickActionsMgr());
        ControllerManager.instance.Register(new ShareController());
        
        ControllerManager.instance.Register(new ReviewQuestionController());
        ControllerManager.instance.Register(new MultiMediaController());
        ControllerManager.instance.Register(new RecommendCardController());
        ControllerManager.instance.Register(new ProfileController());
        ControllerManager.instance.Register(new BottomBubbleController());
        ControllerManager.instance.Register(new AvatarTaskController());

        ControllerManager.instance.Register(new CurrencyController());
        ControllerManager.instance.Register(new GuideController());
        ControllerManager.instance.Register(new ShopController());
        ControllerManager.instance.Register(new SignController());
        // ControllerManager.instance.Register(new LearnPathController());
        ControllerManager.instance.Register(new RankController());

        ControllerManager.instance.Register(new MessagesController());
		ControllerManager.instance.Register(new HeadUrlController());
        ControllerManager.instance.Register(new SocialChatController());
        ControllerManager.instance.Register(new SocialCardController());
        ControllerManager.instance.Register(new IncentiveTaskController());


        ControllerManager.instance.Register(new WebViewController());
        ControllerManager.instance.Register(new StaminaController());
        ControllerManager.instance.Register(new ChatStartController());
        ControllerManager.instance.Register(new FragmentPracticeController());
        ControllerManager.instance.Register(new DrillHubController());
        
        ControllerManager.instance.Register(new ContactsController());
        ControllerManager.instance.Register(new WorldStoryBubbleController());
        ControllerManager.instance.Register(new BenefitController());
        ControllerManager.instance.Register(new PermissMsgController());
        
        ControllerManager.instance.Register(new PhoneQuestionController());
        ControllerManager.instance.Register(new FriendsController());
        ControllerManager.instance.Register(new VideoLessonTaskController());
        ControllerManager.instance.Register(new ExploreController());

        ControllerManager.instance.Register(new WidgetController());
        ControllerManager.instance.Register(new ExplorePushController());
        ControllerManager.instance.Register(new OnBoardFlowController());
        ControllerManager.instance.Register(new ExploreSettlementController());
        ControllerManager.instance.Register(new WhatsappController());

        if (AppConst.IsDebug)
        {
            ControllerManager.instance.Register(new DebuggerController());
        }
    }

    public void StopGame()
    {
        GDeviceManager.instance.Dispose();
        GSenManager.instance.Dispose();
        IconManager.instance.Dispose();
        GSoundManager.instance.Dispose();
        GResManager.instance.Dispose();
        
        NetManager.instance.Close();
        GRPCManager.instance.Close();
    }

    private void InitOVRLip()
    {
//         //临时增加口型插件绑定
#if !UNITY_EDITOR_OSX
        //这个插件在OSX 苹果芯片下会报错，windows下可以用

        var gameMgrGo = GameObject.Find("GameMgr");
        var lsi = new GameObject("LipSyncInterface");
        lsi.transform.SetParent(gameMgrGo.transform);
        lsi.gameObject.AddComponent<OVRLipSync>();
#endif
    }

    public void OnApplicationPause(bool pause)
    {
        ControllerManager.instance.OnApplicationPause(pause);
    }

    public void OnApplicationQuit()
    {
        ControllerManager.instance.OnApplicationQuit();
        
        ProcedureManager.instance.OnExit();
    }

    private static void LoadMetadataForAOTAssemblies()
    {
#if !UNITY_EDITOR
        foreach (var aotDllName in AOTMetaAssemblyFiles)
        {
            HomologousImageMode mode = HomologousImageMode.Consistent;

            // if (aotDllName == "Main")
            {
                mode = HomologousImageMode.SuperSet;
            }
            
            float t1 = Time.realtimeSinceStartup;
            // byte[] dllBytes = GResManager.instance.LoadDll("Assets/Build/Hotfix/" + aotDllName + ".dll.bytes");
            byte[] dllBytes = GResManager.instance.LoadDll($"{aotDllName}.dll");
            LoadImageErrorCode err = RuntimeApi.LoadMetadataForAOTAssembly(dllBytes, mode);
            int t2 = (int)(((float)Time.realtimeSinceStartup - t1) * 1000f);
            Debug.Log($"LoadMetadataForAOTAssembly:{aotDllName}. mode:{mode} ret:{err}, cost:{t2}");
        }
#endif
    }

    private void FGUIInit()
    {
        //FGUI设置
        UIPackage.unloadBundleByFGUI = false;
        UIConfig.defaultFont = FontCfg.DinNextMedium;    //设置默认字体
        //FontManager.RegisterFont();

        //if(DesignControl.instance != null)
        //{
        //    UIConfig.defaultFont = DesignControl.instance.fontModule.LastSFSemiBoldFontName;
        //    DesignControl.instance.fontModule.SetGameFonts();
        //}

        GRoot.inst.SetContentScaleFactor(AppConst.designWidth, AppConst.designHeight);
        //设置FGUI扩展组件
        UIObjectFactory.SetLoaderExtension(typeof(GLoaderExtension));
        UIPackage.AddPackage("UI/buildin", GResManager.LoadResourcesUIPackage);
        UIObjectFactory.SetPackageItemExtension(UIPackage.GetItemURL("buildin", "SpinePanel"), typeof(SpinePanelExtension));
        UIObjectFactory.SetPackageItemExtension(UIPackage.GetItemURL("buildin", "ModelCameraLoader"), typeof(ModelCameraLoaderExtension));
        UIConfig.defaultScrollSnappingThreshold = 0.2f;
    }
}
