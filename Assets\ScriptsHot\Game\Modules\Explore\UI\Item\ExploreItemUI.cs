﻿using System;
using System.Threading.Tasks;
using FairyGUI;
using Msg.basic;
using Msg.explore;
using ScriptsHot.Game.Modules.Explore;
using ScriptsHot.Game.Modules.Explore.UI;
using ScriptsHot.Game.Modules.Scene.Level;
using ScriptsHot.Game.Modules.Scene.Level.Component;
using Spine;
using UIBind.common;
using UIBind.Contacts;
using UIBind.Explore.Item.ItemComponentLogic;
using UnityEngine;
using UnityEngine.Experimental.Rendering;
using YooAsset;
using System.Collections;
using AnimationSystem;

namespace UIBind.Explore.Item
{
    public class ExploreItemUI:ExploreCellBase
    {
        private ExploreItem _modelItem;
        private RenderTexture _renderTexture;
        private Camera _modelCamera;
        private GameObject _modelInstance;
 
        private int _layer;
        private long _avatarId = -1;  // 记录当前绑定的 AvatarID
        private long _entityId = -1;  // 唯一id

        private Level _gameScene;

        #region 组件

        public ExploreTitle ExploreTitle;
        public LoadingCom Loading;
        public ExploreContinueBtn ContinueBtn;
        
        public ExploreDescribeLogic ComDescribe;
        public ExploreProDescribeLogic ComProDescribe;
        public ExploreTargetLogic ComTarget;
        public ExploreAvatarItemLogic AvatarItem;
        public ExplorepPlayerItemLogic PlayerItem;
        public ExploreScaffoldLogic ScaffoldItem;
        public ExploreAdviceLogic AdviceItem;
        public ExploreAwardPanelLogic AwardPanel;
        public ExploreIntroLogic Intro;

        #endregion

        private GLoader _loaderImg;
        private GLoader _loader3d;
        private GLoader _backgroundImg;

        private bool _is3d = false;

        private bool _modelLoadOver = false;

        private ExploreController _controller;
        
        private bool _isLoading = false;
        private string _timerKey1;
        private string _timerKey2;
        private string _timerKey3;

        private PB_Story_Detail _detailInfo;
        public ExploreItemUI(GComponent item,ExploreController con)
        {
            OnInit(item);
            _modelItem = new ExploreItem();
            _modelItem.Construct(item);
            
            _controller = con;

            ExploreTitle = new ExploreTitle();
            ExploreTitle.Construct(item.GetChild("comExploreHead").asCom);
            ExploreTitle.com.onClick.Set(OnClickHead);
            ExploreTitle.com.visible = false;

            ExploreDescribe comDescribe = new ExploreDescribe();
            comDescribe.Construct(item.GetChild("comDescribe").asCom);
            ComDescribe = new ExploreDescribeLogic();
            ComDescribe.Com = comDescribe;
            ComDescribe.Init();
            ComDescribe.SetController(con);
            
            ExploreProDescribe comProDescribe = new ExploreProDescribe();
            comProDescribe.Construct(_modelItem.comMissionTitle.com);
            ComProDescribe = new ExploreProDescribeLogic();
            ComProDescribe.Com = comProDescribe;
            ComProDescribe.Init();
            ComProDescribe.SetController(con);
            
            ExploreTarget comTarget = new ExploreTarget();
            comTarget.Construct(item.GetChild("comTarget").asCom);
            ComTarget = new ExploreTargetLogic();
            ComTarget.Com = comTarget;
            ComTarget.Init();
            ComTarget.SetController(con);

            Loading = new LoadingCom();
            Loading.Construct(item.GetChild("comLoading").asCom);
            Loading.com.visible = false;

            ExploreAvatarItem avatarItem = new ExploreAvatarItem();
            avatarItem.Construct(_modelItem.comAvatarItem.com);
            AvatarItem = new ExploreAvatarItemLogic();
            AvatarItem.Com = avatarItem;
            AvatarItem.Init();
            AvatarItem.SetController(con);
            AvatarItem.Com.com.visible = false;

            ExplorepPlayerItem playerItem = new ExplorepPlayerItem();
            playerItem.Construct(_modelItem.comPlayer.com);
            PlayerItem = new ExplorepPlayerItemLogic();
            PlayerItem.Com = playerItem;
            PlayerItem.Init();
            PlayerItem.SetController(con);
            PlayerItem.Com.com.visible = false;

            ExploreScaffold scaffoldItem = new ExploreScaffold();
            scaffoldItem.Construct(_modelItem.comSaffold.com);
            ScaffoldItem = new ExploreScaffoldLogic();
            ScaffoldItem.Com = scaffoldItem;
            ScaffoldItem.Init();
            ScaffoldItem.SetController(con);
            ScaffoldItem.Com.com.visible = false;

            ExploreAdviceItem adviceItem = new ExploreAdviceItem();
            adviceItem.Construct(_modelItem.comAdcive.com);
            AdviceItem = new ExploreAdviceLogic();
            AdviceItem.Com = adviceItem;
            AdviceItem.Init();
            AdviceItem.SetController(con);
            AdviceItem.Com.com.visible = false;
            
            ExploreAwardPanel awardPanel = new ExploreAwardPanel();
            awardPanel.Construct(_modelItem.comAwardList.com);
            AwardPanel = new ExploreAwardPanelLogic();
            AwardPanel.Com = awardPanel;
            AwardPanel.Init();
            AwardPanel.SetController(con);
            
            ExploreIntro intro = new ExploreIntro();
            intro.Construct(_modelItem.comIntro.com);
            Intro = new ExploreIntroLogic();
            Intro.Com = intro;
            Intro.Init();
            Intro.SetController(con);
            Intro.SetVisible(true);
            
            ContinueBtn = new ExploreContinueBtn();
            ContinueBtn.Construct(_modelItem.comContinueBtn.com);
            ContinueBtn.com.visible = false;
            ContinueBtn.txt.SetKey("ui_explore_btn_continue_chatting");

            _loaderImg = _modelItem.comLoaderImg;
            _loader3d = _modelItem.comLoader3D;
            _backgroundImg = _modelItem.comBgImg;
            _modelItem.comContinueBtn.btnContinue.onClick.Set(OnPauseBtn);
            
            //背景处理
            float screenWidth = GRoot.inst.width;
            float screenHeight = GRoot.inst.height + ExploreConst.ScreenOffsetHeight; 
            _modelItem.imgBG.width = screenWidth + 2;
            _modelItem.imgBG.height = screenHeight;
            
            SetContinueBtnVisible(false);
   
            item.height = GRoot.inst.height + ExploreConst.ScreenOffsetHeight;
            _layer = LayerManager.GetNextLayer();

            if (_layer == -1)
            {
                VFDebug.LogError("请在 Unity 中创建 'RT' 层");
            }

            SetBubbleLoadingVisible(false);
      
        }

        private void OnClickHead()
        {
            //Notifier.instance.SendNotification(NotifyConsts.ShopInfoUpdate);
        }

        public override void SetUI(GComponent ui,GComponent container = null)
        {
            base.SetUI(ui,container); 
            AvatarItem.SetUI(_ui);
            ComDescribe.SetUI(_ui);
            ComProDescribe.SetUI(_ui);
            ScaffoldItem.SetUI(_ui);
            PlayerItem.SetUI(_ui);
            ComTarget.SetUI(_ui);
            AdviceItem.SetUI(_ui);
            
            AvatarItem.SetParentItem(this);
            ScaffoldItem.SetParentItem(this);
            AdviceItem.SetParentItem(this);
            Intro.SetParentItem(this);
        }

        public override bool GetModelLoaded()
        {
            if (!_is3d) return true;
            return _modelLoadOver;
        }

        private void OnPauseBtn()
        {
            //test
            // _controller.CleanupMemory();
            // Notifier.instance.SendNotification(NotifyConsts.ExploreNetExit);
            
            SetContinueBtnVisible(false);
            _controller.GetUI<MultiTabFrameworkUI>(UIConsts.MultiTabHomepage).HideTabs( TabIndex.Explore);
            CLICK_EXLPORE_CONTINUE_CHATTING data = new CLICK_EXLPORE_CONTINUE_CHATTING();
            data.task_id = _controller.CurEntityId;
            data.dialogue_id = _controller.CurEnterEntity.LogicEntity.DialogId_For_Dot;
            data.dialogue_round = _controller.CurEnterEntity.LogicEntity.Round;
            DataDotMgr.Collect(data);
        }

        public void SetContinueBtnVisible(bool boo)
        {
            _controller.IsPause = boo;
            this.ContinueBtn.com.visible = boo;
            this._modelItem.imgMask.visible = boo;
            
            if (boo)
            {
                APPEAR_EXLPORE_CONTINUE_CHATTING data = new APPEAR_EXLPORE_CONTINUE_CHATTING();
                data.task_id = _controller.CurEntityId;
                data.dialogue_id = _controller.CurEnterEntity.LogicEntity.DialogId_For_Dot;
                data.dialogue_round = _controller.CurEnterEntity.LogicEntity.Round;
                DataDotMgr.Collect(data);
            }
        }
        
        public override void Enter()
        {
            base.Enter();
            AvatarItem.Reset();
            ScaffoldItem.Com.com.visible = false;
            AdviceItem.Com.com.visible = false;
            DoDescribe();
            LogDataDot();
        }

        public override void Exit()
        {
            base.Exit();
            // Debug.LogError($"item  exit::: ");
            AvatarItem.Com.com.visible = false;
            ScaffoldItem.Com.com.visible = false;
            AdviceItem.Com.com.visible = false;
            PlayerItem.Com.com.visible = false;
            SetContinueBtnVisible(false);

        }

        private void LogDataDot()
        {
            if (_detailInfo == null) return;
            // if (_detailInfo.descType == PB_Explore_TaskDescType.EO_TDT_GOAL)
            // {
            //     APPEAR_MAIN_GOAL dot = new APPEAR_MAIN_GOAL();
            //     dot.task_id = _controller.CurEntityId;
            //     dot.dialogue_id = _controller.CurEnterEntity.LogicEntity.DialogId_For_Dot;
            //     dot.dialogue_round = _controller.CurEnterEntity.LogicEntity.Round;
            //     DataDotMgr.Collect(dot);
            // }
        }

        public override void AudioOver()
        {
            AvatarItem.AudioOver();
            ScaffoldItem.AudioOver();
        }

        private string _bubbleSpineTimeFlag = String.Empty;
        /// <summary>
        /// avatar  气泡loading 
        /// </summary>
        /// <param name="value"></param>
        public void SetBubbleLoadingVisible(bool value)
        {
            if (value && AvatarItem.Com.com.visible) return;
            if (LogicEntity != null && LogicEntity.Step != ExploreStep.Player) return;
            _modelItem.bubbleSpine.visible = value;
            if (value)
            {
                // 获取 Spine 动画
                var trackEntry = _modelItem.bubbleSpine.spineAnimation.state.SetAnimation(0, "1", false);

                Spine.AnimationState.TrackEntryDelegate onComplete = null;
                onComplete = (TrackEntry entry) =>
                {
                    entry.Complete -= onComplete; 
                    _modelItem.bubbleSpine.spineAnimation.state.SetAnimation(0, "2", true);
                };

                trackEntry.Complete += onComplete;

                _bubbleSpineTimeFlag = TimerManager.instance.RegisterTimer((a) =>
                {
                    _modelItem.bubbleSpine.visible = false;
                },ExploreConst.BubbleSpine);
            }
            else
            {
                if (_bubbleSpineTimeFlag != String.Empty)
                {
                    TimerManager.instance.UnRegisterTimer(_bubbleSpineTimeFlag);
                    _bubbleSpineTimeFlag = String.Empty;
                }
            }
        }

        #region title

        public void UpdateTitle(PB_Story_Avatar info)
        {
            // ExploreTitle.comHead.imgHead.url = info.headPicUrl;
            ExploreTitle.txtName.text = info.name;
            ExploreTitle.txtProfession.text = String.Empty;
            ExploreTitle.txtGroup.EnsureBoundsCorrect();
            
            // ExploreTitle.comHead.ctrlHeadBg.selectedPage = GetJobType(info.headBgColor);

            _modelItem.comFriendInfo.com.visible = false;
            _modelItem.comFriendInfo.txtAvatarName.text =  info.name;
            // ExploreFriendCfg cfg = Cfg.T.TBExploreFriend.Get(1);
            // _modelItem.comFriendInfo.txtLevel.SetKey(cfg.NameKey);
        }

        public void UpdateDescribe(PB_Story_Detail info,long entityId)
        {
            _detailInfo = info;
            _entityId = entityId;

            DoDescribe();
        }

        public void DoDescribe()
        {
            if (_detailInfo == null) return;
            // if (_detailInfo.descType == PB_Explore_TaskDescType.EO_TDT_GOAL)
            // {
            //     ComDescribe.Com.com.visible = false;
            //     ComTarget.Com.com.visible = true;
            //     
            //     ComTarget.InitDescribe(_detailInfo,_entityId);
            // }
            // else if (_detailInfo.descType == PB_Explore_TaskDescType.EO_TDT_TOPIC)
            // {
            //     ComDescribe.Com.com.visible = true;
            //     ComTarget.Com.com.visible = false;
            //     
            //     ComDescribe.UpdateDescribe(_detailInfo,_entityId);
            // }
            
            _modelItem.ctrlTitleType.selectedPage = "Mission";
            
            ComProDescribe.UpdateDescribe(_detailInfo,_entityId);
        }

        #endregion 
        
  

        public void SetIs3d(bool value)
        {
            _is3d = value;
            if (_is3d)
            {
                _loaderImg.visible = false;
                _loader3d.visible = true;
                _backgroundImg.visible = true;
            }
            else
            {
                _loaderImg.visible = true;
                _loader3d.visible = false;
                _backgroundImg.visible = false;
            }
        }

        #region 处理3d模型
        public async void SetModel(Level lv,long entityId,long newAvatarId,string bgId)
        {
            _modelLoadOver = false;
            if (_modelItem == null) return;
            
            // Debug.LogError($"avatarId::{avatarId} + newAvatarId::{newAvatarId}  + _entityId::{entityId}");

            if (_avatarId == newAvatarId && _entityId == entityId)
            {
                // Debug.LogError($"返回  entityId: {entityId} + newAvatarId :{newAvatarId}");
                if (_modelInstance)
                {
                    ObjectUtils.SetLayer(_modelInstance, LayerMask.NameToLayer("RT" + _layer));
                    _modelInstance.SetActive(true);
                }

                _modelLoadOver = true;

                return;
            }
            _gameScene = lv;
            Release();
            _avatarId = -1;
            _entityId = -1;

            _modelCamera = CameraPool.GetCamera(_layer);
            _renderTexture =  RenderTexturePool.GetRenderTexture(); 
            _modelCamera.targetTexture = _renderTexture;
            _modelCamera.enabled = true;
            _modelCamera.name = entityId.ToString() + "_" + _layer;

            _loader3d.texture = new NTexture(_renderTexture);
            _loader3d.align = AlignType.Center;
            _loader3d.verticalAlign = VertAlignType.Middle;
            _loader3d.SetPivot(0f, 0f, true);

            // if (GetGenderById(newAvatarId))
            // {
            //     _loader3d.image.position = new Vector2(0, 200);
            //     _loader3d.image.scale = new Vector2(1.00f, 1.00f);
            // }
            // else
            // {
            //     _loader3d.image.position = new Vector2(-10, 180);
            //     _loader3d.image.scale = new Vector2(0.95f, 0.95f);
            // }
            
            _loader3d.image.position = new Vector2(0, 200);
            _loader3d.image.scale = new Vector2(1.00f, 1.00f);
            
            _loader3d.x = (_container.width- _loader3d.width) / 2;
            _loader3d.y = GRoot.inst.height - _loader3d.height + ExploreConst.ScreenOffsetHeight;  

            _modelInstance = ModelPool.GetModel(newAvatarId, null);
            if (_modelInstance == null)
            {
                SetLoading(true);
                _modelInstance = await Load3DModel(newAvatarId);
                if (_modelInstance != null)
                {
                    SetLoading(false);
                    _modelLoadOver = true;
                }
            }
            else
            {
                _modelLoadOver = true;
            }

            VisibleSameAvatar(entityId,newAvatarId,_layer);

            _modelInstance.name = string.Concat(entityId, "_", newAvatarId, "_", _layer);
            _modelInstance.transform.SetParent(GameObject.Find("RenderTextureContainer").transform, false);
            _modelInstance.transform.localPosition = Vector3.zero;
            _modelInstance.transform.localRotation = Quaternion.Euler(0, 180, 0);
            _modelInstance.transform.localScale = Vector3.one;
            
            //移动FGUI元素会导致RT出界。由于LOAD3DMODEL好像不一定走(至少初始的四个预加载人物不走)所以逻辑先写在这里,对最外层生效。
            if (GetGenderById(newAvatarId) == 0)
            {
                _modelInstance.transform.localPosition = new Vector3(-0.027F, 0.1F, 0);
                _modelInstance.transform.localScale = new Vector3(0.95F,0.95F,0.95F);
            }
            LightManager.Instance.LoadLightForItem(bgId,_modelInstance,LayerMask.GetMask("RT" + _layer));
            ObjectUtils.SetLayer(_modelInstance, LayerMask.NameToLayer("RT" + _layer));
            _modelInstance.SetActive(true);
            _avatarId = newAvatarId;
            _entityId = entityId;
            

        }

        /// <summary>
        /// 绑定 avatar 口型
        /// </summary>
        public void UpdateAudioSource()
        {
            if (_modelInstance)
            {
                _controller.ModelDialogManager = _modelInstance.GetComponentInChildren<DialogManager>();
                GAvatarCtrl avatarCtrl = _modelInstance.GetComponent<GAvatarCtrl>();
                GSoundManager.instance.SetCurrAvatarTTS(avatarCtrl.audioSource);
            }
            else
            {
                _controller.ModelDialogManager = null;
                AudioSource audioSource = GSoundManager.instance.GetChannel("TTS");
                GSoundManager.instance.SetCurrAvatarTTS(audioSource);
            }
        }
        
        // /// <summary>
        // /// 绑定 avatar 口型
        // /// </summary>
        // public void StopAudioSource()
        // {
        //     GSoundManager.instance.StopAvatarTTS();
        // }

        private void VisibleSameAvatar(long entityId,long newAvatarId,int layer)
        {
            GameObject container = GameObject.Find("RenderTextureContainer");
            foreach (Transform child in container.transform)
            {
                if (child.name == string.Concat(entityId, "_", newAvatarId, "_", layer) && child.gameObject.activeSelf)
                {
                    VFDebug.Log($"VisibleSameAvatar:::: {child.name}");
                    ModelPool.ReleaseModel(newAvatarId, child.gameObject);    
                }
            }
        }
        
        private int GetGenderById(long avatarId)
        {
            //女性1,男性0,杂种2
            if (_gameScene.GetComponent<UnitComponent>().GetStyleNameByAvatarId(avatarId).Contains("Girl"))
            {
                return 1;
            }
            else if (_gameScene.GetComponent<UnitComponent>().GetStyleNameByAvatarId(avatarId).Contains("Boy"))
            {
                return 0;
            }
            else return 2;
        }
        
        private async Task<GameObject> Load3DModel(long avatarID)
        {
            var styleName = _gameScene.GetComponent<UnitComponent>().GetStyleNameByAvatarId(avatarID);
            var modelGo = await _gameScene.GetComponent<AvatarComponent>().avatarLoader.LoadNAvatar(styleName,null,true);
            
            SceneController sceneController = ControllerManager.instance.GetController<SceneController>(ModelConsts.Scene);
            
            //加载GameObject
            GameObject parentObject = sceneController.GetCharacterGameObject(ModelPreloader.Instance.GetCharacterGOPath());
            if (modelGo != null)
            {
                modelGo.transform.SetParent(parentObject.transform);
                modelGo.transform.localPosition = Vector3.zero;
                parentObject.transform.SetParent(GameObject.Find("RenderTextureContainer").transform);
                parentObject.transform.localPosition = Vector3.zero;
                parentObject.SetActive(true);
                
                // //移动FGUI元素会导致RT出界。
                // if (GetGenderById(avatarID) == 0)
                // {
                //     modelGo.transform.localPosition = new Vector3(0.036F, 0.11F, 0);
                //     modelGo.transform.localScale = new Vector3(0.95F,0.95F,0.95F);
                // }
                
                PlayModelToIdle(modelGo);
                
                GAvatarCtrl avatarCtrl = ModelPreloader.Instance.GetComponentOrAdd<GAvatarCtrl>(parentObject);
                //1初始化｜添加 口型插件
                ModelPreloader.Instance.InitLipSyncPlugin(avatarCtrl);
                //2口型插件相关绑定
                ModelPreloader.Instance.MatchLipSyncToHeadNode(modelGo, styleName, avatarCtrl);
            }
            
            return parentObject;
        } 

        private void PlayModelToIdle(GameObject model)
        {
            if (model == null)
            {
                return;
            }

            var animator = model.GetComponentInChildren<Animator>();
            if (animator == null)
            {
                return;
            }

            animator.cullingMode = AnimatorCullingMode.AlwaysAnimate;
            animator.Play("Idle");
        }

        #endregion
  
        #region 加载2d图片

        public async void ShowImg2d(string url)
        {
            SetLoading(true);
            _loaderImg.fill = FillType.ScaleMatchHeight;
            float screenHeight = GRoot.inst.height + ExploreConst.ScreenOffsetHeight;
            
            NTexture t = await LoadImageAsync(url);
            if (t != null)
            {
                // 计算原始图片比例
                float imageRatio = (float)t.width / t.height;
                // VFDebug.Log($"2D图片加载: 实际纹理尺寸: {t.width}x{t.height}, 比例: {imageRatio}");
                
                // 强制使用已知的原始资源比例 720:1280 = 0.5625
                float correctRatio = 720f / 1280f;
                
                // 以高度为基准计算宽度，保持原始比例
                float targetWidth = screenHeight * correctRatio;
                
                _loaderImg.SetSize(targetWidth, screenHeight);
                _loaderImg.texture = t;
                UpdateImgCenter(_loaderImg);
                SetLoading(false);
            }
        }

        /// <summary>
        /// 异步加载图片（带缓存）
        /// </summary>
        private async Task<NTexture> LoadImageAsync(string url)
        {
            VFDebug.Log($"加载背景 图片加载开始: {url}");
            Texture2D texture = await ImagePreloader.Instance.GetImageAsync(url);
            if (texture == null) return null;
            return new NTexture(texture);
        }
        

        #endregion
        
        #region 加载背景
        
        public async Task LoadIntroImage(string tagName)
        {
            string backUrl = ResUtils.GetExploreIntroBgImgPath(tagName);
            LoadBackgroundImageAsync2(tagName,Intro.Com.comLoaderImg,backUrl);
        }
        
        public async Task LoadBackgroundImage(string tagName)
        {
            if (_curBg == tagName) return;
            _curBg = tagName;
            string backUrl = ResUtils.GetExploreBgImgPath(tagName);
            LoadBackgroundImageAsync2(tagName,_backgroundImg,backUrl);
        }
        

        private AssetHandle _handle;
        private string _curBg = string.Empty;
        /// <summary>
        /// 直接加载背景 -- 可以直接使用 ，已经经过验证，目前不用是因为 进入游戏快速进入探索页面 会出现加载过程，所以使用了 预载方式 LoadBackgroundImageAsync2
        /// </summary>
        /// <param name="tagName"></param>
        public async Task LoadBackgroundImageAsync(string tagName)
        {
            if (_curBg == tagName) return;
            _curBg = tagName;
            SetLoading(true);
            string url = ResUtils.GetExploreBgImgPath(tagName);
            _handle = YooAssets.LoadAssetAsync<Texture2D>(url);
            await _handle.Task;

            if (_handle != null && _handle.Status == EOperationStatus.Succeed)
            {
                Texture2D texture = _handle.AssetObject as Texture2D;
                if (texture != null)
                {
                    // 使用 ScaleMatchHeight 模式，确保高度填满
                    _backgroundImg.fill = FillType.ScaleMatchHeight;

                    // 计算屏幕高度
                   
                    float screenHeight = GRoot.inst.height + ExploreConst.ScreenOffsetHeight;
                    // 计算原始图片比例
                    float imageRatio = (float)texture.width / texture.height;
                    // VFDebug.Log($"背景图加载: 实际纹理尺寸: {texture.width}x{texture.height}, 比例: {imageRatio}");
                    
                    // 强制使用已知的原始资源比例 720:1280 = 0.5625
                    float correctRatio = 720f / 1280f;
                    
                    // 以高度为基准计算宽度，保持原始比例
                    float targetWidth = screenHeight * correctRatio;
                    
                    _backgroundImg.SetSize(targetWidth, screenHeight);
                    _backgroundImg.texture = new NTexture(texture);
                    UpdateImgCenter(_backgroundImg);
                }
                SetLoading(false);
            }
            else
            {
                VFDebug.LogError($"加载背景 图片加载失败: {url}");
            }

            _handle?.Release();
            _handle = null;
        }

      

        /// <summary>
        /// 通过 加载器加载背景
        /// </summary>
        /// <param name="tagName"></param>
        private async Task LoadBackgroundImageAsync2(string tagName,GLoader img,string backUrl)
        {
            SetLoading(true);
            
            Texture2D texture = await ImagePreloader.Instance.GetBackImageAsync(backUrl);

            if (texture != null)
            {
                float screenWidth = GRoot.inst.width;
                float screenHeight = GRoot.inst.height + ExploreConst.ScreenOffsetHeight;
                float screenRatio = screenWidth / screenHeight;

                float imageRatio = (float)texture.width / texture.height;

                // 判断用哪种填充方式
                if (imageRatio > screenRatio)
                {
                    // 图片更宽，以高度为基准
                    img.fill = FillType.ScaleMatchHeight;
                    float targetWidth = screenHeight * imageRatio;
                    img.SetSize(targetWidth, screenHeight);
                    // Debug.LogError($"图片更宽，以高度为基准 targetWidth:{targetWidth} screenHeight:{screenHeight}");
                }
                else
                {
                    // 图片更高，以宽度为基准
                    img.fill = FillType.ScaleMatchWidth;
                    float targetHeight = screenWidth / imageRatio;
                    img.SetSize(screenWidth, targetHeight);
                    // Debug.LogError($" 图片更高，以宽度为基准 screenWidth:{screenWidth} targetHeight:{targetHeight}");
                }

                img.texture = new NTexture(texture);
                UpdateImgCenter(img);

                SetLoading(false);
            }
        }

        private void UpdateImgCenter(GLoader imageLoader)
        {
            float screenWidth = GRoot.inst.width;
            float screenHeight = GRoot.inst.height + ExploreConst.ScreenOffsetHeight;//这里代表的不是imgBG那种全屏高度

            // 图片居中
            imageLoader.SetXY((_container.width - imageLoader.width) / 2, (screenHeight - imageLoader.height) / 2);
            // Debug.LogError($"screenWidth:{screenWidth}  imageLoader.width:{imageLoader.width} _ui.width:{_container.width}");
        }

        #endregion

        #region 网络

        public void SetLoading(bool value)
        {
            Loading.com.visible = value;
            _isLoading = value;

            if (value)
            {
                // 清除所有可能存在的定时器
                ClearAllTimers();

                bool showBar = _controller.GetUI<MultiTabFrameworkUI>(UIConsts.MultiTabHomepage).CurrTabState ==
                               BottomTabState.showing;
                // 注册三个不同时间点的定时器
                _timerKey1 = TimerManager.instance.RegisterTimer((count) => {
                    if (_isLoading)
                    {
                        CUT_EXPLORE_LOAD_1S dot1 = new CUT_EXPLORE_LOAD_1S();
                        dot1.target_task_id = _controller.CurEntityId;
                        dot1.previous_task_id = _controller.UpTaskId;
                        dot1.is_tab_bar = showBar?"appear":"hide";
                        DataDotMgr.Collect(dot1);
                    }
                }, ExploreConst.LoadingImgDot1);

                _timerKey2 = TimerManager.instance.RegisterTimer((count) => {
                    if (_isLoading)
                    {
                        CUT_EXPLORE_LOAD_2S dot2 = new CUT_EXPLORE_LOAD_2S();
                        dot2.target_task_id = _controller.CurEntityId;
                        dot2.previous_task_id = _controller.UpTaskId;
                        dot2.is_tab_bar = showBar?"appear":"hide";
                        DataDotMgr.Collect(dot2);
                    }
                }, ExploreConst.LoadingImgDot2);

                _timerKey3 = TimerManager.instance.RegisterTimer((count) => {
                    if (_isLoading)
                    {
                        CUT_EXPLORE_LOAD_3S dot3 = new CUT_EXPLORE_LOAD_3S();
                        dot3.target_task_id = _controller.CurEntityId;
                        dot3.previous_task_id = _controller.UpTaskId;
                        dot3.is_tab_bar = showBar?"appear":"hide";
                        DataDotMgr.Collect(dot3);
                    }
                }, ExploreConst.LoadingImgDot3);
            }
            else
            {
                // 如果 Loading 被关闭，清除所有定时器
                ClearAllTimers();
            }
        }

        private void ClearAllTimers()
        {
            if (!string.IsNullOrEmpty(_timerKey1))
            {
                TimerManager.instance.UnRegisterTimer(_timerKey1);
                _timerKey1 = null;
            }
            if (!string.IsNullOrEmpty(_timerKey2))
            {
                TimerManager.instance.UnRegisterTimer(_timerKey2);
                _timerKey2 = null;
            }
            if (!string.IsNullOrEmpty(_timerKey3))
            {
                TimerManager.instance.UnRegisterTimer(_timerKey3);
                _timerKey3 = null;
            }
        }

        #endregion
        
        public void Release()
        {
            if (_modelInstance != null)
            {
                ModelPool.ReleaseModel(_avatarId, _modelInstance);
                _modelInstance = null;
            }

            if (_renderTexture != null)
            {
                RenderTexturePool.ReleaseRenderTexture(_renderTexture); // 🔹 归还到对象池
                _renderTexture = null;
            }
            
            if (_modelCamera != null)
            {
                CameraPool.ReleaseCamera(_modelCamera);
                _modelCamera = null;
            }
        }
        public string GetJobType(PB_BackgroundColorEnum colorEnum)
        {
            string str = "Enterainment";
            switch (colorEnum)
            {
                case PB_BackgroundColorEnum.BGColorYellow:
                    str = "Lifestyle";
                    break;
                case PB_BackgroundColorEnum.BGColorGreen:
                    str = "Healthcare";
                    break;
                case PB_BackgroundColorEnum.BGColorPink:
                    str = "Enterainment";
                    break;
                case PB_BackgroundColorEnum.BGColorOrangeYellow:
                    str = "Finance";
                    break;
                case PB_BackgroundColorEnum.BGColorBabyBlue:
                    str = "Technology";
                    break;
                case PB_BackgroundColorEnum.BGColorCyan:
                    str = "Education";
                    break;
                case PB_BackgroundColorEnum.BGColorPurple:
                    str = "Management";
                    break;
            }

            return str;
        }
        
        
        #region 动态开关

        public void SetCamera(bool on,int tickAdd = 1)
        {
            if (this._modelCamera)
            {
                this._modelCamera.enabled = on;
            }
        }

        private int currTickToOff = 0;
        private const int tickToOff = 120;
        public void SetAvatar(bool on,int tickAdd = 1)
        {
            if (this._modelInstance)
            {
                if (on && !this._modelInstance.activeSelf)
                {
                    this._modelInstance.SetActive(true);
                    currTickToOff = 0;
                }
                else if(!on && this._modelInstance.activeSelf)
                {
                    currTickToOff+=tickAdd;
                    if (currTickToOff >= tickToOff)
                    {
                        currTickToOff = 0;
                        this._modelInstance.SetActive(false);
                    }
                }
            }
        }
        
        #endregion

    }

}
