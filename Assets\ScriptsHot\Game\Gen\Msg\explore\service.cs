// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protobuf/explore/service.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Msg.explore {

  /// <summary>Holder for reflection information generated from protobuf/explore/service.proto</summary>
  public static partial class ServiceReflection {

    #region Descriptor
    /// <summary>File descriptor for protobuf/explore/service.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static ServiceReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Ch5wcm90b2J1Zi9leHBsb3JlL3NlcnZpY2UucHJvdG8aHHByb3RvYnVmL2V4",
            "cGxvcmUvaW5uZXIucHJvdG8aIHByb3RvYnVmL2V4cGxvcmUvcmVjb21tZW5k",
            "LnByb3RvGiFwcm90b2J1Zi9leHBsb3JlL29uYm9hcmRpbmcucHJvdG8aHnBy",
            "b3RvYnVmL2V4cGxvcmUvZ2F0ZXdheS5wcm90bzKYAgoXRXhwbG9yZVJlY29t",
            "bWVuZFNlcnZpY2USRwoQR2V0UmVjb21tZW5kTGlzdBIXLkNTX0dldFJlY29t",
            "bWVuZExpc3RSZXEaGC5TQ19HZXRSZWNvbW1lbmRMaXN0UmVzcCIAEk0KEkdl",
            "dFJlY29tbWVuZExpc3RWMhIZLkNTX0dldFJlY29tbWVuZExpc3RWMlJlcRoa",
            "LlNDX0dldFJlY29tbWVuZExpc3RWMlJlc3AiABJlChpHZXRVc2VySGlzdG9y",
            "eVByb2dyZXNzTGlzdBIhLkNTX0dldFVzZXJIaXN0b3J5UHJvZ3Jlc3NMaXN0",
            "UmVxGiIuU0NfR2V0VXNlckhpc3RvcnlQcm9ncmVzc0xpc3RSZXNwIgAyhwEK",
            "GEV4cGxvcmVPbmJvYXJkaW5nU2VydmljZRJrChxHZXRPbmJvYXJkaW5nQ2hh",
            "dFByZWxvYWREYXRhEiMuQ1NfR2V0T25ib2FyZGluZ0NoYXRQcmVsb2FkRGF0",
            "YVJlcRokLlNDX0dldE9uYm9hcmRpbmdDaGF0UHJlbG9hZERhdGFSZXNwIgAy",
            "WAoTRXhwbG9yZUlubmVyU2VydmljZRJBCg5HZXRVc2VyVXNlRGF0YRIVLlNT",
            "X0dldFVzZXJVc2VEYXRhUmVxGhYuU1NfR2V0VXNlclVzZURhdGFSZXNwIgAy",
            "VAoRRXhwbG9yZUJpelNlcnZpY2USPwoRRXhwbG9yZUNvbm5lY3Rpb24SEC5D",
            "U19FeHBsb3JlVXBNc2caEi5TQ19FeHBsb3JlRG93bk1zZyIAKAEwAUIqWhp2",
            "Zl9wcm90b2J1Zi9zZXJ2ZXIvZXhwbG9yZaoCC01zZy5leHBsb3JlYgZwcm90",
            "bzM="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Msg.explore.InnerReflection.Descriptor, global::Msg.explore.RecommendReflection.Descriptor, global::Msg.explore.OnboardingReflection.Descriptor, global::Msg.explore.GatewayReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, null));
    }
    #endregion

  }
}

#endregion Designer generated code
