﻿using System.Collections.Generic;
using FairyGUI;
using Game.Modules.FragmentPractice;
using Msg.question;
using ScriptsHot.Game.Modules.FragmentPractice;

namespace UIBind.FragmentPractice
{
    public partial class MultipleChoiceAnswer : IQuestionEventListener
    {
        private int _selectedAnswerIndex = -1;
        private List<int> answerList = new List<int>();
        (string, long, int)[] optionsData;
        bool isFirst = true;

        private bool allRight = false;
        protected override void OnAddedToStage()
        {
            optionsList.itemRenderer = OnRendererAnswer;
        }

        protected override void OnRemovedFromStage()
        {
            QuestionEventManager.Instance.RemoveListener(this);
        }

        public override void Init(bool isCurrent, APracticeData practice)
        {
            base.Init(isCurrent, practice);
            if (isCurrent)
            {
                optionsList.onClickItem.Add(OnClickAnswer);
                QuestionEventManager.Instance.AddListener(this);
            }
        }

        public override void ShowPractice(AFragQuestion questionComp)
        {
            _selectedAnswerIndex =-1;
            answerList.Clear();
            ShuffleOptions();
            optionsList.touchable = true;
            optionsList.numItems = Practice.GetAnswerOptions().Length;
            optionsList.ResizeToFit();
        }
        
        //检查答案   
        private void OnRendererAnswer(int index, GObject obj)
        {
            OldBtnBaseAnswer option = obj.asCom.GetChild("baseAnswer") as OldBtnBaseAnswer;
            GRichTextField tfField = obj.asCom.GetChild("content") as GRichTextField;
            if (option == null) return;

            var content = optionsData[index].Item1;
            var ttsId = optionsData[index].Item2;
            tfField.text = content;
            option.SetMode(content, ttsId > index ? ttsId : 0, true, false);
            option.SetState(BtnBaseAnswer.State.Normal);
            option.EnsureBoundsCorrect();
        }

        private void ShuffleOptions()
        {
            string[] options_ = Practice.GetAnswerOptions();
            long[] ttsId_ = Practice.GetAnswerOptionTts();
            optionsData = new (string, long, int)[options_.Length];
            for (int i = 0; i < options_.Length; i++)
            {
                optionsData[i] = (options_[i], ttsId_[i], i);
            }
            // 高德纳洗牌
            for (int i = optionsData.Length - 1; i > 0; i--)
            {
                int j = UnityEngine.Random.Range(0, i + 1);
                (optionsData[i], optionsData[j]) = (optionsData[j], optionsData[i]);
            }
        }


        private void OnClickAnswer(EventContext context)
        {
            if (allRight) return;
            
            if (!IsCurrent || Practice == null) return;
            
            if (context.sender is not GComponent comp) return;

            int index = comp.GetChildIndex(context.data as GObject);
            if (index == _selectedAnswerIndex)
                return;

            VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Medium);

            _selectedAnswerIndex = index;


            var isRight = CheckAnswer();
            if (isRight)
            {
                SoundManger.instance.PlayUI("question_right");
                answerList.Add(optionsData[_selectedAnswerIndex].Item3);
            }
            else
            {
                if (isFirst)
                {
                    isFirst = false;
                    CommitAnswer(0f);
                }
            }

            allRight = false;
            if(AllRight())
            {
                allRight = true;
                TimerManager.instance.RegisterTimer((t) =>
                {
                    CommitAnswer(1f,true,true);
                }, 1000);
            }

            var obj = context.data as GObject;
            OldBtnBaseAnswer option = obj.asCom.GetChild("baseAnswer") as OldBtnBaseAnswer;
            if (option == null) return;
            // var strAnswer = bl ? _selectedAnswerIndex.ToString() : "wrong";
            if (isRight)
            {
                option.SetState(BtnBaseAnswer.State.Right);
            }
            else
            {
                OnErrorAnswer();
                option.SetState(BtnBaseAnswer.State.Error);
                option.touchable = false;
                TimerManager.instance.RegisterTimer((T) =>
                {
                    if(!option.isDisposed)
                        option.SetState(BtnBaseAnswer.State.Gray);
                }, 1000);
            }
        }

        private bool AllRight()
        {
            var Correct =  Practice.GetAnswerIndexes();
            for (int i = 0; i < Correct.Length; i++)
            {
                if (answerList.IndexOf(Correct[i]) == -1)
                {
                    return false;
                }
            }
            return true;
        }
        
        private bool CheckAnswer()
        {
            var Correct =  Practice.GetAnswerIndexes();
            for (int i = 0; i < Correct.Length; i++)
            {
                if (Correct[i] == optionsData[_selectedAnswerIndex].Item3)
                {
                    return true;
                }
            }

            return false;
        }
        
        //IQuestionEventListener
        public void OnAnswered()
        {
            
        }

        public void OnSubmit()
        {
            
        }
        
        public void OnRetry()
        {
            
        }
        
        public void AutoCheck(){}

        public void OnReset()
        {
            allRight = false;
        }
        public void OnJumpListenTask() { }
        public void OnJumpSpeakTask() { }
    }
}