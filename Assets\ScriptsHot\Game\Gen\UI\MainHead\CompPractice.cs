/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.MainHead
{
    public partial class CompPractice : UIBindT
    {
        public override string pkgName => "MainHead";
        public override string comName => "CompPractice";

        public Controller state;
        public GRichTextField tfTitle;
        public GRichTextField tfCost;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            state = com.GetControllerAt(0);
            tfTitle = (GRichTextField)com.GetChildAt(2);
            tfCost = (GRichTextField)com.GetChildAt(3);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            state = null;
            tfTitle = null;
            tfCost = null;
        }
    }
}