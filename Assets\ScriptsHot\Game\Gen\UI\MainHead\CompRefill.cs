/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.MainHead
{
    public partial class CompRefill : UIBindT
    {
        public override string pkgName => "MainHead";
        public override string comName => "CompRefill";

        public Controller state;
        public GRichTextField tfTitle;
        public GRichTextField tfCost;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            state = com.GetControllerAt(0);
            tfTitle = (GRichTextField)com.GetChildAt(2);
            tfCost = (GRichTextField)com.GetChildAt(4);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            state = null;
            tfTitle = null;
            tfCost = null;
        }
    }
}