/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreFriends
{
    public partial class FriendCommonBtn : UIBindT
    {
        public override string pkgName => "ExploreFriends";
        public override string comName => "FriendCommonBtn";

        public Controller <PERSON><PERSON><PERSON>;
        public Controller IsLoading;
        public GGraph OutLineImg;
        public GGraph BtnImg;
        public GTextField title0;
        public GTextField number;
        public GGroup grpSta;
        public Transition t0;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            IsFree = com.GetControllerAt(0);
            IsLoading = com.GetControllerAt(1);
            OutLineImg = (GGraph)com.GetChildAt(0);
            BtnImg = (GGraph)com.GetChildAt(1);
            title0 = (GTextField)com.GetChildAt(2);
            number = (GTextField)com.GetChildAt(4);
            grpSta = (GGroup)com.GetChildAt(5);
            t0 = com.GetTransitionAt(0);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            IsFree = null;
            IsLoading = null;
            OutLineImg = null;
            BtnImg = null;
            title0 = null;
            number = null;
            grpSta = null;
            t0 = null;
        }
    }
}