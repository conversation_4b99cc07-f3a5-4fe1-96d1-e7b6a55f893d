﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Msg.economic;
using Msg.incentive;
using ScriptsHot.Game.Modules.DrillHub;
using UnityEngine;

public enum ChatEnterPos
{
    None,
    RecommendCard,
    MapAvatar,
    LearnPath,
    HomePageQuickEnter,
    DrillHub,
}

public class MainModel : BaseModel
{
    public MainModel() : base(ModelConsts.Main) { }
    // private MainHeaderMode _btnMode;

    //就是大量协议中的 user_id
    private long _userID;
    public long userID
    {
        get { return _userID;}
        private set
        {
            AmazonBi.MainModel_userID = value;
            _userID = value;
        }
    }
    public long playerID { get; private set; }
    public string playerName { get; private set; }
    public string headerURL { get; set; }//和 profile页内的信息冲突？
    
    public long avatarHeadId {get; private set; }
    
    public string headFrameId {get; private set; }

    public PB_HeadItemType avatarHeadType {get; private set; }
    public long flowID { get; private set; }
    
    public string flowName { get; private set; }
    public long PlayerObjId { get; private set; }
    public MemberType PlayerMemberType { get; private set; }//暂时好像没有被使用
    
    public PB_IncentiveDataForPortal incentiveData { get; private set; }
    public string toKen { get; private set; }
    public string roleUniqueName { get; private set; }
    public ChatEnterPos ChatEnterPos { get; private set ; }
    public bool LearnPathAutoShowFlag { get; private set; }
    public bool DrillHubNeedAutoOpen { get; private set; }
    public bool DrillHubAutoShowFlag { get; private set; }
    
    public bool isIncentiveDataReceived { get; private set; }
    public DrillHubViewEnum DrillHubViewType { get; private set; }
    public int SocialChatUnreadCount { get; private set; }
    // public LanguageCode firstLanguage {
    //     get { return this._firstLanguage; } 
    // }

    // public MainHeaderMode btnMode => _btnMode;

    public int aniGrowthExp { get; private set; }

    public void SetChatEnterPos(ChatEnterPos state = ChatEnterPos.None)
    {
        ChatEnterPos = state;
    }

    //user_id
    public void SetUserID(long id)
    {
        this.userID = id;
    }

    public void SetPlayerID(long playerID)
    {
        this.playerID = playerID;

        SendNotification(NotifyConsts.PlayerLogin, this.playerID);
    }

    public void SetPlayerName(string name)
    {
        this.playerName = name;
    }

    public void SetFlowName(string name)
    {
        this.flowName = name;
    }
    
    public void SetFlowId(long id)
    {
        this.flowID = id;
    }

    public void SetMemberType(MemberType type)
    {
        PlayerMemberType = type;
    }

    public void SetToken(string token)
    {
        toKen = token;
    }

    internal void SetRoleUniqueName(string roleUniqueName)
    {
        this.roleUniqueName = roleUniqueName;
    }

    public void SetSelfObjID(long baseInfoObjInsID)
    {
        PlayerObjId = baseInfoObjInsID;
    }

    public void SetUnreadCount(int count)
    {
        SocialChatUnreadCount = count;
    }

    public void SetIncentiveData(PB_IncentiveDataForPortal data)
    {
        //test
        
        //test
        isIncentiveDataReceived = true;
        incentiveData = data;
        headerURL = data?.user_data?.head_url;
        avatarHeadId = data.user_data.head_item_id;
        avatarHeadType = data.user_data.head_item_type;
        headFrameId = data.user_data.user_dress_up_data.frame_id;

        ClearRecoverTimer();
        CheckAndRegisterRecoverTimer();
    }
    
    string recoverTimerId;
    private void ClearRecoverTimer()
    {
        if (string.IsNullOrEmpty(recoverTimerId))
        {
            return;
        }
        TimerManager.instance.UnRegisterTimer(recoverTimerId);
        recoverTimerId = null;
    }
    private void CheckAndRegisterRecoverTimer(bool force = false)
    {
        if (IsUnlimitedStamina())
        {
            return;
        }

        if (incentiveData == null || incentiveData.stamina_data == null)
        {
            RegisterRecoverTimer(0);
            return;
        }

        if (incentiveData.stamina_data.stamina >= incentiveData.stamina_data.max_stamina)
        {
            return;
        }
        
        if (force || string.IsNullOrEmpty(recoverTimerId))
        {
            ClearRecoverTimer();
        }
        
        var next = incentiveData.stamina_data.next_recover_timestamp;
        var now = TimeExt.serverTimestamp;
        var nextRecoverTime = next - now;
        VFDebug.Log($"距离下次恢复体力时间：{nextRecoverTime}毫秒");
        if (nextRecoverTime > 0)
        {
            TimerManager.instance.RegisterTimer(RegisterRecoverTimer,  (int)(nextRecoverTime + 1000) , 1);
        }
        else
        {
            RegisterRecoverTimer(0);
        }
    }

    private void RegisterRecoverTimer(int t)
    {
        GetController<HomepageController>(ModelConsts.Homepage).ReqGetIncentiveData();
    }
    

    // public void SetHeaderMode(MainHeaderMode mode)
    // {
    //     _btnMode = mode;
    //     // Debug.LogError("_btnMode:::" + _btnMode);
    // }

    public void SetLearnPathAutoShow(bool state)
    {
        LearnPathAutoShowFlag = state;
    }

    public void SetAniGrowthExp(int addNum)
    {
        aniGrowthExp += addNum;
    }

    public void ClearAddGrowthExp()
    {
        aniGrowthExp = 0;
    }

    public void SetDrillHubAutoShow(bool state, DrillHubViewEnum viewEnum = DrillHubViewEnum.Main)
    {
        DrillHubAutoShowFlag = state;
        DrillHubViewType = viewEnum;
    }

    public void SetDrillHubNeedAutoOpen(bool state)
    {
        DrillHubNeedAutoOpen = state;
    }

    public bool IsUnlimitedStamina()
    {
        return IsMember() || IsDuringUnlimitedStaminaItem();
    }

    public bool IsMember()
    {
        if (incentiveData.homepage_economic_info != null && incentiveData.homepage_economic_info.member_info != null &&
            incentiveData.homepage_economic_info.member_info.is_member)
            return true;
        return false;
    }
    
    public bool IsDuringUnlimitedStaminaItem()
    {
        if (incentiveData.stamina_data != null)
        {
            if (TimeExt.serverTimestamp < incentiveData.stamina_data.infinite_stamina_end_time)
                return true;
        }
        return false;
    }
}