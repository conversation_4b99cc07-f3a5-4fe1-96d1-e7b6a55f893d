/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.MainPath3D
{
    public partial class CompLdrGrammar : UIBindT
    {
        public override string pkgName => "MainPath3D";
        public override string comName => "CompLdrGrammar";

        public Controller state;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            state = com.GetControllerAt(0);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            state = null;
        }
    }
}