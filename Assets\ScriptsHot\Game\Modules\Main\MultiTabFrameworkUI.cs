using System;
using System.Collections.Generic;
using Cinemachine;
using UnityEngine;
using UIBind.Main;
using FairyGUI;
using DG.Tweening;
using ScriptsHot.Game.Modules.AgoraRtc;
using ScriptsHot.Game.Modules.ExplorePush;
using Modules.DataDot;

#if UNITY_EDITOR
    using UnityEditor;
#endif
public class TabData
{
    public TabIndex TargetIndex;
    public TabIndex LastIndex;
}

/* 扩增tabIndex & icon说明
 * 1 fgui中的tabBtn需要增加新的icon，注意其大小 pivot，分配有效的ctrl编号
 * 2 根据其ctrl编号顺序制定下面的tabIndex要11对应
 */
public enum TabIndex
{
    Course = 0,//这里实际表示的以前叫做centerhome，1.2.0后与chapter合体
    Explore = 1,
    Friend= 2,
    Test =3,//
    Rank =4,
    
}

//底部tab区域动画过程的状态
public enum BottomTabState
{
    showing,
    show2hide,
    hidding,
    hide2show,
}

/// <summary>
/// TabUI配置信息类（精简版）
/// </summary>
public class TabUIConfigInfo
{
    public Func<bool> VisibilityRule { get; }
    public Func<MultiTabFrameworkUI, IBaseUI> Provider { get; }

    public TabUIConfigInfo(Func<bool> visibilityRule, Func<MultiTabFrameworkUI, IBaseUI> provider)
    {
        VisibilityRule = visibilityRule;
        Provider = provider;
    }
}

/// <summary>
/// TabIndex扩展方法，提供到ClickIcon的转化
/// </summary>
public static class TabIndexExtensions
{
    /// <summary>
    /// 将TabIndex转换为对应的ClickIcon枚举值
    /// </summary>
    /// <param name="tabIndex">Tab索引</param>
    /// <returns>对应的ClickIcon字符串</returns>
    public static string ToClickIcon(this TabIndex tabIndex)
    {
        return tabIndex switch
        {
            TabIndex.Explore => ClickIconHomeBottomEnum.explore_icon.ToString(),
            TabIndex.Course => ClickIconHomeBottomEnum.home_icon.ToString(),
            TabIndex.Rank => ClickIconHomeBottomEnum.rank_icon.ToString(),
            TabIndex.Test => ClickIconHomeBottomEnum.voicechat_icon.ToString(),
            TabIndex.Friend => ClickIconHomeBottomEnum.friend_icon.ToString(),
            _ => ClickIconHomeBottomEnum.home_icon.ToString()
        };
    }

    /// <summary>
    /// 获取TabIndex对应的红点键值
    /// </summary>
    /// <param name="tabIndex">Tab索引</param>
    /// <returns>红点键值，如果不是特殊Tab则返回null</returns>
    public static string GetRedDotKey(this TabIndex tabIndex)
    {
        return tabIndex switch
        {
            TabIndex.Explore => PlayPrefsConst.ExploreTabRedStateKey,
            TabIndex.Course => PlayPrefsConst.MainTabRedStateKey,
            _ => null
        };
    }

    /// <summary>
    /// 将TabIndex转换为UI常量名
    /// </summary>
    public static string ToUIConst(this TabIndex tabIndex)
    {
        return tabIndex switch
        {
            TabIndex.Course => UIConsts.CenterHome,
            TabIndex.Explore => UIConsts.ExploreUI,
            TabIndex.Friend => UIConsts.FriendsUI,
            TabIndex.Test => UIConsts.TestTabUI,
            TabIndex.Rank => UIConsts.RankUI,
            _ => string.Empty,
        };
    }
}


public class MultiTabFrameworkUI : BaseUI<MultiTabFramework>, IBaseUIUpdate
{
    /// <summary>
    /// TabIndex到UI配置的映射表，包含可见性规则与UI提供者
    /// </summary>
    private static readonly Dictionary<TabIndex, TabUIConfigInfo> TabUIConfig = new()
    {
        { TabIndex.Course, new TabUIConfigInfo(() => true, self => self.GetUI<CenterHomeUI>(UIConsts.CenterHome)) },
        { TabIndex.Explore, new TabUIConfigInfo(() => true, self => self.GetUI<ExplorePanelUI>(UIConsts.ExploreUI)) },
        { TabIndex.Friend, new TabUIConfigInfo(() => false, self => self.GetUI<FriendsUI>(UIConsts.FriendsUI)) },
        {
            TabIndex.Test,
            new TabUIConfigInfo(() => AppConst.IsDebug || AppConst.AllowShowChattingTab,
                self => self.GetUI<TestTabUI>(UIConsts.TestTabUI))
        },
        { TabIndex.Rank, new TabUIConfigInfo(() => true, self => self.GetUI<RankUI>(UIConsts.RankUI)) },
    };

    public override string uiLayer => UILayerConsts.HomePage; //主UI层
    
    protected override bool isFullScreen => true;
    
    /// <summary>
    /// Tab列表组件
    /// </summary>
    public TabGList tabGList;



    /// <summary>
    /// 初始Tab的scrollIndex编号，新课程为Centerpage实际值为0，探索页为1
    /// </summary>
    public int InitalTabIdx = 0;

    /// <summary>
    /// Tab数量，关键配置项，不应随意修改
    /// </summary>
    private int TabNum = 4;
    
    private bool isFirstTimeEnter = false;

    private const float _tabAniTime = 0.3f;
    
    private CenterHomeUI _centerHome => UIManager.instance.GetUI<CenterHomeUI>(UIConsts.CenterHome);
    private MainModel mainModel => GetModel<MainModel>(ModelConsts.Main);
    private MainController MainController => GetController<MainController>(ModelConsts.Main);

    public MultiTabFrameworkUI(string name) : base(name)
    {
    }

   
    /// <summary>
    /// Tab按钮硬边距空间
    /// </summary>
    private float tabBtnHardPaddingSpace = 55f;

    /// <summary>
    /// Tab按钮软边距空间
    /// </summary>
    private float tabBtnSoftPaddingSpace = 0f;

    /// <summary>
    /// Tab按钮实际宽度
    /// </summary>
    private float tabBtnRealWidth = 68f; //设计图中是34px的外框

    /// <summary>
    /// Tab按钮实际高度
    /// </summary>
    private float tabBtnRealHeight= 68f;

    /// <summary>
    /// 是否允许点击Tab按钮时滚动动画
    /// </summary>
    private bool allowScrollAnimWhenClickTabBtn = false;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    private long lastUpdateTime;

    /// <summary>
    /// Tab图标列表，与实际画面scroll关联
    /// </summary>
    private List<TabBtn> tabIconScrollList = new List<TabBtn>();

    /// <summary>
    /// 当前TabIndex到ScrollIndex的映射字典， scroll 特指FGUI
    /// </summary>
    private Dictionary<TabIndex, int> currTabIndex2ScrollIndexDic = new Dictionary<TabIndex, int>();

    /// <summary>
    /// 当前ScrollIndex到TabIndex的映射字典
    /// </summary>
    private Dictionary<int, TabIndex> currScrollIndex2TabIndexDic = new Dictionary<int, TabIndex>();

    /// <summary>
    /// 原始Tab列表
    /// </summary>
    private List<TabIndex> rawTabList = new List<TabIndex>();

    /// <summary>
    /// Tab区域宽度
    /// </summary>
    private float _tabRegionWidth;

    /// <summary>
    /// Tab单元格宽度
    /// </summary>
    private float _tabCellWidth;


    private CinemachineVirtualCamera virtualCamera;
    private Camera mainCamera;
    private CinemachineCameraOffset cameraOffset;

    protected override void OnInit(GComponent uiCom)
    {
        tabGList = ui.TabGList as TabGList;


        tabGList.scrollItemToViewOnClick = false; //点击时 自动滚动对齐view到所点的那个item

        //todo000 使用751 而非750的问题是因为 运行时scrollPane里的真实宽度被重算过
        tabGList.defaultItemSize = new Vector2(751, 1468); //必须要设，影响条目 宽度计算 
        //条目滑动时的临界位置吸附

        tabGList.OnTabChanged = this.OnTabChanged; //增加且tab时的事件触发

        tabGList.itemRenderer = TabItemRender;
        tabGList.itemProvider = TabItemProvider; //提供自身的资源图标
        tabGList.SetVirtual();

        ConfigDynamicTab();


        //没有scrollPane
        tabGList.autoResizeItem = false; //避免 imgBg元素被限制大小
        tabGList.scrollPane.snapToItem = true;
        tabGList.scrollPane.onScroll.Add(OnScrollMove);
        tabGList.scrollPane.onScrollEnd.Add(OnScrollMoveEnd); //拖拽过程中不会触发此事件


        tabGList.scrollPane.normalScrollVelocity = 10;

        ScrollPane.TWEEN_TIME_GO = 0.6f; //点击按钮的滑动完成的总缓动时长从0.3加大到0.6

        //递归类减速率，log函数细节参看ScrollPane L2133附近
        //原始标准值Mathf.Log(0.12, 0.967) 大约代表会有1s即60帧的缓动时间，
        //目前定义Mathf.Log(0.12, 0.935f)大约代表会有30帧（60fps下）的缓动时间
        tabGList.scrollPane.decelerationRate = 0.935f;

        SetUIContainerVisible(false);

        //NativeSpeaker类 业务初始化
        if (ScriptsHot.Game.Modules.AgoraRtc.VoiceChatManager.instance.IsUserAsNativeSpeaker)
        {
            Notifier.instance.RegisterNotification(NotifyConsts.NSUser_RecvChatMsg, OnRecvChatInvite);
            Notifier.instance.RegisterNotification(NotifyConsts.NSUser_AcceptChatMsg, OnAcceptChatInvite);
            Notifier.instance.RegisterNotification(NotifyConsts.NSUser_CancelChatMsg, OnCancelChatInvite);

            this.ui.VoiceChatInviteMsgBoxList.visible = true;
        }
        else
        {
            this.ui.VoiceChatInviteMsgBoxList.visible = false; //初始不隐藏的话  空白框体会遮挡其他背后的元素
        }
    }

    private void OnClickItem()
    {
        Debug.Log("glist onClickItem ");
    }

    /// <summary>
    /// 设置UI容器可见性
    /// </summary>
    /// <param name="value">是否可见</param>
    /// <returns>UI容器组件</returns>
    public GComponent SetUIContainerVisible(bool value)
    {
        //VFDebug.Log("SetUIContainerVisible::::" + value);
        this.ui.insertContainer.com.visible = value;
        return this.ui.insertContainer.com;
    }


    #region 处理icon  增减扩容

    private List<(TabIndex, bool)> tabVisibleInfoList = new List<(TabIndex, bool)>();

    /// <summary>
    /// 设置指定Tab的可见性
    /// </summary>
    /// <param name="tabIndex">要设置的Tab索引</param>
    /// <param name="isVisible">是否可见</param>
    public void SetTabVisible(TabIndex tabIndex, bool isVisible)
    {
        Debug.Log("TabSetting:  SetTabVisible,tab=" + tabIndex.ToString() + " vis:" + isVisible);
        if (tabVisibleInfoList.Count == 0)
        {
            foreach (TabIndex value in Enum.GetValues(typeof(TabIndex)))
            {
                tabVisibleInfoList.Add((value, true)); //默认都展示
            }
        }

        for (int i = 0; i < tabVisibleInfoList.Count; i++)
        {
            var item = tabVisibleInfoList[i];
            if (item.Item1 == tabIndex)
            {
                if (item.Item2 == isVisible)
                {
                    //目前暂时只考虑 从显示变为不可见的处理，
                    //skip 
                    return;
                }
                else
                {
                    tabVisibleInfoList[i] = (tabIndex, isVisible);
                }
            }
        }
    }

    /// <summary>
    /// 根据配置初始化Tab可见性
    /// </summary>
    private void InitTabVisibilityFromConfig()
    {
        if (tabVisibleInfoList.Count == 0)
        {
            foreach (var kvp in TabUIConfig)
            {
                bool isVisible = kvp.Value.VisibilityRule();
                tabVisibleInfoList.Add((kvp.Key, isVisible));
            }
        }
    }

    /// <summary>
    /// 配置动态Tab，根据可见性规则过滤Tab列表
    /// </summary>
    private void ConfigDynamicTab()
    {
        // 从配置初始化可见性
        InitTabVisibilityFromConfig(); //不可见的这一步里会配置
        
        Debug.Log("TabSetting:begin configDynamicTab");

        foreach (TabIndex value in Enum.GetValues(typeof(TabIndex)))
        {
            rawTabList.Add(value);
        }

        //从满tab状态，逐一剔除tab,过滤后的rawTabList只剩下可见tab
        Debug.Assert(rawTabList.Count == tabVisibleInfoList.Count,
            "初始的rawTab和tabVisibleInfo 数量必须是一致的 "); //有概率出现部分枚举值测ab用不上

        for (int i = 0; i < tabVisibleInfoList.Count; i++)
        {
            bool isVisible = tabVisibleInfoList[i].Item2;
            if (!isVisible)
            {
                rawTabList.Remove(tabVisibleInfoList[i].Item1); //剔除chapter
            }
        }


        foreach (var tab in rawTabList)
        {
            Debug.Log("Raw tab:" + tab.ToString());
        }


        TabNum = rawTabList.Count;

        //这两个dic是运行时tab顺序的双向反查表
        currScrollIndex2TabIndexDic.Clear();
        currTabIndex2ScrollIndexDic.Clear();
        for (int i = 0; i < rawTabList.Count; i++)
        {
            currTabIndex2ScrollIndexDic.Add(rawTabList[i], i);
            currScrollIndex2TabIndexDic.Add(i, rawTabList[i]);
        }

        //检查数量
        Debug.Assert(TabNum <= rawTabList.Count, "tab数量必须在总tabIndex枚举数量之内 "); //有概率出现部分枚举值测ab用不上
        tabGList.SetCurrIndex(this.InitalTabIdx, TabChangeReason.Inital); //设置0号元素为默认的index
        
    }

    private void SetTabIconState(TabIndex tabIndex, bool isSelected)
    {
         
        var scrollIndex = this.currTabIndex2ScrollIndexDic[tabIndex];
        SetTabIconState(scrollIndex, isSelected);
    }
    private void SetTabIconState(int scrollIndex, bool isSelected)
    {
        var tabIndexInt = (int)this.currScrollIndex2TabIndexDic[scrollIndex];
            
        this.tabIconScrollList[scrollIndex].iconType.selectedIndex =isSelected? tabIndexInt + 5:tabIndexInt ;
        if (isSelected)
        {
            this.ui.BottomTabRegion.SelectedTabBgState.selectedIndex = tabIndexInt;//选中时的局部背景
        }
    }

    /// <summary>
    /// 重新初始化Tab系统
    /// </summary>
    public void ReInit()
    {
        //config数值

        //cfg1:BottomTabSelected 动态计算
        this._tabRegionWidth = this.ui.BottomTabRegion.com.width; //运行时根据真实手机宽度会改大小
        this._tabCellWidth = (this._tabRegionWidth - tabBtnHardPaddingSpace * 2) / rawTabList.Count; //


        //cfg2:padding 动态计算
        // tab两侧padding宽度 =（真实总宽- tabBtnHardPaddingSpacex2 - tabCell宽x tab数量）/2 ,BottomTabRegion.com.width不同机器上可能超过750px 

        this.tabBtnSoftPaddingSpace = (this.ui.BottomTabRegion.com.width - tabBtnHardPaddingSpace * 2 -
                                       (_tabCellWidth * rawTabList.Count)) / 2;
        Debug.Log("this.ui.BottomTabRegion.com.width=" + this.ui.BottomTabRegion.com.width);
        Debug.Log("_tabCellWidth" + _tabCellWidth);
        Debug.Log("tabBtnPaddingSpace=" + tabBtnSoftPaddingSpace);
        Debug.Log("tabBtnRealWidth=" + tabBtnRealWidth);


        //== 动态组织 tabIcon容器==
        tabIconScrollList.Clear();

        float tabBtnInCellOffsetX = 0;
        if (_tabCellWidth >= tabBtnRealWidth)
        {
            tabBtnInCellOffsetX = (_tabCellWidth - tabBtnRealWidth) / 2;
            Debug.Log("tabBtnInCellOffsetX=" + tabBtnInCellOffsetX);
        }
        else
        {
            Debug.LogError("tab icon 与cell美术资源宽度不匹配");
        }

        Debug.LogError("rawTabList count=" + this.rawTabList.Count);

        for (int i = 0; i < rawTabList.Count; i++)
        {
            //这里先假定
            int tabIndexVal = (int)rawTabList[i]; //rawTabList 里的i号元素，不一定还是 TabIndex的i号枚举了

            Debug.Log("i=" + i + " intval=" + tabIndexVal);

            var currTabBtn = new TabBtn();
            var tmpCom = UIPackage.CreateObject("Main", "TabBtn").asCom;
            tmpCom.name = ((TabIndex)tabIndexVal).ToString();
            currTabBtn.Construct(tmpCom); //手动强制构造

            currTabBtn.iconType.selectedIndex = tabIndexVal;
            tabIconScrollList.Add(currTabBtn);

            this.ui.BottomTabRegion.com.AddChild(currTabBtn.com);
            currTabBtn.com.visible = true;

            //布局模型 飞书文档：https://visionflow.feishu.cn/docx/C4PldbG1QoGeX9xOkp3cDDsRnNd， 后续改布局的人请一并更新文档
            currTabBtn.com.x = tabBtnHardPaddingSpace + tabBtnSoftPaddingSpace + _tabCellWidth * (i) +
                               tabBtnInCellOffsetX;
            currTabBtn.com.y = (this.ui.BottomTabRegion.com.height - tabBtnRealHeight) / 2; //30px

            Debug.Log("tab" + i + " comX=" + currTabBtn.com.x + " comY=" + currTabBtn.com.y);


            if ((int)TabIndex.Explore - 1 == i)
                currTabBtn.redState.selectedIndex = PlayerPrefs.GetInt(mainModel.userID + PlayPrefsConst.ExploreTabRedStateKey);
            else if ((int)TabIndex.Course - 1 == i)
                currTabBtn.redState.selectedIndex = PlayerPrefs.GetInt(mainModel.userID + PlayPrefsConst.MainTabRedStateKey);
            else
                currTabBtn.redState.selectedIndex = 0;

            //currTabBtn.com.pivotX = 0.5f;//涉及后续缩放变换必须指派
            //currTabBtn.com.pivotY = 0.75f;//这里不用1的原因是，用1时向上的突出效果太大，后续新UI还可能会调整

            AddUIEvent(tabIconScrollList[i].com.onClick, () =>
            {
                var scrollIndex = tabGList.GetCurrIndex();
                TabIndex currTabIndex = this.currScrollIndex2TabIndexDic[scrollIndex];

                Debug.Log("click tab :" + ((TabIndex)tabIndexVal).ToString() + ",indexIntVal:" + tabIndexVal +
                          " currScrollIndex:" + scrollIndex + " currTabIndex:" + (int)currTabIndex);
                if ((int)currTabIndex == tabIndexVal)
                {
                    Debug.Log("click tab skip");
                    return;
                }

                SetUIContainerVisible(false);
                SwitchTab((TabIndex)tabIndexVal, this.allowScrollAnimWhenClickTabBtn, true); //第三个参数会改tab的层级结构
            });
        }


        AddUIEvent(ui.showTabsBtn.com.onClick, () =>
        {
            Debug.Log("click showtabs");
            this.ShowTabs();
        });

        AddUIEvent(ui.VoiceChatInviteMsgBoxList.onClickItem, OnClickItem);
        
        SetTabIconState(InitalTabIdx, true);
    }

    private void OnTabChanged(TabChangeReason reason)
    {
        // 下面两个从tab取的都是scrollIndex
        int currIdx = this.tabGList.GetCurrIndex();
        int lastIdx = this.tabGList.GetLastIndex();


        
        bool isChanged2Explore = false;
        //TabIndex currTabIndex = (TabIndex)currIdx;
        TabIndex currTabIndex = this.currScrollIndex2TabIndexDic[currIdx];
        TabIndex lastTabIndex = this.currScrollIndex2TabIndexDic[lastIdx];

        this.SetTabIconState(lastTabIndex, false);
        this.SetTabIconState(currTabIndex, true);
        
        if (reason != TabChangeReason.Inital)
        {
            DotClickHomeBottomBarIcon dot = new DotClickHomeBottomBarIcon();
            dot.before_page = lastTabIndex.ToString().ToLower();
            dot.after_page = currTabIndex.ToString().ToLower();
            dot.operation_method = reason.ToString();

            // 使用扩展方法获取ClickIcon
            dot.click_icon = currTabIndex.ToClickIcon();

            // 特殊业务逻辑处理
            if (currTabIndex == TabIndex.Course)
            {
                GetController<ExplorePushController>(ModelConsts.ExplorePush).ReqGetHomepageGuideItem();
                GameEntry.ShopC.GetUserVoiceCloneAudio();
            }

            if (PlayerPrefs.GetInt(mainModel.userID + PlayPrefsConst.ExploreTabRedStateKey) == 1)
                dot.red_dot = RedDotHomeBottomEnum.explore_icon.ToString();
            else if (PlayerPrefs.GetInt(mainModel.userID + PlayPrefsConst.MainTabRedStateKey) == 1)
                dot.red_dot = RedDotHomeBottomEnum.session_icon.ToString();
            else
                dot.red_dot = RedDotHomeBottomEnum.none.ToString();
            DataDotMgr.Collect(dot);
        }

        Notifier.instance.SendNotification(NotifyConsts.MainTabChange,
            new TabData() { TargetIndex = currTabIndex, LastIndex = lastTabIndex });


        MainController.UpdateTabRedDotState(currTabIndex);
        UpdateTabRedState();
    }

    /// <summary>
    /// 更新Tab红点状态
    /// </summary>
    public void UpdateTabRedState()
    {
        for (int i = 0; i < tabIconScrollList.Count; i++)
        {
            TabBtn item = tabIconScrollList[i];
            var tabIndex = rawTabList[i];
            var redDotKey = tabIndex.GetRedDotKey();

            if (!string.IsNullOrEmpty(redDotKey))
            {
                item.redState.selectedIndex = PlayerPrefs.GetInt(mainModel.userID + redDotKey);
            }
            else
            {
                item.redState.selectedIndex = 0;
            }
        }
    }

    /// <summary>
    /// Tab项渲染器，根据索引渲染对应的UI
    /// </summary>
    /// <param name="index">渲染索引</param>
    /// <param name="obj">渲染对象</param>
    void TabItemRender(int index, GObject obj)
    {
        var camera = Camera.main;
        mainCamera = camera;

        CinemachineBrain cinemachineBrain = camera.GetComponent<CinemachineBrain>();
        if (cinemachineBrain)
        {
            //有没有什么路径,能够直接获取到此Camera? 能不能在开始时就初始化,而不是一直Update?
            CinemachineVirtualCamera activeVirtualCamera =
                cinemachineBrain.ActiveVirtualCamera as CinemachineVirtualCamera;
            if (activeVirtualCamera)
            {
                //camera.orthographic = true;
                activeVirtualCamera.m_Lens.Orthographic = true;
                activeVirtualCamera.m_Lens.OrthographicSize = 1.5f;
                virtualCamera = activeVirtualCamera;
                CinemachineCameraOffset cameraOffset = virtualCamera.GetComponent<CinemachineCameraOffset>();
                if (cameraOffset) this.cameraOffset = cameraOffset;
            }
        }

        //var tabIdx = (TabIndex)index;
        if (!currScrollIndex2TabIndexDic.ContainsKey(index))
        {
            Debug.LogError("render index fail,currScrollIndex2TabIndexDic not has key :" + index);
            return;
        }

        var tabIdx = currScrollIndex2TabIndexDic[index]; //输入的scrollIndex实际映射到哪个类别动态决定

        // 使用通用化处理获取和显示UI
        var baseUI = GetAndShowTabUI(tabIdx, index);

        if (baseUI != null)
        {
            baseUI.uiComponent.SetSize(ui.TabGList.width, ui.TabGList.height);
        }
    }

    /// <summary>
    /// 通用化处理：获取并显示Tab UI
    /// </summary>
    /// <param name="tabIndex">Tab索引</param>
    /// <param name="index">渲染索引</param>
    /// <returns>获取到的UI对象</returns>
    private IBaseUI GetAndShowTabUI(TabIndex tabIndex, int index)
    {
        if (!TabUIConfig.TryGetValue(tabIndex, out var config))
        {
            Debug.LogError($"TabUIConfig not found for {tabIndex}");
            return null;
        }

        if (config.Provider == null)
        {
            Debug.LogWarning($"No provider for {tabIndex}, skip rendering.");
            return null;
        }

        var baseUI = config.Provider(this);

        if (baseUI == null)
        {
            Debug.LogError($"Failed to get UI for {tabIndex}");
            return null;
        }

        if (!baseUI.isShow)
        {
            Debug.Log($" {tabIndex} not show");
        }

        baseUI.Show4Tab();

        // 特殊逻辑处理
        HandleSpecialTabLogic(tabIndex, baseUI, index);

        return baseUI;
    }

    /// <summary>
    /// 处理特殊Tab逻辑
    /// </summary>
    /// <param name="tabIndex">Tab索引</param>
    /// <param name="baseUI">UI对象</param>
    /// <param name="index">渲染索引</param>
    private void HandleSpecialTabLogic(TabIndex tabIndex, IBaseUI baseUI, int index)
    {
        switch (tabIndex)
        {
            case TabIndex.Explore:
                var exploreUI = baseUI as ExplorePanelUI;
                if (exploreUI != null)
                {
                    exploreUI.onCompleted = () =>
                    {
                        if (!exploreUI.IsCreated)
                        {
                            exploreUI.IsCreated = true;
                            if (this.InitalTabIdx == index)
                            {
                                //首轮创建 且 explore作为起始页时的补丁
                                this.OnTabChanged(TabChangeReason.Inital);
                            }
                        }
                    };
                }

                break;

            case TabIndex.Rank:
                var rankUI = baseUI as RankUI;
                if (rankUI != null)
                {
                    rankUI.RefreshUI(); //强制刷一次数据
                }

                break;
        }
    }

    /// <summary>
    /// Tab项提供器，根据索引提供对应的UI常量
    /// </summary>
    /// <param name="index">提供索引</param>
    /// <returns>对应的UI常量字符串</returns>
    string TabItemProvider(int index)
    {
        Debug.Log("TabItemProvider idx=" + index);
        var tabIdx = currScrollIndex2TabIndexDic[index];


        var uiConst = tabIdx.ToUIConst();
        if (!string.IsNullOrEmpty(uiConst)) return uiConst;

        Debug.LogError($"TabUIConfig not found for {tabIdx}");
        return "";
    }

    #endregion

    protected override void OnShow()
    {
        base.OnShow();
        //待设置


        ReInit();

        this.tabGList.numItems = TabNum; //这里会触发 centerHomeUI的渲染，需要放在ReInit之后

        if (!isFirstTimeEnter)
        {
            isFirstTimeEnter = true;
            SetBottomTabAdapativePos();
            AFHelper.EnterHomepage_finish();
            LoginBIHelper.Cut_home_page();

            Notifier.instance.SendNotification(NotifyConsts.InitMultiTabFramework);
        }

        PayWallDotHelper.LastRootPage = PaywallEntryRootPage.homepage;

        DotAppearHomeBottomBar dot = new DotAppearHomeBottomBar();
        if (PlayerPrefs.GetInt(mainModel.userID + PlayPrefsConst.ExploreTabRedStateKey) == 1)
            dot.red_dot = RedDotHomeBottomEnum.explore_icon.ToString();
        else if (PlayerPrefs.GetInt(mainModel.userID + PlayPrefsConst.MainTabRedStateKey) == 1)
            dot.red_dot = RedDotHomeBottomEnum.session_icon.ToString();
        else
            dot.red_dot = RedDotHomeBottomEnum.none.ToString();
        DataDotMgr.Collect(dot);
    }

    /// <summary>
    /// 切换到指定Tab
    /// </summary>
    /// <param name="tabIndex">目标Tab索引</param>
    /// <param name="ani">是否使用动画</param>
    /// <param name="setFirst">是否设置为第一个</param>
    public void SwitchTab(TabIndex tabIndex, bool ani, bool setFirst)
    {
        VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
        
        
        int indexInScrollView = currTabIndex2ScrollIndexDic[tabIndex]; //因为TabIndex 设定后，可能实际并不适用所有的TabIndex，会做AB实验
        tabGList.ScrollToView(indexInScrollView, ani, setFirst);
    }

    /// <summary>
    /// 切换到上一个Tab
    /// </summary>
    /// <param name="ani">是否使用动画</param>
    /// <param name="setFirst">是否设置为第一个</param>
    public void SwitchLastTab(bool ani, bool setFirst)
    {
        VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
        var idx = tabGList.GetCurrIndex();
        //Debug.Log("SwitchLastTab idx="+idx);
        tabGList.ScrollToView(idx, ani, setFirst);
    }

    /// <summary>
    /// 当前是否在探索页签
    /// </summary>
    /// <returns>是否在探索页签</returns>
    public bool IsExploreTab()
    {
        if (this.tabGList == null) return false;
        int currIdx = this.tabGList.GetCurrIndex();
        TabIndex currTabEnum = currScrollIndex2TabIndexDic[currIdx];
        return currTabEnum == TabIndex.Explore;
    }

    /// <summary>
    /// 当前拖拽到探索页面，处理拖拽放手瞬间，是否显示探索页面
    /// </summary>
    /// <returns>是否应该显示探索页面</returns>
    public bool IfMoveToExploreTab()
    {
        //之所以在这定义，因为游戏中目前显示3个tab,TabNum 却=4 ,无法使用
        int tabCount = 5;
        int exploreTab = 2; //第二个页签
        float min = 1f / (tabCount + 1);
        float max = 1 - min;
        // Debug.LogError($"_scrollXPercent={_scrollXPercent},min={min},max={max}");
        return _scrollXPercent > min && _scrollXPercent < max;
    }

    #region 处理Push 聊天msg的弹窗

	private Dictionary<long, GObject> chatMsgBoxIndexDict = new Dictionary<long, GObject>(capacity:8);

    private Dictionary<long,string> chatMsgBoxTimerDict = new Dictionary<long, string>(capacity:8);

    /// <summary>
    /// 接收聊天邀请
    /// </summary>
    /// <param name="name">通知名称</param>
    /// <param name="obj">通知对象</param>
    public void OnRecvChatInvite(string name, object obj)
    {
        ChatPartnerParam param = (ChatPartnerParam)obj;

        VoiceChatInviteMsgBox msgBoxObj =
            UIPackage.CreateObject("Main", "VoiceChatInviteMsgBox") as VoiceChatInviteMsgBox;
        msgBoxObj.InitData(param); //matchRecId,headerInfo, NUserName, topicVal, levelVal, inviteTimeStamp);

        //防御一手
        if (this.ui.VoiceChatInviteMsgBoxList.visible == false)
        {
            this.ui.VoiceChatInviteMsgBoxList.visible = true;
            this.ui.VoiceChatInviteMsgBoxList.touchable = true;
        }

        var newBox = this.ui.VoiceChatInviteMsgBoxList.AddChild(msgBoxObj);//向后递增
        DataDotMgr.Collect( new VoiceChat_NotChattingSimpleDot( NotChattingSimpleDotType.appear_social_live_chat_invite_panel ));
        
        AddUIEvent( msgBoxObj.AcceptBtn.onClick, msgBoxObj.OnClickAcceptBtn );
        Debug.Log("OnRecvChatInvite matchId="+param.matchRecId);
             chatMsgBoxIndexDict[param.matchRecId] =newBox; //保留box的视图ref

        int maxCD = 295;

        //增加NSUser的接通侧 倒计时，做防御，如果对方超时 自动取消push展示
        chatMsgBoxTimerDict[param.matchRecId] =
            TimerManager.instance.RegisterTimer((c) => { this.RemoveInviteBox(param.matchRecId); }, maxCD * 1000, 1);
    }

    /// <summary>
    /// 接受聊天邀请
    /// </summary>
    /// <param name="name">通知名称</param>
    /// <param name="obj">通知对象</param>
    public void OnAcceptChatInvite(string name, object obj)
    {
        long matchRecId = (long)obj;
        if (chatMsgBoxIndexDict.ContainsKey(matchRecId))
        {
            var msgBox = chatMsgBoxIndexDict[matchRecId];
            
            DataDotMgr.Collect( new VoiceChat_NotChattingSimpleDot( NotChattingSimpleDotType.click_social_live_chat_invite_panel_call_button ));
            bool isSucc =VoiceChatManager.instance.AcceptMatch(matchRecId);
            
            if (isSucc)
            {
                if (chatMsgBoxTimerDict.ContainsKey(matchRecId) &&
                    !string.IsNullOrEmpty(chatMsgBoxTimerDict[matchRecId]))
                {
                    TimerManager.instance.UnRegisterTimer(chatMsgBoxTimerDict[matchRecId]);
                    chatMsgBoxTimerDict.Remove(matchRecId);
                }

                
                this.ui.VoiceChatInviteMsgBoxList.RemoveChild(msgBox);
                this.ui.VoiceChatInviteMsgBoxList.visible = false;


                TabIndex tab = this.currScrollIndex2TabIndexDic[this.tabGList.GetCurrIndex()];
                if (tab != TabIndex.Test)
                {
                    if (this._currTabState != BottomTabState.showing)
                    {
                        //todo
                        Debug.LogError("尚未处理 接受时 tab细节显隐问题");
                    }

                    this.SwitchTab(TabIndex.Test, false, true);
                }
            }
            //有极小 概率失败
        }
        else
        {
            Debug.LogError("移除msgbox id无效，matchRecId=" + matchRecId);
        }
    }

    /// <summary>
    /// 取消聊天邀请
    /// </summary>
    /// <param name="name">通知名称</param>
    /// <param name="obj">通知对象</param>
    public void OnCancelChatInvite(string name, object obj)
    {
        long matchRecId = (long)obj;
        Debug.Log("OnCancelChatInvite ，matchRecId=" + matchRecId);
        RemoveInviteBox(matchRecId);
    }

    private void RemoveInviteBox(long matchRecId)
    {
        if (chatMsgBoxIndexDict.ContainsKey(matchRecId))
        {
            var msgBox = chatMsgBoxIndexDict[matchRecId];

            this.ui.VoiceChatInviteMsgBoxList.RemoveChild(msgBox);
            chatMsgBoxIndexDict.Remove(matchRecId);
            chatMsgBoxTimerDict.Remove(matchRecId);
            Debug.Log("OnCancelChatInvite ，DoRemove matchRecId=" + matchRecId);
            if (this.ui.VoiceChatInviteMsgBoxList.children.Count == 0)
            {
                this.ui.VoiceChatInviteMsgBoxList.visible = false;
            }
        }
        else
        {
            Debug.LogError("OnCancelChatInvite 移除msgbox id无效，matchRecId=" + matchRecId);
        }
    }

    #endregion

    #region MultiTab中的bottomTab的显示隐藏控制

    private BottomTabState _currTabState = BottomTabState.showing;
    public BottomTabState CurrTabState => _currTabState;

#if UNITY_EDITOR
    [MenuItem("Tools/LockScreen")]
    public static void MenuShowTabs() {
        var ctrl = ControllerManager.instance.GetController(ModelConsts.Homepage) as HomepageController;
        ctrl._mtHpUI.LockFrameworkScreen(5000, TabIndex.Course);
    }
#endif

    //[MenuItem("Tools/HideTabs")]
    //public static void MenuHideTabs() {
    //    var ctrl =ControllerManager.instance.GetController(ModelConsts.Homepage) as HomepageController;
    //    ctrl._mtHpUI?.HideTabs();
    //}

    /// <summary>
    /// 设置显示Tab按钮的可见性
    /// </summary>
    /// <param name="visiable">是否可见</param>
    public void SetVisiable4ShowTabsBtn(bool visiable)
    {
        this.ui.showTabsBtn.showTabsBtn.visible = visiable;
    }

    /// <summary>
    /// 显示Tab
    /// </summary>
    public void ShowTabs()
    {
        if (_currTabState == BottomTabState.hidding)
        {
            _currTabState = BottomTabState.hide2show;
            this.tabGList.scrollPane.CancelDragging(); //防御：先强制取消拖拽
            this.DoShowTabsAnim(() =>
            {
                _currTabState = BottomTabState.showing;
                this.tabGList.scrollPane.touchEffect = true; //完全恢复后，启用滑屏
            });
        }
        else
        {
            Debug.LogWarning("Ignore ShowTabs, currState=" + _currTabState);
        }

        Notifier.instance.SendNotification(NotifyConsts.MultiTabShow);
    }

    /// <summary>
    /// 隐藏Tab
    /// </summary>
    /// <param name="tabDuringHide">隐藏期间的Tab索引</param>
    /// <param name="toHide">是否要隐藏</param>
    /// <param name="isDisplayShowTabIcon">是否显示显示Tab图标</param>
    public void HideTabs(TabIndex tabDuringHide, bool allowDispatchHideEvtWhenNotShowing  = false, bool isDisplayShowTabIcon = true)
    {
        // Debug.LogError("HideTabs------------------------------");
        if (_currTabState == BottomTabState.showing)
        {
            _currTabState = BottomTabState.show2hide;

            //如果准备隐藏式 本身还在dragging的过程中

            if (this.tabGList.scrollPane.isDragged)
            {
                Debug.Log("===> isDrag + Hide");
                this.tabGList.scrollPane.CancelTouch(); //强制模拟松手
                this.tabGList.scrollPane.CancelDragging(); //防御：先强制取消拖拽
            }

            //无论是这个瞬间强制中断drag还是自己手动恰好松开，都要再等 TWEEN_TIME_DEFAULT 的惯性回弹时间后再重新做决策
            //需要等待模拟松手后 + TWEEN_TIME_DEFAULT时延的惯性滑动结束后，再执行hide & 禁用
            TimerManager.instance.RegisterTimer(
                count =>
                {
                    int tabIndex = currTabIndex2ScrollIndexDic[tabDuringHide];
                    if (this.tabGList.GetCurrIndex() == tabIndex)
                    {
                        Debug.Log("===> delay触发 cancelDrag的效果");


                        this.tabGList.scrollPane.touchEffect = false; //禁用滑屏

                        this.DoHideTabsAnim(() =>
                        {
                            _currTabState = BottomTabState.hidding;
                            Notifier.instance.SendNotification(NotifyConsts.MultiTabHide);
                        }, isDisplayShowTabIcon);
                    }
                    else
                    {
                        //需要等待模拟松手后，如果切换到的tab页已经不再是 希望隐藏tab那个瞬间想要待的tab时，状态回滚 
                        _currTabState = BottomTabState.showing;
                        Debug.Log("TabState 状态回滚，hidetab失败，不在走隐藏逻辑，这时应该已经不是tabDuringHide：" + tabDuringHide.ToString());
                    }
                },
                Mathf.FloorToInt(ScrollPane.TWEEN_TIME_DEFAULT * 1000 + 30), //增加30ms 是在完成惯性部分后，多+1帧
                1);
        }
        else
        {
            //非BottomTabState.showing状态时
            
            if (allowDispatchHideEvtWhenNotShowing)
            {
                Notifier.instance.SendNotification(NotifyConsts.MultiTabHide);
            }
        }
    }


    private void DoShowTabsAnim(Action onComplete)
    {
        this.ui.showTabsBtn.com.visible = false;
        DOTween.To(
            () => this.ui.BottomTabRegion.com.y,
            y => this.ui.BottomTabRegion.com.y = y,
            this.bottomTab_adaptivePosY, //回复初始高度
            _tabAniTime
        ).OnComplete(() =>
        {
            if (onComplete != null)
            {
                onComplete.Invoke();
            }
        });
    }

    private void DoHideTabsAnim(Action onComplete, bool isDisplayShowTabIcon = true)
    {
        Debug.LogError("start DoHideTabsAnim");
            
        DOTween.To(
            () => this.ui.BottomTabRegion.com.y,
            y => this.ui.BottomTabRegion.com.y = y,
             this.bottomTab_adaptivePosY_toHide,
            _tabAniTime
        ).OnComplete(() =>
        {
            Debug.Log("showTabsBtn.visible = true");

            this.ui.showTabsBtn.com.visible = isDisplayShowTabIcon; //todo 这个是否加初始动画？
            if (onComplete != null)
            {
                onComplete.Invoke();
            }
        });
    }

    private float bottomTab_adaptivePosY;
    private float bottomTab_adaptivePosY_toHide;
    private void SetBottomTabAdapativePos()
    {
        //v1.3.3开始纵向贴底 留边
        //这里的48是设计师要求无论安全区大小如何都需要保留距离底部48px，如果遇到安全区为0的case时，也会留边
        //由于bottomTab本身在 framework内部 已经去除了的UIManager.instance.safeAreaTopHeight 影响，下面暂不考虑顶部安全区的距离问题

        bottomTab_adaptivePosY =UIManager.instance.height  -48 - this.ui.BottomTabRegion.com.height - UIManager.instance.safeAreaTopHeight ;//UIManager.instance.height 是gRoot的全局高度
        bottomTab_adaptivePosY_toHide = bottomTab_adaptivePosY + this.ui.BottomTabRegion.com.height + 48 + 5;//补5px避免边缘溢出
        this.ui.BottomTabRegion.com.SetXY(     this.ui.BottomTabRegion.com.x, bottomTab_adaptivePosY);
    }

   
    #endregion


    /// <summary>
    /// 获取底部Tab高度
    /// </summary>
    /// <returns>底部Tab高度</returns>
    public float GetBottomTabHeight()
    {
        return this.ui.BottomTabRegion.BottomTabSelected.height; //目前和region同高
    }


    void OnScrollMove()
    {
        var x = tabGList.scrollPane.scrollingPosX;
        //var y = tabGList.scrollPane.posY;
        //Debug.Log("==== scroll x="+x + " x2="+ tabGList.scrollPane.scrollingPosX);

        if (cameraOffset)
        {
            float aspect = (float)Screen.width / (float)Screen.height;
            var viewWidth = tabGList.scrollPane.viewWidth;
            MoveCamera_VirtualCamera((x - viewWidth) / viewWidth, (1.5f * 2) * aspect, cameraOffset);
        }

        UpdateBottomTabOnScroll(x);
        //TryUpdateVisibilityOfBg(x);

        //BgOfBg组件挖洞 如需扩展 需要改FairyGUI此组件
        //float maskPageIdx = (int)TabIndex.Chapter;// pageIdx 从0开始, maskPage是留空没有bg的槽位
        float firstPageIdx = 0; // pageIdx 从0开始

        //float firstPagePosX = firstPageIdx * tabGList.scrollPane.viewWidth;
        //float maskPagePosX = maskPageIdx * tabGList.scrollPane.viewWidth;

        //float initPosX = -(ui.BgOfBg.com.width / 2 - tabGList.scrollPane.viewWidth / 2);
        //ui.BgOfBg.com.position = new Vector3(initPosX - (x - tabGList.scrollPane.viewWidth), ui.BgOfBg.com.position.y, 0);
        ui.BgOfBg.com.position = new Vector3(-x + -1 * (firstPageIdx * tabGList.scrollPane.viewWidth),
            ui.BgOfBg.com.position.y, 0);
    }

    private float _scrollXPercent = 0;

    void UpdateBottomTabOnScroll(float scrollX)
    {
        //currSelected x range = 0~500
        var totalMovingRange = _tabCellWidth * (TabNum - 1); //总tab-1 & 单位宽度, -1是因为第一个tab的x起点百分比就是 0%
        var totalScrollRange = _tabRegionWidth * (TabNum - 1); //3是总tab数目

        var scrollXPercent = scrollX / totalScrollRange;
        _scrollXPercent = scrollXPercent;

        float selectWidth = this.ui.BottomTabRegion.BottomTabSelected.width;
        float initPosX = this.tabBtnHardPaddingSpace + 0.5f * (_tabCellWidth - selectWidth);


        this.ui.BottomTabRegion.BottomTabSelected.x = initPosX + totalMovingRange * scrollXPercent;
        Debug.Log(
            $"BottomTabSelected-x={this.ui.BottomTabRegion.BottomTabSelected.x}  initPosX={initPosX} totalMovingRange={totalMovingRange} scrollXPercent={scrollXPercent} ");
        //this.ui.BottomTabRegion.BottomTabSelectedExplore.x =this.tabBtnHardPaddingSpace+ totalMovingRange * scrollXPercent;
        //icon大小缩放 根据x变化
        //cap min=100% max= 160%

        /* 不做缩放
        for (int i = 0; i < TabNum; i++) {
            var tabBtn = tabIconList[i];
            if (tabBtn == null)
            {
                continue;
            }
            else {
                //Debug.Log("UpdateBottomTabOnScroll icon idx="+i);
            }

            // (0~750) : (160%~100%)
            // (i0= ix0~750) : (160%~100%)

            float targetMaxCap = _tabRegionWidth * i;
            float absDisPrecent = Mathf.Abs(scrollX - targetMaxCap) / _tabRegionWidth;

            float scaleMultiplyPrecent = Mathf.Clamp( 1- absDisPrecent,0,1);//当absDisPrecent为0时最大

            float iconScale = 1f + 0.6f * scaleMultiplyPrecent;

            tabBtn.com.scale = new Vector2(iconScale, iconScale);
        }
        */
    }

    //处理两侧drag到极限值 露出天空盒的问题
    void TryUpdateVisibilityOfBg(float scrollX)
    {
        // if (scrollX < 1 || scrollX >= _tabRegionWidth*(TabNum - 1)+1) //todo 这里有临界极限 751 ！=750的问题，_tabRegionWidth是750
        // {
        //     if (this.ui.BgOfBg.visible)
        //     {
        //         // skip
        //     }
        //     else {
        //         this.ui.BgOfBg.visible = true;
        //     }
        // }
        // else
        // {
        //     // 非临界范围时不展示
        //     if (!this.ui.BgOfBg.visible)
        //     {
        //         // skip
        //     }
        //     else
        //     {
        //         this.ui.BgOfBg.visible = false;
        //     }
        // }
    }


    void OnScrollAutoUpdateIndex()
    {
        var scrollX = tabGList.scrollPane.posX;
        float ratio = scrollX / _tabRegionWidth;
        int basePart = Mathf.FloorToInt(ratio);
        float percentPart = ratio - (float)(basePart);
        if (percentPart > 0.5)
        {
            basePart++;
        }

        this.tabGList.SetCurrIndex(basePart, TabChangeReason.swipe);
        Debug.Log("OnScrollEnd auto SetIndex=" + basePart + " x=" + scrollX + " w=" + _tabRegionWidth);
    }

    void OnScrollMoveEnd()
    {
        Debug.Log("==== scroll end ====");

        var x = tabGList.scrollPane.posX;
        if (cameraOffset)
        {
            float aspect = (float)Screen.width / (float)Screen.height;
            var viewWidth = tabGList.scrollPane.viewWidth;
            MoveCamera_VirtualCamera((x - viewWidth) / viewWidth, (1.5f * 2) * aspect, cameraOffset);
        }

        OnScrollAutoUpdateIndex();

        // if (tabGList.GetChildAt(0)?.name == UIConsts.CenterHome) 暂不使用
        // {
        //     this._centerHome.OnScrollToTop();
        // }
    }

    /// <summary>
    /// 更新方法，定期刷新数据
    /// </summary>
    /// <param name="interval">更新间隔</param>
    public void Update(int interval)
    {
        if (isShow)
        {
            if (TimeExt.serverTimestamp - lastUpdateTime > 1000 * 60)
            {
                GetController<RankController>(ModelConsts.Rank).RefreshRankDataUI();
                lastUpdateTime = TimeExt.serverTimestamp;
            }
        }
    }

    //锁定时间 lockMS
    //锁定时，希望锁在哪个tab，tabIdx2Lock （极特殊调用情况下 tab可能在锁定前已经被滑动）
    public void LockFrameworkScreen(int lockMS, TabIndex tabIdx2Lock)
    {
        try
        {
            this.ui.imgBG.visible = true;
            this.HideTabs(tabIdx2Lock, false, false);
            
            TimerManager.instance.RegisterTimer((c) =>
            {
                this.ui.imgBG.visible = false;
                this.ShowTabs();
            }, lockMS, 1);
        }
        catch (System.Exception e)
        {
            //纯防御，防止极端级联case导致锁屏
            this.ui.imgBG.visible = true;
            this.ShowTabs();
        }
    }


    #region 移动摄像机

    static Vector3 cameraPosition = new Vector3(4.45856f, 1.698748f, 3.527631f);

    /// <summary>
    /// 移动相机
    /// </summary>
    /// <param name="moveMultiplier">传递+1代表完全右滑出屏幕;传递-1代表完全左滑出屏幕</param>
    /// <param name="sizeOrDistance">透视相机传主体与相机的距离;非透视相机传Size.</param>
    /// <param name="origin">传相机原始位置</param>
    /// <param name="camera">相机对象</param>
    void MoveCamera(float moveMultiplier, float sizeOrDistance, Vector3 origin, Camera camera)
    {
        Transform cameraTransform = camera.transform;
        Vector3 cameraRight = cameraTransform.right;
        float cameraMoveMultiplier = sizeOrDistance * moveMultiplier;
        camera.transform.position = new(
            cameraPosition.x + cameraRight.x * cameraMoveMultiplier,
            cameraPosition.y + cameraRight.y * cameraMoveMultiplier,
            cameraPosition.z + cameraRight.z * cameraMoveMultiplier);
    }

    /// <summary>
    /// 移动虚拟相机
    /// </summary>
    /// <param name="moveMultiplier">传递+1代表完全右滑出屏幕;传递-1代表完全左滑出屏幕</param>
    /// <param name="sizeOrDistance">透视相机传主体与相机的距离;非透视相机传Size.</param>
    /// <param name="virtualCameraOffset">虚拟相机偏移组件</param>
    void MoveCamera_VirtualCamera(float moveMultiplier, float sizeOrDistance,
        CinemachineCameraOffset virtualCameraOffset)
    {
        virtualCameraOffset.m_Offset = new(
            moveMultiplier * sizeOrDistance,
            0);
    }

    #endregion

    /// <summary>
    /// 获取通知兴趣列表
    /// </summary>
    /// <returns>通知兴趣数组</returns>
    protected override string[] ListNotificationInterests()
    {
        return new string[]
        {
            NotifyConsts.LearnPathReShowEvent,
        };
    }

    /// <summary>
    /// 处理通知
    /// </summary>
    /// <param name="name">通知名称</param>
    /// <param name="body">通知内容</param>
    protected override void HandleNotification(string name, object body)
    {
        var enterPos = mainModel.ChatEnterPos;
        switch (name)
        {
        }
    }

    /// <summary>
    /// 检查当前是否为章节Tab
    /// </summary>
    /// <returns>是否为章节Tab</returns>
    public bool IsHomeTab()
    {
        int currIdx = this.tabGList.GetCurrIndex();
        TabIndex currTabEnum = (TabIndex)currIdx;
        return currTabEnum == TabIndex.Course && _currTabState == BottomTabState.showing;
    }
}