﻿namespace ScriptsHot.Game.Modules.ChatLogicNew.ChatState
{

    
    using Msg.basic;
    using Msg.task_process;
    using ScriptsHot.Game.Modules.Settlement;
    
    using UnityEngine;
    
    public class ChatLogicStateSettlement : ChatLogicStateBase
    {
        private SettlementController Ctr => _manager.GetController<SettlementController>(ModelConsts.Settlement);
        
        public ChatLogicStateSettlement(ChatTypeBase chat) : base(ChatStateName.Settlement,chat)
        {
        }
    
        public override void OnEnter(params object[] args)
        {
            base.OnEnter(args);
            //退出当前对话
            Notifier.instance.SendNotification(NotifyConsts.DoTopLeftBack,true);
            if (_manager.GetModel<ChatModel>(ModelConsts.Chat).chatMode == PB_DialogMode.OnBoarding)
            {
                _manager.GetUI(UIConsts.LoginTaskEnd).Show();
            }
            else
            {
                //这里注释掉 ，因为 结算时候需要手动点击界面进入下一个界面 
                // Ctr.DealResultData();
                // Ctr.ShowNextView();
            }
        }
    
        public override void OnExit()
        {
            base.OnExit();
            //结算的时候请求每日强化练习
            // this._manager.GetUI<MainHeaderUI>(UIConsts.MainHeader).SetVisible(true, true);
            if (_manager.GetModel<ChatModel>(ModelConsts.Chat).chatMode == PB_DialogMode.Intensify)
            {
                //请求每日强化题型数据
                CS_GetDailyTaskInfoReq data = new CS_GetDailyTaskInfoReq(); 
                MsgManager.instance.SendMsg(data);
                Debug.LogError("强化做完 请求题型数据");
            }
        }
    }
}