using System.Collections;
using System.Collections.Generic;
using FairyGUI;
using UIBind.ExploreFriends;
using UnityEngine;
using UnityEngine.UI;
using YooAsset;

public class ExploreFriendsUIHelper
{
    private static List<string> IntroduceIcon = new List<string>()
    {
        "ui://6iy4p04dfn7oc",
        "ui://6iy4p04dfn7ow",
        "ui://6iy4p04dfn7od",
        "ui://6iy4p04dfn7oe"

    };
    public static void SetIntroduceNode(GGroup go , ExploreFriendCfg data)
    {
        IntroduceItem item = new IntroduceItem();

        GObject node = go.GetChildren()[0];
        item.Construct(node.asCom);
        item.ContentTxt1.text = I18N.inst.MoStr("ui_explore_friends_preview_rare_friend_text");
        item.ContentTxt2.text =(data.RarityValue / 100f).ToString("0.0") + "%";
        item.Boutique.text = I18N.inst.MoStr("ui_explore_friends_rare_avatar_badge_text");
        item.iconLoader.url = IntroduceIcon[0];
        item.IsRare.selectedIndex = data.Rarity > 0 ? 1 : 0;

        node = go.GetChildren()[1];
        item.Construct(node.asCom);
        item.ContentTxt1.text = I18N.inst.MoStr("ui_explore_friends_preview_job_text");
        item.ContentTxt2.text = I18N.inst.MoStr(data.Job);
        item.iconLoader.url = IntroduceIcon[1];
        item.IsRare.selectedIndex = 0;
        
        node = go.GetChildren()[2];
        item.Construct(node.asCom);
        item.ContentTxt1.text = I18N.inst.MoStr("ui_explore_friends_preview_hobby_text");
        item.ContentTxt2.text = I18N.inst.MoStr(data.Hobby);
        item.iconLoader.url = IntroduceIcon[2];
        item.IsRare.selectedIndex = 0;
        
        node = go.GetChildren()[3];
        item.Construct(node.asCom);
        item.ContentTxt1.text = I18N.inst.MoStr("ui_explore_friends_preview_character_text");
        item.ContentTxt2.text = I18N.inst.MoStr(data.Character);
        item.iconLoader.url = IntroduceIcon[3];
        item.IsRare.selectedIndex = 0;
    }
    
    
    public static async void SetBGImg(ExploreFriendCfg friendCfg , GLoader bgLoader , bool isResult = false)
    {
        string name;
        if (isResult)
        {
            name = $"FriendBG{friendCfg.BGImgUrl}";
        }
        else
        {
            name = $"FriendBG{friendCfg.BGImgUrlResult}";
        }

        string url = ResUtils.GetExploreBgImgPath(name);
        var handle =  YooAsset.YooAssets.LoadAssetAsync<Texture2D>(url);
        await handle.Task;
        if (handle != null && handle.Status == EOperationStatus.Succeed)
        {
            Texture2D texture = handle.AssetObject as Texture2D;
            if (texture != null)
            {
                // 添加纹理尺寸验证
                if (texture.width <= 0 || texture.height <= 0 ||
                    texture.width > 16384 || texture.height > 16384)
                {
                    VFDebug.LogError($"#lll 纹理尺寸异常: {texture.width}x{texture.height}");
                    return;
                }
                bgLoader.texture = new NTexture(texture);
            }
        }

    }
    
}
