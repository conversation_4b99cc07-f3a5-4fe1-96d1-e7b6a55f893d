/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.FragmentPractice
{
    public partial class AduioCom : UIBindT
    {
        public override string pkgName => "FragmentPractice";
        public override string comName => "AduioCom";

        public Controller playCtrl;
        public Controller btnState;
        public GGraph audioBg;
        public GLoader btnPlay;
        public GLoader backup;
        public GLoader foward;
        public BtnAudio btn;
        public GGroup btnGrp;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            playCtrl = com.GetControllerAt(0);
            btnState = com.GetControllerAt(1);
            audioBg = (GGraph)com.GetChildAt(0);
            btnPlay = (GLoader)com.GetChildAt(1);
            backup = (GLoader)com.GetChildAt(2);
            foward = (GLoader)com.GetChildAt(3);
            btn = (BtnAudio)com.GetChildAt(4);
            btnGrp = (GGroup)com.GetChildAt(5);

            SetMultiLanguageInChildren();
            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            playCtrl = null;
            btnState = null;
            audioBg = null;
            btnPlay = null;
            backup = null;
            foward = null;
            btn = null;
            btnGrp = null;
        }

        public void SetMultiLanguageInChildren()
        {
            this.btnPlay.SetKey("extends=ABtnAudio");  // ""
        }
    }
}