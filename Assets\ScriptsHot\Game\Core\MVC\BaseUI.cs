﻿using System;
using System.Collections.Generic;
using FairyGUI;
using UnityEngine;

abstract public class BaseUI<UIT> : IBaseUI where UIT : UIBindT
{
    //UI名字
    private string _name = "";
    //UI实例
    protected FairyGUI.GComponent uiCom = null;
    //传入参数
    protected object[] args = null;
    //事件回调函数
    private NotifierCallBack _notifierCallBack = null;
    //UI是否显示
    private bool _isShow = false;
    //是否加载中
    private bool _isLoading = false;
    //是否加载时被关闭
    private bool _isHideWhenLoading = false;
    //是否完全显示
    private bool _isFullShow = false;
    //是否init
    private bool _isInit = false;
    //缓存UI
    private FairyGUI.GComponent _cacheUICom = null;
    //关闭Transition
    private FairyGUI.Transition _hideTransition = null;
    //UI父容器
    private FairyGUI.GComponent _container = null;
    //父UI
    private IBaseUI _parentUI = null;
    //子UI
    private Dictionary<string, IBaseUI> _childUIS = null;
    //OnInit后注册的UI事件
    private List<BaseUIEventVO> _eventsAfterInit = null;
    //OnShow后注册的UI事件
    private List<BaseUIEventVO> _eventsAfterShow = null;
    //OnShow后注册的Timer
    private List<string> _timerAfterShow = new List<string>();
    //UI绑定
    protected UIT ui;
    public Action OnHideAniCompleted { get; set; }
    private Action _onCompleted;
    public event Action onShow;//与 _onCompleted的作用重复，命名含义不清晰
    public event Action onHide;
    private string lazyShowTimerKey;

    //是否是第一次作为tab加载
    private bool _isLoadUIAsTab = false;
    public bool isLoadingPackage { get => _isLoading; }
    

    public bool IsLoadUIAsTab
    {
        get => _isLoadUIAsTab;
    }

    //特殊写法，这是Show调用完毕时的“回调”“回调”“回调”，是每次赋予使用后会被消耗
    public Action onCompleted 
    {
        get
        {
            return _onCompleted;
        }
        set
        {
            if (!isShow) 
                _onCompleted = value;
            else
            {
                value.Invoke(); 
            }
                
        }
    }

    public BaseUI(string name)
    {
        if (name == "")
            Debug.LogError("undefined name");
        this._name = name;
        this._eventsAfterInit = new List<BaseUIEventVO>();
        this._eventsAfterShow = new List<BaseUIEventVO>();
    }

    public BaseUI()
    {
        this._name = UIManager.GetUIClassName<BaseUI<UIT>>();
        Debug.LogError("Test auto uiname:" + this._name);
        this._eventsAfterInit = new List<BaseUIEventVO>();
        this._eventsAfterShow = new List<BaseUIEventVO>();
    }

  

    /**
     * UI创建Layer
     */
    public virtual string uiLayer
    {
        get
        {
            return string.Empty;
        }
    }

    public string name
    {
        get
        {
            return this._name;
        }
    }

    public bool isShow
    {
        get
        {
            return this._isShow;
        }
    }

    public GComponent uiComponent
    {
        get
        {
            return this.uiCom;
        }
    }

    public bool isInit
    {
        get
        {
            return this._isInit;
        }
    }

    public bool isFullShow
    {
        get
        {
            return this._isFullShow;
        }
    }

    public IBaseUI Show(Action onShowCompleteCallback, params object[] args)
    {
        this._onCompleted = onShowCompleteCallback;
        return this.Show(args);
    }
    
    public void PopShow(PopUIManager.Priority  priority = PopUIManager.Priority.Middle ,  params object[] args)
    {
        PopShow(null,priority, args);
    }
    
    public void PopShow(Action onShowCompleteCallback, PopUIManager.Priority  priority = PopUIManager.Priority.Middle ,  params object[] args)
    {
        this._onCompleted = onShowCompleteCallback;
        PopUIManager.instance.AddPopUI(this, priority , args);
    }

    public void PopTryRemove()
    {
        PopUIManager.instance.TryRemovePopUI(this);
    }

    /// <summary>
    /// 全新接口
    /// 内部的OnUILoad 实际上也做了show的工作
    /// </summary>
    /// <returns></returns>
    public IBaseUI LoadTab() {
        //Debug.Log("do LoadTab name:"+this.name);
        if (this._isLoading)
        {
            return this;
        }
        if (this._cacheUICom != null)
        {
            this.uiCom = this._cacheUICom;
            this._cacheUICom = null;
            if (this.ui == null)
            {
                UIT nUI = Activator.CreateInstance<UIT>();
                this.ui = nUI;
                this.ui.Construct(this.uiCom);
            }

            //tab中实际add的操作被转移
            //this.OnUILoad4Tab();

            //_onCompleted?.Invoke();
            //_onCompleted = null;
            //onShow?.Invoke();
        }
        else
        {
            this._isLoading = true;
            UIT nUI = Activator.CreateInstance<UIT>();
            string pkgName = nUI.pkgName;
            string comName = nUI.comName;

            //Debug.Log("loadTab pkgName="+ pkgName+ " comName="+ comName);
            //var gObj = UIPackage.CreateObject(pkgName, comName);

            //if (gObj == null)
            //{

            //    Debug.Log("loadTab get null");
            //}
            //else {
            //    this.uiCom = gObj.asCom;
            //}



            //if (this.uiCom == null)
            //{
            //    Debug.LogError("UIPackage CreateUI Error." + pkgName + comName);

            //}


            //this.uiCom.fairyBatching = true;
            //this.uiCom.name = this._name;
            ////
            //if (this.ui == null)
            //{
            //    this.ui = nUI;
            //    this.ui.Construct(this.uiCom);
            //}
            ////
            //this.OnInit(this.uiCom);
            //this._isInit = true;
            //this.OnUILoad();

            //_onCompleted?.Invoke();
            //_onCompleted = null;
            //onShow?.Invoke();

            //loadTab 的资源是 目前是需要立即加载的
            //将放在preload

            UIManager.instance.LoadPackage(pkgName, () =>
            {
                this._isLoading = false;
                if (this._isHideWhenLoading)
                {
                    return;
                }

                //add by raybit for TabPage

                Debug.Log("uiCon ==null");
                FairyGUI.UIPackage uiPackage = FairyGUI.UIPackage.GetByName(pkgName);
                if (uiPackage == null)
                {
                    Debug.LogError("Get UIPackage Error." + pkgName);
                    return;
                }
                this.uiCom = uiPackage.CreateObject(comName).asCom;
                if (this.uiCom == null)
                {
                    Debug.LogError("UIPackage CreateUI Error." + pkgName + comName);
                    return;
                }


                this.uiCom.fairyBatching = true;
                this.uiCom.name = this._name;
                //
                if (this.ui == null)
                {
                    this.ui = nUI;
                    this.ui.Construct(this.uiCom);
                }
                //
                this.OnInit(this.uiCom);
                this._isInit = true;
                //this.OnUILoad();

                //_onCompleted?.Invoke();
                //_onCompleted = null;
                //onShow?.Invoke();
            });
        }

        return this;
    }

    public void RemoveChildAt4Tab(GComponent parentComp, int index)
    {
        parentComp.AddChildAt(this.uiCom, index);
        //不处理 selected问题
        //不处理 click问题
    }

    public void AddChildAt4Tab(GComponent parentComp,int index)
    {
        Debug.Log("AddChildAt4Tab index="+index+" name="+this.name);
        parentComp.AddChildAt(this.uiCom,index); //tab加载时 会绑定parent所以不在使用 uilayer
        //不处理 selected问题
        //不处理 click问题
        this._isLoadUIAsTab = true;
        OnUILoad4Tab();

  
        //Show4Tab(); //只在后续ItemRender的逻辑中执行一次Show4Tab
    }

    public void Show4Tab() {

        this._isLoadUIAsTab = true;
        this._isShow = true;
        if(this.uiCom!=null) this.uiCom.visible = true;//补丁
        this.OnShow();

        //Tab类的展示不震动
        //if (uiLayer == UILayerConsts.Top)
        //{
        //    VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Pop);
        //}

        this.RegisterNotification();

        //Tab类默认不触发 transition的自定义动画
        //FairyGUI.Transition showTransition = this.uiCom?.GetTransition("OnShow");
        //if (showTransition != null)
        //{
        //    showTransition.invalidateBatchingEveryFrame = true;
        //    showTransition.Play(() =>
        //    {
        //        this.DoShow();
        //    });
        //}
        //else if (TryGetTransitionUI(this, out var cutInEffect))
        //{
        //    if (cutInEffect == CutInEffect.Popup)
        //    {
        //        var startPivot = ui.com.pivot;
        //        ui.com.scale = new Vector2(0.95f, 0.95f);
        //        ui.com.pivot = new Vector2(0.5f, 0.5f);
        //        ui.com.alpha = 0.0f;
        //        ui.com.TweenFade(1.0f, 0.1f);
        //        ui.com.TweenScale(new Vector2(1.05f, 1.05f), 0.1f).OnComplete(() =>
        //        {
        //            ui.com.TweenScale(Vector2.one, 0.1f).OnComplete(() =>
        //            {
        //                ui.com.pivot = startPivot;
        //                this.DoShow();
        //            });
        //        });
        //    }
        //    else
        //    {
        //        var rootSize = FairyGUI.GRoot.inst.size;
        //        var startPos = Vector2.zero;
        //        var endPos = Vector2.zero;
        //        switch (cutInEffect)
        //        {
        //            case CutInEffect.LeftToRight:
        //                startPos = new Vector2(-10 - rootSize.x, 0);
        //                break;
        //            case CutInEffect.RightToLeft:
        //                startPos = new Vector2(10 + rootSize.x, 0);
        //                break;
        //            case CutInEffect.TopToBottom:
        //                startPos = new Vector2(0, -10 - rootSize.y);
        //                break;
        //            case CutInEffect.BottomToTop:
        //                startPos = new Vector2(0, 10 + rootSize.y);
        //                break;
        //        }

        //        ui.com.xy = startPos;
        //        ui.com.TweenMove(endPos, 0.2f).SetEase(FairyGUI.EaseType.SineOut).OnComplete(() =>
        //        {
        //            ui.com.xy = Vector2.zero;
        //            this.DoShow();
        //        });
        //    }
        //}
        //else
        //{
        //    this.DoShow();
        //}

        this.DoShow();

        UIManager.instance.CheckMutexGroup(this);
        UIManager.instance.OnUIShow(this);

        //========================================
        _onCompleted?.Invoke();//不确定是否要调用
        _onCompleted = null;

        onShow?.Invoke();//目前没人调用
    }
    
    /**
     * 显示UI
     * @param param 参数
     */
    public IBaseUI Show(params object[] args)
    {    
        if (this._childUIS == null)
        {
            this._childUIS = new Dictionary<string, IBaseUI>();
            this.OnCreateChildUI();
        }
        //
        if (this._hideTransition != null)
        {
            this._hideTransition.Stop(false, true);
            this._hideTransition = null;
            this.DoHide();
        }

        if (this.isShow)
        {
            _onCompleted?.Invoke();
            _onCompleted = null;
            onShow?.Invoke();
            return this;
        }
        this.args = args;
        this._isHideWhenLoading = false;
        if (this._isLoading)
        {
            return this;
        }

        if (this._cacheUICom != null)
        {
            if (uiLayer == UILayerConsts.Top)
            {
                VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Pop);
            }
            this.uiCom = this._cacheUICom;
            this._cacheUICom = null;
            this.OnUILoad(args);

            _onCompleted?.Invoke();
            _onCompleted = null;
            onShow?.Invoke();
        }
        else
        {
            this._isLoading = true;
            UIT nUI = Activator.CreateInstance<UIT>();
            string pkgName = nUI.pkgName;
            string comName = nUI.comName;
            UIManager.instance.LoadPackage(pkgName, () =>
            {
                this._isLoading = false;
                if (this._isHideWhenLoading)
                {
                    return ;
                }

                //add by raybit for TabPage
                if (this.uiCom == null)
                {
                    //Debug.Log($"uiCon == null:{pkgName} {comName}");
                    FairyGUI.UIPackage uiPackage = FairyGUI.UIPackage.GetByName(pkgName);
                    if (uiPackage == null)
                    {
                        Debug.LogError("Get UIPackage Error." + pkgName);
                        return;
                    }
                    this.uiCom = uiPackage.CreateObject(comName).asCom;
                    if (this.uiCom == null)
                    {
                        Debug.LogError("UIPackage CreateUI Error." + pkgName + comName);
                        return;
                    }
                }
                else {
                    Debug.Log("uiCom !=null skip");
                }
                
                this.uiCom.fairyBatching = true;
                this.uiCom.name = this._name;
                //
                if (this.ui == null)
                {
                    this.ui = nUI;
                    this.ui.Construct(this.uiCom);
                }
                //
                this.OnInit(this.uiCom);
                this._isInit = true;

                if (uiLayer == UILayerConsts.Top)
                {
                    VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Pop);
                }
                this.OnUILoad(args);

                _onCompleted?.Invoke();
                _onCompleted = null;
                onShow?.Invoke();
            });
        }

        return this;
    }

    /**
     * 尚未显示时，尽量lazy显示UI,默认delay 500ms
     * 
     * 使用lazyShow之后，可以通过调用Hide cancel掉未触发的lazyShow的timer
     * 
     */
    public virtual void LazyShow(int timerInerval = 500, params object[] args)
    {
        if (string.IsNullOrEmpty(this.lazyShowTimerKey))
        { 
            this.lazyShowTimerKey = TimerManager.instance.RegisterTimer((t) => {
                this.Show(args);
                this.lazyShowTimerKey = null;
                Debug.Log("dtrigger lazyShow:"+this.name);
            }, timerInerval, 1);
            
        }
        else {
            Debug.Log("Unfinished LazyShow is called, not use it at same time. uiName=" +this.name);
        }
        
    }
         

    /**
     * 关闭UI
     */
    public void Hide(params object[] param)
    {
        if (!string.IsNullOrEmpty(this.lazyShowTimerKey))
        {
            TimerManager.instance.UnRegisterTimer(this.lazyShowTimerKey);
            this.lazyShowTimerKey = null;
            //执行这里的时候，如果还没有实际show，则消除show的lazyShowTimer
            return;
        }
        if (this._isLoading)
        {
            this._isHideWhenLoading = true;
            return;
        }
        if (_childUIS != null)
        {
            foreach (var item in _childUIS)
            {
                item.Value.Hide();
            }
        }
        if (!this._isShow)
            return;
        if (this.uiCom != null)
        {
            if (TryGetTransitionUI(this, out var cutInEffect))
            {
                var rootSize = FairyGUI.GRoot.inst.size;
                var startPos = Vector2.zero;
                var endPos = Vector2.zero;
                bool moveState = false;
                if (cutInEffect == CutInEffect.Popup)
                {
                    var startPivot = ui.com.pivot;
                    var startScale = ui.com.scale;
                    ui.com.pivot = new Vector2(0.5f, 0.5f);
                    ui.com.TweenFade(0.0f, 0.1f);
                    ui.com.TweenScale(new Vector2(0.95f, 0.95f), 0.1f).OnComplete(() =>
                    {
                        ui.com.pivot = startPivot;
                        ui.com.alpha = 1.0f;
                        ui.com.scale = startScale;
                        this.DoHide();
                        VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Soft);
                    });
                }
                else
                {
                    switch (cutInEffect)
                    {
                        case CutInEffect.LeftToRight:
                            endPos = new Vector2(-10 - rootSize.x, 0);
                            break;
                        case CutInEffect.RightToLeft:
                            endPos = new Vector2(10 + rootSize.x, 0);
                            break;
                        case CutInEffect.TopToBottom:
                            endPos = new Vector2(0, -10 - rootSize.y);
                            break;
                        case CutInEffect.BottomToTop:
                            endPos = new Vector2(0, 10 + rootSize.y);
                            break;
                    }

					if (!moveState)
	                {
	                    ui.com.xy = Vector2.zero;
	                    this.DoHide();
	                    return;
	                }
                    ui.com.xy = startPos;
                    ui.com.TweenMove(endPos, 0.2f).OnComplete(() =>
                    {
                        ui.com.xy = Vector2.zero;
                        this.DoHide();
                        VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Soft);
                    });
                }
            }
            else
            {
                if (this._hideTransition != null)
                {
                    return;
                }
                this._hideTransition = this.uiCom.GetTransition("OnHide");
                if (this._hideTransition == null)
                {
                    this.DoHide();
                    return;
                }

                this._hideTransition.Play(() => {
                        this.DoHide(); 
                        OnHideAniCompleted?.Invoke();
                    });
            }
        }
        else
        {
            this.DoHide();
        }
        
    }

    // public void HideImmediate()
    // {
    //     OnHideAniCompleted = null;
    //     this.DoHide(); 
    // }

    private void OnUILoad(params object[] args)
    {
        ContainerUI containerUI = args != null && args.Length > 0 && args[0] is ContainerUI ? args[0] as ContainerUI : null;

        if (this._container != null)
        {
            Debug.Log("do OnUILoad:" + this.name);
            this._container.AddChild(this.uiCom);
            if (this.isFitContainer)
            {
                this.uiCom.width = this._container.width;
                this.uiCom.height = this._container.height;
                this.uiCom.AddRelation(this._container, FairyGUI.RelationType.Width);
                this.uiCom.AddRelation(this._container, FairyGUI.RelationType.Height);
            }
            this.uiCom.InvalidateBatchingState();
        }
        else
        {
            if (containerUI != null)
            {
                containerUI.Container.AddChild(this.uiCom);
            }
            else
            {
                FairyGUI.GComponent layer = UIManager.instance.GetLayer(this.uiLayer);
                layer.AddChild(this.uiCom);
                layer.InvalidateBatchingState(true);
                if (this.isFullScreen)
                {
                    if (layer.width > 0 && layer.height > 0)
                    {
                        this.uiCom.width = layer.width;
                        this.uiCom.height = layer.height;
                        this.uiCom.AddRelation(layer, FairyGUI.RelationType.Width);
                        this.uiCom.AddRelation(layer, FairyGUI.RelationType.Height);
                    }
                    else
                    {
                        this.uiCom.width = FairyGUI.GRoot.inst.width;
                        this.uiCom.height = FairyGUI.GRoot.inst.height;
                        this.uiCom.AddRelation(FairyGUI.GRoot.inst, FairyGUI.RelationType.Width);
                        this.uiCom.AddRelation(FairyGUI.GRoot.inst, FairyGUI.RelationType.Height);
                    }
                }
            }
        }

        this._isShow = true;
        this.OnResize();
        this.RefreshLanguageComs();
        VFDebug.Log(this._name + " OnShow");
        this.OnShow();
        this.RegisterNotification();
        //

        FairyGUI.Transition showTransition = this.uiCom?.GetTransition("OnShow");
        if (showTransition != null)
        {
            showTransition.invalidateBatchingEveryFrame = true;
            showTransition.Play(() =>
            {
                this.DoShow();
            });
        }
        else if (TryGetTransitionUI(this, out var cutInEffect))
        {
            if (cutInEffect == CutInEffect.Popup)
            {
                var startPivot = ui.com.pivot;
                ui.com.scale = new Vector2(0.95f, 0.95f);
                ui.com.pivot = new Vector2(0.5f, 0.5f);
                ui.com.alpha = 0.0f;
                ui.com.TweenFade(1.0f, 0.1f);
                ui.com.TweenScale(new Vector2(1.05f, 1.05f), 0.1f).OnComplete(() =>
                {
                    ui.com.TweenScale(Vector2.one, 0.1f).OnComplete(() =>
                    {
                        ui.com.pivot = startPivot;
                        this.DoShow();
                    });
                });
            }
            else
            {
                var rootSize = FairyGUI.GRoot.inst.size;
                var startPos = Vector2.zero;
                var endPos = Vector2.zero;
                switch (cutInEffect)
                {
                    case CutInEffect.LeftToRight:
                        startPos = new Vector2(-10 - rootSize.x, 0);
                        break;
                    case CutInEffect.RightToLeft:
                        startPos = new Vector2(10 + rootSize.x, 0);
                        break;
                    case CutInEffect.TopToBottom:
                        startPos = new Vector2(0, -10 - rootSize.y);
                        break;
                    case CutInEffect.BottomToTop:
                        startPos = new Vector2(0, 10 + rootSize.y);
                        break;
                }

                ui.com.xy = startPos;
                ui.com.TweenMove(endPos, 0.2f).SetEase(FairyGUI.EaseType.SineOut).OnComplete(() =>
                {
                    ui.com.xy = Vector2.zero;
                    this.DoShow();
                });
            }
        }
        else
        {
            this.DoShow();
        }

        UIManager.instance.CheckMutexGroup(this);
        UIManager.instance.OnUIShow(this);
        
        if(this.uiCom != null)AutoAddBlur(this.uiCom);
        
    }

    private void AutoAddBlur(GObject parent)
    {
        if (parent.data is I18NData { key: "BLUR" } data)
        {
            if (parent is GImage || parent is GGraph )
            {
                BeforeUIPassManager.AddRenderer(parent.displayObject);
            }
            else if (parent is GButton parent2)
            {
                BeforeUIPassManager.AddRenderer(parent2);
            }
        }
        
        if (parent is GComponent parentComp)
        {
            int childCount = parentComp.numChildren;
            for (int i = 0; i < childCount; i++)
            {
                GObject child = parentComp.GetChildAt(i);
                if (child != null)
                {
                    AutoAddBlur(child);
                }
            }
        }
    }

    private void OnUILoad4Tab()
    {
        if (this._container != null)
        {
            Debug.Log("do OnUILoad4Tab:"+this.name);
            this._container.AddChild(this.uiCom);
            if (this.isFitContainer)
            {
                this.uiCom.width = this._container.width;
                this.uiCom.height = this._container.height;
                this.uiCom.AddRelation(this._container, FairyGUI.RelationType.Width);
                this.uiCom.AddRelation(this._container, FairyGUI.RelationType.Height);
            }
            this.uiCom.InvalidateBatchingState();
        }
        else
        {
            if (this.isFullScreen)
            {
                this.uiCom.width = FairyGUI.GRoot.inst.width;
                this.uiCom.height = FairyGUI.GRoot.inst.height;
                this.uiCom.AddRelation(FairyGUI.GRoot.inst, FairyGUI.RelationType.Width);
                this.uiCom.AddRelation(FairyGUI.GRoot.inst, FairyGUI.RelationType.Height);
            }
        }
        
   
        this.OnResize();

        this.RefreshLanguageComs();
        //VFDebug.Log(this._name + " OnShowInTab");

     
        //剩余的触发逻辑放到 Add Tab里面
    }

    private bool TryGetTransitionUI(IBaseUI ui, out CutInEffect cutInEffect)
    {
        cutInEffect = CutInEffect.None;
        var attributes = ui.GetType().GetCustomAttributes(typeof(TransitionUI), false);
        if (attributes != null && attributes.Length > 0)
        {
            cutInEffect = (attributes[0] as TransitionUI).EnumValue;
            return true;
        }
        return false;
    }

    private void DoShow()
    {
        this._isFullShow = true;
        this.OnFullShow();
    }

    private void DoHide()
    {
        VFDebug.Log("Onhide:" + this._name);
        if (this._hideTransition != null)
        {
            this._hideTransition.Stop();
            this._hideTransition = null;
        }
      
        if (this.uiCom != null && this._isShow)
        {
            this.UnRegisterNotification();
            this._isShow = false;
            this.OnHide();
            this.onHide?.Invoke();
			VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
            this.RemoveUIEventAfterShow();
            this.RemoveTimerAfterShow();
            //
            FairyGUI.GComponent container = this.uiCom.parent;
            if (container != null)
            {
                container.RemoveChild(this.uiCom);
                this._cacheUICom = this.uiCom;
                this.uiCom = null;
            }
            UIManager.instance.OnUIHide(this);
            PopUIManager.instance.OnUIHide(this);
        }
        this.args = null;
        this._isShow = false;
        this._isFullShow = false;
    }



    public void DeleteUI()
    {
        if(this._childUIS != null)
        {
            foreach (var childUI in this._childUIS.Values)
            {
                childUI.Hide();
                childUI.DeleteUI();
            }
        }
        //
        this._isLoading = false;
        this._container = null;
        string pkgName = string.Empty;
        if (this.ui != null)
        {
            pkgName = this.ui.pkgName;
            this.ui.Dispose();
            this.ui = null;
        }
        if (this._cacheUICom != null)
        {
            this.RemoveUIEventAfterInit();
            this.OnDestroy();
            this._cacheUICom.Dispose();
            this._cacheUICom = null;
            if(string.IsNullOrEmpty(pkgName))
            {
                UIT nUI = Activator.CreateInstance<UIT>();
                pkgName = nUI.pkgName;
            }
            UIManager.instance.UnloadPackage(pkgName);
        }
        this._isInit = false;
    }

    /**
     * 注册UI事件
     * 默认按钮点击间隔
     */
    public void AddUIEvent(FairyGUI.EventListener eventListener, FairyGUI.EventCallback0 callback, int buttonClickInterval = 300)
    {
        this.AddUIEvent(eventListener, callback, null, buttonClickInterval);
    }
    public void AddUIEvent(FairyGUI.EventListener eventListener, FairyGUI.EventCallback1 callback, int buttonClickInterval = 300)
    {
        this.AddUIEvent(eventListener, null, callback, buttonClickInterval);
    }

    //没有联合类型好鸡儿麻烦
    private void AddUIEvent(FairyGUI.EventListener eventListener,
        FairyGUI.EventCallback0 callback0, FairyGUI.EventCallback1 callback1, int buttonClickInterval)
    {
        for (int i = 0; i < this._eventsAfterInit.Count; i++)
        {
            BaseUIEventVO vo = this._eventsAfterInit[i];
            if (vo.eventListener == eventListener && (vo.callback0 == callback0 || vo.callback1 == callback1))
            {
                return;
            }
        }
        for (int i = 0; i < this._eventsAfterShow.Count; i++)
        {
            BaseUIEventVO vo = this._eventsAfterShow[i];
            if (vo.eventListener == eventListener && (vo.callback0 == callback0 || vo.callback1 == callback1))
            {
                return;
            }
        }
        //
        BaseUIEventVO eventVO = new BaseUIEventVO();
        eventVO.eventListener = eventListener;
        eventVO.callback0 = callback0;
        eventVO.callback1 = callback1;
        eventVO.eventTriggerInterval = eventListener.type == "onClick" ? buttonClickInterval : -1;
        eventVO.listenerCallback = eventContext =>
        {
            //在当前状态下禁止ui的点击操作  
            MainController mainController = GetController<MainController>(ModelConsts.Main);
            if (!string.IsNullOrEmpty(mainController.GetCurStateName()) )
            {
                if(mainController.GetCurStateName() == GameState.SceneLoading)//现在跨场景进聊天 是此状态
                    return;
            }
            
            SceneController sceneController = GetController<SceneController>(ModelConsts.Scene);
            if (!string.IsNullOrEmpty(sceneController.GetCurStateName()) )
            {
                if(sceneController.GetCurStateName() == SceneState.Task ||     //点击任务卡片进对话
                   sceneController.GetCurStateName() == SceneState.PreChat ||  //在进入聊天前的状态
                sceneController.GetCurStateName() == SceneState.ChatRecoonect  //对话重连
                ) 
                    return;
            }
            
            //按钮点击间隔判定
            if (eventVO.eventTriggerInterval > -1)
            {
                var currTime = TimeExt.currTime;
                if (currTime - eventVO.eventTriggerTime < eventVO.eventTriggerInterval)
                    return;
                eventVO.eventTriggerTime = currTime;
            }
            if (callback0 != null)
                callback0();
            if (callback1 != null)
                callback1(eventContext);
        };
        if (!this._isInit)
            this._eventsAfterInit.Add(eventVO);
        else
            this._eventsAfterShow.Add(eventVO);
        eventListener.Add(eventVO.listenerCallback);
    }

    private void RemoveUIEventAfterShow()
    {
        for (int i = 0; i < this._eventsAfterShow.Count; i++)
        {
            BaseUIEventVO vo = this._eventsAfterShow[i];
            vo.eventListener.Remove(vo.listenerCallback);
            vo.eventListener = null;
            vo.callback0 = null;
            vo.callback1 = null;
            vo.listenerCallback = null;
        }
        this._eventsAfterShow.Clear();
    }

    private void RemoveUIEventAfterInit()
    {
        for (int i = 0; i < this._eventsAfterInit.Count; i++)
        {
            BaseUIEventVO vo = this._eventsAfterInit[i];
            vo.eventListener.Remove(vo.listenerCallback);
            vo.eventListener = null;
            vo.callback0 = null;
            vo.callback1 = null;
            vo.listenerCallback = null;
        }
        this._eventsAfterInit.Clear();
    }

    public IBaseUI parentUI
    {
        get { return this._parentUI; }
        set { this._parentUI = value; }
    }


    /**
     * 设置UI父容器
     */
    public void SetContainer(FairyGUI.GComponent container)
    {
        this._container = container;
    }

    /**
     * 设置所有子UI的父容器
     */
    public void SetAllChildContainer(FairyGUI.GComponent container)
    {
        foreach (var childUI in this._childUIS.Values)
        {
            childUI.SetContainer(container);
        }
    }
    /**
     * 添加子UI
     */
    public void AddChildUI(IBaseUI childUI)
    {
        string childUIName = childUI.name;
        childUI.parentUI = this;
        this._childUIS[childUI.name] = childUI;
        UIManager.instance.AddUI(childUI);
    }

    /**
     * 显示子UI
     */
    public void ShowChildUI(string childUIName, bool unshowOther = true, bool showAni = false, params object[] args)
    {
        IBaseUI childUI;
        this._childUIS.TryGetValue(childUIName, out childUI);
        if (childUI == null)
            return;

        if (unshowOther)
        {
            List<string> keys = new List<string>(this._childUIS.Keys);
            for (int i = 0; i < keys.Count; i++)
            {
                if (keys[i] != childUIName)
                {
                    IBaseUI hideChildUI = this._childUIS[keys[i]];
                    VFDebug.Log("hideChildUI name: " + hideChildUI.name);
                    hideChildUI.OnHideAniCompleted = null;
                    hideChildUI.Hide();
                }
            }
        }
        childUI.Show(args);

        // if (childUI.isShow && showAni)
        // {
        //     childUI.OnHideAniCompleted = () =>{
        //             childUI.Show(args);
        //         };
        //     childUI.Hide();
        // }
        // else
        // {
        //     childUI.Show(args);
        // }
    }

    public void HideChildUI(string childUIName)
    {
        IBaseUI childUI;
        this._childUIS.TryGetValue(childUIName, out childUI);
        if (childUI != null && childUI.isShow)
        {
            childUI.Hide();
        }
    }

    /**
     * 获取子UI
     */
    public T GetChildUI<T>(string childUIName) where T : IBaseUI
    {
        IBaseUI childUI;
        this._childUIS.TryGetValue(childUIName, out childUI);
        return (T)childUI;
    }
    
    public IBaseUI GetChildUI(string childUIName)
    {
        IBaseUI childUI;
        this._childUIS.TryGetValue(childUIName, out childUI);
        return childUI;
    }
    
    public void OnResize()
    {
        this.UpdatePosition();
        this.UpdateMaskPosition();
        this.UpdateBGPosition();
    }

    /**
     * 界面加载完成，子类实现
     */
    virtual protected void OnInit(FairyGUI.GComponent uiCom) { }

    /**
     * 界面显示回调，子类实现
     */
    virtual protected void OnShow() { }

    virtual protected void OnShow4Tab() { }

    /**
     * 界面完全显示回调,子类实现
     */
    virtual protected void OnFullShow() { }

    /**
     * 界面关闭回调，子类实现
     */
    virtual protected void OnHide() { }

    /**
     * 界面销毁时回调，子类实现
     */
    virtual protected void OnDestroy() { }

    /**
     * 创建子UI,子类实现
     */
    virtual protected void OnCreateChildUI() { }


    /**
     * 是否全屏界面,子类实现
     */
    virtual protected bool isFullScreen
    {
        get
        {
            return false;
        }
    }

    virtual protected bool isFitContainer
    {
        get { return false; }
    }

    /**
     * 界面互斥组,相同组的只能同时有一个界面存在,默认0不互斥
     */
    virtual public int mutexGroup
    {
        get
        {
            return 0;
        }
    }

    /**
     * 界面销毁规则 -1永不回收 0正常卸载 1为n分钟后回收
     */
    virtual public EUIDeleteRule deleteRule
    {
        get
        {
            return EUIDeleteRule.Auto;
        }
    }

    virtual protected float width
    {
        get
        {
            if (this.uiCom == null)
                return 0;
            return (float)Math.Floor(this.uiCom.width / this.uiCom.scaleX);
        }
    }

    virtual protected float height
    {
        get
        {
            if (this.uiCom == null)
                return 0;
            return (float)Math.Floor(this.uiCom.height / this.uiCom.scaleY);
        }
    }

    /**
     * 更新界面位置
     */
    protected void UpdatePosition()
    {
        if (this.isFullScreen)
            return;
        if (this.parentUI != null)
            return;
        if (!this.isShow)
            return;
        BaseUIPositionVO position = this.GetPositionCfg();
        this.uiCom.x = position.x - position.offsetX;
        this.uiCom.y = position.y - position.offsetY;
        this.UpdateMaskPosition();
        this.UpdateBGPosition();
    }

    private BaseUIPositionVO GetPositionCfg()
    {
        //todo read cfg
        BaseUIPositionVO positionCfg = this.GetUIPosition();
        float stageWidth = UIManager.instance.width;
        float stageHeight = UIManager.instance.height;
        float uiWidth = this.width;
        float uiHeight = this.height;
        //
        float x = Math.Abs(positionCfg.x) > 1 ?
            (positionCfg.x > 0 ? positionCfg.x : stageWidth + positionCfg.x) :
            (positionCfg.x >= 0 ? stageWidth * positionCfg.x : stageWidth * (1 + positionCfg.x));
        float y = Math.Abs(positionCfg.y) > 1 ?
            (positionCfg.y > 0 ? positionCfg.y : stageHeight + positionCfg.y) :
            (positionCfg.y >= 0 ? stageHeight * positionCfg.y : stageHeight * (1 + positionCfg.y));

        float offsetX = Math.Abs(positionCfg.offsetX) > 1 ? positionCfg.offsetX :
            (positionCfg.offsetX >= 0 ? uiWidth * positionCfg.offsetX : uiWidth * (1 + positionCfg.offsetX));
        float offsetY = Math.Abs(positionCfg.offsetY) > 1 ? positionCfg.offsetY :
            (positionCfg.offsetY >= 0 ? uiHeight * positionCfg.offsetY : uiHeight * (1 + positionCfg.offsetY));
        return new BaseUIPositionVO
        {
            x = (float)Math.Floor(x),
            y = (float)Math.Floor(y),
            offsetX = (float)Math.Floor(offsetX),
            offsetY = (float)Math.Floor(offsetY)
        };
    }

    virtual protected BaseUIPositionVO GetUIPosition()
    {
        return new BaseUIPositionVO { x = 0.5f, y = 0.5f, offsetX = 0.5f, offsetY = 0.5f };
    }


    /**
     * 更新界面蒙版
     */
    protected void UpdateMaskPosition()
    {
        if (this.uiCom == null) return;
        var mask = this.uiCom.GetChild("maskBox");
        if (mask == null) return;
        CutInEffect cutInEffect = CutInEffect.None;
        TryGetTransitionUI(this, out cutInEffect);
        if (cutInEffect == CutInEffect.Popup)
        {
            //Multiply by 1.1 to avoid the mask being cut off on popup transitions.
            
            mask.width = (UIManager.instance.width) * 1.6f;
            mask.height = (UIManager.instance.height + UIManager.instance.safeAreaTopHeight + UIManager.instance.safeAreaBottomHeight) * 1.6f;
            var maskOffsetX = mask.width * 0.2f;
            var maskOffsetY = mask.height * 0.15f;
            mask.x = -this.uiCom.x - maskOffsetX;
            mask.y = -this.uiCom.y - UIManager.instance.safeAreaTopHeight - maskOffsetY;
        }
        else
        {
            mask.width = UIManager.instance.width;
            mask.height = UIManager.instance.height + UIManager.instance.safeAreaTopHeight + UIManager.instance.safeAreaBottomHeight;
            mask.x = -this.uiCom.x;
            mask.y = -this.uiCom.y - UIManager.instance.safeAreaTopHeight;
        }
    }

    protected void UpdateBGPosition()
    {
        if (this.uiCom == null) return;
        var imgBG = this.uiCom.GetChild("imgBG");

        if (imgBG != null) {
            imgBG.width = UIManager.instance.width;
            imgBG.height = UIManager.instance.height;// + UIManager.instance.safeAreaTopHeight + UIManager.instance.safeAreaBottomHeight;

            if (!this._isLoadUIAsTab)
            {
                imgBG.x = -this.uiCom.x; //不作为 底tab元素时允许 x轴的自动对齐
            }
            FairyGUI.GComponent layer = UIManager.instance.GetLayer(this.uiLayer);
            imgBG.y = -this.uiCom.y - layer.y;
            
            UpdateSafeTop(imgBG.y);
        }
    }
    
    protected void UpdateSafeTop(float imgBG_y)
    {
        if (this.uiCom == null) return;
        var safeTop = this.uiCom.GetChild("safeTop");

        if (safeTop != null) {
            safeTop.y = imgBG_y + UIManager.instance.safeAreaTopHeight;
        }
    }
    
    /**
     * 发送事件
     */
    protected void SendNotification(string name, object body = null)
    {
        Notifier.instance.SendNotification(name, body);
    }

    //注册事件
    private void RegisterNotification()
    {
        string[] notifications = this.ListNotificationInterests();
        if (notifications == null)
            return;
        if (this._notifierCallBack == null)
        {
            this._notifierCallBack = (string name, object body) =>
            {
                this.HandleNotification(name, body);
            };
        }
        for (int i = 0; i < notifications.Length; i++)
        {
            Notifier.instance.RegisterNotification(notifications[i], this._notifierCallBack);
        }
    }

    //取消注册事件
    private void UnRegisterNotification()
    {
        if (this._notifierCallBack == null)
            return;
        string[] notifications = this.ListNotificationInterests();
        if (notifications == null)
            return;
        for (int i = 0; i < notifications.Length; i++)
        {
            Notifier.instance.UnRegisterNotification(notifications[i], this._notifierCallBack);
        }
    }

    //处理事件
    virtual protected void HandleNotification(string name, object body) { }

    //返回监听的事件列表
    virtual protected string[] ListNotificationInterests()
    {
        return null;
    }

    public void RefreshLanguageComs()
    {
        // this.ui.SetMultiLanguageInChildren();
        // foreach(var gCom in comList)
        // {
        //     if (gCom.data is string)
        //     {
        //         // 兼容旧的在编辑器里挂key的格式
        //         string key = (string)gCom.data;
        //         if (string.IsNullOrEmpty(key)) continue;
        //         gCom.SetKey(key);
        //     }
        // }
    }

    protected string RegisterTimer(TimerCallBack callback, int delay, int repeatCount = 1)
    {
        var key = TimerManager.instance.RegisterTimer(callback, delay, repeatCount);
        this._timerAfterShow.Add(key);
        return key;
    }

    protected void UnRegisterTimer(string key)
    {
        int index = this._timerAfterShow.IndexOf(key);
        if (index > -1)
            this._timerAfterShow.RemoveAt(index);
        TimerManager.instance.UnRegisterTimer(key);
    }

    private void RemoveTimerAfterShow()
    {
        
        foreach(var key in this._timerAfterShow)
        {
            TimerManager.instance.UnRegisterTimer(key);
        }
        this._timerAfterShow.Clear();
    }

    public T GetController<T>(string name) where T : IBaseController
    {
        return (T)ControllerManager.instance.GetController(name);
    }

    public T GetModel<T>(string name) where T : IBaseModel
    {
        return (T)ModelManager.instance.GetModel(name);
    }

    public T GetUI<T>(string name) where T : IBaseUI
    {
        return (T)UIManager.instance.GetUI(name);
    }

    public IBaseUI GetUI(string name)
    {
        return UIManager.instance.GetUI(name);
    }

    public virtual void OnBackBtnClick()
    {
        this.Hide();
    }
}

internal class BaseUIEventVO
{
    public FairyGUI.EventListener eventListener;
    public FairyGUI.EventCallback0 callback0;
    public FairyGUI.EventCallback1 callback1;
    public FairyGUI.EventCallback1 listenerCallback;
    public int eventTriggerTime = 0;
    public int eventTriggerInterval = -1;
}

public class BaseUIPositionVO
{
    public float x;
    public float y;
    public float offsetX;
    public float offsetY;
}

public enum EUIDeleteRule
{
    Never = -1,
    RightNow = 0,
    Auto = 1,
}

public enum CutInEffect
{
    None,
    TopToBottom,
    BottomToTop,
    LeftToRight,
    RightToLeft,
    Popup,
}

public class TransitionUI : Attribute
{
    public CutInEffect EnumValue { get; private set; }
 
    public TransitionUI(CutInEffect enumValue)
    {
        EnumValue = enumValue;
    }
}

public class ContainerUI
{
    public GComponent Container;
}