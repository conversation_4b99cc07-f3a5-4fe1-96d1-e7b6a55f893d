using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using FairyGUI;
using ScriptsHot.Game.Modules.Common;
using ScriptsHot.Game.Modules.FragmentPractice;
using ScriptsHot.Game.Modules.ReviewQuestion.Questions.Parser;
using UnityEngine;

namespace UIBind.FragmentPractice
{
    public partial class TitlePage : ExtendedComponent
    {
        private BookModel bookData;
        private Action onFinish;

        GRichTextFieldPlus TextPro => title as GRichTextFieldPlus;

        override protected void OnAddedToStage()
        {            
            // TextPro.SetFormat(FontCfg.DinNextBold, new Color(11f / 255f, 11f / 255f, 11f / 255f, 1), 48);
            // TextPro.Align = AlignType.Center;
        }

        public void ShowBook(BookModel bookData, Action onFinish = null)
        {
            this.onFinish = onFinish;
            this.bookData = bookData;
            
            DotPracticeManager.Instance.Collect(new DataDot_Title());

            FillTitleText();
        }

        private async void OnTTSComplete()
        {
            await UniTask.Delay(TimeSpan.FromSeconds(1f));  // 等1秒

            // Create a tween to fly the page out to the left
            Vector2 originalPosition = this.position;
            Vector2 offScreenPosition = new Vector2(-this.width, originalPosition.y);

            // Set the duration of the animation
            float duration = 0.5f;

            // Create the tween
            this.TweenMove(offScreenPosition, duration)
                .SetDelay(2f) // Delay before starting the animation
                .SetEase(EaseType.QuadOut)
                .OnComplete(() => 
                {
                    // Hide the page after it flies out
                    this.visible = false;
                    // Reset position for future use
                    this.position = originalPosition;
                    onFinish?.Invoke();
                });

        }


        private void FillTitleText()
        {
            TextPro.text = bookData.GetTitle();

            // TextPro.AutoSize = AutoSizeType.Height;

            var audioId = bookData.GetTitleAudioId();
            btnAudio.visible = audioId > 0;
            (btnAudio as ABtnAudio).AudioId = audioId;
            (btnAudio as ABtnAudio).Play(OnTTSComplete);
            btnAudio.onClick.Add(() =>
            {
                DotPracticeManager.Instance.Collect(new DataDot_PlayTitle());
            });

            // 渐变淡入+位移动画
            tileFront.alpha = 1f;
            TextPro.y -= 50f; // 初始向上偏移30像素

            DOTween.To(() => tileFront.alpha, x => tileFront.alpha = x, 0f, 0.8f);
            TextPro.TweenMoveY(TextPro.y + 50f, 0.5f).SetEase(EaseType.BackOut); 
        }
    }
}