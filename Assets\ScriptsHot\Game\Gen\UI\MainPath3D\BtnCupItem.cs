/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.MainPath3D
{
    public partial class BtnCupItem : UIBindT
    {
        public override string pkgName => "MainPath3D";
        public override string comName => "BtnCupItem";

        public CupItem item;
        public Transition idle0;
        public Transition idle1;
        public Transition idle2;
        public Transition idle3;
        public Transition idle4;
        public Transition idle5;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            item = new CupItem();
            item.Construct(com.GetChildAt(0).asCom);
            idle0 = com.GetTransitionAt(0);
            idle1 = com.GetTransitionAt(1);
            idle2 = com.GetTransitionAt(2);
            idle3 = com.GetTransitionAt(3);
            idle4 = com.GetTransitionAt(4);
            idle5 = com.GetTransitionAt(5);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            item.Dispose();
            item = null;
            idle0 = null;
            idle1 = null;
            idle2 = null;
            idle3 = null;
            idle4 = null;
            idle5 = null;
        }
    }
}