/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.VideoLessonTask
{
    public partial class CompVideoMain : GComponent
    {
        public static string pkgName => "VideoLessonTask";
        public static string comName => "CompVideoMain";
        public static string url => "ui://shj5g74gsq9rl";

        public VideoMask videoComp;
        public CompSubTitlePanel compSubTitle;
        public GComponent compTask;
        public CompVideoCtrl compVideoCtrl;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(CompVideoMain));
        }

        public override void ConstructFromXML(XML xml)
        {
            videoComp = new VideoMask();
            videoComp.Construct(GetChildAt(0).asCom);
            compSubTitle = GetChildAt(1) as CompSubTitlePanel;
            compTask = GetChildAt(2) as GComponent;
            compVideoCtrl = GetChildAt(3) as CompVideoCtrl;
        }
        public override void Dispose()
        {
            videoComp.Dispose();
            videoComp = null;
            compSubTitle = null;
            compTask = null;
            compVideoCtrl = null;

            base.Dispose();
        }
    }
}