/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.ExploreFriends
{
    public partial class FriendCommonBar : UIBindT
    {
        public override string pkgName => "ExploreFriends";
        public override string comName => "FriendCommonBar";

        public Controller ctrl;
        public Controller ctrlIcon;
        public GImage barBack;
        public GImage barBase;
        public GImage bar1;
        public GImage bar2;
        public GImage bar3;
        public GImage bar4;
        public GImage bar5;
        public GTextField txtTitle;
        public GGraph holder;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            ctrl = com.GetControllerAt(0);
            ctrlIcon = com.GetControllerAt(1);
            barBack = (GImage)com.GetChildAt(0);
            barBase = (GImage)com.GetChildAt(1);
            bar1 = (GImage)com.GetChildAt(2);
            bar2 = (GImage)com.GetChildAt(3);
            bar3 = (GImage)com.GetChildAt(4);
            bar4 = (GImage)com.GetChildAt(5);
            bar5 = (GImage)com.GetChildAt(6);
            txtTitle = (GTextField)com.GetChildAt(7);
            holder = (GGraph)com.GetChildAt(13);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            ctrl = null;
            ctrlIcon = null;
            barBack = null;
            barBase = null;
            bar1 = null;
            bar2 = null;
            bar3 = null;
            bar4 = null;
            bar5 = null;
            txtTitle = null;
            holder = null;
        }
    }
}