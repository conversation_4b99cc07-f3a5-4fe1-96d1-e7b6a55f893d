﻿
using System;
using System.Threading.Tasks;
using CommonUI;
using Google.Protobuf;
using Msg.explore;
using UnityEngine;

public partial class ExploreFriendsController
{
    public enum DrawType
    {
        freeDraw = 0,
        consumeDiamondDraw,
        switchDraw
    }
    //更新好友数据
    private void SendInitRequest()
    {
        SendUpdateFriendDataReq();
        SendUpdateClosenessLevelListReq();
    }

    public void SendUpdateFriendDataReq()
    {
        //friend基础数据
        CS_GetFriendSlotListReq req = new CS_GetFriendSlotListReq();
        MsgManager.instance.SendMsg(req, (type, message) => 
            GetUI<CommonToastUI>(UIConsts.CommonToast).ShowToastI18N("webview_notice_timeout" , callBack:SendInitRequest));
    }

    /// <summary>
    /// todo @tanglei自己整
    /// </summary>
    public void SendUpdateClosenessLevelListReq()
    {
        CS_GetClosenessLevelListReq req = new CS_GetClosenessLevelListReq();
        MsgManager.instance.SendMsg(req);
    }

    public void SendDrawNewFriendReq(DrawType drawType,int slotIndex = 1)
    {
        CS_DrawNewFriendReq req = new CS_DrawNewFriendReq();
        req.drawSource = drawType.ToString();
        req.slotIndex = slotIndex;
        req.idempotentSign = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        Debug.Log($"req.drawSource = {req.drawSource}");
        MsgManager.instance.SendMsg(req);
    }

    public void SendBecomeFriendReq(long avatarId,long recordId,int slotIndex = 1)
    {
        CS_BecomeFriendReq req = new CS_BecomeFriendReq();
        req.avatarId = avatarId;
        req.drawRecordId = recordId;
        req.slotIndex = slotIndex;
        MsgManager.instance.SendMsg(req);
    }
    
    
    private void StartASRReqTask()
    {
        Task.Run(async () =>
        {
            while (true)
            {
                await Task.Delay(ASRGapTime);

                if (_exit)
                {
                    VFDebug.Log("Friend 退出对话");
                    break;
                }

                if (_enter && IsMicrophoneing && IfCanMicrophoneSend) 
                {
                    int nowDataLength = this._micPhoneData.Length;
                    byte[] data = new byte[nowDataLength];
                    Array.Copy(this._micPhoneData, 0, data, 0, nowDataLength);
                    this._micPhoneData = new byte[0];
                    
                    if(data.Length == 0) continue;

                    ByteString strByte = ByteString.CopyFrom(data);
                    // VFDebug.LogError("Friend 音频发送长度::" + data.Length + "    _enterChatAvatarId::" + _enterChatAvatarId);

                    CS_ExploreUpMsg reqMsg = new CS_ExploreUpMsg
                    {
                        friendChatMsg = new PB_FriendChatUpMsg()
                        {
                            avatarId = _enterChatAvatarId,
                            userInputAudio = new PB_Explore_FriendChatAudioUpFrame()
                            {
                                audio = strByte,
                                sample_rate = (uint)GMicrophoneManager.instance.SampleRate,
                                num_channels = 1,
                            }, 
                        },
                    };

                    try
                    {
                        if (GetExploreController().GetClientCell() != null)
                        {
                            await GetExploreController().GetClientCell().RequestStream.WriteAsync(reqMsg);
                        }
                    }
                    catch (Exception ex)
                    {
                        VFDebug.LogError("friends Task 异常终止: " + ex);
                    }
                }
            }
           
        });
    }
    
    /// <summary>
    /// friend对话功能上行消息
    /// </summary>
    /// <param name="type"></param>
    public async void UpEvent(PB_Explore_FriendChatUpBizEvent type)
    {
        // VFDebug.LogError("friend :/friend对话功能上行消息:type::" + type);
        CS_ExploreUpMsg reqMsg = new CS_ExploreUpMsg
        {
            friendChatMsg = new PB_FriendChatUpMsg()
            {
                avatarId = _enterChatAvatarId,
                upBizEvent = type
            },
        };
        try
        {
            await GetExploreController().GetClientCell().RequestStream.WriteAsync(reqMsg);
        }
        catch (Exception e)
        {
            VFDebug.LogError("GRPC 链接断开：失败的发送：" +"friend对话功能上行消息:type::" + type);
        }

    }
    
    /// <summary>
    /// friend对话下行 - 业务事件
    /// </summary>
    /// <param name="msg"></param>
    private void OnDownMsgForBizEvent(SC_FriendChatDownMsgForBizEvent msg)
    {
        //收到消息 就当网络恢复了
         VFDebug.LogError($"friend msg :---friend收到对话下行业务事件:{msg.bizEvent} + msg.code：{msg.code}");
        
        //test 延迟
        // TimerManager.instance.RegisterTimer((a) =>
        // {
        switch (msg.bizEvent)
        {
            case PB_Explore_FriendChatUpBizEvent.EO_FC_USER_MANUAL_START://用户手动开始
                VFDebug.LogError($"Explore Friends收到开始事件:{msg.bizEvent} + msg.code：{msg.code}");
                
                // 如果已经超时取消了录音，则忽略这个消息
                if (_recordTimeoutTimerId == string.Empty && !_receivedStartResponse)
                {
                    VFDebug.Log("Explore录音已超时取消，忽略后续消息");
                    return;
                }
                
                // 标记已收到响应
                _receivedStartResponse = true;
                // 清除超时定时器
                ClearRecordTimeoutTimer();
                
                if (msg.code == PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS)
                {
                    IfCanMicrophoneSend = true;
                    
                    // 如果等待提交，则发送提交协议
                    if (_pendingSubmit)
                    {
                        TimerManager.instance.RegisterTimer((a) =>
                        {
                            _pendingSubmit = false;
                            StopAsr();
                            UpEvent(PB_Explore_FriendChatUpBizEvent.EO_FC_USER_MANUAL_SUBMIT);
                        }, 300); //等待音频写入
                    }
                }
                else
                {
                    //开始录音失败
                    VFDebug.LogError("friend  开始录音失败");
                    _pendingSubmit = false; // 重置等待提交标志
                }
        
                break;
     
        }
        // }, 7000);

    }
}
