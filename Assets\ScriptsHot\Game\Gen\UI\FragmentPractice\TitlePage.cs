/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class TitlePage : ExtendedComponent
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "TitlePage";
        public static string url => "ui://cmoz5osjnnrh3u";

        public GGraph imgBG;
        public GGraph screenBG;
        public GGraph tileBg;
        public GButton btnAudio;
        public GRichTextField title;
        public GGraph tileFront;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(TitlePage));
        }

        public override void ConstructFromXML(XML xml)
        {
            imgBG = GetChildAt(0) as GGraph;
            screenBG = GetChildAt(1) as GGraph;
            tileBg = GetChildAt(2) as GGraph;
            btnAudio = GetChildAt(5) as GButton;
            title = GetChildAt(6) as GRichTextField;
            tileFront = GetChildAt(8) as GGraph;

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();

            imgBG = null;
            screenBG = null;
            tileBg = null;
            btnAudio = null;
            title = null;
            tileFront = null;

            base.Dispose();
        }
    }
}