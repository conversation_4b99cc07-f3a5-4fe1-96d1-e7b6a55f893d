﻿using Msg.core;
using ScriptsHot.Game.Modules.Consts;
using ScriptsHot.Game.Modules.Scene.Level.Component;
using UnityEngine;
using NotImplementedException = System.NotImplementedException;

namespace ScriptsHot.Game.Modules.Procedure.Drama.StoryChat
{
    public class DramaLookAvatarTalkToAvatar:BaseDrama
    {
        public override void OnEvent()
        {
            base.OnEvent();  
            MsgManager.instance.RegisterCallBack<SC_CommandQuitChatDramaNtf>(CommandQuitChatDramaNtf);
            Notifier.instance.RegisterNotification(NotifyConsts.StoryAvatarTalkToAvatarOut,OnStoryAvatarTalkToAvatarOut);
        }
        
        public override void Do(bool canFinish = true)
        {
            base.Do();
            SetAvatarVisible(false);
            
            UIManager.instance.HideLayerBeyond(UILayerConsts.Home, UILayerConsts.Top, UILayerConsts.Loading, UILayerConsts.Float,UILayerConsts.Guide);
            // Notifier.instance.SendNotification(NotifyConsts.CloseUI,UIConsts.MainHeader);
            Notifier.instance.SendNotification(NotifyConsts.OpenUI,new UIManager.UIParams(){viewName = UIConsts.ChatStoryOutUI});

            CameraModeParam param = new CameraModeParam();
            param.mode = CameraScript.ViewMode.AvatarChat;
            
            Notifier.instance.SendNotification(NotifyConsts.ChangeCameraMode,param);
        }

        public override void OnFinish(bool isBreak)
        {
            base.OnFinish(isBreak);
            
            MsgManager.instance.UnRegisterCallBack<SC_CommandQuitChatDramaNtf>(CommandQuitChatDramaNtf);
            Notifier.instance.UnRegisterNotification(NotifyConsts.StoryAvatarTalkToAvatarOut,OnStoryAvatarTalkToAvatarOut);
            
            SetAvatarVisible(true);
            
            CameraModeParam param = new CameraModeParam();
            param.mode = CameraScript.ViewMode.FreeLook;
            UIManager.instance.ShowAllLayer();
            Notifier.instance.SendNotification(NotifyConsts.ChangeCameraMode,param);
            Notifier.instance.SendNotification(NotifyConsts.CloseUI,UIConsts.ChatStoryOutUI);
            // Notifier.instance.SendNotification(NotifyConsts.OpenUI,new UIManager.UIParams(){viewName = UIConsts.MainHeader});
    
            GHeadBarManager.instance.HideStoryTitlePage();
        }

        private void CommandQuitChatDramaNtf(SC_CommandQuitChatDramaNtf msg)
        {
            this.Finish();
        }
        
        private void OnStoryAvatarTalkToAvatarOut(string name, object body)
        {
            this.Finish();
        }

        private void SetAvatarVisible(bool boo)
        {
            return;
            UserRunParam avatarParam = this.Params as UserRunParam;
            
            SceneController sceneController = ControllerManager.instance.GetController(ModelConsts.Scene) as SceneController;
            Avatar mainAvatar = sceneController.scene.GetComponent<AvatarComponent>().GetAvatar(sceneController.scene.GetComponent<UnitComponent>().mainUnitID);

            if (boo)
            {
                GameUtils.SetCameraCullingMask(LayerMask.NameToLayer("player"), true);
            
                for (int i = 0; i < avatarParam.avatarIds.Count; i++)
                {
                    Avatar avatar =  sceneController.scene.GetComponent<AvatarComponent>().GetAvatar(avatarParam.avatarIds[i]);
                    if (avatar != null)
                    {
                        ObjectUtils.SetLayer(avatar.gameObject, LayerMask.NameToLayer("player"));
                    }
                }
                ObjectUtils.SetLayer(mainAvatar.gameObject, LayerMask.NameToLayer("player"));
            }
            else
            {
                GameUtils.SetCameraCullingMask(LayerMask.NameToLayer("player"), false);
            
                for (int i = 0; i < avatarParam.avatarIds.Count; i++)
                {
                    Avatar avatar =  sceneController.scene.GetComponent<AvatarComponent>().GetAvatar(avatarParam.avatarIds[i]);
                    if (avatar != null)
                    {
                        ObjectUtils.SetLayer(avatar.gameObject, LayerMask.NameToLayer("chat"));
                    }
                }
                ObjectUtils.SetLayer(mainAvatar.gameObject, LayerMask.NameToLayer("player"));
            }
        }
    }
}