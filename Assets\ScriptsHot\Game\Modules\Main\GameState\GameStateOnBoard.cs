﻿using UnityEngine;
using System.Collections.Generic;
using DG.Tweening;
using FairyGUI;
using ScriptsHot.Game.Modules.Scene.Level;
using ScriptsHot.Game.Modules.Scene.Level.Component;
using Unity.Mathematics;


/**
 * 选角状态
 */
public class GameStateOnBoard : GameStateBase
{
    #region 参数

    private SceneController sceneController;
    private Level gameScene;
    private Camera camera;
    private Tweener tweener;

    private List<OnBoardRolesCfg> cfgList = new();
    private OnBoardRolesCfg currentRolesCfg;

    private readonly List<GameObject> selectRoleList = new();
    private GameObject role;
    private int currentRoleIndex;
    private bool selectedRole = false;

    private List<Transform> selectRoleTrList;
    private List<Transform> selectRolCamTrList;
    private List<Transform> selectNearCamTrList;
    private List<Transform> startCamTrList;
    private Transform bottomParticle;
    private Transform topParticle;

    private int pageId = 0;
    private int clickRoleId = -1;
    private string timeLimit;
    
    private GameObject flow;
    private AudioSource _flowAudioSource;
    private Vector3 originalPos;
    #endregion

    #region 系统

    public GameStateOnBoard() : base(GameState.OnBoard)
    {
    }

    public override void OnEnter(params object[] args)
    {
        Debug.Log("进入 OnBoarding 界面");
        base.OnEnter(args);
        // UIManager.instance.GetUI(UIConsts.MainHeader)?.Hide();
        UIManager.instance.GetUI(UIConsts.RecommendCardUI)?.Hide();

        sceneController = owner.GetController<SceneController>(ModelConsts.Scene);
        gameScene = sceneController.scene;
        camera = Camera.main;

        // ClearAllInstGam();
        // OnEnterInit();

        var styleId = owner.GetModel<LoginOnBoardingModel>(ModelConsts.Login).Style;
        UIManager.instance.GetUI(UIConsts.SceneLoading).Hide();
        owner.GetUI(UIConsts.OnBoardUI).Show();

        // TODO
        InitMessage();
    }

    public override void OnExit()
    {
        foreach (var game in selectRoleList)
        {
            Object.Destroy(game);
        }

        selectRoleList.Clear();
    }

    #endregion

    #region 方法

    private void ClearAllInstGam()
    {
        if (gameScene == null)
        {
            Debug.LogWarning("OnBoarding ClearAllInstGam  gameScene == null");
            return;
        }

        if (gameScene.GetComponent<AvatarComponent>().avatarLoader == null)
        {
            gameScene.GetComponent<AvatarComponent>().EnterScene();
        }

        // Clear
        foreach (var roleGam in selectRoleList)
        {
            gameScene.GetComponent<AvatarComponent>().avatarLoader.UnLoadNAvatar(roleGam);
        }

        selectRoleList.Clear();

        if (role != null)
        {
            gameScene.GetComponent<AvatarComponent>().avatarLoader.UnLoadNAvatar(role);
        }
    }

    private void OnEnterInit()
    {
        if (gameScene.GetComponent<AvatarComponent>().avatarLoader == null)
        {
            gameScene.GetComponent<AvatarComponent>().EnterScene();
        }

        currentRoleIndex = 0;
        InitStateData();
        this.LoadSelectRole();
    }

    // 固定配置参数
    private void InitStateData()
    {
        var selectRolePos = GameObject.Find("SelectRolePos").transform;
        var selectRoleCamPos = GameObject.Find("SelectRoleCamPos").transform;
        var selectNearCamera = GameObject.Find("SelectNearCamera").transform;
        var startCamera = GameObject.Find("StartCamera").transform;
        var particle = GameObject.Find("Particle").transform;

        selectRoleTrList = GetChildTransform(selectRolePos);
        selectRolCamTrList = GetChildTransform(selectRoleCamPos);
        selectNearCamTrList = GetChildTransform(selectNearCamera);
        startCamTrList = GetChildTransform(startCamera);

        bottomParticle = particle.GetChild(0);
        topParticle = particle.GetChild(1);
    }
    
    //onBoarding 中从配置表内 获取style
    private async void LoadSelectRole()
    {
        //OnBoardRoles获取前 x个的role sytle id
        // styleid 通过avatarStyle 转为 styleName
        //造 avatar 到selectRoleTrList的节点下，

        if (selectRoleTrList.Count <=0) {

            Debug.LogError("GameStateOnBoard selectRoleTrList invalid");
            return;
        }
        var roleNodeParent = selectRoleTrList[0].parent;
        this.originalPos = roleNodeParent.position;//缓存下
        selectRoleTrList[0].parent.position = this.originalPos + new Vector3(10000, 0, 0);//移动到视野外

        cfgList = Cfg.T.TBOnBoardRoles.DataList;//已经裁剪为6条数据
        
        var count = math.min(cfgList.Count, selectRoleTrList.Count);//数据裁剪？到前6条
        if (cfgList.Count < selectRoleTrList.Count) {
            Debug.LogError("FatalErr in OnBoarding: selectRole Number > related role data Number");
        }
        for (var i = 0; i < count; i++)
        {
            string avatarStyleName = cfgList[i].roleStyleName;
      

            Debug.Log("LoadSelectRole   " + avatarStyleName);
            var modelGo = await gameScene.GetComponent<AvatarComponent>().avatarLoader.LoadNAvatar(avatarStyleName);
            if (modelGo != null)
            {
                modelGo.transform.parent = selectRoleTrList[i].transform;
                modelGo.transform.localPosition = Vector3.zero;
                modelGo.SetActive(true);
                selectRoleList.Add(modelGo);
                ObjectUtils.SetLayer(modelGo.gameObject, LayerMask.NameToLayer("chat"));

                PlayModelToIdle(modelGo);
            }
        }

        role = selectRoleList[currentRoleIndex];

       // ShowRoleById(-1);
    }

    //需要与LoadSelectRole 配对使用，它只负责转移部分节点到原始位置
    private void LoadSelectRoleAtOriginalPos()
    {
        if (selectRoleTrList.Count <= 0)
        {

            Debug.LogError("GameStateOnBoard selectRoleTrList invalid");
            return;
        }
        selectRoleTrList[0].parent.position = this.originalPos ;//移动到视野外
    }

    private List<Transform> GetChildTransform(Transform transform)
    {
        var childList = new List<Transform>();
        var childCount = transform.childCount;
        for (var i = 0; i < childCount; i++)
        {
            childList.Add(transform.GetChild(i));
        }

        return childList;
    }

    private void PlayTrMove(Transform target, Transform one, Transform two, float time)
    {
        target.position = one.position;
        target.rotation = one.rotation;

        tweener.Kill();
        tweener = target.transform.DOMove(two.position, time);
        tweener.onUpdate = () => { };
        tweener.Play();
    }
    
    private void ResetCamPos()
    {
        camera.transform.position = startCamTrList[1].position;
    }

    private void PlayModelToIdle(GameObject model)
    {
        if (model == null)
        {
            return;
        }

        var animator = model.GetComponentInChildren<Animator>();
        if (animator == null)
        {
            return;
        }

        animator.cullingMode = AnimatorCullingMode.AlwaysAnimate;
        animator.Play("Idle");
    }

    #endregion

    #region 相机 接口

    public void PlayCameraStart2FocusRoles()
    {
        this.LoadSelectRoleAtOriginalPos();
        ResetCamPos();
        var startCameraTime = 1.5f;
        PlayTrMove(camera.transform, startCamTrList[0], startCamTrList[1], startCameraTime);
        ShowAllRole();
    }

    public void PlayPlayerToTwo()
    {
        pageId = 1;
        ResetCamPos();
        var startCameraTime = 1f;
        var selectRolePos = GameObject.Find("SelectRolePos").transform;
        PlayTrMove(selectRolePos, selectRolCamTrList[0], selectRolCamTrList[1], startCameraTime);
    }

    public void PlayPlayerToOne()
    {
        pageId = 0;
        var startCameraTime = 1f;
        var selectRolePos = GameObject.Find("SelectRolePos").transform;
        PlayTrMove(selectRolePos, selectRolCamTrList[1], selectRolCamTrList[0], startCameraTime);
    }
    
    
    public void PlaySimpleClick()
    {
        var beGet = BeClickRole(out var hit, out var hitId);
        if (beGet)
        {
            ClickRoleById(hitId);
        }
    }
    public void CameraToNearSelectRole()
    {
        if (clickRoleId < 0)
        {
            return;
        }

        var clickRolePageId = clickRoleId < 3 ? 0 : 1;
        var beInSamePage = clickRolePageId == pageId;
        Debug.Log(clickRoleId+ "  " +clickRolePageId+ "  " + pageId);
        if (beInSamePage)
        {
            CameraToNearSelect();
        }
        else
        {
            MovePageAndToNearSelect();
        }

    }

    private void CameraToNearSelect()
    {
        ShowRoleById(clickRoleId);
        var nearPosId = clickRoleId % 3;
        var moveTime = 0.8f;
        selectedRole = true;
        bottomParticle.gameObject.SetActive(false);
        topParticle.gameObject.SetActive(false);
        PlayTrMove(camera.transform, camera.transform, selectNearCamTrList[nearPosId], moveTime);  
    }

    private void MovePageAndToNearSelect()
    {
        if (selectRoleList.Count == 0)
        {
            return;
        }
        
        var clickRolePageId = clickRoleId < 3 ? 0 : 1;
        if (clickRolePageId == 0)
        {
            PlayPlayerToOne();
        }
        else
        {
            PlayPlayerToTwo();
        }

        var delayedTimer = 1.0f;
        float timer = 0;
 
        Tween t = DOTween.To(() => timer, x => timer = x, 1, delayedTimer)
            .OnComplete(CameraToNearSelect);
    }
    public void ShowAllRole()
    {
        foreach (var t in selectRoleTrList)
        {
            t.gameObject.SetActive(true);
        }
    }
    
    public bool BeSelectRole()
    {
        return clickRoleId >= 0;
    }
    
    public void PlayRoleAniByStateName(string stateName)
    {
        if (clickRoleId < 0)
        {
            return;
        }
        clickRoleId = math.clamp(clickRoleId, 0, selectRoleList.Count);
        var animator = selectRoleTrList[clickRoleId].transform.GetComponentInChildren<Animator>();
        animator.applyRootMotion = true;
        animator.CrossFade(stateName, 0);
    }

    public Transform GetSelectedRole()
    {
        return selectRoleTrList[clickRoleId];
    }

    
    #endregion

    #region 接口

    public OnBoardRolesCfg GetCurrentRoleCfg()
    {
        return cfgList[clickRoleId];
    }

    public OnBoardRolesCfg ShowRolePre()
    {
        return cfgList[currentRoleIndex];
    }

    public OnBoardRolesCfg ShowRoleNext()
    {
        return cfgList[currentRoleIndex];
    }

    public void ChangeStateToSelect()
    {
        OnEnter();
    }

    #endregion
    
    
    #region 角色单击
    
    private bool BeActiveClickUi()
    {
        return Stage.isTouchOnUI && !string.IsNullOrEmpty(Stage.inst.touchTarget.name);
    }

    private bool BeClickRole(out RaycastHit hit, out int hitId)
    {
        hitId = 0;
        var ray = camera.ScreenPointToRay(Input.mousePosition);

        if (Physics.Raycast(ray, out hit))
        {
            var beGet = int.TryParse(hit.transform.name, out hitId);
            Debug.Log("BeClickRole " + hit.transform.name);
            return beGet;
        }

        return false;
    }

    private void ClickRoleById(int id)
    {
        if (selectedRole)
            return;
        id = math.clamp(id, 0, selectRoleList.Count);
        var beClickSame = (id == clickRoleId);
        clickRoleId = id;

        if (beClickSame)
        {
            return;
        }

        bottomParticle.gameObject.SetActive(true);
        topParticle.gameObject.SetActive(true);
        
        var roleTr = selectRoleTrList[id];
        var bottom = roleTr.GetChild(0);
        var top = roleTr.GetChild(1);
        bottomParticle.parent = bottom;
        bottomParticle.position = bottom.position;

        topParticle.parent = top;
        topParticle.position = top.position;
        sceneController.SendNotification(NotifyConsts.OnBoardClickedRole, clickRoleId);
    }

    #endregion

    #region 选定角色
 
    private void ShowRoleById(int id)
    {
        for (var i = 0; i < selectRoleTrList.Count; i++)
        {
            selectRoleTrList[i].gameObject.SetActive(i == id);
        }
    }

    #endregion

    #region Flow

    private void InitFlow()
    {
        
    }

    private void InitFlowAudio()
    {
        var head = flow.transform.GetChild(0).Find("Body");
        _flowAudioSource = head.gameObject.AddComponent<AudioSource>();
        GSoundManager.instance.SetCurrAvatarTTS(_flowAudioSource);

#if !UNITY_EDITOR_OSX
                var lsc = _flowAudioSource.gameObject.AddComponent<OVRLipSyncContext>();
                lsc.audioLoopback = true;
                lsc.audioSource = _flowAudioSource;
                var lsm = _flowAudioSource.gameObject.AddComponent<OVRLipSyncContextMorphTarget>();
                lsm.skinnedMeshRenderer = _flowAudioSource.gameObject.GetComponent<SkinnedMeshRenderer>();
#endif
    }
    public void PlayNpcSound(long tts_record_id)
    {
        TTSManager.instance.PlayTTS(tts_record_id, default, true, TTSManager.AudioChannel.Flow, default, _flowAudioSource);
    }

    public AudioSource GetCurrentAudio()
    {
        return _flowAudioSource;
    }


    #endregion

    #region Test

    private void InitMessage()
    {
        // Messenger.AddListener((int)MessageDataEnum.ShowAllRole, ShowAllRole);
        // Messenger.AddListener((int)MessageDataEnum.StartCamera, PlayCameraStart);
        //
        // Messenger.AddListener((int)MessageDataEnum.ToOne, PlayPlayerToOne);
        // Messenger.AddListener((int)MessageDataEnum.ToTwo, PlayPlayerToTwo);
        //
        // Messenger.AddListener((int)MessageDataEnum.ToNear, FocusOnSelectRole);
    }

    #endregion
}