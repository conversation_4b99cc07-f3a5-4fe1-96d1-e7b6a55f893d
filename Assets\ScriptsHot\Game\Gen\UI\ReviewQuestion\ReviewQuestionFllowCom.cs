/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.ReviewQuestion
{
    public partial class ReviewQuestionFllowCom : AQuestionCard
    {
        public static string pkgName => "ReviewQuestion";
        public static string comName => "ReviewQuestionFllowCom";
        public static string url => "ui://xlh8p6j0crla15";

        public GTextField tfTitle;
        public GComponent comText;
        public GTextField tfTrans;
        public GButton btnPlayerVoic;
        public GButton btnPlayVoic;
        public GGroup grpVoice;
        public GComponent btnRecord;
        public GImage imgCurrent;
        public Transition tweenBreath;
        public Transition OnShowTT;
        public Transition OnHideTT;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(ReviewQuestionFllowCom));
        }

        public override void ConstructFromXML(XML xml)
        {
            tfTitle = GetChildAt(1) as GTextField;
            comText = GetChildAt(4) as GComponent;
            tfTrans = GetChildAt(5) as GTextField;
            btnPlayerVoic = GetChildAt(6) as GButton;
            btnPlayVoic = GetChildAt(7) as GButton;
            grpVoice = GetChildAt(8) as GGroup;
            btnRecord = GetChildAt(9) as GComponent;
            imgCurrent = GetChildAt(10) as GImage;
            tweenBreath = GetTransitionAt(0);
            OnShowTT = GetTransitionAt(1);
            OnHideTT = GetTransitionAt(2);
        }
        public override void Dispose()
        {
            tfTitle = null;
            comText = null;
            tfTrans = null;
            btnPlayerVoic = null;
            btnPlayVoic = null;
            grpVoice = null;
            btnRecord = null;
            imgCurrent = null;
            tweenBreath = null;
            OnShowTT = null;
            OnHideTT = null;

            base.Dispose();
        }
    }
}