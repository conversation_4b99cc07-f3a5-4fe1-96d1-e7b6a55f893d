using System;
using System.Diagnostics;
using ScriptsHot.Game.UGUI.WebView;
using ScriptsN.UI.WebView;
using UnityEngine;
using Object = UnityEngine.Object;

namespace ScriptsHot.Game.Modules.IncentiveTask
{
    public class IncentiveTaskController : BaseController
    {
        public IncentiveTaskController() : base(ModelConsts.IncentiveTask) { }

        private IncentiveTaskModel _incentiveTaskModel => GetModel<IncentiveTaskModel>(ModelConsts.IncentiveTask);

        private string _key;
        
        private MainModel _mainModel => GetModel<MainModel>(ModelConsts.Main);

        public override void OnUIInit()
        {
            // RegisterUI(new RankUI(UIConsts.RankUI));
            // RegisterUI(new RankUnlockUI(UIConsts.RankUnlockUI));
            // RegisterUI(new RankResultUI(UIConsts.RankResultUI));
            // RegisterUI(new RankRewardUI(UIConsts.RankRewardUI));
        }

        public override void OnInit()
        {
            RegisterModel(new IncentiveTaskModel());
        }

        public override void OnUpdate(int interval)
        {
            
        }



   
        public void EnterIncentiveTask()
        {
            Stopwatch stopwatch = new Stopwatch();
            
            stopwatch.Start();
            // GameObject ctlPrefab = Resources.Load<GameObject>("Prefabs/WebViewCtl");
            GameObject ctlPrefab = GResManager.instance.LoadPrefab("WebViewCtl");           
            GameObject newCtl = Object.Instantiate(ctlPrefab);
            
            WebViewCtl ctl = newCtl.GetComponent<WebViewCtl>();
            if (ctl == null)
            {
                ctl = newCtl.AddComponent<WebViewCtl>();
            }
            ctl.Init(5f, I18N.inst.MotherLanguageStr, I18N.inst.ForeignLanguageStr, _mainModel.toKen, I18N.inst.TempUILanguageStr,
                false,
                false,
                () =>
                {
                    VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Light);
                    
                    GetController<CurrencyController>(ModelConsts.CurrencyController).SendGetEconomicInfoReqAsync(GameEventName.GameEnter, () =>
                    {
                        SendNotification(NotifyConsts.MainHeadRefreshEvent);
                    });
                },() =>
                {
                    GetUI<CommBusyUI>(UIConsts.CommBusy).LazyShow();
                },
                () =>
                {
                    GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
                }
                );

            ctl.LoadUrl(GameEntry.WebviewC.GetUrl("task"));
            stopwatch.Stop();
            VFDebug.Log("======>WebView初始化时长（现在可以显示了）: " + stopwatch.ElapsedMilliseconds + " ms");
        }
        
        public static long GetCurrentUnixTimestampMilliseconds()
        {
            DateTimeOffset now = DateTimeOffset.Now;
            long unixTimestamp = now.ToUnixTimeMilliseconds();
            return unixTimestamp;
        }
    }
}
