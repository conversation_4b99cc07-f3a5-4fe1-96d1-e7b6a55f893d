﻿namespace ScriptsHot.Game.Modules.Procedure
{
    public enum EProcedureType
    {
        None = 0,                                                // 非法值
        ChatStoryLookAvatarTalkToAvatar = 1,                     // 
        ChatRolePlaySceneDesc,                                   // roleplay 场景描述
        ChatAvatar,                                              // 聊天中 avatar的内容
        ChatAudioPlay,                                           // 聊天音频播放
        ChatPlayer,                                              // 聊天中 玩家自己内容
        ChatScaffold,                                            // 脚手架
         
        ExploreEnterAudio,                                       // 探索进入entity 时候 第一次audio播放
        ExploreAudioPlay,
        ExploreShowRecordUI,                                     // 显示 RecordUI
        ExploreAvatarCallFirst,                                  // 第一次显示 avatarCell
        
        OnBoardingAudioPlay,
        OnBoardingChatEnd
    }
}