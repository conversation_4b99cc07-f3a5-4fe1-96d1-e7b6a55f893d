/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class BtnTF : GButton
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "BtnTF";
        public static string url => "ui://cmoz5osjrof1uvptcj";

        public Controller TFstate;
        public Controller btnState;
        public GLoader FBtn;
        public GLoader Tbtn;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(BtnTF));
        }

        public override void ConstructFromXML(XML xml)
        {
            TFstate = GetControllerAt(0);
            btnState = GetControllerAt(1);
            FBtn = GetChildAt(3) as GLoader;
            Tbtn = GetChildAt(4) as GLoader;
        }
        public override void Dispose()
        {
            TFstate = null;
            btnState = null;
            FBtn = null;
            Tbtn = null;

            base.Dispose();
        }
    }
}