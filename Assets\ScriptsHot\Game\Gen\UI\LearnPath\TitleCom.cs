/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.LearnPath
{
    public partial class TitleCom : ChapterItem
    {
        public static string pkgName => "LearnPath";
        public static string comName => "TitleCom";
        public static string url => "ui://94jvztftcby3xxx7q";

        public Controller color;
        public GTextField tfTitle;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(TitleCom));
        }

        public override void ConstructFromXML(XML xml)
        {
            color = GetControllerAt(0);
            tfTitle = GetChildAt(0) as GTextField;
        }
        public override void Dispose()
        {
            color = null;
            tfTitle = null;

            base.Dispose();
        }
    }
}