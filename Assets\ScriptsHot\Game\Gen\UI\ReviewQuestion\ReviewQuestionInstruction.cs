/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.ReviewQuestion
{
    public partial class ReviewQuestionInstruction : AQuestionCard
    {
        public static string pkgName => "ReviewQuestion";
        public static string comName => "ReviewQuestionInstruction";
        public static string url => "ui://xlh8p6j0l44c3b";

        public GTextField tfTitle;
        public GTextField mainTip;
        public GTextField subtitle;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(ReviewQuestionInstruction));
        }

        public override void ConstructFromXML(XML xml)
        {
            tfTitle = GetChildAt(1) as GTextField;
            mainTip = GetChildAt(4) as GTextField;
            subtitle = GetChildAt(5) as GTextField;

            SetMultiLanguageInChildren();
        }
        public override void Dispose()
        {
            tfTitle = null;
            mainTip = null;
            subtitle = null;

            base.Dispose();
        }
        public void SetMultiLanguageInChildren()
        {
            this.tfTitle.SetKey("question_instruction_tip");  // "Tips"
        }
    }
}