using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Cysharp.Threading.Tasks;

using Msg.explore;

public enum VoiceChatRunningStatus
{
    UnInit = 0,              // 未初始化
    Idle = 1,                // 空闲
    PreMatch = 2,            // 预匹配，常规文档里的Matching的第1阶段
    PreMatchCancelling = 3,            // 预匹配，常规文档里的Matching的第1阶段
    PreMatchConfirming = 4,  // 很短的ack部分，常规文档里的Matching的第2阶段
    ChattingSelfJoin = 5,    // 仅自己加入
    ChattingBothJoin = 6,     // 双方都加入
    Disposing = 7           // 声网引擎正在销毁过程中的不可用时段 
}


//负责 根据整体匹配撮合状态 来wrap调用 声网的RtcEngine相关接口
namespace ScriptsHot.Game.Modules.AgoraRtc
{
    public struct MatchInfo
    {
        public long matchRecordId ;
        public long normalUserId ;
        //public long normalUserId;
    }

    public class VoiceChatManager : MonoSingleton<VoiceChatManager>
    {
        private VoiceChatRunningStatus _status = VoiceChatRunningStatus.UnInit;

        public VoiceChatRunningStatus Status
        {
            get
            {
                return _status;
                
            }
            private set
            {
                _status = value;
                //Debug.LogError("assign status="+_status);
            }
        }
        public bool IsUserAsNativeSpeaker { get; set; } = false;
        public long ClientUserId { get; set; }

        public ChatPartnerParam PartnerParam
        {
            get { return this.currPartnerParam; }
        }

        //key msg.match_record_id, val:otherEndUserId
        private Dictionary<long, MatchInfo> matchInfoDic = new Dictionary<long, MatchInfo>();
        private ChatPartnerParam currPartnerParam = new ChatPartnerParam();
        
        private RtcEngineManager engine = RtcEngineManager.Instance;
        internal ExploreNetStrategy CurNetStrategy;
        internal VoiceChatDialogTextController textCtrl = new VoiceChatDialogTextController();
        internal VoiceChatDialogTextModel textModel = new VoiceChatDialogTextModel();

        private long matchRecordID = -1;//
        
        private string cancelActionTimerKey;
        private int defaultClientTimerDelay= 700;//ms

        private long selfUid;
        private long partnerUid;
    
        private bool isUsingNewNativeSpeakerLogic = true;

        private string bothJoinCountDownTimer = null;
        private int bothJoinCountDownMaxTime = 4000;//4 second
        
        #region MonoSingleton 基础行为
        
        protected override async UniTask OnInit()
        {
            await base.OnInit();

            textCtrl.Init(textModel);
            textCtrl.InitRegister();//双向流的下行监听

            //下行的核心消息
            if (!isUsingNewNativeSpeakerLogic)
            {
                Debug.LogError("已经不支持了");
            }
            else
            {
                if (this.IsUserAsNativeSpeaker)
                {
                    //  Native speaker侧 被动处理下发的逻辑 和 User侧不太一样
                    MsgManager.instance.RegisterCallBack<SC_NativeSpeakerDown_MatchStatus>(this.OnRecvMatchStatus);
                    MsgManager.instance.RegisterCallBack<SC_NativeSpeakerDown_MatchedResult>(this.OnRecvMatchResult);
                    MsgManager.instance.RegisterCallBack<SC_NativeSpeakerDown_MatchFailed>(this.OnRecvMatchFailedByTimeout);
                    
                    
                }
                else
                {
                    
                    MsgManager.instance.RegisterCallBack<SC_NativeSpeakerDown_MatchStatus>(this.OnRecvMatchStatus);
                    MsgManager.instance.RegisterCallBack<SC_NativeSpeakerDown_MatchedResult>(this.OnRecvMatchResult);
                    MsgManager.instance.RegisterCallBack<SC_NativeSpeakerDown_MatchFailed>(this.OnRecvMatchFailedByTimeout);
                }

                
            }

            Notifier.instance.RegisterNotification(NotifyConsts.InitMultiTabFramework, this.OnInitMultiTabFramework);
        }
    
        
        #region 下行消息处理 
        private void OnRecvMatchStatus(SC_NativeSpeakerDown_MatchStatus msg)
        {
            
            if (this.IsUserAsNativeSpeaker)
            {
                if (matchRecordID <= 0 && msg.match_record_id > 0)
                {
                    matchRecordID = msg.match_record_id; //todo：tanglei 这里不应该占用1个matchRecordID 需要多个
                }
          
                VFDebug.Log($"OnRecvMatchStatus2 msgStatus={msg.downMatchStatus.ToString()}  selfStatus ={this.Status.ToString()}");

                
                switch (msg.downMatchStatus)
                {
                    //不知道后台为什么 用waiting来干这个事，但暂时约定如此
                    case ENUM_NativeSpeakerMatchStatus.EO_NativeSpeakerMatchStatus_Waiting: //1
                        if (this.Status == VoiceChatRunningStatus.UnInit || this.Status == VoiceChatRunningStatus.Idle || this.Status == VoiceChatRunningStatus.PreMatch )
                        {
                            VFDebug.Log("收到 EO_MatchingStatus_Waiting push");
                 
                            if (!this.matchInfoDic.ContainsKey(msg.match_record_id))
                            {
                                this.matchInfoDic[msg.match_record_id] = new MatchInfo()
                                {
                                    matchRecordId = msg.match_record_id,
                                    normalUserId = msg.user_id,
                                }; //此时是对端id   
                                
                                AssignPartenerParam(msg); 
                                Notifier.instance.SendNotification(NotifyConsts.NSUser_RecvChatMsg,  currPartnerParam);//呼叫闲时  ChatInviteMsg

                                
                                //matchRecordID = msg.match_record_id; native speaker 在未accpet前不应该使用 match_record_id
                            }
                            else
                            {
                                Debug.Log("正常打印 收到重复的waitng");
                            }

                        }
                        else
                        {
                            VFDebug.LogError("在未定义的流程上遇到 EO_MatchingStatus_Waiting， status:" + this.Status.ToString());
                        }
                        break;
                    case ENUM_NativeSpeakerMatchStatus.EO_NativeSpeakerMatchStatus_MatchFailed:
                    case ENUM_NativeSpeakerMatchStatus.EO_NativeSpeakerMatchStatus_MatchCanceled://4
                        this.matchInfoDic.Remove(msg.match_record_id); //此时是对端id   
                        Notifier.instance.SendNotification(NotifyConsts.NSUser_CancelChatMsg,  msg.match_record_id);
                        
                        break;
                    
                    default:
                        VFDebug.Log("不应该触发吧 "+msg.downMatchStatus.ToString() + ",status=" + this.Status.ToString() + "isUserAsNS:"+this.IsUserAsNativeSpeaker);
                        break;
                }
            }
            else
            {
                //普通user的
                if (matchRecordID <= 0 && msg.match_record_id > 0)
                {
                    matchRecordID = msg.match_record_id; //todo 
                }
                else if(matchRecordID!= msg.match_record_id && msg.match_record_id > 0)
                {
                    VFDebug.LogError("OnRecvMatchStatus2 MatchStatus出现前后不一致，preMatchRecordID=" + matchRecordID+"，msgRecID="+ msg.match_record_id);
                    matchRecordID = msg.match_record_id; //降档防御
                }
                else if(matchRecordID!= msg.match_record_id )
                {
                    VFDebug.LogError("OnRecvMatchStatus2 未知问题 MatchRecID =" + matchRecordID+"，msgRecID="+ msg.match_record_id);
                    matchRecordID = msg.match_record_id; //降档防御
                }

                VFDebug.Log($"OnRecvMatchStatus2 msgStatus={msg.downMatchStatus.ToString()}  selfStatus ={this.Status.ToString()} matchRecordID={matchRecordID}");

                switch (msg.downMatchStatus)
                {
                    case ENUM_NativeSpeakerMatchStatus.EO_NativeSpeakerMatchStatus_Waiting://
                        //NUser 不再使用waiting中的msg数据AssignPartenerParam(msg);这个数据值有误 
                        break;
                    //结束类流程
                    case ENUM_NativeSpeakerMatchStatus.EO_NativeSpeakerMatchStatus_MatchCanceled://4  确认取消成功
                        
                    case ENUM_NativeSpeakerMatchStatus.EO_NativeSpeakerMatchStatus_MatchFailed: //5 //发起找人的整体timeout，另外一方 
                        //对侧取消了
                        if (this.Status == VoiceChatRunningStatus.PreMatchCancelling
                           
                            || this.Status == VoiceChatRunningStatus.PreMatch)
                        {
                            DoCancellCompleteAction();
                        }
                        else if (this.Status == VoiceChatRunningStatus.Idle)
                        {
                            VFDebug.LogWarning(msg.downMatchStatus.ToString() + ",status=" + this.Status.ToString() + " ==> 已经变为Idle了，可能是超时自动强制切换的");
                        }
                        else
                        {
                            VFDebug.LogError(msg.downMatchStatus.ToString() + ",status=" + this.Status.ToString() + " ==> do nothing");
                        }
               
                        break;
                    case ENUM_NativeSpeakerMatchStatus.EO_NativeSpeakerMatchStatus_MatchSuccess:
                        //没特殊处理，主要在matchResult侧
                        break;
                    default:
                        //没有实际的处理诉求？？
                        matchRecordID = -1;
                        VFDebug.Log("不应该触发吧 "+msg.downMatchStatus.ToString() + ",status=" + this.Status.ToString() + "isUserAsNS:"+this.IsUserAsNativeSpeaker +" matchRecordID = -1;");
                    
                        break;
                }
            }
        }

        private void AssignPartenerParam(SC_NativeSpeakerDown_MatchStatus msg)
        {   
            currPartnerParam.matchRecId = msg.match_record_id;
            currPartnerParam.NUserName = msg.play_name;//待检查
            currPartnerParam.headItemId = msg.head_item_id;
            currPartnerParam.headItemType = msg.head_item_type;
            currPartnerParam.levelVal = msg.language_level;
            currPartnerParam.topicVal = msg.topic;
            currPartnerParam.inviteBeginTimeStamp = msg.begin_time;
            currPartnerParam.gender = msg.gender;
        }
        
        private void AssignPartenerParam(SC_NativeSpeakerDown_MatchedResult msg)
        {   
            currPartnerParam.matchRecId = msg.match_record_id;
            currPartnerParam.NUserName = msg.play_name ;
            currPartnerParam.headItemId = msg.head_item_id;
            currPartnerParam.headItemType = msg.head_item_type;
            currPartnerParam.levelVal = string.Empty;
            currPartnerParam.topicVal = msg.topic;
            currPartnerParam.inviteBeginTimeStamp = 0;
            currPartnerParam.gender = msg.gender;
        }

        //User收到
        private void OnRecvMatchResult(SC_NativeSpeakerDown_MatchedResult msg)
        {
            //userchat_id
            VFDebug.Log($"OnRecvMatchResult2 ,uChatId:{msg.user_chat_id} avatarID:{msg.avatarId}  ");

            this.selfUid = (long)msg.Agora_uid_self;
            this.partnerUid = (long)msg.Agora_uid_self;
            
            // 新版本中 没有preMatchSucc 

            if( this.IsUserAsNativeSpeaker )
            {
                this.matchRecordID = msg.match_record_id; //这时是accept后的最终赋值，是有效的
                // nativespeaker 用户
                if (this.Status == VoiceChatRunningStatus.Idle || this.Status == VoiceChatRunningStatus.PreMatch )
                {
                    VFDebug.LogError("VChat临时日志01-a：NSUser-OnRecvMatchResult-MatchRecordID=" + matchRecordID );
                    this.JoinChannel(msg.Agora_token, msg.Agora_channel,msg.Agora_uid_self);
                }
                else if (this.Status == VoiceChatRunningStatus.UnInit)
                {
                    //首次初始化 比较慢？，已经在从uninit 到idle的过程中了？
                    TimerManager.instance.RegisterTimer((c) =>
                    {
                        if (this.Status == VoiceChatRunningStatus.Idle)
                        {
                            VFDebug.LogError("VChat临时日志01-b：NSUser-OnRecvMatchResult-MatchRecordID=" + matchRecordID );
                            this.JoinChannel(msg.Agora_token, msg.Agora_channel,msg.Agora_uid_self);
                        }
                        else
                        {
                            Debug.LogError("NS侧的 启动Engine时间 和 收到MatchResult时机过近，需要调整参数");
                        }
                    }, 300, 1);
                }
                else
                {
                    Debug.LogError("OnRecvMatchResult 时状态异常，status="+Status + "isUserAsNS:"+this.IsUserAsNativeSpeaker);
                    this.CancelPreMatch();
                }
            }
            else
            {
                
                AssignPartenerParam(msg);
                if (matchRecordID != msg.match_record_id)
                {
                    VFDebug.LogError("OnRecvMatchResult id出现前后不一致，preMatchRecordID=" + matchRecordID + "，msgRecID=" + msg.match_record_id);
                    return;
                }
                
                //  普通用户
                if ( this.Status == VoiceChatRunningStatus.PreMatch )
                {
                   VFDebug.LogError("VChat临时日志11：NormalUser-OnRecvMatchResult-MatchRecordID=" + matchRecordID );
                    this.JoinChannel(msg.Agora_token, msg.Agora_channel,msg.Agora_uid_self);
                }
                else
                {
                    Debug.LogError("OnRecvMatchResult 时状态异常，status="+Status+" IsUserAsNS:"+IsUserAsNativeSpeaker);
                    this.CancelPreMatch();
                }
            }

          
        }

        
        //新状态中仅用于匹配超时
        private void OnRecvMatchFailedByTimeout(SC_NativeSpeakerDown_MatchFailed msg)
        {
            if (this.IsUserAsNativeSpeaker)
            {
                //某个user侧超时的信息 没必要让ns知晓
                //他自己接收or拒绝的处理后的 结果也不会触发MatchFailed
                VFDebug.LogError("NS用户不应该收到 OnRecvMatchFailedByTimeout reason:"+ msg.reason);
                
                //目前NativeSpeaker侧是收到cancel的消息而非 matchFailed
                
            }
            else
            {
                VFDebug.Log("OnRecvMatchFailedByTimeout reason:"+ msg.reason);
                DoCancellCompleteAction();//后端已经自己处理了状态，客户端只要自己直接取消即可
            }

          
            
        }


        private void DoCancellCompleteAction() {
            this.Status = VoiceChatRunningStatus.Idle;
            matchRecordID = -1;

            Notifier.instance.SendNotification(NotifyConsts.VoiceChat_CancelPreMatchComplete);//通知视图改变
            

            if (!string.IsNullOrEmpty(this.cancelActionTimerKey)) {
                TimerManager.instance.UnRegisterTimer(this.cancelActionTimerKey);
                this.cancelActionTimerKey = string.Empty;
            }
            VFDebug.Log("Do Cancell Complete ,matchRecordID=-1");
        }
        #endregion


        #endregion MonoSingleton 基础行为

        #region 状态迁移 actions
        /* 状态迁移
        StartEngine ， UnInit -> Idle
        StartPreMatch，  Idle -> PreMatch
        RecvPreMatchConfirm，  PreMatch -> PreMatchConfirming         CancelPreMatch，   PreMatch -> Idle
        CancelPreMatchConfirming ，  PreMatchConfirming -> Idle         StepBackFromPreMatchConfirming,  PreMatchConfirming -> PreMatch

        JoinChannel ，   PreMatchConfirming -> ChattingSelfJoin
        RecvOtherJoinChannelMsg， ChattingSelfJoin-> ChattingBothJoin
        LeaveChannel ，ChattingSelfJoin  -> Idle
        LeaveChannel ，ChattingBothJoin -> Idle
        */
        
        /* 20250801 新版本-状态迁移
         StartEngine ， UnInit -> Idle  首轮通话前，一直默认处在Uninit状态
         StartPreMatch，  Idle -> PreMatch
         
         CancelPreMatch，   PreMatch -> Idle
         JoinChannel2 ，   PreMatch -> ChattingSelfJoin
         
         RecvOtherJoinChannelMsg， ChattingSelfJoin-> ChattingBothJoin
         LeaveChannel ，ChattingSelfJoin  -> Idle
         LeaveChannel ，ChattingBothJoin -> Idle
         */

        //启动初始化
        private void OnInitMultiTabFramework(string name, object body)
        {
            //StartEngine();
        }

        private void StartEngine(Action completeCallBack)
        {
            if (Status == VoiceChatRunningStatus.UnInit)
            {
                engine.Initialize(AppConstExt.AgoraSDKAppId);
                var ctrl = ControllerManager.instance.GetController<ExploreController>(ModelConsts.Explore) as ExploreController;
                CurNetStrategy = ctrl.CurNetStrategy;

                Status = VoiceChatRunningStatus.Idle;
           
                if (completeCallBack != null)
                {
                    completeCallBack.Invoke();
                }
            }
            else
            {
                throw new InvalidOperationException("StartEngine: 状态错误");
            }
        }

        //成功时返true
        private void Net_FindPartner(ENUM_MatchType matchType= ENUM_MatchType.EO_MatchType_Random, long otherEndUserId =0)
        {
            if (this.isUsingNewNativeSpeakerLogic)
            {
                Debug.Log("Net_FindPartner SendMatchingMsg2 upFindNativeSpeakerReq");
                CurNetStrategy.SendStartMatch(matchType, matchRecordID, ClientUserId,otherEndUserId);
                
            }
         
        }
        
        //应该传 NS user 点击item的对应matchRecordID
        private void Net_Accept(long matchRecId =0)
        {
            if (this.isUsingNewNativeSpeakerLogic && this.IsUserAsNativeSpeaker)
            {
                if (matchRecId == 0)
                {
                    matchRecId = this.matchRecordID;
                    Debug.Log("Net_Accept :: matchRecId = "+this.matchRecordID);
                }

                if (!this.matchInfoDic.ContainsKey(matchRecId))
                {
                    Debug.LogError("Net_Accept :: matchInfoDic不存在 matchRecId ="+matchRecId);
                }

                CurNetStrategy?.SendMatchingMsg(Msg.explore.PB_NativeSpeakerUpMsg.upBizMsgOneofCase.upAcceptMatchReq, matchRecId,ClientUserId,this.matchInfoDic[matchRecId].normalUserId );//异步请求,preMatchRecordID这时无作用
            }
            else
            {
                Debug.LogError("未实现过Net_Accept");
            }
        }
        private void Net_Reject()
        {
            if (this.isUsingNewNativeSpeakerLogic&& this.IsUserAsNativeSpeaker)
            {
                CurNetStrategy?.SendMatchingMsg(Msg.explore.PB_NativeSpeakerUpMsg.upBizMsgOneofCase.upRejectMatchReq, matchRecordID,ClientUserId);//异步请求,preMatchRecordID这时无作用
            }
            else
            {
                Debug.LogError("未实现过Net_Reject");
            }
        }

        private void Net_Cancel()
        {
            if (this.isUsingNewNativeSpeakerLogic)
            {
                if (this.IsUserAsNativeSpeaker)
                {
                    Debug.LogError("UserAsNS 不应该自己手动触发Cancel");
                }
                else
                {
                    CurNetStrategy?.SendMatchingMsg(Msg.explore.PB_NativeSpeakerUpMsg.upBizMsgOneofCase.upCancelMatchReq,matchRecordID,ClientUserId); //异步请求，preMatchRecordID这时有作用
                }
            }
       
        }

        //仅User端
        public bool StartPreMatch()
        {
            if (Status == VoiceChatRunningStatus.UnInit)
            {
                StartEngine( ()=>{
                    //回调时认为前置一定正常完成了到了 idle状态了
                    this.Net_FindPartner();
                    Status = VoiceChatRunningStatus.PreMatch;
                } );
                return true;
            }
            else  if (Status == VoiceChatRunningStatus.Idle)
            {
                this.Net_FindPartner();
                Status = VoiceChatRunningStatus.PreMatch;
                return true;
            }
            else if(Status == VoiceChatRunningStatus.Disposing)
            {
                VFDebug.LogWarning("StartPreMatch: 等待上一轮销毁完毕");
                return false;
            }
            else 
            {
                VFDebug.LogError("StartPreMatch: 状态错误");
                return false;
            }
        }
        
        //仅NS-User端
        //obj 应该为long matchRecId
        public bool AcceptMatch(long matchRecId = 0)
        {   
            Debug.Log("点击 accept时，recordID="+ this.matchRecordID);
            
            if (Status == VoiceChatRunningStatus.UnInit)
            {
                StartEngine( ()=>{
                    //回调时认为前置一定正常完成了到了 idle状态了
                    this.Net_Accept(matchRecId);
                    Status = VoiceChatRunningStatus.PreMatch;
                } );
                return true;
            }
            else  if (Status == VoiceChatRunningStatus.Idle)
            {
                this.Net_Accept(matchRecId);
                Status = VoiceChatRunningStatus.PreMatch;
                return true;
            }
            else if(Status == VoiceChatRunningStatus.Disposing)
            {
                VFDebug.LogWarning("StartPreMatch: 等待上一轮销毁完毕");
                return false;
            }
            else 
            {
                VFDebug.LogError("StartPreMatch: 状态错误");
                return false;
            }
        }

        public void CancelPreMatch()
        {
            if (Status != VoiceChatRunningStatus.PreMatch)
            {
                
                Debug.LogWarning("CancelPreMatch: 状态错误 Status="+Status.ToString());
                
            }

            Net_Cancel();
            Status = VoiceChatRunningStatus.PreMatchCancelling; //此时尚未立刻发生cancell行为
            this.cancelActionTimerKey = TimerManager.instance.RegisterTimer( c=> {
                DoCancellCompleteAction();
            },this.defaultClientTimerDelay, 1);    
           
         
        }


        //后端 确认给MatchResult后自动 join到房间里
        private async void JoinChannel(string token,string channel ,uint uid=0)
        {
            if (Status == VoiceChatRunningStatus.PreMatch)
            {
                int joinResult = engine.JoinChannel(token, channel, uid);
                VFDebug.Log("JoinChannel joinResult="+ joinResult);

                if (joinResult == 0)
                {
                    //succ flow
                    Status = VoiceChatRunningStatus.ChattingSelfJoin;
                    VFDebug.Log("到达 ChattingSelfJoin状态，等待userJoin事件");
                    
                    
                    //NS-User 点accept时优先进房等着，如果对方没实际接入 允许在退房
                    if (this.IsUserAsNativeSpeaker)
                    {
                        VFDebug.LogError("VChat临时日志02：NSUser-Join成功-进入聊天view mRecID=" + matchRecordID );
                        Notifier.instance.SendNotification(NotifyConsts.VoiceChat_JoinChatting);
                        
                        // 20250814 临时增加的特殊防御
                        this.bothJoinCountDownTimer = TimerManager.instance.RegisterTimer((c) =>
                        {
                            //Countdown结束时如果对端尚未达到chattingroom使得 bothJoin的状态达成，则我方也自动退出
                            //赌声网的正常双端联通时间必然在0-3以内
                        
                            if (this.Status != VoiceChatRunningStatus.ChattingBothJoin)
                            {
                                Debug.LogError("VChat临时日志05：启动进房无人的自动4s退出流程");
                                this.LeaveChannel(false);//非自己原因 而是系统防御行为
                            }
                            else
                            {
                                Debug.Log("join 4s后的检查状态检查 Status="+this.Status.ToString() + " 已经BothJoin的case 不应该发生!");
                            }

                        },this.bothJoinCountDownMaxTime,1);

                    }
                    else
                    {
                        VFDebug.LogError("VChat临时日志12：NormalUser-Join成功 mRecID=" + matchRecordID );
                    }

                }
                else {
                    VFDebug.LogError("VChat临时日志： JoinChannel Failed,result=" +joinResult+" mRecID="+ matchRecordID );
                    
                    this.CancelPreMatch();
                }
                
            }
            else
            {
                if (Status == VoiceChatRunningStatus.ChattingBothJoin)
                {
                    VFDebug.LogError("已联通的状态下 又收到了额外的matchResult 不合理, 暂不不处理,等正克排查");
                }
                else {
                    //todo0-tanglei 必须处理的case
                    throw new InvalidOperationException("JoinChannel: 状态错误，Status="+Status);
                }
                
            }
        }
        

        //todo 目前这里其实不只是 对方反复join
        public void RecvOtherJoinChannelMsg()
        {
            //主叫端
            if (Status == VoiceChatRunningStatus.ChattingSelfJoin)
            {
                Status = VoiceChatRunningStatus.ChattingBothJoin;
                
                //取消特殊防御
                if (!string.IsNullOrEmpty(this.bothJoinCountDownTimer))
                {
                    TimerManager.instance.UnRegisterTimer(bothJoinCountDownTimer);
                    this.bothJoinCountDownTimer = null;
                }

                var isSucc=  this.engine.ConfigMicAudio(true);
                if (!isSucc) {
                    VFDebug.LogError("config mic error");
                }

                if (this.IsUserAsNativeSpeaker)
                {
                    VFDebug.LogError("VChat临时日志04：NSUser 看到 NormalUser 进入房间，切view mRecID=" + matchRecordID );    
                }
                else
                {
                    VFDebug.LogError("VChat临时日志14：NormalUser看到 NSUser 进入房间，切view mRecID=" + matchRecordID );    
                }


                Notifier.instance.SendNotification(NotifyConsts.VoiceChat_JoinChatting);
                
               
            }
            else
            {
               Debug.LogError("RecvOtherJoinChannelMsg: 状态错误");
            }
        }

        //isSelfReason =true 就是自己点退出的case
        public void LeaveChannel(bool isSelfReason)
        {
            if (engine != null)
            {
                bool isValid = Status == VoiceChatRunningStatus.ChattingSelfJoin ||
                               Status == VoiceChatRunningStatus.ChattingBothJoin;
                if (!isValid)
                {
                    Debug.LogError("LeaveChannel时 可能前置状态有错误 Status="+Status);
                }

                
                int ret = engine.LeaveChannel();
                if (ret < 0) {
                    VFDebug.LogError(" LeaveChannel not succ, code="+ ret);
                }   
                

                
            
                Status = VoiceChatRunningStatus.Idle;
                if (matchRecordID < 0) {
                    VFDebug.LogError(" LeaveChannel matchRecordID<0,matchRecordID="+ matchRecordID);
                }
                else if (this.matchInfoDic.ContainsKey(matchRecordID))
                {
                    this.CurNetStrategy.SendUserChatExitMsg(this.selfUid, this.partnerUid, this.matchRecordID);
                    this.matchInfoDic.Remove(matchRecordID);
                    this.matchRecordID = -1;
                }
            
                if (isSelfReason)
                {
                    VFDebug.Log(" LeaveChannel selfReason ");
                }
          
                //尝试隔1点时间后销毁（1帧在移动端不够） 可能与LeaveChannel的执行耗时冲突，导致崩溃
                Notifier.instance.SendNotification(NotifyConsts.VoiceChat_ExitChannel);
                
            }
            else
            {
                //engine 已经disoose过的时候
                VFDebug.LogError("VChat临时日志：尝试退出时已经销毁过engine不需要重复销毁");
            }

        }

        #endregion 状态迁移 actions

        public bool ConfigMicVol(bool isTurnOn) {
            return this.engine.ConfigMicAudio(isTurnOn);
        }
        
        // isTurnOn = true时有声，否则屏蔽发生
        public bool ConfigSpeakerVol(bool isTurnOn) {
            return this.engine.ConfigSpeakerAudio(isTurnOn);
        }

        public void DisposeVoiceEngine()
        {
            this.Status = VoiceChatRunningStatus.Disposing; 
            TimerManager.instance.RegisterTimer((c) =>
            {
                this.engine.Dispose();
                this.Status = VoiceChatRunningStatus.UnInit;
            },150,1);
        }
        
    }
}