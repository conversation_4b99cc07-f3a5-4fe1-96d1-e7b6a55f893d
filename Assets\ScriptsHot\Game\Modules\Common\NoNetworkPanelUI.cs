﻿


using System;
using FairyGUI;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Common
{
    public class NoNetworkPanelUI : BaseUI<UIBind.CommonUI.NoNetworkPanel>
    {
        public NoNetworkPanelUI(string name) : base(name) { }

        public override string uiLayer => UILayerConsts.Float;
        protected override void OnInit(GComponent uiCom)
        {
             ui.btnConnect.onClick.Add(OnBtnConnect);
             ui.txtDesc.SetKey("ui_explore_tip_refresh");
             ui.txtRetry.SetKey("ui_explore_btn_retry");
        }

        private void OnBtnConnect(EventContext context)
        {
            this.Hide();
            GetUI<MultiTabFrameworkUI>(UIConsts.MultiTabHomepage).SetUIContainerVisible(false);
            Notifier.instance.SendNotification(NotifyConsts.RefresUINet);
        }

        protected override void OnShow()
        {
        }
    }
}