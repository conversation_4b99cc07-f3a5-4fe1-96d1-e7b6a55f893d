/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.Review
{
    public partial class SummaryListAvatar : GComponent
    {
        public static string pkgName => "Review";
        public static string comName => "SummaryListAvatar";
        public static string url => "ui://l7zo233dd2k92d";

        public GTextField tfContent;
        public GImage flow_all;
        public GImage flow_not_all;
        public Transition showText;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(SummaryListAvatar));
        }

        public override void ConstructFromXML(XML xml)
        {
            tfContent = GetChildAt(0) as GTextField;
            flow_all = GetChildAt(1) as GImage;
            flow_not_all = GetChildAt(2) as GImage;
            showText = GetTransitionAt(0);
        }
        public override void Dispose()
        {
            tfContent = null;
            flow_all = null;
            flow_not_all = null;
            showText = null;

            base.Dispose();
        }
    }
}