/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.MainHead
{
    public partial class CompUnlimit : UIBindT
    {
        public override string pkgName => "MainHead";
        public override string comName => "CompUnlimit";

        public GRichTextField tfTitle;
        public GRichTextField tfCost;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            tfTitle = (GRichTextField)com.GetChildAt(2);
            tfCost = (GRichTextField)com.GetChildAt(3);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            tfTitle = null;
            tfCost = null;
        }
    }
}