/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class ClozeOptionText : GComponent
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "ClozeOptionText";
        public static string url => "ui://cmoz5osjti2auvptcq";

        public GTextField text;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(ClozeOptionText));
        }

        public override void ConstructFromXML(XML xml)
        {
            text = GetChildAt(0) as GTextField;
        }
        public override void Dispose()
        {
            text = null;

            base.Dispose();
        }
    }
}