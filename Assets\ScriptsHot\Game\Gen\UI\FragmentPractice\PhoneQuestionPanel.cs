/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;

namespace UIBind.FragmentPractice
{
    public partial class PhoneQuestionPanel : UIBindT
    {
        public override string pkgName => "FragmentPractice";
        public override string comName => "PhoneQuestionPanel";

        public Controller playState;
        public Controller playProgressCtrl;
        public Controller PageCtrl;
        public GGraph imgBG;
        public GGraph mask;
        public CompBarPractice BarPractice;
        public AvatarLeftFrameCom leftAvatar;
        public AvatarRightFrameCom rightAvatar;
        public CompPone CompPhone;
        public GButton BtnClose;
        public GTextField coverTitle;
        public GGroup coverGrp;

        public override void Construct(GComponent com)
        {
            base.Construct(com);

            playState = com.GetControllerAt(0);
            playProgressCtrl = com.GetControllerAt(1);
            PageCtrl = com.GetControllerAt(2);
            imgBG = (GGraph)com.GetChildAt(0);
            mask = (GGraph)com.GetChildAt(1);
            BarPractice = (CompBarPractice)com.GetChildAt(2);
            leftAvatar = new AvatarLeftFrameCom();
            leftAvatar.Construct(com.GetChildAt(3).asCom);
            rightAvatar = new AvatarRightFrameCom();
            rightAvatar.Construct(com.GetChildAt(4).asCom);
            CompPhone = new CompPone();
            CompPhone.Construct(com.GetChildAt(6).asCom);
            BtnClose = (GButton)com.GetChildAt(7);
            coverTitle = (GTextField)com.GetChildAt(10);
            coverGrp = (GGroup)com.GetChildAt(12);

            OnConstructed();
        }
        public override void Dispose()
        {
            OnWillDispose();
            playState = null;
            playProgressCtrl = null;
            PageCtrl = null;
            imgBG = null;
            mask = null;
            BarPractice = null;
            leftAvatar.Dispose();
            leftAvatar = null;
            rightAvatar.Dispose();
            rightAvatar = null;
            CompPhone.Dispose();
            CompPhone = null;
            BtnClose = null;
            coverTitle = null;
            coverGrp = null;
        }
    }
}