/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.FragmentPractice
{
    public partial class OldBtnBaseAnswer : GButton
    {
        public static string pkgName => "FragmentPractice";
        public static string comName => "OldBtnBaseAnswer";
        public static string url => "ui://cmoz5osjz7rm3l";

        public Controller grpState;
        public Controller state;
        public GTextField tfImageAnswer;
        public GTextField tfAnswer;
        public GTextField tfBoldAnswer;
        public GComponent wave;
        public GGroup grpAnswer;
        public GGraph holder;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(OldBtnBaseAnswer));
        }

        public override void ConstructFromXML(XML xml)
        {
            grpState = GetControllerAt(0);
            state = GetControllerAt(1);
            tfImageAnswer = GetChildAt(7) as GTextField;
            tfAnswer = GetChildAt(9) as GTextField;
            tfBoldAnswer = GetChildAt(10) as GTextField;
            wave = GetChildAt(11) as GComponent;
            grpAnswer = GetChildAt(12) as GGroup;
            holder = GetChildAt(13) as GGraph;
        }
        public override void Dispose()
        {
            grpState = null;
            state = null;
            tfImageAnswer = null;
            tfAnswer = null;
            tfBoldAnswer = null;
            wave = null;
            grpAnswer = null;
            holder = null;

            base.Dispose();
        }
    }
}