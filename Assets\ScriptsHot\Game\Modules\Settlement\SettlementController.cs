using System;
using System.Collections.Generic;
using FairyGUI;
using Msg.basic;
using Msg.center;
using Msg.core;
using Msg.dialog_task;
using Msg.question_process;
using Msg.task_process;
using ScriptsHot.Game.Modules.ChatStart;
using ScriptsHot.Game.Modules.Managers;
using ScriptsHot.Game.Modules.DrillHub;
using ScriptsHot.Game.Modules.Settlement.SettlementUI;
using ScriptsHot.Game.Modules.System;
using ScriptsHot.Game.UGUI.iOSWidget;
using UIBind.Settlement;
using UnityEngine;
using System.Threading;
using Firebase.Analytics;
using ScriptsHot.Game.Modules.Shop;
using ScriptsHot.Game.Modules.Sign;
using Msg.course;
using ScriptsHot.Game.Modules.ExplorePush;
using ScriptsHot.Game.Modules.FragmentPractice;
using ScriptsHot.Game.Modules.MainPath;

namespace ScriptsHot.Game.Modules.Settlement
{
    public class DialogSettlement
    {
        public string ViewType;
        public object[] Args;
        public PB_SettlementData SettleData;
    }

    public class SettlementController : BaseController
    {
        public SettlementController() : base(ModelConsts.Settlement) { }

        private MainModel MainMod => GetModel<MainModel>(ModelConsts.Main);
        private ChatModel ChatMod => GetModel<ChatModel>(ModelConsts.Chat);
        private SettlementModel SettlementMod => GetModel<SettlementModel>(ModelConsts.Settlement);
        private ChatController ChatCtrl => GetController<ChatController>(ModelConsts.Chat);
        private DrillHubController DrillHubCtrl => GetController<DrillHubController>(ModelConsts.DrillHub);

        public override void OnInit()
        {
            RegisterModel(new SettlementModel());
            MsgManager.instance.RegisterCallBack<SC_ClickWordAck>(RespClickWordAck);
            
            MsgManager.instance.RegisterCallBack<SC_GetDialogSettlementAck>(RespDialogResultAck);
            MsgManager.instance.RegisterCallBack<SC_GetCourseSettlementAck>(RespCourseSettleAck);
        }

        public override void OnUIInit()
        {
            RegisterUI(new SettlementStaminaUI(UIConsts.SettlementStaminaUI));
            RegisterUI(new SettlementCommonUI(UIConsts.SettlementCommon));
            RegisterUI(new SettlementDrillHubUI(UIConsts.SettlementDrillHub));
            RegisterUI(new SettlementGrowthUI(UIConsts.SettlementGrowthUI));
            RegisterUI(new SettlementFriendsUI(UIConsts.SettlementFriendsUI));
            RegisterUI(new SettlementAdUI(UIConsts.SettlementAdUI));
            
            //好友任务结算界面
            RegisterUI(new SettlementFriendsQuestRewardUI(UIConsts.SettlementFriendsQuestReward)); 
            RegisterUI(new SettlementFriendsQuestProgressUI(UIConsts.SettlementFriendsQuestProgress));
            RegisterUI(new SettlementNewFriendsQuestUI(UIConsts.SettlementNewFriendsQuest));
            RegisterUI(new SettlementSelectTeammateUI(UIConsts.SettlementSelectTeammate));
            
            //好友连胜结算页面
            RegisterUI(new SettlementFriendStreakUI(UIConsts.SettlementFriendStreakUI));
            RegisterUI(new SettlementFriendStreakProcessUI(UIConsts.SettlementFriendStreakProcessUI));
            
            CompSettleList.Bind();
            CompSettleMark.Bind();
        }
        
        public Action<SC_ClickWordAck> NewWordResp;
        private void RespClickWordAck(SC_ClickWordAck msg)
        {
            NewWordResp?.Invoke(msg);
        }

        private void RespCourseSettleAck(SC_GetCourseSettlementAck msg)
        {
            if (msg.code == PB_Code.Normal)
            {
                SettlementMod.SetDialogResultListData(msg.data);
                SettlementMod.SetDialogMode(msg.data.dialog_mode);
                DealResultData();//生产界面队列，内部将触发course path的获取刷新逻辑

                ShowNextView( ()=> { GetUI<FragmentPracticeUI>(UIConsts.FragmentPracticeUI).Hide(); });
                
            }
            else
            {
                FailExceptionCallback();
            }
        }
        
        private void RespDialogResultAck(SC_GetDialogSettlementAck msg)
        {        
            if (msg.code == PB_Code.Normal)
            {
                SettlementMod.SetDialogResultListData(msg.data);
                SettlementMod.SetDialogMode(msg.data.dialog_mode);
            }
            else
            {
                FailExceptionCallback();
                return;
            }


            if (ChatCtrl.IsNewDialogMode(msg.data.dialog_mode) || ChatCtrl.GetChatStateName() == ChatState.Settlement)
            {

                DealResultData();//生产界面队列，内部将触发course path的获取刷新逻辑
                ShowNextView(); //老接口，未明确定义调用者会是哪个ui，无法指派回调
                Notifier.instance.SendNotification(NotifyConsts.SettlementReqDialogResultFinishEvent);

        

            }
            else {
                Debug.LogError("RespDialogResultAck wrong logic:需要确认下 怎么会有更古老的流程 未命中另一个if分支的逻辑");
            }

        }

        public void RequestDialogResult(long dialogId, PB_DialogMode chatMode, int roundId, long taskRecordId,
            PB_CourseLearnPathParams mainPathParam = null, long avatarId = 0, long taskId = 0, long topicId = 0)
        {
            CS_GetDialogSettlementReq msg = new CS_GetDialogSettlementReq
            {
                dialog_id = dialogId,
                dialog_mode = chatMode,
                avatar_id = avatarId,
                topic_id = topicId,
                task_id = taskId,
                task_record_id = taskRecordId,
                // learn_path_params = GetModel<LearnPathModel>(ModelConsts.LearnPath).GetPathParams(),
                round_id = roundId,
                course_learn_path_params = mainPathParam
            };

            MsgManager.instance.SendMsg(msg, (a, b) => FailExceptionCallback());
        }

        /// <summary>
        /// 结算页面队列
        /// </summary>
        private Queue<DialogSettlement> _queueSettlement = new();
        
        /// <summary>
        /// 处理结算数据 按照服务器下发顺序依次展示对应界面
        /// </summary>
        public void DealResultData()
        {
            if (SettlementMod.DialogResultListData != null)
            {
                VFDebug.Log($"DealResultData queue count {_queueSettlement.Count}");
                var len = SettlementMod.DialogResultListData.dialog_result_list.Count;
                for (int i = 0; i < len; i++)
                {
                    var dialogResult = SettlementMod.DialogResultListData.dialog_result_list[i];
                    switch (dialogResult.settlement_type)
                    {
                        case PB_SettlementTypeEnum.SettlementNone: //无意义
                            VFDebug.Log("结算SettlementNone 无意义 不该出现");
                            break;
                        case PB_SettlementTypeEnum.SettlementTrain: //训练中心
                            EnqueueSettlementDrillHub(dialogResult);
                            break;
                        case PB_SettlementTypeEnum.SettlementDialog: //所有结算view的数据
                            // 客户端页面 不处理了
                            break;
                        case PB_SettlementTypeEnum.SettlementSelect: //三选一
                            EnqueueSettlementSelect(dialogResult);
                            break;
                        case PB_SettlementTypeEnum.SettlementCheckin: //打卡
                            EnqueueSettlementCheckin(dialogResult);
                            break;
                        case PB_SettlementTypeEnum.SettlementMilestone: //里程碑
                            EnqueueSettlementMilestone(dialogResult);
                            break;
                        case PB_SettlementTypeEnum.SettlementRanking: //排行榜
                            EnqueueSettlementRanking(dialogResult);
                            break;
                        case PB_SettlementTypeEnum.SettlementIncentiveQuest: //激励任务结算
                            EnqueueSettlementIncentiveQuest(dialogResult);
                            break;
                        case PB_SettlementTypeEnum.SettlementAuto: //开扉页
                            EnqueueSettlementAuto(dialogResult);
                            break;
                        case PB_SettlementTypeEnum.SettlementPop: //经验弹窗
                            //EnqueueSettlementPop(dialogResult); 先不弹 等优化方案
                            break;
                        case PB_SettlementTypeEnum.SettlementGrowth: //经验弹窗
                            EnqueueSettlementGrowth(dialogResult);
                            break;
                        case PB_SettlementTypeEnum.SettlementWidget: //小组件引导
                            EnqueueSettlementWidgetGuider(dialogResult);
                            break;
                        case PB_SettlementTypeEnum.SettlementNotification: //推送引导
                            // EnqueueSettlementWidgetNotifGuider(dialogResult);
                            EnqueueSettlementWhatsapp(dialogResult);
                            break;
                        case PB_SettlementTypeEnum.SettlementFriendship: //好友推荐
                            EnqueueSettlementFriend(dialogResult);
                            break;
                        case PB_SettlementTypeEnum.SettlementFriendTaskMatch:
                            EnqueueSettlementSelectTeammate(dialogResult);
                            break;
                        case PB_SettlementTypeEnum.SettlementFriendTask:
                            EnqueueSettlementFriendsQuestProgress(dialogResult);
                            break;
                        case PB_SettlementTypeEnum.SettlementCommercial:
                            EnqueueSettlementCommercial(dialogResult);
                            break;
                        case PB_SettlementTypeEnum.SettlementAd:
                            EnqueueSettlementAd(dialogResult);
                            break;
                        case PB_SettlementTypeEnum.SettlementFriendStreakMatch:
                            EnqueueSettlementFriendStreakMatch(dialogResult);
                            break;
                        case PB_SettlementTypeEnum.SettlementFriendStreak:
                            EnqueueSettlementFriendStreak(dialogResult);
                            break;
                        default:
                            VFDebug.Log(
                                "<color=#FF0000>DealRewardInfo 有未解析的结算数据 是否新加了类型？</color> PB_SettlementTypeEnum :" +
                                dialogResult.settlement_type);
                            break;
                    }
                }

                VFDebug.Log($"DealResultData queue final count {_queueSettlement.Count}");


                //保证获取到数据后再进行更新
                GetController<ShopController>(ModelConsts.Shop).ReqShopInfo();
                CacheMsgManager.instance.ReqCommonCacheDataBatch();

                GetController<CurrencyController>(ModelConsts.CurrencyController).SendGetEconomicInfoReqAsync(
                    GameEventName.GameEnter, () =>
                        GetController<HomepageController>(ModelConsts.Homepage).ReqGetIncentiveData()); //后面那个可以刷新头部条

                GetController<RankController>(ModelConsts.Rank).RefreshRankDataUI();
                WidgetUtils.UpdateAndRefreshWidgetOnceOneDay();
                
                var mainPathModel = GetController<MainPathController>(ModelConsts.MainPath).Model;
                GetController<MainPathController>(ModelConsts.MainPath).RequestGetUserCourse(
                    MainPathController.MainPathOperateType.SettleRefresh, mainPathModel.FocusSectionId); //结算队列

                Notifier.instance.SendNotification(NotifyConsts.AfterDealResultData);
                Notifier.instance.SendNotification(NotifyConsts.SC_GetCourseSettlementAck);

                GetController<ExplorePushController>(ModelConsts.ExplorePush)
                    .ReqGetHomepageGuideItem(ExplorePushModel.ExplorePushEntrance.finish_course_back_main_path);
            }
        }

        /// <summary>
        /// 出队列 显示下一个View
        /// </summary>
        public void ShowNextView(Action onNextShowComplete=null)
        {
            if (SettlementMod.DialogResultListData == null || !SettlementMod.SettlementLocalCanShowNextView)
            {
                onNextShowComplete?.Invoke();
                return;
            }
            

            if (_queueSettlement.Count > 0)
            {
                PerformanceSaver.instance.UseNormalFPS();

                DialogSettlement taskSettlement  = _queueSettlement.Dequeue();
                switch (taskSettlement.SettleData.settlement_type)
                {
                    case PB_SettlementTypeEnum.SettlementNone:
                        VFDebug.LogError("结算状态：PB_SettlementTypeEnum.SettlementNone");
                        return;
                    case PB_SettlementTypeEnum.SettlementTrain:
                        SettlementMod.SetTrainResultData(taskSettlement.SettleData.train_data);
                        break;
                    case PB_SettlementTypeEnum.SettlementSelect:
                        SettlementMod.SetSelectData(taskSettlement.SettleData.select_data);
                        break;
                    case PB_SettlementTypeEnum.SettlementCheckin:
                        SettlementMod.SetCheckInData(taskSettlement.SettleData.checkin_data);
                        break;
                    case PB_SettlementTypeEnum.SettlementMilestone:
                        SettlementMod.SetMilestoneData(taskSettlement.SettleData.milestone_data);
                        break;
                    case PB_SettlementTypeEnum.SettlementRanking:
                        GetModel<RankModel>(ModelConsts.Rank).SetSettlementRankInfo(taskSettlement.SettleData.ranking_data);
                        break;
                    case PB_SettlementTypeEnum.SettlementIncentiveQuest:
                        SettlementMod.SetSignDailyData(taskSettlement.SettleData.incentive_quest_data);
                        break;
                    case PB_SettlementTypeEnum.SettlementAuto:
                        PB_TaskDescriptionData nextTask = taskSettlement.SettleData.auto_data.next_task;
                        if (nextTask != null)
                        {
                            VFDebug.Log($"收到 PB_SettlementTypeEnum.SettlementAuto ，nextTask.task_type：" + nextTask.task_type);
                            if (nextTask.task_type == PB_TaskTypeEnum.RewardBoxTask) //下一关是宝箱
                            {
                                // GetModel<LearnPathModel>(ModelConsts.LearnPath).NeedAutoOpen =
                                //     !taskSettlement.SettleData.auto_data.is_box_unclaimed; //领过了->路径  首次做 要领->首页
                                ChatCtrl.SettlementExitChat();
                            }
                            else
                            {
                                long dialogId = SettlementMod.DialogMode == PB_DialogMode.WarmupPractice
                                    ? -1 //warmup没有dialogId
                                    : ChatMod.dialogId;
                                GetController<ChatStartController>(ModelConsts.ChatStartController).EnterChatStart(
                                    nextTask.avatar_info?.avatar_id ?? 0, nextTask.task_id,
                                    nextTask.task_type, taskSettlement.SettleData.auto_data.dialog_source,
                                    () =>
                                    {
                                        // GetModel<LearnPathModel>(ModelConsts.LearnPath).NeedAutoOpen = false; //点击下一个任务前往对话时,不自动打开学习路径
                                        ChatCtrl.SettlementExitAndContinueChat();
                                    },
                                    dialogId);
                            }
                        }
                        else
                            ChatCtrl.SettlementExitChat();
                        return;
                    case PB_SettlementTypeEnum.SettlementPop:
                        SettlementMod.SetSettlementPopData(taskSettlement.SettleData.pop_data);
                        break;
                    case PB_SettlementTypeEnum.SettlementFriendship:
                        SettlementMod.SetSettlementFriendsData(taskSettlement.SettleData.friendship_data);
                        break;
                    case PB_SettlementTypeEnum.SettlementFriendTaskMatch:
                        SettlementMod.SetFriendsQuestTeammateData(taskSettlement.SettleData.friendship_task_match_data);
                        break;
                    case PB_SettlementTypeEnum.SettlementFriendTask:
                        SettlementMod.SetFriendsQuestProgressData(taskSettlement.SettleData.friendship_task_data);
                        break;
                    case PB_SettlementTypeEnum.SettlementGrowth:
                        break;
                    case PB_SettlementTypeEnum.SettlementCommercial:
                        break;
                    case PB_SettlementTypeEnum.SettlementAd:
                        break;
                    case PB_SettlementTypeEnum.SettlementFriendStreakMatch:
                        break;
                    case PB_SettlementTypeEnum.SettlementFriendStreak:
                        break;
                    case PB_SettlementTypeEnum.SettlementNotification:
                        break;
                    default://不显示 或 还未作处理的走default
                        VFDebug.Log(
                            "<color=#FF0000>ShowNextView 有已解析但未处理的数据</color> PB_SettlementTypeEnum :" +
                            taskSettlement.SettleData.settlement_type);
                        onNextShowComplete?.Invoke();
                        ShowNextView();
                        return;
                }
                
                var ui = GetUI(taskSettlement.ViewType);

                Debug.Log($"Whatsapp - ui = {ui.name}");
                if (onNextShowComplete != null)
                {
                    //ui.Show(taskSettlement.Args).onCompleted = onNextShowComplete; 不能这么写 广告有可能进去直接给关了 oncomplete根本没加上
                    ui.onCompleted = onNextShowComplete;
                    ui.Show(taskSettlement.Args);
                    Debug.Log($"Whatsapp - ui = {ui.name} show");
                }
                else {
                    ui.Show(taskSettlement.Args);
                }
              

                if (_queueSettlement.Count <= 1) {
                    //结算只剩1个的时候就撤下遮挡
                    //撤早了会难看
                    Debug.Log("=== WarmupLoadingCutInUI tryhide ===");
                    GetUI<WarmupLoadingCutInUI>(UIConsts.WarmupLoadingCutInUI)?.TryHide(0.5f);  // 临时挡一下结束
                }
            }
            else
            {
                GetModel<MainModel>(ModelConsts.Main).SetDrillHubNeedAutoOpen(false);
                GetController<ExplorePushController>(ModelConsts.ExplorePush)
                    .ReqGetHomepageGuideItem(ExplorePushModel.ExplorePushEntrance.finish_course_back_main_path);

                SendNotification(NotifyConsts.SettlementFinishEvent);
                
                ChatCtrl.SettlementExitChat();

                onNextShowComplete?.Invoke();
            }
        }
        
        /// <summary>
        /// 清除缓存数据
        /// </summary>
        public void ClearData()
        {
            SettlementMod.ClearTaskSettlementInfo();
            _queueSettlement.Clear();
        }

        private void EnqueueSettlementSelect(PB_SettlementData dialogResult)
        {
            if (dialogResult.select_data == null)
                return;
            
            DialogSettlement dialogSettlement = new DialogSettlement
            {
                ViewType = UIConsts.SettlementChoice,
                Args = null,
                SettleData = dialogResult
            };
            _queueSettlement.Enqueue(dialogSettlement);
        }

        private void EnqueueSettlementCheckin(PB_SettlementData dialogResult)
        {
            if (dialogResult.checkin_data == null)
                return;

            //正常签
            if (dialogResult.checkin_data.is_complete_recheckin || dialogResult.checkin_data.is_checkin)
            {
                AFHelper.Onetaskcompletion(MainMod.userID);
                DialogSettlement dialogSettlement = new DialogSettlement
                {
                    ViewType = UIConsts.SignPersistUI,
                    Args = null,
                    SettleData = dialogResult
                };
                _queueSettlement.Enqueue(dialogSettlement);
            }
        }

        private void EnqueueSettlementWidgetGuider(PB_SettlementData dialogResult)
        {
            DialogSettlement dialogSettlement = new DialogSettlement
            {
                ViewType = UIConsts.WidgetGuiderUI,
                Args = null,
                SettleData = dialogResult
            };
            if (GetController<WidgetController>(ModelConsts.Widget).CanShowGuiderUI)
            {
                _queueSettlement.Enqueue(dialogSettlement);
            }
        }
        private void EnqueueSettlementWidgetNotifGuider(PB_SettlementData dialogResult)
        {
            if (GetController<PermissMsgController>(ModelConsts.PermissMsg).PremissNotifIsOpen)
            {
                return;
            }
            
            bool isForce = false;
            if (dialogResult != null &&
                dialogResult.notification_data != null &&
                dialogResult.notification_data.rank_change_type ==
                PB_RankingChangeTypeForTrigger.PB_RANKING_CHANGE_TYPE_FOR_TRIGGER_UPGRADE)
            {
                isForce = true;
            }
            
            DialogSettlement dialogSettlement = new DialogSettlement
            {
                ViewType = UIConsts.PushGuiderUI,
                Args = null,
                SettleData = dialogResult
            };

            if (isForce && !GetController<PermissMsgController>(ModelConsts.PermissMsg).ShowPushGuiderToday)
            {
                GetController<PermissMsgController>(ModelConsts.PermissMsg).ForceOpen = true;
                _queueSettlement.Enqueue(dialogSettlement);
            }
            else if (GetController<PermissMsgController>(ModelConsts.PermissMsg).CanShowPushGuiderUI)
            {
                _queueSettlement.Enqueue(dialogSettlement);
            }
        }     
        private void EnqueueSettlementWhatsapp(PB_SettlementData dialogResult)
        {
            Debug.Log($"Whatsapp - EnqueueSettlementWhatsapp");
            if (GetController<WhatsappController>(ModelConsts.Whatsapp).CanShowWhatsappPop)
            {
                DialogSettlement dialogSettlement = new DialogSettlement
                {
                    ViewType = UIConsts.WhatsappGuider2,
                    Args = null,
                    SettleData = dialogResult
                };
                _queueSettlement.Enqueue(dialogSettlement);
                Debug.Log($"Whatsapp - EnqueueSettlementWhatsapp add2Queue");
            }
        }             
        private void EnqueueSettlementMilestone(PB_SettlementData dialogResult)
        {
            if (dialogResult.milestone_data == null)
                return;
            
            DialogSettlement dialogSettlement = new DialogSettlement
            {
                ViewType = UIConsts.SignStreakUI,
                Args = null,
                SettleData = dialogResult
            };
            _queueSettlement.Enqueue(dialogSettlement);
        }

        private void EnqueueSettlementRanking(PB_SettlementData dialogResult)
        {
            if (dialogResult.ranking_data == null)
                return;
            
            DialogSettlement dialogSettlement = new DialogSettlement();
            if (dialogResult.ranking_data.RankChangeType ==
                PB_RankingChangeTypeForTrigger.PB_RANKING_CHANGE_TYPE_FOR_TRIGGER_JOIN)
            {
                dialogSettlement.ViewType = UIConsts.SettlementRankUI;
                dialogSettlement.Args = new object[] { -1 };
                dialogSettlement.SettleData = dialogResult;
                _queueSettlement.Enqueue(dialogSettlement);
            }
            else if (dialogResult.ranking_data.RankChangeType !=
                     PB_RankingChangeTypeForTrigger.PB_RANKING_CHANGE_TYPE_FOR_TRIGGER_NONE)
            {
                var lastRank = LocalCfgMgr.instance.GetGlobal("last_rank");
                var lastRankAccount = LocalCfgMgr.instance.GetGlobal("last_rank_account");
                if (!(!string.IsNullOrEmpty(lastRank) && !string.IsNullOrEmpty(lastRankAccount) &&
                      lastRankAccount == MainMod.userID.ToString()))
                    lastRank = "-1";
                LocalCfgMgr.instance.SetGlobal("last_rank", dialogResult.ranking_data.current_order.ToString());
                LocalCfgMgr.instance.SetGlobal("last_rank_account", MainMod.userID.ToString());

                if (!int.TryParse(lastRank, out var rank)) return;
                dialogSettlement.ViewType = UIConsts.SettlementRankUI;
                dialogSettlement.Args = new object[] { rank };
                dialogSettlement.SettleData = dialogResult;
                _queueSettlement.Enqueue(dialogSettlement);
            }
        }

        private void EnqueueSettlementIncentiveQuest(PB_SettlementData dialogResult)
        {
            if (dialogResult.incentive_quest_data == null)
                return;

            if (dialogResult.incentive_quest_data.show_quest_page)
            {
                DialogSettlement dialogSettlement = new DialogSettlement
                {
                    ViewType = UIConsts.SignDailyRewardUI,
                    Args = null,
                    SettleData = dialogResult
                };
                _queueSettlement.Enqueue(dialogSettlement);
            }
        }

        private void EnqueueSettlementAuto(PB_SettlementData dialogResult)
        {
            if (dialogResult.auto_data == null)
                return;
            
            DialogSettlement dialogSettlement = new DialogSettlement
            {
                ViewType = "",
                Args = null,
                SettleData = dialogResult
            };
            _queueSettlement.Enqueue(dialogSettlement);
        }

        private void EnqueueSettlementPop(PB_SettlementData dialogResult)
        {
            DialogSettlement dialogSettlement = new DialogSettlement
            {
                ViewType = UIConsts.SettlementExp,
                Args = null,
                SettleData = dialogResult
            };
            _queueSettlement.Enqueue(dialogSettlement);
        }

        private void EnqueueSettlementGrowth(PB_SettlementData dialogResult)
        {
            DialogSettlement dialogSettlement = new DialogSettlement
            {
                ViewType = UIConsts.SettlementGrowthUI,
                Args = new []{dialogResult.growth_data},
                SettleData = dialogResult
            };
            _queueSettlement.Enqueue(dialogSettlement);
        }

        private void EnqueueSettlementDrillHub(PB_SettlementData dialogResult)
        {
            DialogSettlement dialogSettlement = new DialogSettlement
            {
                ViewType = UIConsts.SettlementDrillHub,
                Args = null,
                SettleData = dialogResult
            };
            _queueSettlement.Enqueue(dialogSettlement);
        }
        
        private void EnqueueSettlementFriend(PB_SettlementData dialogResult)
        {
            DialogSettlement dialogSettlement = new DialogSettlement
            {
                ViewType = UIConsts.SettlementFriendsUI,
                Args = null,
                SettleData = dialogResult
            };
            _queueSettlement.Enqueue(dialogSettlement);
        }
            
        private void EnqueueSettlementCommercial(PB_SettlementData dialogResult)
        {
            // 250327 中国版过审修改
            if (AppConst.CN_Shop_IsClose)
            {
                return;
            }
            
            DialogSettlement dialogSettlement = new DialogSettlement
            {
                ViewType = UIConsts.SpeakSettlementPlanSubscribeUI,
                Args = new object[]{4},
                SettleData = dialogResult
            };
            _queueSettlement.Enqueue(dialogSettlement);  
        }

        private void EnqueueSettlementAd(PB_SettlementData dialogResult)
        {
            if (AppConst.IsCN)
            {
                return;
            }
            DialogSettlement dialogSettlement = new DialogSettlement
            {
                ViewType = UIConsts.SettlementAdUI,
                Args = null,
                SettleData = dialogResult
            };
            _queueSettlement.Enqueue(dialogSettlement);
        }

        private void EnqueueSettlementFriendStreakMatch(PB_SettlementData dialogResult)
        {
            DialogSettlement dialogSettlement = new DialogSettlement
            {
                ViewType = UIConsts.SettlementFriendStreakUI,
                Args = new []{dialogResult.friend_streak_recommend_data},
                SettleData = dialogResult
            };
            _queueSettlement.Enqueue(dialogSettlement);
        }
        
        private void EnqueueSettlementFriendStreak(PB_SettlementData dialogResult)
        {
            DialogSettlement dialogSettlement = new DialogSettlement
            {
                ViewType = UIConsts.SettlementFriendStreakProcessUI,
                Args = new []{dialogResult.friend_streak_data},
                SettleData = dialogResult
            };
            _queueSettlement.Enqueue(dialogSettlement);
        }
        
        private void EnqueueSettlementSelectTeammate(PB_SettlementData dialogResult)
        {
            DialogSettlement dialogSettlement = new DialogSettlement
            {
                ViewType = UIConsts.SettlementSelectTeammate,
                Args = null,
                SettleData = dialogResult
            };
            _queueSettlement.Enqueue(dialogSettlement);
        }
        
        private void EnqueueSettlementFriendsQuestProgress(PB_SettlementData dialogResult)
        {
            //dialogResult.friendship_task_data.reward_status = PB_CheckinRewardStatus.PB_CheckinRewardStatus_UnFinish;
            DialogSettlement dialogSettlement = new DialogSettlement
            {
                ViewType = UIConsts.SettlementFriendsQuestProgress,
                Args = null,
                SettleData = dialogResult
            };
            _queueSettlement.Enqueue(dialogSettlement);
        }
        
        private void FailExceptionCallback()
        {
            GetUI<CommConfirmUI>(UIConsts.CommConfirm)
                .OpenI18N("page-error-pop", () =>
                {
                    ChatCtrl.SettlementExitChat();
                }, null, 1, null, "common_check");
        }
    }
}