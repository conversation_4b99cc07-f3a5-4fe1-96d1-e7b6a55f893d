using Msg;
using Msg.basic;
using Msg.dialog_task;
using UnityEngine;

public class ChatStateSelect : BaseChatMachineState
{
    ChatController _chatController => this.owner.GetController<ChatController>(ModelConsts.Chat);
    private ChatModel _chatModel  => owner.GetModel<ChatModel>(ModelConsts.Chat);
    private string preState = ChatState.None;
    public ChatStateSelect() : base(ChatState.Select)
    {
        
    }

    public override void OnEnter(params object[] args)
    {
        base.OnEnter(args);
        preState = (string)args[0];
        this.owner.GetUI(UIConsts.Chat).Show();
        this.owner.GetUI(UIConsts.RecordUI).Show();
 
        OnUIModelSelect();
        _chatController.CreateDialog();//这个流程里是需要做很多参数准备
        
        _chatController.SetPlayNewWordAble(true);


     

        //这里需要延迟触发降低fps,因为EnterDialog事件目前将触发人物的相机推拉，30fps时处理这个部分明显有一定的不适感
        //该推拉的blend时间在 brain的customdata内指派，切入为750ms
        //这里使用1500ms 是多预留750ms给其他初始动画表现部分。
        TimerManager.instance.RegisterTimer((c) => {
            PerformanceSaver.instance.UseLowFPS();
        }, 1500, 1);

    }

    public void OnUIModelSelect()
    {
        // if (_chatModel.chatMode == PB_DialogMode.Exercise ||_chatModel.chatMode == PB_DialogMode.Career)
        // {
        //     this.owner.GetUI<MainHeaderUI>(UIConsts.MainHeader).SetHeader(MainHeaderMode.ExerciseTalk);
        // }
        // else
        // {
        //     this.owner.GetUI<MainHeaderUI>(UIConsts.MainHeader).SetHeader(MainHeaderMode.Talk);
        // }
        _chatController.ChangeState(ChatState.PreTalk);
    }
    
    public override void OnExit(string nextState)
    {
        base.OnExit(nextState);

        _chatController.GetUI(UIConsts.ChatMode).Hide();
    }
    
    public override bool OnHandleMsg(MsgData msg)
    {
        bool isAccept = DealHandleMsgData(msg);
        return isAccept;
    }


    private bool DealHandleMsgData(MsgData msg)
    {
        if (msg.msgId == (short)GameMSGID.SC_GetDialogTaskModeListAck_ID)
        {
            Debug.Log("DealHandleMsgData  msg.msgId 1111");
            SC_GetDialogTaskModeListAck ack = (SC_GetDialogTaskModeListAck)msg.data;
            _chatModel.SetModeData(ack);
            Debug.Log("ChatModeUI will show");
            // _chatController.GetUI<ChatModeUI>(UIConsts.ChatMode).BindData();
            return true;
        }
        else if(msg.msgId == (short)GameMSGID.SC_GetModeInfoAck_ID)
        {
            
            Debug.Log("DealHandleMsgData  msg.msgId 2222");
            // _chatModel.SetChatMode(PB_DialogMode.Tutor);
            SC_GetModeInfoAck ack = (SC_GetModeInfoAck)msg.data;
            _chatModel.SetChatMode(ack.data.mode);
            _chatModel.SetSingleModeData(ack);
            owner.GetController<ChatController>(ModelConsts.Chat).GetUI(UIConsts.ChatModeRetry)
                .Show(_chatController.curTaskId, _chatController.curAvatarId);
            // owner.GetUI<ChatModeRetryUI>(UIConsts.ChatModeRetry).BindData();
            return true;
        }
        else if(msg.msgId == (short)GameMSGID.SC_GetModeInfoForCareerAck_ID)
        {
            SC_GetModeInfoForCareerAck ack = (SC_GetModeInfoForCareerAck)msg.data;
            _chatModel.SetChatMode(PB_DialogMode.Career);
            //显示扉页
            owner.GetController<ChatController>(ModelConsts.Chat).GetUI(UIConsts.ChatModeTitlePage).Show(ack);
            owner.GetController<RecommendCardController>(ModelConsts.RecommendCard).GetUI(UIConsts.RecommendCardUI).Hide();
            // owner.GetController<MainController>(ModelConsts.Main).GetUI(UIConsts.MainHeader).Hide();
            return true;
        }
        return false;
    }
    
    public override void OnHandleError(params object[] args)
    {
        base.OnHandleError();

    }
}