[{"ID": "button_quit", "Path": "button_quit", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "click_flow_map", "Path": "click_flow_map", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "combo", "Path": "combo", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 1}, {"ID": "cost_gold", "Path": "cost_gold", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "evaluate", "Path": "evaluate", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "gain_star", "Path": "gain_star", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "gold1", "Path": "gold1", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "gold2", "Path": "gold2", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "good", "Path": "good", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "hide_task_bubble", "Path": "hide_task_bubble", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "medal", "Path": "medal", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 2}, {"ID": "mission", "Path": "mission", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "show_scafforder", "Path": "show_scafforder", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "show_task_bubble", "Path": "show_task_bubble", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "taskFinish", "Path": "taskFinish", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "listen", "Path": "listen", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "back", "Path": "back", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "click_follow", "Path": "click_follow", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "combo_feedback", "Path": "combo_feedback", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "settlement", "Path": "settlement", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 7}, {"ID": "chat_feedback_positive", "Path": "chat_feedback_positive", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 2}, {"ID": "chat_feedback_negative", "Path": "chat_feedback_negative", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 1}, {"ID": "audio_record_record", "Path": "RecordingHint", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "audio_record_send", "Path": "MessageSent1", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 1}, {"ID": "bad", "Path": "bad", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "click_flow", "Path": "click_flow", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 1}, {"ID": "click_go", "Path": "click_go", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 1}, {"ID": "level_up", "Path": "level_up", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 2}, {"ID": "chat_score_above_60", "Path": "chat_score_above_60", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 2}, {"ID": "chat_score_below_60", "Path": "chat_score_below_60", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "GetGold", "Path": "GetGold", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "score_total", "Path": "score_total", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 1}, {"ID": "star_fly_progress", "Path": "star_fly_progress", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "learnpath_chapter_complete", "Path": "learnpath_chapter_complete", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 2}, {"ID": "learnpath_chapter_progress_complete", "Path": "learnpath_chapter_progress_complete", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 1}, {"ID": "learnpath_chapter_unlock", "Path": "learnpath_chapter_unlock", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 2}, {"ID": "onboard_next", "Path": "onboard_next", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "sign_finish", "Path": "sign_finish", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 1}, {"ID": "sign_finish_milestone", "Path": "sign_finish_milestone", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 1}, {"ID": "sign_flow_jump", "Path": "sign_flow_jump", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 1}, {"ID": "sign_success_transtion", "Path": "sign_success_transtion", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 1}, {"ID": "sign_reward", "Path": "sign_reward", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 1}, {"ID": "rank_Unlock", "Path": "rank_Unlock", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 1}, {"ID": "rank_enter", "Path": "rank_enter", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "rank_up", "Path": "rank_up", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 2}, {"ID": "rank_down", "Path": "rank_down", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "button_next", "Path": "button_next", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "button_chapter", "Path": "button_chapter", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "question_settle", "Path": "question_settle", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 2}, {"ID": "question_combo", "Path": "question_combo", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 2}, {"ID": "question_error", "Path": "Wrong2", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 6}, {"ID": "question_right", "Path": "Correct", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 5}, {"ID": "question_check", "Path": "question_check", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "contacts_click_collection", "Path": "contacts_click_collection", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "drillhub_mis_resolve", "Path": "drillhub_mis_resolve", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "question_warning", "Path": "chat_score_below_60", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 8}, {"ID": "star5_win5", "Path": "Win5-1;Win5-2;Win5-3", "IsLoop": false, "Cd": 0, "Volume": 28, "VibrateType": 0}, {"ID": "star5_win10", "Path": "Win10-1;Win10-2;Win10-3", "IsLoop": false, "Cd": 0, "Volume": 28, "VibrateType": 0}, {"ID": "star5_onStart", "Path": "Start-1", "IsLoop": false, "Cd": 0, "Volume": 28, "VibrateType": 0}, {"ID": "star5_right", "Path": "Win-1;Win-2;Win-3", "IsLoop": false, "Cd": 0, "Volume": 28, "VibrateType": 0}, {"ID": "star5_wrong", "Path": "Wrong-1;Wrong-2;Wrong-3", "IsLoop": false, "Cd": 0, "Volume": 28, "VibrateType": 0}, {"ID": "star5_onexit", "Path": "End-1", "IsLoop": false, "Cd": 0, "Volume": 28, "VibrateType": 0}, {"ID": "star5_review", "Path": "Review-1", "IsLoop": false, "Cd": 0, "Volume": 28, "VibrateType": 0}, {"ID": "settle_daily_done_diamond", "Path": "DailyTaskDoneDiamond", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "settle_growth_up_partA", "Path": "GrowthLevelUpPartA", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "settle_growth_up_partB", "Path": "GrowthLevelUpPartB", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "settle_common_show", "Path": "PracticeWin", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}, {"ID": "settle_streak_win", "Path": "StreakWin", "IsLoop": false, "Cd": 0, "Volume": 100, "VibrateType": 0}]