﻿

using System;
using AnimationSystem;
using Msg.explore;
using ScriptsHot.Game.Modules.Explore;
using UnityEngine;

public partial class ExploreFriendsController
{
    private RecordUI _recordUI;
    /// <summary>
    /// 录音超时定时器ID
    /// </summary>
    private string _recordTimeoutTimerId = string.Empty;
    
    /// <summary>
    /// 音频发送超时定时器ID
    /// </summary>
    private string _responseTimeoutTimerId = string.Empty;
    
    public DialogManager ModelDialogManager;
    
    /// <summary>
    /// 是否等待提交
    /// </summary>
    private bool _pendingSubmit = false;
    
    /// <summary>
    /// 是否已收到录音开始响应
    /// </summary>
    private bool _receivedStartResponse = false;
    
    /// <summary>
    /// 是否正在录音
    /// </summary>
    public bool IsMicrophoneing = false;

    /// <summary>
    /// 是否允许发送录音
    /// </summary>
    public bool IfCanMicrophoneSend = false;

    public void HideRecordUI()
    {
        if (_recordUI != null)
        {
            _recordUI.Hide();
        }
    }
    
    /// <summary>
    /// 隐藏录音按钮
    /// </summary>
    public void HideRecordButton()
    {
        _recordUI?.HideRecordButton();
    }

    public void ShowRecordUI()
    {
        // Debug.LogError("打开 录音界面！！！！！");
        _recordUI = this.GetUI<RecordUI>(UIConsts.RecordUI);
    
        if (!GetUI<RecordUI>(UIConsts.RecordUI).isShow)
        {
            GetUI<RecordUI>(UIConsts.RecordUI).Show().onCompleted += () =>
            {
                _recordUI.ShowRecord(true, OnRecordingUserAudio, OnSendClick, OnCancelClick,true,SendStartRecord,false,OnCompleteClickButton,true);
                _recordUI.OpenNewSocial(true);
                _recordUI.SetTouchLong(true);
                // _recordUI.HideNewRecordText();
                _recordUI.FinishBtnClick = false;

            };
        }
        else
        {
            _recordUI.ShowRecord(true, OnRecordingUserAudio, OnSendClick, OnCancelClick,true,SendStartRecord,false,OnCompleteClickButton,true);
            _recordUI.OpenNewSocial(true);
            _recordUI.SetTouchLong(true);
            // _recordUI.HideNewRecordText();
            _recordUI.FinishBtnClick = false;
        }
    }
    
    
    //开始点击录音
    private void OnRecordingUserAudio()
    {
        IsMicrophoneing = true;

        VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Medium);
        if (_recordUI != null)
        {
            _recordUI.SetSendTipsVisible(true);
        }

        StopStreamAudioTTs();
        Notifier.instance.SendNotification(NotifyConsts.ExploreFriend_AvatarAudioPlayOver);

        UpEvent(PB_Explore_FriendChatUpBizEvent.EO_FC_USER_MANUAL_START);
        GMicrophoneManager.instance.BeginRecord(MaxASRTime / 1000f);
        
        RequestAsr("",false,true,String.Empty,true);
        _receivedStartResponse = false;
        ClearRecordTimeoutTimer();
        ClearResponseTimeoutTimer();
        
        ModelDialogManager?.EnterPlayerSpeakingState();
        
        // Debug.LogError("开始录音！！！！_recordTimeoutTimerId！");
        // 设置10秒超时定时器
        _recordTimeoutTimerId = TimerManager.instance.RegisterTimer((a) =>
        {
            if (!_receivedStartResponse)
            {
                VFDebug.LogError("录音开始请求超时，执行取消操作");
                OnCancelRecored();
            }
            _recordTimeoutTimerId = string.Empty;
        }, ExploreConst.MicStartDelayTime); // 10秒超时
        
    }
    
    //点击对号
    private void OnSendClick(DownsideEvent evt)
    {
        _recordUI.ShowRecordLoading();
        HideBulbLight();
        
        // 检查是否已经收到了返回协议
        if (IfCanMicrophoneSend)
        {
            TimerManager.instance.RegisterTimer((a) =>
            {
                StopAsr();
                // 如果已经收到了返回协议，直接发送提交协议
                UpEvent(PB_Explore_FriendChatUpBizEvent.EO_FC_USER_MANUAL_SUBMIT);
                
                // 设置10秒超时定时器
                _responseTimeoutTimerId = TimerManager.instance.RegisterTimer((a) =>
                {
                    VFDebug.Log("录音发送，一直没收到回复，执行取消操作");
                    OnCancelRecored();
                    _responseTimeoutTimerId = string.Empty;
                }, ExploreConst.MicStartDelayTime); // 10秒超时
                
            }, 300);  // 这个值 是根据 net 写入  设置的间隔200 ms定的
            
        }
        else
        {
            // 设置等待提交标志
            _pendingSubmit = true;
        }

    }
    
    //点击取消按钮 取消发送数据
    public void OnCancelClick(DownsideEvent evt)
    {
        VFDebug.Log("OnCancelClick");
        OnCancelRecored();
    }
    
    public void OnCancelRecored()
    {
        IsMicrophoneing = false;
        ModelDialogManager?.EnterDialogueIdleState();
        
        _pendingSubmit = false;
        _recordUI.ShowRecord(true, OnRecordingUserAudio, OnSendClick, OnCancelClick,true,SendStartRecord,false,OnCompleteClickButton,true);
        _recordUI.OpenNewSocial(true);
        _recordUI.SetTouchLong(true);
        _recordUI.FinishBtnClick = false;
        // UIManager.instance.SwitchLayer(UIConsts.RecordUI, UILayerConsts.HomePage);
    }
    
    public void HideBulbLight()
    {
        _recordUI?.HideBulbNew();
    }

    public void Finish()
    {
        _recordUI?.ShowFinish(true,null,true);
    }

    private void SendStartRecord()
    {
        // HideBulbLight();
    }
    
    public void OnCompleteClickButton()
    {
    }
    
    /// <summary>
    /// 清除录音超时定时器
    /// </summary>
    private void ClearRecordTimeoutTimer()
    {
        // Debug.LogError("清除录音超时定时器");
        if (!string.IsNullOrEmpty(_recordTimeoutTimerId))
        {
            TimerManager.instance.UnRegisterTimer(_recordTimeoutTimerId);
            _recordTimeoutTimerId = string.Empty;
        }
    }
    
    /// <summary>
    /// 清除发送音频，一直收不到回复计时器
    /// </summary>
    private void ClearResponseTimeoutTimer()
    {
        // Debug.LogError("清除发送音频，一直收不到回复计时器");
        if (!string.IsNullOrEmpty(_responseTimeoutTimerId))
        {
            TimerManager.instance.UnRegisterTimer(_responseTimeoutTimerId);
            _responseTimeoutTimerId = string.Empty;
        }
    }

    private void StopAsr()
    {
        this.IsMicrophoneing = false;
        GMicrophoneManager.instance.EndRecord();
    }
}
