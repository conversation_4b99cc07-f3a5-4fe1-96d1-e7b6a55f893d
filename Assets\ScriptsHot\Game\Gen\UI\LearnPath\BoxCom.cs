/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.LearnPath
{
    public partial class BoxCom : ChapterItem
    {
        public static string pkgName => "LearnPath";
        public static string comName => "BoxCom";
        public static string url => "ui://94jvztftyrl0xxx6u";

        public Controller splitterCtrl;
        public ChapterSplitter splitter;
        public BoxComInner inner;
        public GGroup grpContent;
        public GGraph arrowAnchor;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(BoxCom));
        }

        public override void ConstructFromXML(XML xml)
        {
            splitterCtrl = GetControllerAt(0);
            splitter = new ChapterSplitter();
            splitter.Construct(GetChildAt(0).asCom);
            inner = new BoxComInner();
            inner.Construct(GetChildAt(1).asCom);
            grpContent = GetChildAt(2) as GGroup;
            arrowAnchor = GetChildAt(3) as GGraph;
        }
        public override void Dispose()
        {
            splitterCtrl = null;
            splitter.Dispose();
            splitter = null;
            inner.Dispose();
            inner = null;
            grpContent = null;
            arrowAnchor = null;

            base.Dispose();
        }
    }
}