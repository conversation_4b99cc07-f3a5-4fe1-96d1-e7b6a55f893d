/*
****************************************************
# 作者：Huangshiwen
# 创建时间：   2025/01/15 20:54:23 星期三
# 功能：Nothing
****************************************************
*/

using System;
using System.Collections.Generic;
using FairyGUI;
using ScriptsHot.Game.Modules.Profile;
using ScriptsHot.Game.Modules.Settlement;
using UIBind.Rank;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Rank.UI
{
    public class RankUIBase<T> : BaseUI<T> where T : UIBindT
    {
        #region 系统 参数

        private string timeLimit;

        public RankUIBase(string name) : base(name)
        {
        }

        public override string uiLayer => UILayerConsts.Home; //主UI层
        protected override bool isFullScreen => true;

        private List<PB_UserItem> _user_item = new List<PB_UserItem>();
        private RankModel _rankModel => GetModel<RankModel>(ModelConsts.Rank);
        private RankController _RankController => GetController<RankController>(ModelConsts.Rank);


        private List<string> _orderColor =
            new List<string>()
            {
                "#805C00", "#537080", "#804C3B"
            };

        private List<string> _cardColor =
            new List<string>()
            {
                "#FFF9D7", "#EFF8FB", "#FAEFEB"
            };

        private string _myColor = "#6632FF";
        private string _defaultColor = "#111111";
        private Color TimeColorPurple;
        private Color TimeColorRed;
        private Vector2 bigSize = new Vector2(176, 179);
        private Vector2 smallSize = new Vector2(128, 128);
        private static int MEDAL_NORMAL_SIZE = 110;
        private static int MEDAL_BIG_SIZE = 152;
        protected bool _playingAni;
        private bool _isJoinedRank;
        private int _preOrderIdx;
        private int _curOrderIdx;
        private int _lastRankOrder;
        private float _lastUpdateTime;
        private RankCom _rankCom;
        private RankUIState _curState;
        private RankContentComponent _comRankContent;

        private string initTimer;

        public enum RankUIState
        {
            normal = 0,
            locked,
            joined,
        }

        #endregion

        #region 系统函数

        protected void InitRankUIBase(RankContentComponent cmp)
        {
            _comRankContent = cmp;
            AddUIEvent(_comRankContent.btn_close.onClick, ButtonCloseClick);
            AddUIEvent(_comRankContent.btnUnlock.onClick, ButtonCloseClick);
            AddUIEvent(_comRankContent.btnDotask.onClick, ButtonDoTaskClick);
            _comRankContent.listCard.itemRenderer = RenderListCardItem;
            _comRankContent.listCard.itemProvider = GetListItemResource;
            _comRankContent.listCard.scrollPane.onScrollEnd.Add(OnScrollMoveEnd);

            _comRankContent.listCard.SetVirtual();

            ColorUtility.TryParseHtmlString("#6632FF", out TimeColorPurple);
            ColorUtility.TryParseHtmlString("#FF007A", out TimeColorRed);
        }


        protected void SetRankPage(RankUIState state, int lastOrder = -1)
        {
            DataDotAppear_Leaderboard model = new DataDotAppear_Leaderboard();
            DataDotMgr.Collect(model);
            _lastRankOrder = lastOrder;
            if (state == RankUIState.joined)
            {
                VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Success);
            }
            RefreshUI(state);
        }

        // protected void RefreshByArgs(RankUIState state)
        // {
        //     if (args != null && args.Length > 0)
        //     {
        //         _isJoinedRank = state == RankUIState.joined && args.Length == 1;
        //         if ((state == RankUIState.joined || state == RankUIState.normal) && args.Length > 1)
        //         {
        //             _lastRankOrder = (int)args[1];
        //         }
        //
        //         if (state == RankUIState.joined)
        //         {
        //             VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Success);
        //         }
        //
        //         if (state != RankUIState.locked)
        //         {
        //             var userCnt = state == RankUIState.normal
        //                 ? _rankModel.rankingPortalData.user_item_list.Count
        //                 : _rankModel.settlementRankInfo.user_item_list.Count;
        //             if (_lastRankOrder > userCnt)
        //                 _lastRankOrder = userCnt;
        //         }
        //
        //         RefreshUI(state);
        //     }
        //     else
        //     {
        //         RefreshUI(RankUIState.normal);
        //     }
        // }

        #endregion

        #region Button Event

        private void ButtonCloseClick()
        {
            if (_playingAni)
                return;
            DataDotClick_Leaderboard_quit model = new DataDotClick_Leaderboard_quit();
            DataDotMgr.Collect(model);


            if (_curState == RankUIState.joined || _curState == RankUIState.locked)
            {
                GetController<SettlementController>(ModelConsts.Settlement).ShowNextView( ()=> { Hide(); });
            }
            else {
                Hide();
            }
        }

        private void ButtonDoTaskClick()
        {
            // Notifier.instance.SendNotification(LearnPathCallEvent.ShowChapterProgress);
            var dot = new DataDotClick_Leaderboard_unlock_go();
            dot.CurScene = "Map";
            DataDotMgr.Collect(dot);

            UIManager.instance.GetUI<MultiTabFrameworkUI>(UIConsts.MultiTabHomepage).SwitchTab(TabIndex.Course, false, true);
        }

        #endregion

        #region 其他

        private int GetListIdxByOrder(int order)
        {
            for (var i = 0; i < _user_item.Count; i++)
            {
                if (_user_item[i].user_order == order)
                    return i;
            }

            return 0;
        }

        private void PlayRankChangedAni(int current_order, int userCnt)
        {
            if (_curState == RankUIState.joined)
            {
                if (_rankModel.settlementRankInfo.RankChangeType == PB_RankingChangeTypeForTrigger.PB_RANKING_CHANGE_TYPE_FOR_TRIGGER_MAINTAIN)
                    return;
            }

            var selfID = GetModel<MainModel>(ModelConsts.Main).userID;
            _playingAni = current_order != _lastRankOrder &&
                          _user_item.Count >= current_order &&
                          _user_item.Count >= _lastRankOrder;
            if (!_playingAni)
                return;
            _comRankContent.listCard.touchable = false;
            _preOrderIdx = GetListIdxByOrder(_lastRankOrder);
            _curOrderIdx = GetListIdxByOrder(current_order);
            var scrollStartIdx = 0;
            var startLocalIdx = 0;
            if (_lastRankOrder <= 3)
            {
                //排名前三 移到第一
                scrollStartIdx = GetListIdxByOrder(1);
                startLocalIdx = _lastRankOrder;
            }
            else if (_lastRankOrder >= userCnt - 3)
            {
                //排名后三 移到后三-5
                var tmpOrder = userCnt - 5;
                var order = tmpOrder > 0 ? tmpOrder : 1;
                scrollStartIdx = GetListIdxByOrder(order);
                startLocalIdx = _lastRankOrder - order + 1;
            }
            else
            {
                //其他滑倒自己居中（当前列表里第4）
                scrollStartIdx = GetListIdxByOrder(_lastRankOrder - 3);
                startLocalIdx = 4;
            }

            var scrollEndIdx = 0;
            var endLocalIdx = 0;
            var changedIdx = 0;
            if (current_order <= 3)
            {
                //排名前三 移到第一
                scrollEndIdx = GetListIdxByOrder(1);
                endLocalIdx = _curOrderIdx;
            }
            else if (current_order >= userCnt - 3)
            {
                //排名后三 移到后三-5
                var tmpOrder = userCnt - 5;
                var order = tmpOrder > 0 ? tmpOrder : 1;
                scrollEndIdx = GetListIdxByOrder(order);
                endLocalIdx = _lastRankOrder - order + 1;
            }
            else
            {
                //其他滑倒自己居中（当前列表里第4）
                scrollEndIdx = GetListIdxByOrder(current_order - 3);
                endLocalIdx = 4;
            }

            var startChangeComsNum = 0;
            for (var i = scrollStartIdx; i <= GetListIdxByOrder(_lastRankOrder); i++)
            {
                if (_user_item[i].user_id == 1 || _user_item[i].user_id == 2)
                    startChangeComsNum++;
            }

            var endChangeComsNum = 0;
            for (var i = scrollEndIdx; i <= GetListIdxByOrder(current_order); i++)
            {
                if (_user_item[i].user_id == 1 || _user_item[i].user_id == 2)
                    endChangeComsNum++;
            }

            _comRankContent.listCard.ScrollToView(scrollStartIdx, false, true);
            var idxOffset = endLocalIdx - startLocalIdx;
            var offsetY = idxOffset * 144 + (endChangeComsNum - startChangeComsNum) * 80;
            if (_comRankContent.listCard.ItemIndexToChildIndex(_curOrderIdx) < _comRankContent.listCard.numChildren &&
                _comRankContent.listCard.ItemIndexToChildIndex(_curOrderIdx) >= 0)
            {
                _comRankContent.listCard.GetChildAt(_comRankContent.listCard.ItemIndexToChildIndex(_curOrderIdx)).visible = false;
                var changedComs = new List<GObject>();
                var isUp = true;
                if (current_order > _lastRankOrder)
                {
                    for (var i = _lastRankOrder + 1; i <= current_order; i++)
                    {
                        changedComs.Add(_comRankContent.listCard.GetChildAt(_comRankContent.listCard.ItemIndexToChildIndex(GetListIdxByOrder(i))));
                    }
                }
                else if (current_order < _lastRankOrder)
                {
                    isUp = false;
                    for (var i = current_order; i <= _lastRankOrder - 1; i++)
                    {
                        changedComs.Add(_comRankContent.listCard.GetChildAt(_comRankContent.listCard.ItemIndexToChildIndex(GetListIdxByOrder(i))));
                    }
                }

                if (changedComs.Count > 0)
                {
                    var targetOffset = isUp ? -144 * (changedComs.Count + 1 - idxOffset) : 144 * (changedComs.Count + 1 + idxOffset);
                    var orderFix = isUp ? -1 : 1;
                    foreach (var com in changedComs)
                    {
                        com.visible = false;
                        RankCom originCom = new RankCom();
                        originCom.Construct(com.asCom);
                        RankCom cloneCom = new RankCom();
                        cloneCom.Construct(UIPackage.GetByName("Rank").CreateObject("RankCom").asCom);
                        _comRankContent.comRankAni.AddChildAt(cloneCom.com, _comRankContent.comRankAni.numChildren);
                        cloneCom.com.fairyBatching = true;
                        var pos = _comRankContent.comRankAni.GlobalToLocal(com.LocalToGlobal(Vector2.zero));
                        cloneCom.com.xy = new Vector2(pos.x, pos.y);
                        cloneCom.com.size = originCom.com.size;
                        var order = int.Parse(originCom.tfOrder.text);
                        var idx = GetListIdxByOrder(order + orderFix);
                        var userInfo = new PB_UserItem();
                        userInfo.user_id = _user_item[idx].user_order;
                        userInfo.user_order = _user_item[idx].user_order - orderFix;
                        userInfo.change_type = _user_item[idx].change_type;
                        userInfo.head_url = _user_item[idx].head_url;
                        userInfo.exp = _user_item[idx].exp;
                        userInfo.player_name = _user_item[idx].player_name;
                        userInfo.role_unique_name = _user_item[idx].role_unique_name;
                        SetItem(cloneCom.com, userInfo, selfID, 0);
                        cloneCom.com.visible = true;
                        var risingComFix = 0;
                        if (Math.Abs(GetListIdxByOrder(_user_item[idx].user_order) - GetListIdxByOrder(_user_item[idx].user_order - orderFix)) > 1)
                        {
                            if (isUp)
                            {
                                risingComFix = -80;
                            }
                            else
                            {
                                risingComFix = 80;
                            }
                        }

                        TimerManager.instance.RegisterTimer((i) =>
                        {
                            VibrationManager.Ins.Vibrate(0.6f, 0.8f, 0.4f);
                            cloneCom.com.TweenMoveY(pos.y + targetOffset + risingComFix, 0.4f).OnComplete(() =>
                            {
                                com.visible = true;
                                cloneCom.com.Dispose();
                            });
                        }, 1000);
                    }
                }
            }

            var preCom = _comRankContent.listCard.GetChildAt(_comRankContent.listCard.ItemIndexToChildIndex(_preOrderIdx));
            preCom.visible = false;
            _rankCom = new();
            _rankCom.Construct(UIPackage.GetByName("Rank").CreateObject("RankCom").asCom);
            SetItem(_rankCom.com, _user_item[_curOrderIdx], selfID, 0, _lastRankOrder);
            _comRankContent.comRankAni.AddChildAt(_rankCom.com, _comRankContent.comRankAni.numChildren);
            _rankCom.com.fairyBatching = true;
            var scrollStartPos = _comRankContent.comRankAni.GlobalToLocal(preCom.LocalToGlobal(Vector2.zero));
            _rankCom.com.xy = scrollStartPos;
            _rankCom.com.size = preCom.size;
            TimerManager.instance.RegisterTimer((i) =>
            {
                VibrationManager.Ins.Vibrate(VibrationManager.VibrationType.Vibrate);
                _rankCom.com.TweenMoveY(scrollStartPos.y + offsetY, 0.4f).OnComplete(() =>
                {
                    _rankCom.com.Dispose();
                    _rankCom = null;
                    _playingAni = false;
                    _comRankContent.listCard.touchable = true;
                    _comRankContent.listCard.GetChildAt(_comRankContent.listCard.ItemIndexToChildIndex(_curOrderIdx)).visible = true;
                    if (_comRankContent.listCard.ItemIndexToChildIndex(_preOrderIdx) < _comRankContent.listCard.numChildren &&
                        _comRankContent.listCard.ItemIndexToChildIndex(_preOrderIdx) >= 0)
                    {
                        _comRankContent.listCard.GetChildAt(_comRankContent.listCard.ItemIndexToChildIndex(_preOrderIdx)).visible = true;
                    }
                });
                _comRankContent.listCard.ScrollToView(scrollEndIdx, true, true);
            }, 1000);
        }

        protected void RefreshUI(RankUIState state)
        {
            _curState = state;
            _comRankContent.state.SetSelectedPage(state.ToString());
            _comRankContent.listCard.touchable = true;
            _user_item = _rankModel.GetUserItem();
            _comRankContent.listCard.numItems = _user_item.Count;

            if (!_rankModel.isInited)
            {
                initTimer = TimerManager.instance.RegisterTimer((c) =>
                {
                    if (_rankModel.isInited)
                    {
                        RefreshUI(state);
                        if (string.IsNullOrEmpty(initTimer))
                        {
                            TimerManager.instance.UnRegisterTimer(initTimer);
                            initTimer = null;
                        }
                    }
                }, 300, 5);
                return; //网络初始化没完成时 不做什么处理
            }

            if (_curState == RankUIState.normal || _curState == RankUIState.joined)
            {
                int current_order = _curState == RankUIState.normal ? _rankModel.rankingPortalData.current_order : _rankModel.settlementRankInfo.current_order;
                int userCnt = _curState == RankUIState.normal
                    ? _rankModel.rankingPortalData.user_item_list.Count
                    : _rankModel.settlementRankInfo.user_item_list.Count;
                LocalCfgMgr.instance.SetGlobal("last_rank", current_order.ToString());
                LocalCfgMgr.instance.SetGlobal("last_rank_account", GetModel<MainModel>(ModelConsts.Main).userID.ToString());

                if (_lastRankOrder > 0 && _lastRankOrder > current_order && current_order <= _user_item.Count && _lastRankOrder <= _user_item.Count)
                    PlayRankChangedAni(current_order, userCnt);
                else
                {
                    if (current_order <= 3)
                    {
                        _comRankContent.listCard.ScrollToView(1, false, true);
                    }
                    else
                    {
                        _comRankContent.listCard.ScrollToView(GetListIdxByOrder(current_order - 3), false, true);
                    }
                }
            }

            if (state == RankUIState.locked)
            {
                var _data = _rankModel.rankingPortalData;
                var key = _data.current_level.level_id;
                _comRankContent.tfTitle.SetKey(Cfg.T.TBRankItems[key].languageKey);
                _comRankContent.tfLockDesc.SetKeyArgs("ui_rank_lock_rank_join_desc", _data.last_unlock_count);
                _comRankContent.btnDotask.SetKey("ui_rank_btn_lock");
                SetMedal();
            }

            if (state == RankUIState.joined)
            {
                _comRankContent.tfUnlocked.text =
                    _rankModel.settlementRankInfo.RankChangeType == PB_RankingChangeTypeForTrigger.PB_RANKING_CHANGE_TYPE_FOR_TRIGGER_JOIN
                        ? I18N.inst.MoStr("ui_rank_unlock_rank_join_desc")
                        : _rankModel.settlementRankInfo.RankChangeType == PB_RankingChangeTypeForTrigger.PB_RANKING_CHANGE_TYPE_FOR_TRIGGER_MAINTAIN
                            ? I18N.inst.MoStr("ui_rank_will_rise_desc")
                            : I18N.inst.MoStr("ui_rank_rise_desc");
                _comRankContent.btnUnlock.SetKey("ui_rank_notify_btn");
                _comRankContent.imgLoader.url = Cfg.T.TBRankItems[_rankModel.settlementRankInfo.current_level.level_id].iconPath;
                SetMedalVisible(false);
            }

            if (state == RankUIState.normal)
            {
                var _data = _rankModel.rankingPortalData;
                var key = _data.current_level.level_id;
                _comRankContent.tfTitle.SetKey(Cfg.T.TBRankItems[key].languageKey);
                _comRankContent.tfTimeleft.SetKey("ui_rank_time_left_title");
                _comRankContent.tfUnit.SetKey("ui_rank_time_left_day");
                _comRankContent.tfToday.SetKey("ui_rank_changed_title");
                _comRankContent.tfPlace.SetKey("ui_rank_changed_place");
                int place = _data.pre_order - _data.current_order;
                Color nowColor;
                if (place == 0)
                {
                    ColorUtility.TryParseHtmlString("#888888", out nowColor);
                    _comRankContent.ctrPlace.selectedIndex = 2;
                }
                else if (place > 0)
                {
                    ColorUtility.TryParseHtmlString("#21D089", out nowColor);
                    _comRankContent.ctrPlace.selectedIndex = 0;
                }
                else
                {
                    ColorUtility.TryParseHtmlString("#FF007A", out nowColor);
                    _comRankContent.ctrPlace.selectedIndex = 1;
                }

                _comRankContent.tfPlaceNum.text = Math.Abs(place).ToString();
                _comRankContent.tfPlaceNum.color = nowColor;
                _comRankContent.tfPlace.color = nowColor;
                SetMedal();
            }
        }

        private void SetMedalVisible(bool visible)
        {
            _comRankContent.comRankMedel1.com.visible = visible;
            _comRankContent.comRankMedel2.com.visible = visible;
            _comRankContent.comRankMedel3.com.visible = visible;
            _comRankContent.comRankMedel4.com.visible = visible;
        }

        private void SetMedal()
        {
            SetMedalVisible(true);
            var offsetX = (MEDAL_BIG_SIZE - MEDAL_NORMAL_SIZE) / 2;
            var interval = (_comRankContent.imgMedalBg.width - 3 * MEDAL_NORMAL_SIZE - MEDAL_BIG_SIZE) / 3;
            var levelId = _rankModel.rankingPortalData.current_level.level_id;
            var startPos = _comRankContent.imgMedalBg.xy;

            if (levelId == 1)
            {
                _comRankContent.comRankMedel1.imgLoaderBig.url = Cfg.T.TBRankItems[levelId].iconPath;
                _comRankContent.comRankMedel1.imgLoaderBig.visible = true;
                _comRankContent.comRankMedel2.imgLoaderBig.visible = false;
                _comRankContent.comRankMedel2.imgLoaderSmall.visible = false;
                _comRankContent.comRankMedel3.imgLoaderBig.visible = false;
                _comRankContent.comRankMedel3.imgLoaderSmall.visible = false;
                _comRankContent.comRankMedel4.imgLoaderBig.visible = false;
                _comRankContent.comRankMedel4.imgLoaderSmall.visible = false;
                _comRankContent.comRankMedel1.com.xy = startPos;
                _comRankContent.comRankMedel2.com.xy = new Vector2(startPos.x + MEDAL_BIG_SIZE + interval - offsetX, startPos.y);
                _comRankContent.comRankMedel3.com.xy = new Vector2(startPos.x + MEDAL_BIG_SIZE + MEDAL_NORMAL_SIZE + interval * 2 - offsetX, startPos.y);
                _comRankContent.comRankMedel4.com.xy = new Vector2(startPos.x + MEDAL_BIG_SIZE + MEDAL_NORMAL_SIZE * 2 + interval * 3 - offsetX, startPos.y);
            }
            else if (levelId == 2)
            {
                _comRankContent.comRankMedel1.imgLoaderSmall.url = Cfg.T.TBRankItems[levelId - 1].iconPath;
                _comRankContent.comRankMedel1.imgLoaderSmall.visible = true;
                _comRankContent.comRankMedel1.imgLoaderBig.visible = false;
                _comRankContent.comRankMedel2.imgLoaderBig.url = Cfg.T.TBRankItems[levelId].iconPath;
                _comRankContent.comRankMedel2.imgLoaderBig.visible = true;
                _comRankContent.comRankMedel2.imgLoaderSmall.visible = false;
                _comRankContent.comRankMedel3.imgLoaderBig.visible = false;
                _comRankContent.comRankMedel3.imgLoaderSmall.visible = false;
                _comRankContent.comRankMedel4.imgLoaderBig.visible = false;
                _comRankContent.comRankMedel4.imgLoaderSmall.visible = false;
                _comRankContent.comRankMedel1.com.xy = new Vector2(startPos.x - offsetX, startPos.y);
                _comRankContent.comRankMedel2.com.xy = new Vector2(startPos.x + MEDAL_NORMAL_SIZE + interval, startPos.y);
                _comRankContent.comRankMedel3.com.xy = new Vector2(startPos.x + MEDAL_NORMAL_SIZE + MEDAL_BIG_SIZE + interval * 2 - offsetX, startPos.y);
                _comRankContent.comRankMedel4.com.xy = new Vector2(startPos.x + MEDAL_NORMAL_SIZE * 2 + MEDAL_BIG_SIZE + interval * 3 - offsetX, startPos.y);
            }
            else if (levelId == 3)
            {
                _comRankContent.comRankMedel1.imgLoaderSmall.url = Cfg.T.TBRankItems[levelId - 2].iconPath;
                _comRankContent.comRankMedel1.imgLoaderSmall.visible = true;
                _comRankContent.comRankMedel1.imgLoaderBig.visible = false;
                _comRankContent.comRankMedel2.imgLoaderSmall.url = Cfg.T.TBRankItems[levelId - 1].iconPath;
                _comRankContent.comRankMedel2.imgLoaderSmall.visible = true;
                _comRankContent.comRankMedel2.imgLoaderBig.visible = false;
                _comRankContent.comRankMedel3.imgLoaderBig.url = Cfg.T.TBRankItems[levelId].iconPath;
                _comRankContent.comRankMedel3.imgLoaderBig.visible = true;
                _comRankContent.comRankMedel3.imgLoaderSmall.visible = false;
                _comRankContent.comRankMedel4.imgLoaderBig.visible = false;
                _comRankContent.comRankMedel4.imgLoaderSmall.visible = false;
                _comRankContent.comRankMedel1.com.xy = new Vector2(startPos.x - offsetX, startPos.y);
                _comRankContent.comRankMedel2.com.xy = new Vector2(startPos.x + MEDAL_NORMAL_SIZE + interval - offsetX, startPos.y);
                _comRankContent.comRankMedel3.com.xy = new Vector2(startPos.x + MEDAL_NORMAL_SIZE * 2 + interval * 2, startPos.y);
                _comRankContent.comRankMedel4.com.xy = new Vector2(startPos.x + MEDAL_NORMAL_SIZE * 2 + MEDAL_BIG_SIZE + interval * 3 - offsetX, startPos.y);
            }
            else
            {
                _comRankContent.comRankMedel1.imgLoaderSmall.url = Cfg.T.TBRankItems[levelId - 3].iconPath;
                _comRankContent.comRankMedel1.imgLoaderSmall.visible = true;
                _comRankContent.comRankMedel1.imgLoaderBig.visible = false;
                _comRankContent.comRankMedel2.imgLoaderSmall.url = Cfg.T.TBRankItems[levelId - 2].iconPath;
                _comRankContent.comRankMedel2.imgLoaderSmall.visible = true;
                _comRankContent.comRankMedel2.imgLoaderBig.visible = false;
                _comRankContent.comRankMedel3.imgLoaderSmall.url = Cfg.T.TBRankItems[levelId - 1].iconPath;
                _comRankContent.comRankMedel3.imgLoaderSmall.visible = true;
                _comRankContent.comRankMedel3.imgLoaderBig.visible = false;
                _comRankContent.comRankMedel4.imgLoaderBig.url = Cfg.T.TBRankItems[levelId].iconPath;
                _comRankContent.comRankMedel4.imgLoaderBig.visible = true;
                _comRankContent.comRankMedel4.imgLoaderSmall.visible = false;
                _comRankContent.comRankMedel1.com.xy = new Vector2(startPos.x - offsetX, startPos.y);
                _comRankContent.comRankMedel2.com.xy = new Vector2(startPos.x + MEDAL_NORMAL_SIZE + interval - offsetX, startPos.y);
                _comRankContent.comRankMedel3.com.xy = new Vector2(startPos.x + MEDAL_NORMAL_SIZE * 2 + interval * 2 - offsetX, startPos.y);
                _comRankContent.comRankMedel4.com.xy = new Vector2(startPos.x + MEDAL_NORMAL_SIZE * 3 + interval * 3, startPos.y);
            }
        }

        protected void Update(int interval)
        {
            if (!_rankModel.isInited)
            {
                return;
            }
            //  有可能update时 rank相关ui压根没有被初始化和展示
            if (_comRankContent == null || !this.isShow)
            {
                return;
            }
            if (_rankCom != null)
            {
                _rankCom.com.fairyBatching = true;
            }
        
            if (_curState != RankUIState.normal)
                return;


        
            if (Time.time - _lastUpdateTime > 0.2f)
            {
                _lastUpdateTime = Time.time;
        
                long endTimeMillis = _rankModel.rankingPortalData.left_timestamp;
        
        
                // 计算剩余时间（秒）
                long remainingMillis = endTimeMillis - TimeExt.serverTimestamp / 1000;
                if (remainingMillis < 60)
                {
                    _comRankContent.ctrTime.selectedPage = "red";
                    _comRankContent.tfNum.text = "1";
                    _comRankContent.tfUnit.SetKey("ui_rank_time_left_minute");;
                    _comRankContent.tfNum.color = TimeColorRed;
                    _comRankContent.tfUnit.color = TimeColorRed;
                    return;
                }
        
                // 计算天、小时、分钟
                long totalSeconds = remainingMillis;
                long days = totalSeconds / (24 * 3600);
                long hours = (totalSeconds % (24 * 3600)) / 3600;
                long minutes = (totalSeconds % 3600) / 60;
                if (days > 0)
                {
                    _comRankContent.ctrTime.selectedPage = "purple";
                    _comRankContent.tfNum.text = days.ToString();
                    _comRankContent.tfUnit.SetKey("ui_rank_time_left_day");
                    _comRankContent.tfNum.color = TimeColorPurple;
                    _comRankContent.tfUnit.color = TimeColorPurple;
                }
                else if (hours > 0)
                {
                    _comRankContent.ctrTime.selectedPage = "purple";
                    _comRankContent.tfNum.text = hours.ToString();
                    _comRankContent.tfUnit.SetKey("ui_rank_time_left_hour");
                    _comRankContent.tfNum.color = TimeColorPurple;
                    _comRankContent.tfUnit.color = TimeColorPurple;
                }
                else
                {
                    _comRankContent.tfNum.color = TimeColorRed;
                    _comRankContent.tfUnit.color = TimeColorRed;
                    _comRankContent.ctrTime.selectedPage = "red";
                    _comRankContent.tfNum.text = minutes.ToString();
                    _comRankContent.tfUnit.SetKey("ui_rank_time_left_minute");
                }
            }
        }


        //列表回调
        private void RenderListCardItem(int index, GObject obj)
        {
            if (index >= _user_item.Count)
            {
                //VFDebug.LogError($"goal数据索引超了index:{index},goalInfo count:{_rankInfo.goalInfo.chapter_list.Count}");
                return;
            }

            PB_UserItem userInfo = _user_item[index];
            var user_id = GetModel<MainModel>(ModelConsts.Main).userID;

            if (_playingAni)
            {
                obj.visible = !(index == _preOrderIdx || index == _curOrderIdx) && userInfo.user_id != 3;
            }
            else
            {
                obj.visible = userInfo.user_id != 3;
            }

            if (index == 0 || index == _user_item.Count - 1)
                return;
            else if (userInfo.user_id == 1)
            {
                obj.asCom.GetChild("tfRising").asTextField.SetKey("ui_rank_rising_area");
                return;
            }
            else if (userInfo.user_id == 2)
            {
                obj.asCom.GetChild("tfFalling").asTextField.SetKey("ui_rank_falling_area");
                return;
            }
            else if (userInfo.user_id == 3)
            {
                return;
            }

            SetItem(obj, userInfo, user_id, index);
        }

        private void SetItem(GObject obj, PB_UserItem userInfo, long selfID, int index, int preOrder = 0)
        {
            RankCom rankCom = new RankCom();
            rankCom.Construct(obj.asCom);

            Color nowColor;
            Color CardColor;

            if (preOrder != 0 && preOrder <= 3)
            {
                var idx = preOrder - 1;
                ColorUtility.TryParseHtmlString(_orderColor[idx], out nowColor);
                rankCom.ctrOrder.selectedIndex = idx;
                ColorUtility.TryParseHtmlString(_cardColor[idx], out CardColor);
            }
            else if (preOrder == 0 && userInfo.user_order <= 3)
            {
                var idx = userInfo.user_order - 1;
                ColorUtility.TryParseHtmlString(_orderColor[idx], out nowColor);
                rankCom.ctrOrder.selectedIndex = idx;
                ColorUtility.TryParseHtmlString(_cardColor[idx], out CardColor);
            }
            else
            {
                ColorUtility.TryParseHtmlString(_defaultColor, out nowColor);
                rankCom.ctrOrder.selectedIndex = 3;
                ColorUtility.TryParseHtmlString("#F0EBFF", out CardColor);
            }

            var user_id = selfID;
            rankCom.tfOrder.color = nowColor;

            if (user_id == userInfo.user_id)
            {
                if (userInfo.user_order > 3)
                {
                    ColorUtility.TryParseHtmlString(_myColor, out nowColor);
                    rankCom.tfOrder.color = nowColor;
                }
            }
            else
            {
                ColorUtility.TryParseHtmlString(_defaultColor, out nowColor);

                ColorUtility.TryParseHtmlString("#FFFFFF", out CardColor);
            }

            rankCom.imgCard.color = CardColor;
            //chapterCom.tfChapterIndex.SetKeyArgs("learnPath_chapter_name", briefInfo.chapter_index+1);
            rankCom.tfOrder.text = preOrder != 0 ? preOrder.ToString() : userInfo.user_order.ToString();


            var nameTextF = rankCom.userName.GetChild("userName").asRichTextField;
            nameTextF.textFormat.size = 32;
            nameTextF.text = userInfo.player_name.ToString();
            //nameTextF.color = nowColor;
            rankCom.tfExp.text = userInfo.exp + "xp";
            rankCom.tfExp.color = nowColor;

            if (userInfo.user_order > 3 && user_id != userInfo.user_id)
            {
                if (userInfo.change_type == PB_RankingChangeType.PB_RANKING_CHANGE_TYPE_UPGRADE)
                {
                    ColorUtility.TryParseHtmlString("#21D089", out nowColor);
                }
                else if (userInfo.change_type == PB_RankingChangeType.PB_RANKING_CHANGE_TYPE_DOWNGRADE)
                {
                    ColorUtility.TryParseHtmlString("#FF007A", out nowColor);
                }

                rankCom.tfOrder.color = nowColor;
            }

            SetHead(rankCom, userInfo);
            SetFrame(rankCom, userInfo);
            SetName(rankCom, userInfo, nowColor);
            rankCom.btnMask.data = index;

            AddUIEvent(rankCom.btnMask.onClick, ClickHead);
        }

        private void SetHead(RankCom rankcom, PB_UserItem userInfo)
        {
            var loader = rankcom.headFrame.GetChild("headLoader").asCom.GetChild("loader").asLoader;
            if (userInfo.head_item_type == PB_HeadItemType.HEAD_ITEM_TYPE_MATERIA)
            {
                loader.size = new Vector2(128, 128);
                if (Cfg.T.TBItemTable.DataMap.ContainsKey(userInfo.head_item_id))
                {
                    var cfg = Cfg.T.TBItemTable.Get(userInfo.head_item_id);
                    loader.url = cfg.iconNormalizePath;
                }
            }
            else if (userInfo.head_item_type == PB_HeadItemType.HEAD_ITEM_TYPE_AVATAR)
            {
                loader.size = new Vector2(160, 160);
                if (Cfg.T.TBRoleInfo.DataMap.ContainsKey(userInfo.head_item_id.ToString()))
                {
                    var cfg = Cfg.T.TBRoleInfo.Get(userInfo.head_item_id.ToString());
                    loader.url = cfg.headUrl;
                }
            }
            else
            {
                loader.size = new Vector2(160, 160);
                VFDebug.Log("【Rank头像】 出现未知类型");
                loader.url = userInfo.head_url;
            }
        }

        private void SetFrame(RankCom rankcom, PB_UserItem userInfo)
        {
            var ctrl = rankcom.headFrame.GetController("effectCtrl");
            var id = userInfo.user_dress_up_data?.frame_id;
            if (string.IsNullOrEmpty(id))
            {
                var profileModel = GetModel<ProfileModel>(ModelConsts.Profile);
                id = profileModel.defaultFrameId;
            }

            if (!string.IsNullOrEmpty(id) && Cfg.T.TBItemTable.DataMap.ContainsKey(long.Parse(id)))
            {
                var cfg = Cfg.T.TBItemTable.Get(long.Parse(id));
                rankcom.headFrame.GetChild("Frame").asLoader.url = cfg.iconNormalizePath;
                if (cfg.effectPath.Count > 0)
                {
                    var spineLoader = rankcom.headFrame.GetChild("effect").asLoader3D;
                    spineLoader.url = cfg.iconNormalizePath;
                    spineLoader.animationName = cfg.effectPath[0];
                    spineLoader.loop = true;
                }

                ctrl.selectedIndex = cfg.effectPath.Count > 0 ? 1 : 0;
            }
            else
            {
                ctrl.selectedIndex = 0;
            }
        }

        private void SetName(RankCom rankcom, PB_UserItem userInfo, Color nowColor)
        {
            var textF = rankcom.userName.GetChild("userName").asRichTextField;
            var ctrl = rankcom.userName.GetController("effectCtrl");
            var id = userInfo.user_dress_up_data?.name_style_id;
            //id = "n0003";
            if (!string.IsNullOrEmpty(id) && Cfg.T.TBItemTable.DataMap.ContainsKey(long.Parse(id)))
            {
                var cfg = Cfg.T.TBItemTable.Get(long.Parse(id));
                textF.textFormat.outline = 0.5f;
                textF.textFormat.faceDilate = 0.5f;
                textF.textFormat.outlineSoftness = 0.5f;
                var color = Color.white;
                ColorUtility.TryParseHtmlString(cfg.textOutlineColor, out color);
                textF.textFormat.outlineColor = color;
                textF.text = string.Format("<font color={1}>{0}</font>", userInfo.player_name, cfg.textColor);
                if (cfg.effectPath.Count > 0)
                {
                    var left = rankcom.userName.GetChild("left").asLoader3D;
                    left.url = cfg.iconNormalizePath;
                    left.animationName = cfg.effectPath[0];
                    left.loop = true;
                    var right = rankcom.userName.GetChild("right").asLoader3D;
                    right.url = cfg.iconNormalizePath;
                    right.animationName = cfg.effectPath[1];
                    right.loop = true;
                }

                var left2 = rankcom.userName.GetChild("left2").asLoader;
                left2.scale = Vector2.one * 0.8f;

                var right2 = rankcom.userName.GetChild("right2").asLoader;
                right2.scale = Vector2.one * 0.8f;
                ctrl.selectedIndex = cfg.effectPath.Count > 0 ? 1 : 0;
            }
            else
            {
                //textF.color = nowColor;
                //textF.textFormat.outlineColor = Color.white;
                var newTextFomat = new TextFormat();
                newTextFomat.font = textF.textFormat.font;
                newTextFomat.size = textF.textFormat.size;
                newTextFomat.color = textF.textFormat.color;
                newTextFomat.outlineSoftness = 0f;
                newTextFomat.faceDilate = 0;
                newTextFomat.outline = 0f;

                textF.textFormat = newTextFomat;
                textF.text = userInfo.player_name;
                textF.visible = false;
                textF.visible = true;
                ctrl.selectedIndex = 0;
            }
        }

        //点击头像
        private void ClickHead(EventContext context)
        {
            if (_curState != RankUIState.normal)
                return;
            if (_playingAni)
                return;
            var myid = GetModel<MainModel>(ModelConsts.Main).userID;
            int index = (int)((context.sender as GButton).data);
            long uid = _user_item[index].user_id;
            var pc = ControllerManager.instance.GetController(ModelConsts.Profile) as ProfileController;
            if (myid == uid)
            {
                pc.OnClickSelf();
            }
            else
            {
                pc.OnClickOther(uid);//, _user_item[index].player_name, _user_item[index].role_unique_name, null);
            }
        }

        //根据类型获取不同的Item
        private string GetListItemResource(int index)
        {
            var msg = _user_item[index];
            if (index == 0)
                return "ui://Rank/placeHolderCom";
            else if (index == _user_item.Count - 1)
            {
                return "ui://Rank/placeHolderFootCom";
            }
            else if (msg.user_id == 1)
            {
                return "ui://Rank/RisingCom";
            }
            else if (msg.user_id == 2)
            {
                return "ui://Rank/DownCom";
            }

            return "ui://Rank/RankCom";
        }


        protected override void OnHide()
        {
            _comRankContent.listCard.numItems = 0;
        }

        //滑动结束监听
        void OnScrollMoveEnd()
        {
            DataDotSwitch_Chapter_list model = new DataDotSwitch_Chapter_list();
            DataDotMgr.Collect(model);
        }
        #endregion
        //====================================外部调用
    }
}