/*
 ****************************************************
 * 作者：CuiMengLin
 * 创建时间：2024/09/12 14:24:52 星期四
 * 功能：Nothing
 ****************************************************
 */

using System;
using System.Collections;
using FairyGUI;
using Msg.basic;
using Msg.dialog_task;
using ScriptsHot.Game.Modules.Guide;
using UIBind.Progress;
using UnityEngine;

namespace ScriptsHot.Game.Modules.Task
{
    public enum ProgressFreeTalkTipsState
    {
        None,
        Setting,
        //Finish
    }

    public class ProgressFreeTalkTaskUI: BaseUI<UIBind.Progress.ProgressFreeTalkTaskPanel>
    {
        public override void OnBackBtnClick()
        {
            // var mainHeaderUI = GetUI<MainHeaderUI>(UIConsts.MainHeader);
            // if (mainHeaderUI.isShow)
            // {
            //     mainHeaderUI.OnBtnLeftTopClick();
            // }
        }
        
        public ProgressFreeTalkTaskUI(string name) : base(name){ }
        public override string uiLayer => UILayerConsts.Home;
        
        protected override bool isFullScreen => true;

        private readonly int _progressStartNum = 5;//最开始要有起始值
        //private readonly int _progressEndNum = 99;
        private ProgressFreeTalkTipsState _curState = ProgressFreeTalkTipsState.None;
        private bool _isMax = false;
        private Coroutine _co;
        private Action _changeCallback = null;
        protected override void OnInit(GComponent uiCom)
        {
            // 引导：在建议按钮上添加标记
            var guideController = GetController<GuideController>(ModelConsts.Guide);
            guideController.RegisterMark(GuideConst.ChatFreeGuideProgress, ui.CompFreeProgress.com);
            guideController.RegisterMark(GuideConst.ChatFreeGuideProgressTarget, ui.CompFreeProgress.target);
            guideController.RegisterMark(GuideConst.ChatFreeGuideProgressParent, ui.com);
        }
        
        protected override void OnShow()
        {
            if (args.Length != 1)
            {
                VFDebug.LogError("参数不对");
                return;
            }

            ui.CompFreeProgress.target.visible = false;
            ui.CompFreeProgress.comProgressFreeTalk.max = 100;
            ui.CompFreeProgress.comProgressFreeBack.max = 100;
            ui.CompFreeProgress.comProgressFreeTalk.value = _progressStartNum;
            ui.CompFreeProgress.comProgressFreeBack.value = _progressStartNum;
            ui.CompFreeProgress.leftLv.spineHeart.animationName = "3";
            ui.CompFreeProgress.rightLv.spineHeart.animationName = "1";
            _isMax = false;
            ChangeTipsState(ProgressFreeTalkTipsState.None);
            SetProgressCallback(null);
            ChangeValue((PB_DialogProgressInfo)args[0], true);
            ui.tfSettingTips.SetKey("ui_free_help_mode_tip");
            ui.tfFinishTIps.SetKey("ui_free_progress_finish_tips");
            
            ui.Reset.Play();
        }
        
        private PB_DialogProgressInfo _lastProgressInfo;

        public void SetProgressCallback(Action callback)
        {
            _changeCallback = callback;
        }

        public void ChangeValue(PB_DialogProgressInfo progressInfo, bool init = false)
        {
            ui.CompFreeProgress.spineStar.visible = false;
            
            if (init)
            {
                _lastProgressInfo = null;
                ui.CompFreeProgress.tfProgress.SetVar("cur", progressInfo.cur_progress.ToString())
                    .SetVar("max", progressInfo.total_progress.ToString()).FlushVars();
                
                float endValue = _progressStartNum + progressInfo.cur_progress * 1.0f / progressInfo.total_progress *
                    (100 - _progressStartNum);
                ui.CompFreeProgress.comProgressFreeTalk.max = 100;
                ui.CompFreeProgress.comProgressFreeBack.max = 100;
                ui.CompFreeProgress.comProgressFreeTalk.value = endValue;
                ui.CompFreeProgress.comProgressFreeBack.value = endValue;
                
                _lastProgressInfo = progressInfo;
                ui.max.selectedIndex = 0;
                ui.CompFreeProgress.leftLv.max.selectedIndex = 0;
                ui.CompFreeProgress.rightLv.max.selectedIndex = 0;
                ui.CompFreeProgress.leftLv.txLv.SetVar("lv", progressInfo.cur_level.ToString()).FlushVars();
                ui.CompFreeProgress.rightLv.txLv.SetVar("lv", progressInfo.next_level.ToString()).FlushVars();
                if (progressInfo.is_upper_limit)
                {
                    ui.CompFreeProgress.leftLv.max.selectedIndex = 1;
                    ui.max.selectedIndex = 1;
                    return;
                }
                if (!progressInfo.is_upper_limit && progressInfo.is_next_upper_limit)
                    ui.CompFreeProgress.rightLv.max.selectedIndex = 1;
                return;
            }
            
            if (_lastProgressInfo.is_upper_limit)
                return;
            
            if (_co != null)
            {
                Timers.inst.StopCoroutine(_co);
                _co = null;
            }

            ui.CompFreeProgress.tfProgress.SetVar("cur", _lastProgressInfo.cur_progress.ToString())
                .SetVar("max", _lastProgressInfo.total_progress.ToString()).FlushVars();
            bool add = _lastProgressInfo.cur_level < progressInfo.cur_level ||
                       (_lastProgressInfo.cur_level == progressInfo.cur_level &&
                        _lastProgressInfo.cur_progress < progressInfo.cur_progress);
            // SoundManger.instance.PlayUI(add ? "free_progress_add" : "progress_back");

            if (_lastProgressInfo.cur_progress == progressInfo.cur_progress && _lastProgressInfo.cur_level == progressInfo.cur_level)
                return;
            
            ui.CompFreeProgress.compChange.com.visible = true;
            ui.CompFreeProgress.target.visible = true;
            _co = Timers.inst.StartCoroutine(ShowProgressChange(progressInfo, add));
            
            _lastProgressInfo = progressInfo;
        }

        private IEnumerator ShowProgressChange(PB_DialogProgressInfo progressInfo, bool add)
        {
            int curLv = _lastProgressInfo.cur_level;
            int initValue = add ? _progressStartNum : 100;
            float initCompValue = ui.CompFreeProgress.comProgressFreeTalk.x +
                                ui.CompFreeProgress.comProgressFreeTalk.actualWidth *
                                (add ? _progressStartNum * 1.0f / 100 : 1);
            int step = add ? 1 : -1;

            //刷新compChange组件内容
            CompChange compChange = ui.CompFreeProgress.compChange;
            float lastPosX = ui.CompFreeProgress.comProgressFreeTalk.x +
                             ui.CompFreeProgress.comProgressFreeTalk.actualWidth *
                             ((_progressStartNum + _lastProgressInfo.cur_progress * 1.0f /
                                 _lastProgressInfo.total_progress * (100 - _progressStartNum)) / 100);
            compChange.com.position = new Vector3(lastPosX, compChange.com.position.y, 0);
            compChange.add.selectedIndex = add ? 1 : 0;
            compChange.tfNum.SetVar("num",Math.Abs(progressInfo.incr_progress).ToString()).FlushVars();
            
            if (curLv == progressInfo.cur_level) //未跨等级
            {
                if (_lastProgressInfo.is_next_upper_limit && !_lastProgressInfo.is_upper_limit && !progressInfo.is_upper_limit)
                    ui.CompFreeProgress.rightLv.max.selectedIndex = 1;//99-99
                
                float lastValue = _progressStartNum +
                                  _lastProgressInfo.cur_progress * 1.0f / _lastProgressInfo.total_progress *
                                  (100 - _progressStartNum);
                float endValue = _progressStartNum + progressInfo.cur_progress * 1.0f /
                    progressInfo.total_progress * (100 - _progressStartNum);
                ui.CompFreeProgress.comProgressFreeTalk.value = lastValue;
                ChangeProgress(curLv, progressInfo.cur_progress, progressInfo.total_progress, endValue, add, 0.8f);
                yield return new WaitForSeconds(0.8f);
            }
            else //跨等级了
            {
                while (curLv != progressInfo.cur_level)
                {
                    if (!progressInfo.is_upper_limit && progressInfo.is_next_upper_limit &&
                        curLv == progressInfo.cur_level ||
                        progressInfo.is_upper_limit && curLv + 1 == progressInfo.cur_level)
                        ui.CompFreeProgress.rightLv.max.selectedIndex = 1; //99级

                    int endValue = add ? 100 : _progressStartNum;
                    ui.CompFreeProgress.comProgressFreeTalk.value = curLv == _lastProgressInfo.cur_level
                        ? _progressStartNum + _lastProgressInfo.cur_progress * 1.0f / _lastProgressInfo.total_progress *
                        (100 - _progressStartNum)
                        : initValue;

                    float compPosX = curLv == _lastProgressInfo.cur_level ? lastPosX : initCompValue;
                    compChange.com.position = new Vector3(compPosX, compChange.com.position.y, 0);
                    ChangeProgress(curLv, endValue, 100, endValue, add);
                    yield return new WaitForSeconds(add ? 1.9f : 0.4f); //1.5动画 + 0.4效果
                    curLv += step;
                }

                if (curLv == progressInfo.cur_level)
                {
                    if (progressInfo.is_upper_limit)
                    {
                        ui.max.selectedIndex = 1;
                        ui.CompFreeProgress.leftLv.max.selectedIndex = 1;
                    }
                    else
                    {
                        if (progressInfo.is_next_upper_limit)
                            ui.CompFreeProgress.rightLv.max.selectedIndex = 1; //99级
                        float endValue = _progressStartNum + progressInfo.cur_progress * 1.0f /
                            progressInfo.total_progress * (100 - _progressStartNum);
                        ui.CompFreeProgress.comProgressFreeTalk.value = initValue;
                        compChange.com.position = new Vector3(initCompValue, compChange.com.position.y, 0);
                        ChangeProgress(curLv, progressInfo.cur_progress, progressInfo.total_progress, endValue, add);
                        yield return new WaitForSeconds(0.4f);
                    }
                }
            }

            if (add && !progressInfo.is_upper_limit)
            {
                float x = ui.CompFreeProgress.comProgressFreeTalk.x +
                          ui.CompFreeProgress.comProgressFreeTalk.actualWidth *
                          (progressInfo.cur_progress * 1.0f / progressInfo.total_progress);
                ui.CompFreeProgress.spineStar.x = x;
                ui.CompFreeProgress.spineStar.visible = true;
                ui.CompFreeProgress.spineStar.spineAnimation.AnimationState.ClearListenerNotifications();
                ui.CompFreeProgress.spineStar.spineAnimation.AnimationState.SetAnimation(0, "animation", false)
                    .Complete += (t) =>
                {
                    ui.CompFreeProgress.spineStar.visible = false;
                    compChange.com.visible = false;
                };
            }
            else
                compChange.com.visible = false;
            _changeCallback?.Invoke();
            ui.CompFreeProgress.tfProgress.SetVar("cur", progressInfo.cur_progress.ToString())
                .SetVar("max", progressInfo.total_progress.ToString()).FlushVars();
        }

        private void ChangeProgress(int curLevel, int endValueProgress, int totalProgress, float endValue, bool add ,float time = 0.4f)
        {
            float endPosX = ui.CompFreeProgress.comProgressFreeTalk.x + ui.CompFreeProgress.comProgressFreeTalk.actualWidth *
                ((_progressStartNum + endValueProgress * 1.0f / totalProgress * (100 - _progressStartNum)) / 100);
                //(_progressStartNum + endValueProgress * 1.0f / totalProgress);
            
            ui.CompFreeProgress.comProgressFreeBack.value = ui.CompFreeProgress.comProgressFreeTalk.value;
            ui.CompFreeProgress.leftLv.txLv.SetVar("lv", curLevel.ToString()).FlushVars();
            ui.CompFreeProgress.rightLv.txLv.SetVar("lv", (curLevel + 1).ToString()).FlushVars();
            ui.CompFreeProgress.comProgressFreeTalk.TweenValue(endValue, time).OnComplete(() =>
            {
                if (endValueProgress == totalProgress && add)
                {
                    // SoundManger.instance.PlayUI("free_level_up");
                    ui.CompFreeProgress.rightLv.spineHeart.spineAnimation.AnimationState.ClearListenerNotifications();
                    ui.CompFreeProgress.rightLv.spineHeart.spineAnimation.AnimationState.SetAnimation(0, "2", false).Complete +=
                        (t) => { ui.CompFreeProgress.rightLv.spineHeart.spineAnimation.AnimationState.SetAnimation(0, "1", false); };
                }
                ui.CompFreeProgress.comProgressFreeBack.TweenValue(endValue, time);
            });
            ui.CompFreeProgress.compChange.com.TweenMoveX(endPosX, time).SetEase(EaseType.Linear);
        }

        public void ChangeTipsState(ProgressFreeTalkTipsState tipsState)
        {
            switch (tipsState)
            {
                case ProgressFreeTalkTipsState.None:
                    ui.ctrlTips.selectedPage = "None";
                    break;
                case ProgressFreeTalkTipsState.Setting:
                    ui.ctrlTips.selectedPage = "Setting";
                    CS_ChangeMarkInfoReq msg = new CS_ChangeMarkInfoReq();
                    msg.option = PB_MarkOperationTypeEnum.OpAssistLevelPop;
                    MsgManager.instance.SendMsg(msg);
                    break;
                // case ProgressFreeTalkTipsState.Finish:
                //     ui.ctrlTips.selectedPage = "Finish";
                //     break;
            }

            _curState = tipsState;
        }

        public ProgressFreeTalkTipsState GetFreeTalkTipsState()
        {
            return _curState;
        }
        
        public void DismissWithAnimation()
        {
            if (isShow)
                ui.Out.Play(() => { this.Hide(); });
        }

        protected override void HandleNotification(string name, object body)
        {
            base.HandleNotification(name, body);
            switch (name)
            {
                case NotifyConsts.DismissWithAnimation:
                    DismissWithAnimation();
                    break;
            }
        }

        protected override string[] ListNotificationInterests()
        {
            return new[]
            {
                NotifyConsts.DismissWithAnimation,
            };
        }

        protected override void OnHide()
        {
            
        }
    }
}