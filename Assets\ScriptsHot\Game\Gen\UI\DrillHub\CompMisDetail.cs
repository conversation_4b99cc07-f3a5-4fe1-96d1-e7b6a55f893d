/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace UIBind.DrillHub
{
    public partial class CompMisDetail : GComponent
    {
        public static string pkgName => "DrillHub";
        public static string comName => "CompMisDetail";
        public static string url => "ui://9mx0po3ifdph17";

        public GTextField tfTitle;
        public GTextField tfMistake;
        public GTextField tfMistakeDesc;
        public GTextField tfCorrect;
        public GComponent tfCorrectDesc;
        public GTextField tfExplain;
        public GTextField tfExplainDesc;
        public GTextField tfExamples;
        public GList list;

        public static void Bind()
        {
            UIObjectFactory.SetPackageItemExtension(url, typeof(CompMisDetail));
        }

        public override void ConstructFromXML(XML xml)
        {
            tfTitle = GetChildAt(0) as GTextField;
            tfMistake = GetChildAt(4) as GTextField;
            tfMistakeDesc = GetChildAt(5) as GTextField;
            tfCorrect = GetChildAt(7) as GTextField;
            tfCorrectDesc = GetChildAt(8) as GComponent;
            tfExplain = GetChildAt(11) as GTextField;
            tfExplainDesc = GetChildAt(12) as GTextField;
            tfExamples = GetChildAt(15) as GTextField;
            list = GetChildAt(16) as GList;
        }
        public override void Dispose()
        {
            tfTitle = null;
            tfMistake = null;
            tfMistakeDesc = null;
            tfCorrect = null;
            tfCorrectDesc = null;
            tfExplain = null;
            tfExplainDesc = null;
            tfExamples = null;
            list = null;

            base.Dispose();
        }
    }
}