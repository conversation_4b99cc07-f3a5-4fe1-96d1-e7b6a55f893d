﻿using System.Linq;
using Msg.basic;
using Msg.course;
using Msg.incentive;
using ScriptsHot.Game.Modules.ExplorePush;
using ScriptsHot.Game.Modules.FragmentPractice;
using ScriptsHot.Game.Modules.MainBookshelf;
using UIBind.Main;
using UIBind.MainPath3D;
using UnityEngine;

namespace ScriptsHot.Game.Modules.MainPath
{
    public class MainPathController : BaseController
    {
        public MainPathController() : base(ModelConsts.MainPath)
        {
        }
        
        public enum MainPathOperateType
        {
            Init = 1,//登录初始数据
            SkipRefresh = 2,//跳听力关 领宝箱
            SettleRefresh = 3,// 跳关 复习 正常 结算
            ChangeLanguage = 4,//相当于切course了
            SwitchSection = 5,//切section
        }

        public MainPathModel Model => GetModel<MainPathModel>(ModelConsts.MainPath);

        private FragmentPracticeController FragController =>
            GetController<FragmentPracticeController>(ModelConsts.FragmentPractice);

        private ChatModel ChatModel => GetModel<ChatModel>(ModelConsts.Chat);
        private RecommendCardModel RecommendCardMod => GetModel<RecommendCardModel>(ModelConsts.RecommendCard);
        private CurrencyController CurrencyController =>
            GetController<CurrencyController>(ModelConsts.CurrencyController);

        private string initLaunchDataLoadingTimerKey;
        public override void OnInit()
        {
            base.OnInit();
            RegisterModel(new MainPathModel());
            
            MsgManager.instance.RegisterCallBack<SC_GetUserCourseAck>(SuccessGetUserCourse);
            MsgManager.instance.RegisterCallBack<SC_SkipCourseAck>(SuccessSkipCourse);
            MsgManager.instance.RegisterCallBack<SC_RewardBoxAck>(SuccessRewardBox);
            MsgManager.instance.RegisterCallBack<SC_GetUserShelfAck>(SuccessBookShelf);
            
            Notifier.instance.RegisterNotification(NotifyConsts.SettlementFinishEvent, DoSettlementFinishEvent);
            
            MainPathBackGround.Bind();
        }

        public override void OnUIInit()
        {
            base.OnUIInit();
            RegisterUI(new MainBookshelfUI(UIConsts.MainBookshelf));

            CompNodeStart.Bind();
        }
        
        public override void OnEnterGame()//登录时候的请求
        {
            TimerLogHelper.Ins.SaveTimeStart(TimerLogHelper.TimeType.LoadData_MainPath);
            //两个数据请求，一个课程，一个explore
            RequestGetUserCourse();
            ReqSignData(); //请求打卡数据 不然课程场景没灯
      
            GetController<ExplorePushController>(ModelConsts.ExplorePush).ReqGetHomepageGuideItem();
        }

        private MainPathOperateType _lastType = MainPathOperateType.Init;

        /// <summary>
        /// 这个方法 进入游戏之前 别乱用 ！！！！！！！
        /// </summary>
        public void RequestGetUserCourse(MainPathOperateType operateType = MainPathOperateType.Init, long sectionId = -1)
        {
            if (operateType == MainPathOperateType.SwitchSection &&
                Model.DicSectionDataCache.TryGetValue(sectionId, out var section) &&
                section.ServerSectionData.status == PB_ProgressStatusEnum.PSPass)
            {
                Model.SetFocusData(section, section.UnitDataList[0], section.UnitDataList[0].LevelDataList[0], true);
                SendNotification(NotifyConsts.UpdateMainPathEvent, "ClearData");
                SendNotification(NotifyConsts.CloseUI, UIConsts.MainBookshelf);
                return;
            }

            _msgAlreadyToUpdate = false;
            if (operateType == MainPathOperateType.SettleRefresh)
                _settleFinishToUpdate = false;

            _lastType = operateType;
            GetUI<CommBusyUI>(UIConsts.CommBusy).LazyShow();
            CS_GetUserCourseReq msg = new CS_GetUserCourseReq();
            msg.op_type = (int)operateType;
            msg.section_id = operateType == MainPathOperateType.Init ? -1 : sectionId;
            
            initLaunchDataLoadingTimerKey = TimerManager.instance.RegisterTimer(c =>
            {
                Debug.LogError("CS_GetUserCourseReq 5s 内未返回数据，可能是当前账号数据有损 or 开了VPN or 服务端有bug，请自查，仍有问题请上报【 服务环境+截取UserCourse traceid】");
            }, 5000, 1);

            MsgManager.instance.SendMsg(msg, RequestGetCourseFailCallback);
        }

        private void RequestGetCourseFailCallback(GRPCManager.ErrorType errorType = GRPCManager.ErrorType.None, Google.Protobuf.IMessage msg = null)
        {
            GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
            GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N("webview_notice_timeout", () =>
            {
                RequestGetUserCourse(_lastType);//重试类
            }, null, 1, null, "common_check");
        }

        private void SuccessGetUserCourse(SC_GetUserCourseAck msg)
        {
            if (!string.IsNullOrEmpty(initLaunchDataLoadingTimerKey))
            {
                TimerManager.instance.UnRegisterTimer(initLaunchDataLoadingTimerKey);
            }

            TimerLogHelper.Ins.SaveTimeEnd(TimerLogHelper.TimeType.LoadData_MainPath);
            if (msg.code == PB_Code.Normal)
            {
                _msgAlreadyToUpdate = true;
                GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
                HandleOpType(msg.data.op_type, msg.data);
            }
            else
            {
                RequestGetCourseFailCallback();
            }
                
            
            Notifier.instance.SendNotification(NotifyConsts.ScenePreLoadEvent_MainPath);
            AutoSection();
        }

        private void HandleOpType(int opType, PB_CourseData courseData)
        {
            PB_SectionData section = courseData.section_data_list[0];
            if (courseData.section_data_list.Count > 1)
                Model.SetNextSectionSimpleData(courseData.section_data_list[1]);
            else
                Model.SetNextSectionSimpleData(null);
            switch ((MainPathOperateType)opType)
            {
                case MainPathOperateType.Init:
                case MainPathOperateType.SkipRefresh:
                    if (opType == (int)MainPathOperateType.Init)
                        Model.SetCourseId(courseData.course_id);
                    Model.SetModelData(section, opType == (int)MainPathOperateType.Init);
                    Model.SetFocusData(Model.CurSectionData, Model.CurUnitData, Model.CurLevelData, true);
                    SendNotification(NotifyConsts.UpdateMainPathEvent);
                    break;
                case MainPathOperateType.SwitchSection:
                    Model.SetModelData(section);
                    MainPathSectionData sectionData = Model.DicSectionDataCache[section.section_id];
                    Model.SetFocusData(sectionData, sectionData.UnitDataList[0], sectionData.UnitDataList[0].LevelDataList[0], true);
                    SendNotification(NotifyConsts.UpdateMainPathEvent, "ClearData");
                    SendNotification(NotifyConsts.CloseUI, UIConsts.MainBookshelf);
                    break;
                case MainPathOperateType.SettleRefresh:
                    Model.SetModelData(section);
                    if (_settleFinishToUpdate && Model.FocusData.NeedChange)
                        DoSettlementFinishEvent();
                    return;
                case MainPathOperateType.ChangeLanguage:
                    Model.ClearMainPathData();
                    Model.SetCourseId(courseData.course_id);
                    Model.SetModelData(section);
                    Model.SetFocusData(Model.CurSectionData, Model.CurUnitData, Model.CurLevelData, true);
                    SendNotification(NotifyConsts.UpdateMainPathEvent, "ClearData");
                    break;
            }

            DoUpdateLevelComplete();
        }

        //数据和msg都到了才更新
        private bool _settleFinishToUpdate;
        private bool _msgAlreadyToUpdate;
        private void DoSettlementFinishEvent(string str = "", object obj = null)
        {
            _settleFinishToUpdate = true;
            if (_msgAlreadyToUpdate && Model.FocusData.NeedChange)//复习 or 下一关
            {
                Model.SetFocusData(Model.CurSectionData, Model.CurUnitData, Model.CurLevelData, true);
                SendNotification(NotifyConsts.UpdateMainPathEvent);
                DoUpdateLevelComplete();
            }
        }

        private void DoUpdateLevelComplete()
        {
            if (Model.RunningLevelData != Model.LastRunningLevelData) //最新通关的要通知
            {
                VFDebug.LogError($"FUCK 3D Complete Level -- furNum -> {Model.FocusData.UnitData.ServerUnitData.unit_scene_data.completed_furniture_count}");
                SendNotification(NotifyConsts.MainPath3DCompleteLevel,
                    new CompleteLevel
                    {
                        furNum = Model.FocusData.UnitData.ServerUnitData.unit_scene_data
                            .completed_furniture_count,
                        sceneId = Model.FocusData.UnitData.ServerUnitData.unit_scene_data.scene_name,
                    });
            }
        }

        public void RequestSkipCourse(int sectionIndex,int unitIndex,int levelIndex,PB_SkipTypeEnum skipType = PB_SkipTypeEnum.SkipTLevel)
        {
            GetUI<CommBusyUI>(UIConsts.CommBusy).LazyShow();
            CS_SkipCourseReq msg = new CS_SkipCourseReq();
            msg.skip_type = skipType;
            msg.section_index = sectionIndex;
            msg.unit_index = unitIndex;
            msg.level_index = levelIndex;
            msg.course_id = Model.CourseId;
            MsgManager.instance.SendMsg(msg,RequestSkipFailCallback);
        }
        
        private void RequestSkipFailCallback(GRPCManager.ErrorType errorType = GRPCManager.ErrorType.None, Google.Protobuf.IMessage msg = null)
        {
            GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N("common_netError", () =>
            {
                GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
            }, null, 1, null, "common_check");
        }

        private void SuccessSkipCourse(SC_SkipCourseAck msg)
        {
            if (msg.code == PB_Code.Normal)
                RequestGetUserCourse(MainPathOperateType.SkipRefresh);//跳过api
            else
                RequestSkipFailCallback();
        }

        // 切换Section
        public void Change2FinishOrRunningSection(long sectionID)
        {
            RequestGetUserCourse(MainPathOperateType.SwitchSection, sectionID);
        }

        public void RequestBookShelf(int opType = 0)
        {
            GetUI<CommBusyUI>(UIConsts.CommBusy).LazyShow();
            CS_GetUserShelfReq msg = new CS_GetUserShelfReq();
            msg.op_type = opType;
            MsgManager.instance.SendMsg(msg, RequestBookShelfFailCallback);
        }
        
        public void SuccessBookShelf(SC_GetUserShelfAck msg)
        {
            if (msg.data != null && msg.code == PB_Code.Normal)
            {
                Model.SetBookShelfCourseData(msg.data);
                GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
                GetUI<MainBookshelfUI>(UIConsts.MainBookshelf).Show();
            }
            else
            {
                RequestBookShelfFailCallback();
            }
        }
        
        private void RequestBookShelfFailCallback(GRPCManager.ErrorType errorType = GRPCManager.ErrorType.None, Google.Protobuf.IMessage msg = null)
        {
            GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N("common_netError", () =>
            {
                GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
            }, null, 1, null, "common_check");
        }

        /// <summary>
        /// 进关前触发 仅在路径结点使用
        /// </summary>
        public void EnterStage(MainPathSectionData.MainPathNodesStruct nodeData, PB_SessionData sessionData,
            bool isReview = false, bool needTicket = true)
        {
            if (nodeData.NodeData.ServerLevelData.level_type == PB_LevelTypeEnum.LTCup ||
                nodeData.NodeData.ServerLevelData.level_type == PB_LevelTypeEnum.LTStar ||
                nodeData.NodeData.ServerLevelData.level_type == PB_LevelTypeEnum.LTDumbBell ||
                nodeData.NodeData.ServerLevelData.level_type == PB_LevelTypeEnum.LTBook ||
                nodeData.NodeData.ServerLevelData.level_type == PB_LevelTypeEnum.LTRadio)
            {
                DoNewEnterStage(nodeData, sessionData, isReview, needTicket);
            }
            else
            {
                DoOldEnterStage(nodeData, sessionData, isReview, needTicket);
            }

        }

        private void DoNewEnterStage(MainPathSectionData.MainPathNodesStruct nodeData, PB_SessionData sessionData,
            bool isReview = false, bool needTicket = true)
        {
            PB_CourseTypeEnum courseType = isReview ? PB_CourseTypeEnum.CTReview : PB_CourseTypeEnum.CTSession;

            void Callback()
            {
                Model.SetFocusData(Model.FocusData.SectionData, nodeData.UnitData, nodeData.NodeData, !isReview);
                ChatModel.SetChatMode(sessionData.dialog_mode); //TODO:这个 老东西了
                switch (nodeData.NodeData.ServerLevelData.level_type)
                {
                    case PB_LevelTypeEnum.LTCup:
                    case PB_LevelTypeEnum.LTStar:
                    case PB_LevelTypeEnum.LTDumbBell:
                        FragController.EnterPractice(Model.CourseId, courseType, Model.FocusData.SectionData.ID,
                            nodeData.UnitData.ServerUnitData.unit_id, nodeData.UnitData.ServerUnitData.unit_index,
                            nodeData.NodeData.NodeId, nodeData.NodeData.ServerLevelData.level_index,
                            nodeData.NodeData.ServerLevelData.level_type, sessionData.session_id,
                            sessionData.session_index);
                        break;
                    case PB_LevelTypeEnum.LTBook:
                        FragController.EnterBook(Model.CourseId, nodeData, isReview);
                        break;
                    case PB_LevelTypeEnum.LTRadio:
                        FragController.EnterRadio(Model.CourseId, nodeData, isReview);
                        break;
                }
            }

            if (needTicket)
                CurrencyController.SendBuyCourseTicketReq(Callback, nodeData, Model.FocusData.SectionData,
                Model.CourseId, courseType);
            else
                Callback();
        }

        private void DoOldEnterStage(MainPathSectionData.MainPathNodesStruct nodeData, PB_SessionData sessionData,
            bool isReview = false, bool needTicket = true)
        {
            PB_CourseTypeEnum courseType = isReview ? PB_CourseTypeEnum.CTReview : PB_CourseTypeEnum.CTSession;
            
            void Callback()
            {
                Model.SetFocusData(Model.FocusData.SectionData, nodeData.UnitData, nodeData.NodeData, !isReview);
                ChatModel.SetChatMode(sessionData.dialog_mode);
                switch (nodeData.NodeData.ServerLevelData.level_type)
                {
                    case PB_LevelTypeEnum.LTVideo:
                    case PB_LevelTypeEnum.LTAua:
                        SceneStateChatParam chatParam = new SceneStateChatParam
                        {
                            unitUid = nodeData.UnitData.ServerUnitData.unit_id,
                            avatarId = 0,
                            isTask = true,
                            taskId = sessionData.session_id,
                            entranceType = PB_DialogSourceEnum.DialogSourceLearnPath,
                            chatMode = sessionData.dialog_mode,
                            mainPathParams = new PB_CourseLearnPathParams
                            {
                                course_id = Model.CourseId,
                                section_index = Model.FocusData.SectionData.ServerSectionData.section_index,
                                unit_index = nodeData.UnitData.ServerUnitData.unit_index,
                                level_index = nodeData.NodeData.ServerLevelData.level_index,
                                session_index = sessionData.session_index,
                                level_type = nodeData.NodeData.ServerLevelData.level_type,
                                course_type = courseType
                            }
                        };
                        
                        Notifier.instance.SendNotification(NotifyConsts.ChatNewEnter, chatParam);
                        break;
                    case PB_LevelTypeEnum.LTWarmUp:
                        RecommendCardMod.SetCurTaskID(sessionData.session_id);
                        GetController<ReviewQuestionController>(ModelConsts.ReviewQuestionModel)
                            .EnterReviewQuestion(PB_DialogSourceEnum.DialogSourceLearnPath, sessionData.session_id,
                                new PB_CourseLearnPathParams
                                {
                                    course_id = Model.CourseId,
                                    section_index = Model.FocusData.SectionData.ServerSectionData.section_index,
                                    unit_index = nodeData.UnitData.ServerUnitData.unit_index,
                                    level_index = nodeData.NodeData.ServerLevelData.level_index,
                                    session_index = sessionData.session_index,
                                    level_type = nodeData.NodeData.ServerLevelData.level_type,
                                    course_type = courseType
                                });
                        break;
                }
            }

            if (needTicket)
                CurrencyController.SendBuyDialogTicketReq(sessionData.dialog_mode, Callback,
                    sessionData.session_id, sessionData.avatar_id);
            else
                Callback();
        }

        /// <summary>
        /// 跳Unit
        /// </summary>
        public void EnterJump(PB_UnitData unitData)
        {
            PB_LevelData levelData = unitData.level_data_list[0];
            PB_SessionData sessionData = levelData.session_data_list[0];
            Model.SetFocusSectionId(Model.FocusData.SectionData.ServerSectionData.section_id);
            FragController.EnterPractice(Model.CourseId, PB_CourseTypeEnum.CTUnitTest,
                Model.CurSectionData.ID, unitData.unit_id, unitData.unit_index, levelData.level_id,
                levelData.level_index, levelData.level_type, sessionData.session_id, sessionData.session_index, true);
        }
        
        /// <summary>
        /// 书架页跳关 
        /// </summary>
        public void EnterJump(PB_SectionData sectionData)
        {
            PB_UnitData unitData = sectionData.unit_data_list[0];
            PB_LevelData levelData = unitData.level_data_list[0];
            PB_SessionData sessionData = levelData.session_data_list[0];
            //先SetFocusData 再强行SetFocusSectionId
            Model.SetFocusData(Model.FocusData.SectionData, Model.FocusData.UnitData, Model.FocusData.LevelData, true);
            Model.SetFocusSectionId(sectionData.section_id);
            FragController.EnterPractice(Model.CourseId, PB_CourseTypeEnum.CTSectionTest,
                sectionData.section_index, sectionData.unit_data_list[0].unit_id, unitData.unit_index,
                levelData.level_id, levelData.level_index, levelData.level_type, sessionData.session_id,
                sessionData.session_index, true);
        }

        /// <summary>
        /// 看当前进度在哪就从哪发 按道理来说不会有curNode为空的情况
        /// </summary>
        public void EnterCurrentLevel()
        {
            MainPathSectionData.MainPathNodesStruct curNode =
                Model.CurSectionData.NodeList.FirstOrDefault(u =>
                    u.NodeData?.ServerLevelData.status == PB_ProgressStatusEnum.PSRunning);
            
            EnterStage(curNode, curNode.NodeData.GetNextSessionData(), false, false);
        }
        
        public void ReqOpenRewardBox()
        {
            GetUI<CommBusyUI>(UIConsts.CommBusy).LazyShow();
            var msg = new CS_RewardBoxReq();
            MsgManager.instance.SendMsg(msg,RequestRewardFailCallback);
        }
        
        private void RequestRewardFailCallback(GRPCManager.ErrorType errorType = GRPCManager.ErrorType.None, Google.Protobuf.IMessage msg = null)
        {
            GetUI<CommConfirmUI>(UIConsts.CommConfirm).OpenI18N("common_netError", () =>
            {
                GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
            }, null, 1, null, "common_check");
        }

        private void SuccessRewardBox(SC_RewardBoxAck msg)
        {
            if (msg.code == PB_Code.Normal)
            {
                RequestGetUserCourse(MainPathOperateType.SkipRefresh);//宝箱类
                GetUI<CommBusyUI>(UIConsts.CommBusy).Hide();
                GetUI<CommonGiftUI>(UIConsts.CommonGift)
                    .ShowGiftDiamondUI(msg.data.economic_info.diamond_info.permanent_info);
                GetController<CurrencyController>(ModelConsts.CurrencyController)
                    .SendGetEconomicInfoReq(GameEventName.GameEnter);//刷新钻石数
            }
            else
            {
                RequestRewardFailCallback();
            }
        }

        private async void ReqSignData()
        {
            var resp = await MsgManager.instance.SendAsyncMsg<SC_GetCheckinSummaryAck>(new CS_GetCheckinSummaryReq());
            VFDebug.Log("HSW CS_GetCheckinSummaryReq");
            if (resp != null)
                GameEntry.SignC.SignModel.SetSignSummary(resp.data);
        }

        #region Onboarding之后初始进入场景

        private bool _needAuto = false;
        public bool IsOnBoarding => _needAuto;
        private bool _skipFlag = false;
        private bool _autoIsA1 = false;
        private int _autoSectionIndex = -1;
        public void SetOnBoardingDatas(bool isA1 , int index)
        {
            _needAuto = true;
            _autoIsA1 = isA1;
            _autoSectionIndex = index;
        }
        public void SetOnBoardingDatas()
        {
            _needAuto = true;
        }
        private void AutoSection()
        {
            //debug
            //A1
            // _needAuto = true;
            // _autoIsA1 = true;
            // _autoSectionIndex = -1;
            //
            // //A1 + 
            // _needAuto = true;
            // _autoIsA1 = false;
            // _autoSectionIndex = 5;
            //debug
            
            
            if (_needAuto)
            {
                Notifier.instance.SendNotification(NotifyConsts.MainPath3DLeavePath);
                GameEntry.MainPathC.EnterCurrentLevel();
                _needAuto = false;
            }
        }

        public void OnSettlementSkipOver(bool is_skip_success)
        {
            if (!is_skip_success && _skipFlag)
            {
                GetUI<MainBookshelfUI>(UIConsts.MainBookshelf).Show();
            }
            _skipFlag = false;
        }
        #endregion
    }
}