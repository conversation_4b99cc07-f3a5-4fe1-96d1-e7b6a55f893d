﻿using System.Collections.Generic;
using FairyGUI;
using Game.Modules.FragmentPractice;
using Game.Modules.Record;
using Msg.question;
using ScriptsHot.Game.Modules.Common;
using ScriptsHot.Game.Modules.ReviewQuestion.Questions.Parser;
using UnityEngine;

namespace UIBind.FragmentPractice
{
    public partial class HideShowQuestion : IRecordEventListener, IQuestionEventListener
    {
        GRichTextFieldPlus TfQuestion => tfSentence as GRichTextFieldPlus;

        protected override void OnAddedToStage()
        {
            btnDisplay.onClick.Add(OnClickDisplayStem);
            RecordEventManager.Instance.AddListener(this);
            TfQuestion.EnableTranslate = false;
            TfQuestion.EnableMatchColoring = true;
        }

        protected override void OnRemovedFromStage()
        {
            base.OnRemovedFromStage();
            RecordEventManager.Instance.RemoveListener(this);
            TTSManager.instance.StopTTS();//清空声音
        }

        public override void ShowPractice(AFragAnswer answerComp)
        {
            if (Practice.AudioId <= 0)
                VFDebug.LogError("Practice.AudioId 为:: " + Practice.AudioId);

            showStem.selectedIndex = 0;

            var blocks = GRichTextFieldPlus.SplitWords(Practice.GetStem().Trim(), defaultColor:ClearColor, enableTranslate:false);

            foreach (var block in blocks)
            {
                block.isUnderline = !string.IsNullOrWhiteSpace(block.text);
                block.underlineColor = TfQuestion.color;
            }

            TfQuestion.SetWords(blocks);


            if (!IsCurrent) return;
            btnPlay.AudioId = Practice.AudioId;
            btnPlay.Play();
            btnPlay.onClick.Add(() =>
            {
                RecordEventManager.Instance.DispatchRecordCancel();
            });
        }

        private void OnClickDisplayStem()
        {
            if (!IsCurrent) return;
            RecordEventManager.Instance.DispatchRecordCancel();

            showStem.selectedIndex = 1;

            var blocks = GRichTextFieldPlus.SplitWords(Practice.GetStem().Trim(), enableTranslate:false);

            foreach (var block in blocks)
            {
                block.isUnderline = !string.IsNullOrWhiteSpace(block.text);
                block.underlineColor = TfQuestion.color;
            }

            TfQuestion.SetWords(blocks);

            btnPlay.Play();

            DotPracticeManager.Instance.Collect(new DataDot_DisplayText());
        }

        private int recordId = -1;


        public bool IsReceivingEvents => IsCurrent;

        public void OnRecordStart(string rawString, int recordId)
        {
            this.recordId = recordId;
            btnPlay.Stop();
            showStem.selectedIndex = 0;
            ClearText();
        }

        public void ClearText()
        {
            var blocks = TfQuestion.blocks;
            foreach (var block in blocks)
            {
                block.fontColor = ClearColor;
            }
            TfQuestion.SetWords(blocks);
        }

        public void OnRecordStop()
        {
            StopRecord(true);
        }

        public void OnRecordCancel()
        {
            StopRecord(false);
        }

        public void OnVad()
        {
            StopRecord(true);
        }

        public void OnCountDown() { }

        public void OnMatchAll()
        {
            StopRecord(true);
        }

        public void OnTranscription(string transcribedText, int recordId)
        {
            if (this.recordId != recordId) return;
            RefreshText(false);
        }

        private void StopRecord(bool showText)
        {
            this.recordId = -1;

            if (showText)
            {
                RefreshText(true);
            }
        }

        private void RefreshText(bool showAll)
        {
            showStem.selectedIndex = 1;

            List<MatchResult> results =
                SpeechToTextManager.instance.MatchWordsInReference(MatchedColor, showAll?BlackColor:ClearColor);                

            Dictionary<string, Color> wordColors = new(20);
            foreach (var result in results)
            { 
                wordColors[result.MatchedWord] = result.Color;
            }
            TfQuestion.MatchColoring(wordColors);
        }

        public void OnAnswered() { }

        public void OnSubmit() { }

        public void OnRetry()
        {
            if (showStem == null)
                return;
            showStem.selectedIndex = 0;
        }

        public void AutoCheck() { }
        public void OnReset() { }
        public void OnJumpListenTask() { }
        public void OnJumpSpeakTask() { }
    }
}