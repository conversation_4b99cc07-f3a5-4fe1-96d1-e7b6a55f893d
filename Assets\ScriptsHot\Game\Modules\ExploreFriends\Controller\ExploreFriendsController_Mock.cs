using System.Collections;
using System.Collections.Generic;
using Msg.explore;
using UnityEngine;

public partial class ExploreFriendsController
{
    public void GM_OpenMainUI()
    {
        if(Model.HasFriend())
        {
            UIManager.instance.GetUI(UIConsts.ExploreFriendsBagPanel).Show();
        }
        else
        {
            UIManager.instance.GetUI(UIConsts.ExploreFriendsNewHandPanel).Show();
        }
    }
    
    
    public void MockOnDrawNewFriendResp()
    {
        SC_DrawNewFriendResp msg = new SC_DrawNewFriendResp()
        {
            code = PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS,
            data = new PB_DrawNewFriendData()
            {
                avatarId = 17212001975922698,
                drawRecordId = 1001,
            }
        };
        Model.SetDrawNewFriendResp(msg);
    }
   
    
    public void MockOnBecomeFriendResp()
    {
        Model.UpdateFriendInfo(new SC_GetFriendSlotListResp()
        {
            code = PB_Explore_BizCode.EO_BIZ_CODE_SUCCESS,
            drawConsumeDiamondCount = 99,
            switchConsumeDiamondCount = 9,
            selectedSlotIndex = 1,
            slotList = { 
                new PB_SlotDetailData()
                {
                    slotIndex = 1,
                    slotIsOpen = true,
                    slotId = 1,
                    avatarId = 17212001975922698,
                    levelId = 1,
                    closenessValue = 1,
                }
            }
        });

    }
}
