using System.Collections;
using System.Collections.Generic;
using System.Net.NetworkInformation;
using UnityEngine;

public class BI
{
     private static Queue<Dictionary<string, object>> _dicPool = new Queue<Dictionary<string, object>>();
     private static MainModel _mainModel => ModelManager.instance.GetModel(ModelConsts.Main) as MainModel;

    public static Dictionary<string, object> ApplyBiDic()
    {
        Dictionary<string, object> dic;
        if (_dicPool.Count > 0)
            dic = _dicPool.Dequeue();
        {
            dic = new Dictionary<string, object>();
        }
        return dic;
    }

    public static void reclaimTask(Dictionary<string, object> dic)
    {
        if (dic == null) return;
        dic.Clear();
        _dicPool.Enqueue(dic);
    }

    /// <summary>
    /// 打点公共事件
    /// </summary>
    /// <param name="eventName">事件名称</param>
    /// <param name="paramDict">打点数据</param>
    public static void Collect(string eventName, Dictionary<string, object> paramDict = null)
    {
        AddBaseData(paramDict);
        AmazonBi.SendBI(eventName,paramDict);
        reclaimTask(paramDict);
    }

    private static void AddBaseData(Dictionary<string, object> dic)
    {
        dic.TryAdd(BIkey.client_version, AppConstExt.LogicVersion);
        dic.TryAdd(BIkey.userId, _mainModel.userID);
        dic.TryAdd(BIkey.model, SystemInfo.deviceModel);        
        dic.TryAdd(BIkey.networkType, Application.internetReachability);
  
        if (Application.platform == RuntimePlatform.IPhonePlayer)
        {
            dic.TryAdd(BIkey.os, "iOS");
        }
        else if (Application.platform == RuntimePlatform.Android)
        {
            dic.TryAdd(BIkey.os, "Android");
        }
        dic.TryAdd(BIkey.osVersion, SystemInfo.operatingSystem);
        dic.TryAdd(BIkey.version, Application.version);
        dic.TryAdd(BIkey.ip, AmazonBi.GetLocalIPAddress());
        dic.TryAdd(BIkey.sessionId, Main.BiGuid);
    }
}
