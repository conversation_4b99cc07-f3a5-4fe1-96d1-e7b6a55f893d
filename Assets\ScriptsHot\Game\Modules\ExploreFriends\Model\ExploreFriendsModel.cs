﻿using System.Collections.Generic;

public partial class ExploreFriendsModel:BaseModel
{
    List<ExploreFriendCfg> friendList= new List<ExploreFriendCfg>();
    public List<ExploreFriendCfg> FriendList => friendList;
    public ExploreFriendsModel() : base(ModelConsts.ExploreFriends)
    {
    }
    
    public void OnInit()
    {
        InitSlotData();
        InitStaticData();
    }

    private void InitStaticData()
    {
        friendList.Clear();
        foreach (var exploreFriendCfg in Cfg.T.TBExploreFriend.DataList)
        {
            friendList.Add(exploreFriendCfg);
        }
    }
    
    public ExploreFriendCfg  GetFriendCfgByID(long id)
    {
        foreach (var exploreFriendCfg in FriendList)
        {
            if (exploreFriendCfg.AvatarModleId == id)
            {
                return exploreFriendCfg;
            }
        }

        return null;
    }
}